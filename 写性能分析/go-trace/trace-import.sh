#!/bin/bash
set -e -x

# 开启trace
curl -X POST -k https://193.116.2.194:2524/debug/trace/start | jq .

# 版本包导入
cd /paasdata/op-data/pdm-cli/pdm/tools/swr_snapshot_import
time bash -x kubeall_snapshotimport.sh /paasdata/offline/paas/pdm-cli/pkgs/kubeall-pkg/swr-data swr-plat 2524

# 停止trace
curl -X POST -k https://193.116.2.194:2524/debug/trace/stop | jq .

# 获取trace状态
curl -k https://193.116.2.194:2524/debug/trace/status | jq .

# 获取trace日志
ls /paasdata/op-log/op-kubeall-swr/trace-*
mv /paasdata/op-log/op-kubeall-swr/trace-* /home/<USER>/
chown ubuntu:ubuntu /home/<USER>/trace-*