#!/bin/bash
set -e -x

# 开启trace
curl -X POST -k https://193.116.2.194:2524/debug/trace/start | jq .

# 上传镜像
docker push swr-plat:2524/admin/image/test:v1.1

# 停止trace
curl -X POST -k https://193.116.2.194:2524/debug/trace/stop | jq .

# 清除已上传的镜像
curl -X DELETE -k https://swr-plat:2524/v2/admin/image/test/manifests/v1.1 | jq .
curl -X POST -k https://swr-plat:2524/autogc/run | jq .

# 获取trace状态
curl -k https://193.116.2.194:2524/debug/trace/status | jq .

# 获取trace日志
ls /paasdata/op-log/op-kubeall-swr/trace-*
mv /paasdata/op-log/op-kubeall-swr/trace-* /home/<USER>/
chown ubuntu:ubuntu /home/<USER>/trace-*