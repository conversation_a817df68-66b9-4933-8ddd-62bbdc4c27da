<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg version="1.1" width="1200" height="502" onload="init(evt)" viewBox="0 0 1200 502" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<!-- Flame graph stack visualization. See https://github.com/brendangregg/FlameGraph for latest version, and http://www.brendangregg.com/flamegraphs.html for examples. -->
<!-- NOTES:  -->
<defs>
	<linearGradient id="background" y1="0" y2="1" x1="0" x2="0" >
		<stop stop-color="#eeeeee" offset="5%" />
		<stop stop-color="#eeeeb0" offset="95%" />
	</linearGradient>
</defs>
<style type="text/css">
	text { font-family:Verdana; font-size:12px; fill:rgb(0,0,0); }
	#search, #ignorecase { opacity:0.1; cursor:pointer; }
	#search:hover, #search.show, #ignorecase:hover, #ignorecase.show { opacity:1; }
	#subtitle { text-anchor:middle; font-color:rgb(160,160,160); }
	#title { text-anchor:middle; font-size:17px}
	#unzoom { cursor:pointer; }
	#frames > *:hover { stroke:black; stroke-width:0.5; cursor:pointer; }
	.hide { display:none; }
	.parent { opacity:0.5; }
</style>
<script type="text/ecmascript">
<![CDATA[
	"use strict";
	var details, searchbtn, unzoombtn, matchedtxt, svg, searching, currentSearchTerm, ignorecase, ignorecaseBtn;
	function init(evt) {
		details = document.getElementById("details").firstChild;
		searchbtn = document.getElementById("search");
		ignorecaseBtn = document.getElementById("ignorecase");
		unzoombtn = document.getElementById("unzoom");
		matchedtxt = document.getElementById("matched");
		svg = document.getElementsByTagName("svg")[0];
		searching = 0;
		currentSearchTerm = null;

		// use GET parameters to restore a flamegraphs state.
		var params = get_params();
		if (params.x && params.y)
			zoom(find_group(document.querySelector('[x="' + params.x + '"][y="' + params.y + '"]')));
                if (params.s) search(params.s);
	}

	// event listeners
	window.addEventListener("click", function(e) {
		var target = find_group(e.target);
		if (target) {
			if (target.nodeName == "a") {
				if (e.ctrlKey === false) return;
				e.preventDefault();
			}
			if (target.classList.contains("parent")) unzoom(true);
			zoom(target);
			if (!document.querySelector('.parent')) {
				// we have basically done a clearzoom so clear the url
				var params = get_params();
				if (params.x) delete params.x;
				if (params.y) delete params.y;
				history.replaceState(null, null, parse_params(params));
				unzoombtn.classList.add("hide");
				return;
			}

			// set parameters for zoom state
			var el = target.querySelector("rect");
			if (el && el.attributes && el.attributes.y && el.attributes._orig_x) {
				var params = get_params()
				params.x = el.attributes._orig_x.value;
				params.y = el.attributes.y.value;
				history.replaceState(null, null, parse_params(params));
			}
		}
		else if (e.target.id == "unzoom") clearzoom();
		else if (e.target.id == "search") search_prompt();
		else if (e.target.id == "ignorecase") toggle_ignorecase();
	}, false)

	// mouse-over for info
	// show
	window.addEventListener("mouseover", function(e) {
		var target = find_group(e.target);
		if (target) details.nodeValue = "Function: " + g_to_text(target);
	}, false)

	// clear
	window.addEventListener("mouseout", function(e) {
		var target = find_group(e.target);
		if (target) details.nodeValue = ' ';
	}, false)

	// ctrl-F for search
	// ctrl-I to toggle case-sensitive search
	window.addEventListener("keydown",function (e) {
		if (e.keyCode === 114 || (e.ctrlKey && e.keyCode === 70)) {
			e.preventDefault();
			search_prompt();
		}
		else if (e.ctrlKey && e.keyCode === 73) {
			e.preventDefault();
			toggle_ignorecase();
		}
	}, false)

	// functions
	function get_params() {
		var params = {};
		var paramsarr = window.location.search.substr(1).split('&');
		for (var i = 0; i < paramsarr.length; ++i) {
			var tmp = paramsarr[i].split("=");
			if (!tmp[0] || !tmp[1]) continue;
			params[tmp[0]]  = decodeURIComponent(tmp[1]);
		}
		return params;
	}
	function parse_params(params) {
		var uri = "?";
		for (var key in params) {
			uri += key + '=' + encodeURIComponent(params[key]) + '&';
		}
		if (uri.slice(-1) == "&")
			uri = uri.substring(0, uri.length - 1);
		if (uri == '?')
			uri = window.location.href.split('?')[0];
		return uri;
	}
	function find_child(node, selector) {
		var children = node.querySelectorAll(selector);
		if (children.length) return children[0];
	}
	function find_group(node) {
		var parent = node.parentElement;
		if (!parent) return;
		if (parent.id == "frames") return node;
		return find_group(parent);
	}
	function orig_save(e, attr, val) {
		if (e.attributes["_orig_" + attr] != undefined) return;
		if (e.attributes[attr] == undefined) return;
		if (val == undefined) val = e.attributes[attr].value;
		e.setAttribute("_orig_" + attr, val);
	}
	function orig_load(e, attr) {
		if (e.attributes["_orig_"+attr] == undefined) return;
		e.attributes[attr].value = e.attributes["_orig_" + attr].value;
		e.removeAttribute("_orig_"+attr);
	}
	function g_to_text(e) {
		var text = find_child(e, "title").firstChild.nodeValue;
		return (text)
	}
	function g_to_func(e) {
		var func = g_to_text(e);
		// if there's any manipulation we want to do to the function
		// name before it's searched, do it here before returning.
		return (func);
	}
	function update_text(e) {
		var r = find_child(e, "rect");
		var t = find_child(e, "text");
		var w = parseFloat(r.attributes.width.value) -3;
		var txt = find_child(e, "title").textContent.replace(/\([^(]*\)$/,"");
		t.attributes.x.value = parseFloat(r.attributes.x.value) + 3;

		// Smaller than this size won't fit anything
		if (w < 2 * 12 * 0.59) {
			t.textContent = "";
			return;
		}

		t.textContent = txt;
		var sl = t.getSubStringLength(0, txt.length);
		// check if only whitespace or if we can fit the entire string into width w
		if (/^ *$/.test(txt) || sl < w)
			return;

		// this isn't perfect, but gives a good starting point
		// and avoids calling getSubStringLength too often
		var start = Math.floor((w/sl) * txt.length);
		for (var x = start; x > 0; x = x-2) {
			if (t.getSubStringLength(0, x + 2) <= w) {
				t.textContent = txt.substring(0, x) + "..";
				return;
			}
		}
		t.textContent = "";
	}

	// zoom
	function zoom_reset(e) {
		if (e.attributes != undefined) {
			orig_load(e, "x");
			orig_load(e, "width");
		}
		if (e.childNodes == undefined) return;
		for (var i = 0, c = e.childNodes; i < c.length; i++) {
			zoom_reset(c[i]);
		}
	}
	function zoom_child(e, x, ratio) {
		if (e.attributes != undefined) {
			if (e.attributes.x != undefined) {
				orig_save(e, "x");
				e.attributes.x.value = (parseFloat(e.attributes.x.value) - x - 10) * ratio + 10;
				if (e.tagName == "text")
					e.attributes.x.value = find_child(e.parentNode, "rect[x]").attributes.x.value + 3;
			}
			if (e.attributes.width != undefined) {
				orig_save(e, "width");
				e.attributes.width.value = parseFloat(e.attributes.width.value) * ratio;
			}
		}

		if (e.childNodes == undefined) return;
		for (var i = 0, c = e.childNodes; i < c.length; i++) {
			zoom_child(c[i], x - 10, ratio);
		}
	}
	function zoom_parent(e) {
		if (e.attributes) {
			if (e.attributes.x != undefined) {
				orig_save(e, "x");
				e.attributes.x.value = 10;
			}
			if (e.attributes.width != undefined) {
				orig_save(e, "width");
				e.attributes.width.value = parseInt(svg.width.baseVal.value) - (10 * 2);
			}
		}
		if (e.childNodes == undefined) return;
		for (var i = 0, c = e.childNodes; i < c.length; i++) {
			zoom_parent(c[i]);
		}
	}
	function zoom(node) {
		var attr = find_child(node, "rect").attributes;
		var width = parseFloat(attr.width.value);
		var xmin = parseFloat(attr.x.value);
		var xmax = parseFloat(xmin + width);
		var ymin = parseFloat(attr.y.value);
		var ratio = (svg.width.baseVal.value - 2 * 10) / width;

		// XXX: Workaround for JavaScript float issues (fix me)
		var fudge = 0.0001;

		unzoombtn.classList.remove("hide");

		var el = document.getElementById("frames").children;
		for (var i = 0; i < el.length; i++) {
			var e = el[i];
			var a = find_child(e, "rect").attributes;
			var ex = parseFloat(a.x.value);
			var ew = parseFloat(a.width.value);
			var upstack;
			// Is it an ancestor
			if (0 == 0) {
				upstack = parseFloat(a.y.value) > ymin;
			} else {
				upstack = parseFloat(a.y.value) < ymin;
			}
			if (upstack) {
				// Direct ancestor
				if (ex <= xmin && (ex+ew+fudge) >= xmax) {
					e.classList.add("parent");
					zoom_parent(e);
					update_text(e);
				}
				// not in current path
				else
					e.classList.add("hide");
			}
			// Children maybe
			else {
				// no common path
				if (ex < xmin || ex + fudge >= xmax) {
					e.classList.add("hide");
				}
				else {
					zoom_child(e, xmin, ratio);
					update_text(e);
				}
			}
		}
		search();
	}
	function unzoom(dont_update_text) {
		unzoombtn.classList.add("hide");
		var el = document.getElementById("frames").children;
		for(var i = 0; i < el.length; i++) {
			el[i].classList.remove("parent");
			el[i].classList.remove("hide");
			zoom_reset(el[i]);
			if(!dont_update_text) update_text(el[i]);
		}
		search();
	}
	function clearzoom() {
		unzoom();

		// remove zoom state
		var params = get_params();
		if (params.x) delete params.x;
		if (params.y) delete params.y;
		history.replaceState(null, null, parse_params(params));
	}

	// search
	function toggle_ignorecase() {
		ignorecase = !ignorecase;
		if (ignorecase) {
			ignorecaseBtn.classList.add("show");
		} else {
			ignorecaseBtn.classList.remove("show");
		}
		reset_search();
		search();
	}
	function reset_search() {
		var el = document.querySelectorAll("#frames rect");
		for (var i = 0; i < el.length; i++) {
			orig_load(el[i], "fill")
		}
		var params = get_params();
		delete params.s;
		history.replaceState(null, null, parse_params(params));
	}
	function search_prompt() {
		if (!searching) {
			var term = prompt("Enter a search term (regexp " +
			    "allowed, eg: ^ext4_)"
			    + (ignorecase ? ", ignoring case" : "")
			    + "\nPress Ctrl-i to toggle case sensitivity", "");
			if (term != null) search(term);
		} else {
			reset_search();
			searching = 0;
			currentSearchTerm = null;
			searchbtn.classList.remove("show");
			searchbtn.firstChild.nodeValue = "Search"
			matchedtxt.classList.add("hide");
			matchedtxt.firstChild.nodeValue = ""
		}
	}
	function search(term) {
		if (term) currentSearchTerm = term;
		if (currentSearchTerm === null) return;

		var re = new RegExp(currentSearchTerm, ignorecase ? 'i' : '');
		var el = document.getElementById("frames").children;
		var matches = new Object();
		var maxwidth = 0;
		for (var i = 0; i < el.length; i++) {
			var e = el[i];
			var func = g_to_func(e);
			var rect = find_child(e, "rect");
			if (func == null || rect == null)
				continue;

			// Save max width. Only works as we have a root frame
			var w = parseFloat(rect.attributes.width.value);
			if (w > maxwidth)
				maxwidth = w;

			if (func.match(re)) {
				// highlight
				var x = parseFloat(rect.attributes.x.value);
				orig_save(rect, "fill");
				rect.attributes.fill.value = "rgb(230,0,230)";

				// remember matches
				if (matches[x] == undefined) {
					matches[x] = w;
				} else {
					if (w > matches[x]) {
						// overwrite with parent
						matches[x] = w;
					}
				}
				searching = 1;
			}
		}
		if (!searching)
			return;
		var params = get_params();
		params.s = currentSearchTerm;
		history.replaceState(null, null, parse_params(params));

		searchbtn.classList.add("show");
		searchbtn.firstChild.nodeValue = "Reset Search";

		// calculate percent matched, excluding vertical overlap
		var count = 0;
		var lastx = -1;
		var lastw = 0;
		var keys = Array();
		for (k in matches) {
			if (matches.hasOwnProperty(k))
				keys.push(k);
		}
		// sort the matched frames by their x location
		// ascending, then width descending
		keys.sort(function(a, b){
			return a - b;
		});
		// Step through frames saving only the biggest bottom-up frames
		// thanks to the sort order. This relies on the tree property
		// where children are always smaller than their parents.
		var fudge = 0.0001;	// JavaScript floating point
		for (var k in keys) {
			var x = parseFloat(keys[k]);
			var w = matches[keys[k]];
			if (x >= lastx + lastw - fudge) {
				count += w;
				lastx = x;
				lastw = w;
			}
		}
		// display matched percent
		matchedtxt.classList.remove("hide");
		var pct = 100 * count / maxwidth;
		if (pct != 100) pct = pct.toFixed(1)
		matchedtxt.firstChild.nodeValue = "Matched: " + pct + "%";
	}
]]>
</script>
<rect x="0.0" y="0" width="1200.0" height="502.0" fill="url(#background)"  />
<text id="title" x="600.00" y="24" >Flame Graph</text>
<text id="details" x="10.00" y="485" > </text>
<text id="unzoom" x="10.00" y="24" class="hide">Reset Zoom</text>
<text id="search" x="1090.00" y="24" >Search</text>
<text id="ignorecase" x="1174.00" y="24" >ic</text>
<text id="matched" x="1090.00" y="485" > </text>
<g id="frames">
<g >
<title>github.com/labstack/echo/v4.(*Echo).add.func1 (11,323 samples, 1.12%)</title><rect x="271.1" y="357" width="13.2" height="15.0" fill="rgb(245,184,44)" rx="2" ry="2" />
<text  x="274.09" y="367.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/infra/httputil.HttpDoData (3,623 samples, 0.36%)</title><rect x="274.9" y="277" width="4.3" height="15.0" fill="rgb(247,194,46)" rx="2" ry="2" />
<text  x="277.95" y="287.5" ></text>
</g>
<g >
<title>k8s.io/klog/v2.(*loggingT).printf (652 samples, 0.06%)</title><rect x="1043.3" y="373" width="0.8" height="15.0" fill="rgb(223,83,19)" rx="2" ry="2" />
<text  x="1046.33" y="383.5" ></text>
</g>
<g >
<title>go.etcd.io/etcd/raft/v3.(*node).Propose (14,916 samples, 1.47%)</title><rect x="1073.8" y="421" width="17.4" height="15.0" fill="rgb(221,77,18)" rx="2" ry="2" />
<text  x="1076.83" y="431.5" ></text>
</g>
<g >
<title>io.CopyBuffer (222,274 samples, 21.94%)</title><rect x="285.5" y="357" width="258.9" height="15.0" fill="rgb(227,102,24)" rx="2" ry="2" />
<text  x="288.52" y="367.5" >io.CopyBuffer</text>
</g>
<g >
<title>go.etcd.io/etcd/server/v3/etcdserver/api/rafthttp.(*streamWriter).run (43,448 samples, 4.29%)</title><rect x="185.0" y="421" width="50.6" height="15.0" fill="rgb(238,152,36)" rx="2" ry="2" />
<text  x="187.95" y="431.5" >go.et..</text>
</g>
<g >
<title>net/http.(*Client).do (1,635 samples, 0.16%)</title><rect x="850.2" y="197" width="1.9" height="15.0" fill="rgb(230,117,28)" rx="2" ry="2" />
<text  x="853.19" y="207.5" ></text>
</g>
<g >
<title>net/http.(*Transport).roundTrip (669 samples, 0.07%)</title><rect x="10.7" y="405" width="0.8" height="15.0" fill="rgb(232,126,30)" rx="2" ry="2" />
<text  x="13.74" y="415.5" ></text>
</g>
<g >
<title>runtime.selectgo (1,085 samples, 0.11%)</title><rect x="852.7" y="101" width="1.3" height="15.0" fill="rgb(212,35,8)" rx="2" ry="2" />
<text  x="855.70" y="111.5" ></text>
</g>
<g >
<title>plat-swr/pkg/zraft/etcd-raft.(*raftNode).serveChannels (71,077 samples, 7.01%)</title><rect x="1105.6" y="421" width="82.7" height="15.0" fill="rgb(206,7,1)" rx="2" ry="2" />
<text  x="1108.58" y="431.5" >plat-swr/..</text>
</g>
<g >
<title>plat-swr/pkg/zraft/core.(*Receiver).dispatch (3,520 samples, 0.35%)</title><rect x="1043.3" y="405" width="4.1" height="15.0" fill="rgb(211,29,7)" rx="2" ry="2" />
<text  x="1046.33" y="415.5" ></text>
</g>
<g >
<title>net/http.(*Transport).RoundTrip (8,299 samples, 0.82%)</title><rect x="618.0" y="149" width="9.7" height="15.0" fill="rgb(239,157,37)" rx="2" ry="2" />
<text  x="621.05" y="159.5" ></text>
</g>
<g >
<title>io.(*PipeWriter).Write (189,432 samples, 18.70%)</title><rect x="628.0" y="261" width="220.6" height="15.0" fill="rgb(214,45,10)" rx="2" ry="2" />
<text  x="631.04" y="271.5" >io.(*PipeWriter).Write</text>
</g>
<g >
<title>runtime.selectgo (532 samples, 0.05%)</title><rect x="10.9" y="373" width="0.6" height="15.0" fill="rgb(212,35,8)" rx="2" ry="2" />
<text  x="13.90" y="383.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/infra/httputil.retryDo (1,635 samples, 0.16%)</title><rect x="850.2" y="245" width="1.9" height="15.0" fill="rgb(220,69,16)" rx="2" ry="2" />
<text  x="853.19" y="255.5" ></text>
</g>
<g >
<title>net/http.(*Transport).getConn (137 samples, 0.01%)</title><rect x="10.7" y="389" width="0.2" height="15.0" fill="rgb(240,165,39)" rx="2" ry="2" />
<text  x="13.74" y="399.5" ></text>
</g>
<g >
<title>net/http.(*response).finishRequest (15,172 samples, 1.50%)</title><rect x="236.0" y="405" width="17.7" height="15.0" fill="rgb(233,128,30)" rx="2" ry="2" />
<text  x="239.01" y="415.5" ></text>
</g>
<g >
<title>plat-swr/pkg/zraft/core.(*Exec).doProcessMessage (268,182 samples, 26.47%)</title><rect x="617.6" y="389" width="312.3" height="15.0" fill="rgb(239,156,37)" rx="2" ry="2" />
<text  x="620.56" y="399.5" >plat-swr/pkg/zraft/core.(*Exec).doProcessM..</text>
</g>
<g >
<title>runtime.selectgo (49,895 samples, 4.92%)</title><rect x="1130.2" y="405" width="58.1" height="15.0" fill="rgb(212,35,8)" rx="2" ry="2" />
<text  x="1133.24" y="415.5" >runtim..</text>
</g>
<g >
<title>net/http.(*persistConn).readLoop.func4 (501 samples, 0.05%)</title><rect x="10.2" y="341" width="0.5" height="15.0" fill="rgb(209,22,5)" rx="2" ry="2" />
<text  x="13.16" y="351.5" ></text>
</g>
<g >
<title>io.copyBuffer (189,432 samples, 18.70%)</title><rect x="628.0" y="293" width="220.6" height="15.0" fill="rgb(220,71,17)" rx="2" ry="2" />
<text  x="631.04" y="303.5" >io.copyBuffer</text>
</g>
<g >
<title>plat-swr/pkg/zraft/core.(*FeedbackCollector).checkConsensus (119 samples, 0.01%)</title><rect x="270.9" y="309" width="0.1" height="15.0" fill="rgb(207,9,2)" rx="2" ry="2" />
<text  x="273.89" y="319.5" ></text>
</g>
<g >
<title>plat-swr/pkg/zraft/etcd-raft.(*raftNode).startRaft.gowrap1 (137 samples, 0.01%)</title><rect x="1105.4" y="437" width="0.2" height="15.0" fill="rgb(227,105,25)" rx="2" ry="2" />
<text  x="1108.42" y="447.5" ></text>
</g>
<g >
<title>sync.(*Cond).Wait (573 samples, 0.06%)</title><rect x="568.2" y="149" width="0.7" height="15.0" fill="rgb(227,102,24)" rx="2" ry="2" />
<text  x="571.24" y="159.5" ></text>
</g>
<g >
<title>plat-swr/pkg/zraft/core.(*Raft).proposeMsg (677 samples, 0.07%)</title><rect x="271.1" y="197" width="0.8" height="15.0" fill="rgb(209,22,5)" rx="2" ry="2" />
<text  x="274.09" y="207.5" ></text>
</g>
<g >
<title>github.com/labstack/echo/v4.(*Echo).ServeHTTP (11,522 samples, 1.14%)</title><rect x="270.9" y="389" width="13.4" height="15.0" fill="rgb(235,139,33)" rx="2" ry="2" />
<text  x="273.86" y="399.5" ></text>
</g>
<g >
<title>go.etcd.io/etcd/server/v3/wal.newFilePipeline.gowrap1 (106 samples, 0.01%)</title><rect x="235.6" y="437" width="0.1" height="15.0" fill="rgb(213,40,9)" rx="2" ry="2" />
<text  x="238.55" y="447.5" ></text>
</g>
<g >
<title>net/http.(*Transport).roundTrip (731 samples, 0.07%)</title><rect x="279.2" y="293" width="0.8" height="15.0" fill="rgb(232,126,30)" rx="2" ry="2" />
<text  x="282.17" y="303.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/ui/callbacks.(*RegistryCallback).CommitWrite (221,488 samples, 21.86%)</title><rect x="628.0" y="357" width="258.0" height="15.0" fill="rgb(252,218,52)" rx="2" ry="2" />
<text  x="631.04" y="367.5" >plat-swr/pkg/registry-proxy/ui/cal..</text>
</g>
<g >
<title>runtime.selectgo (801 samples, 0.08%)</title><rect x="284.6" y="405" width="0.9" height="15.0" fill="rgb(212,35,8)" rx="2" ry="2" />
<text  x="287.58" y="415.5" ></text>
</g>
<g >
<title>sync.(*Cond).Wait (15,172 samples, 1.50%)</title><rect x="236.0" y="373" width="17.7" height="15.0" fill="rgb(227,102,24)" rx="2" ry="2" />
<text  x="239.01" y="383.5" ></text>
</g>
<g >
<title>runtime.selectgo (7,563 samples, 0.75%)</title><rect x="568.9" y="149" width="8.8" height="15.0" fill="rgb(212,35,8)" rx="2" ry="2" />
<text  x="571.92" y="159.5" ></text>
</g>
<g >
<title>plat-swr/pkg/zraft/api.(*RaftService).ExecuteCmd (299 samples, 0.03%)</title><rect x="577.7" y="133" width="0.4" height="15.0" fill="rgb(234,135,32)" rx="2" ry="2" />
<text  x="580.73" y="143.5" ></text>
</g>
<g >
<title>net/http.(*Transport).roundTrip (143 samples, 0.01%)</title><rect x="235.7" y="309" width="0.1" height="15.0" fill="rgb(232,126,30)" rx="2" ry="2" />
<text  x="238.67" y="319.5" ></text>
</g>
<g >
<title>plat-swr/pkg/zraft/core.(*Raft).waitForProposalResult (1,266 samples, 0.12%)</title><rect x="271.9" y="133" width="1.5" height="15.0" fill="rgb(243,179,42)" rx="2" ry="2" />
<text  x="274.88" y="143.5" ></text>
</g>
<g >
<title>net/http.(*http2serverConn).readPreface (288 samples, 0.03%)</title><rect x="253.8" y="325" width="0.3" height="15.0" fill="rgb(249,202,48)" rx="2" ry="2" />
<text  x="256.76" y="335.5" ></text>
</g>
<g >
<title>sync.(*Mutex).Lock (789 samples, 0.08%)</title><rect x="947.9" y="325" width="0.9" height="15.0" fill="rgb(231,119,28)" rx="2" ry="2" />
<text  x="950.91" y="335.5" ></text>
</g>
<g >
<title>k8s.io/klog/v2.(*loggingT).printf (251 samples, 0.02%)</title><rect x="627.8" y="341" width="0.2" height="15.0" fill="rgb(223,83,19)" rx="2" ry="2" />
<text  x="630.75" y="351.5" ></text>
</g>
<g >
<title>runtime.selectgo (177,131 samples, 17.48%)</title><rect x="642.4" y="229" width="206.2" height="15.0" fill="rgb(212,35,8)" rx="2" ry="2" />
<text  x="645.37" y="239.5" >runtime.selectgo</text>
</g>
<g >
<title>net/http.http2chunkWriter.Write (509 samples, 0.05%)</title><rect x="578.6" y="325" width="0.6" height="15.0" fill="rgb(229,113,27)" rx="2" ry="2" />
<text  x="581.62" y="335.5" ></text>
</g>
<g >
<title>plat-swr/pkg/zraft/core.(*Receiver).run (19,909 samples, 1.96%)</title><rect x="1043.3" y="421" width="23.2" height="15.0" fill="rgb(236,145,34)" rx="2" ry="2" />
<text  x="1046.33" y="431.5" >p..</text>
</g>
<g >
<title>sync.(*Mutex).Lock (251 samples, 0.02%)</title><rect x="627.8" y="293" width="0.2" height="15.0" fill="rgb(231,119,28)" rx="2" ry="2" />
<text  x="630.75" y="303.5" ></text>
</g>
<g >
<title>net/http.(*Transport).roundTrip (439 samples, 0.04%)</title><rect x="567.1" y="69" width="0.5" height="15.0" fill="rgb(232,126,30)" rx="2" ry="2" />
<text  x="570.11" y="79.5" ></text>
</g>
<g >
<title>runtime.selectgo (407 samples, 0.04%)</title><rect x="567.1" y="37" width="0.5" height="15.0" fill="rgb(212,35,8)" rx="2" ry="2" />
<text  x="570.15" y="47.5" ></text>
</g>
<g >
<title>runtime.selectgo (650 samples, 0.06%)</title><rect x="283.2" y="133" width="0.7" height="15.0" fill="rgb(212,35,8)" rx="2" ry="2" />
<text  x="286.16" y="143.5" ></text>
</g>
<g >
<title>net/http.(*persistConn).writeLoop (241,404 samples, 23.82%)</title><rect x="285.5" y="421" width="281.1" height="15.0" fill="rgb(234,133,31)" rx="2" ry="2" />
<text  x="288.52" y="431.5" >net/http.(*persistConn).writeLoop</text>
</g>
<g >
<title>sync.(*Mutex).Lock (369 samples, 0.04%)</title><rect x="617.6" y="309" width="0.4" height="15.0" fill="rgb(231,119,28)" rx="2" ry="2" />
<text  x="620.56" y="319.5" ></text>
</g>
<g >
<title>plat-swr/pkg/zraft/core.(*Raft).proposeMsg (2,540 samples, 0.25%)</title><rect x="280.2" y="229" width="3.0" height="15.0" fill="rgb(209,22,5)" rx="2" ry="2" />
<text  x="283.20" y="239.5" ></text>
</g>
<g >
<title>plat-swr/pkg/zraft/etcd-raft.(*raftNode).Process (13,275 samples, 1.31%)</title><rect x="130.3" y="421" width="15.5" height="15.0" fill="rgb(250,209,50)" rx="2" ry="2" />
<text  x="133.33" y="431.5" ></text>
</g>
<g >
<title>runtime.selectgo (43,446 samples, 4.29%)</title><rect x="185.0" y="405" width="50.6" height="15.0" fill="rgb(212,35,8)" rx="2" ry="2" />
<text  x="187.96" y="415.5" >runti..</text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/ui/proxy/handler.sendChunkData (1,320 samples, 0.13%)</title><rect x="271.9" y="245" width="1.5" height="15.0" fill="rgb(249,205,49)" rx="2" ry="2" />
<text  x="274.87" y="255.5" ></text>
</g>
<g >
<title>k8s.io/klog/v2.Infof (652 samples, 0.06%)</title><rect x="1043.3" y="389" width="0.8" height="15.0" fill="rgb(247,194,46)" rx="2" ry="2" />
<text  x="1046.33" y="399.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/ui/proxy/handler.ExecRaftCmd (1,320 samples, 0.13%)</title><rect x="273.4" y="293" width="1.5" height="15.0" fill="rgb(245,184,44)" rx="2" ry="2" />
<text  x="276.41" y="303.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/ui/proxy/handler.BreakPointHandler (717 samples, 0.07%)</title><rect x="283.2" y="309" width="0.8" height="15.0" fill="rgb(215,48,11)" rx="2" ry="2" />
<text  x="286.16" y="319.5" ></text>
</g>
<g >
<title>plat-swr/pkg/zraft/core.(*FeedbackCollector).HandleFeedback (198 samples, 0.02%)</title><rect x="270.9" y="341" width="0.2" height="15.0" fill="rgb(217,58,13)" rx="2" ry="2" />
<text  x="273.86" y="351.5" ></text>
</g>
<g >
<title>bufio.(*Writer).Flush (341 samples, 0.03%)</title><rect x="578.2" y="325" width="0.4" height="15.0" fill="rgb(246,189,45)" rx="2" ry="2" />
<text  x="581.22" y="335.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/ui/proxy/handler.SendRaftClusterCmdBreakPoint (8,652 samples, 0.85%)</title><rect x="568.0" y="245" width="10.1" height="15.0" fill="rgb(240,162,38)" rx="2" ry="2" />
<text  x="571.00" y="255.5" ></text>
</g>
<g >
<title>net/http.(*http2serverConn).readFrames (3,161 samples, 0.31%)</title><rect x="579.2" y="421" width="3.7" height="15.0" fill="rgb(217,57,13)" rx="2" ry="2" />
<text  x="582.21" y="431.5" ></text>
</g>
<g >
<title>k8s.io/klog/v2.(*loggingT).printfDepth (251 samples, 0.02%)</title><rect x="627.8" y="325" width="0.2" height="15.0" fill="rgb(236,146,34)" rx="2" ry="2" />
<text  x="630.75" y="335.5" ></text>
</g>
<g >
<title>plat-swr/pkg/zraft/core.(*ExecGroup).DispatchCmdToExec (1,045 samples, 0.10%)</title><rect x="948.8" y="389" width="1.2" height="15.0" fill="rgb(211,31,7)" rx="2" ry="2" />
<text  x="951.83" y="399.5" ></text>
</g>
<g >
<title>net/http.(*bodyEOFSignal).condfn (154 samples, 0.02%)</title><rect x="280.0" y="261" width="0.2" height="15.0" fill="rgb(231,120,28)" rx="2" ry="2" />
<text  x="283.02" y="271.5" ></text>
</g>
<g >
<title>plat-swr/pkg/zraft/core.(*Raft).proposeMsg (1,320 samples, 0.13%)</title><rect x="273.4" y="245" width="1.5" height="15.0" fill="rgb(209,22,5)" rx="2" ry="2" />
<text  x="276.41" y="255.5" ></text>
</g>
<g >
<title>net/http.(*Client).Do (9,182 samples, 0.91%)</title><rect x="583.0" y="293" width="10.7" height="15.0" fill="rgb(237,147,35)" rx="2" ry="2" />
<text  x="586.05" y="303.5" ></text>
</g>
<g >
<title>k8s.io/klog/v2.(*loggingT).printfDepth (652 samples, 0.06%)</title><rect x="1043.3" y="357" width="0.8" height="15.0" fill="rgb(236,146,34)" rx="2" ry="2" />
<text  x="1046.33" y="367.5" ></text>
</g>
<g >
<title>sync.(*RWMutex).Lock (3,975 samples, 0.39%)</title><rect x="886.0" y="341" width="4.6" height="15.0" fill="rgb(239,158,37)" rx="2" ry="2" />
<text  x="888.98" y="351.5" ></text>
</g>
<g >
<title>runtime.unique_runtime_registerUniqueMapCleanup.gowrap1 (1,418 samples, 0.14%)</title><rect x="1188.3" y="437" width="1.7" height="15.0" fill="rgb(243,178,42)" rx="2" ry="2" />
<text  x="1191.35" y="447.5" ></text>
</g>
<g >
<title>plat-swr/pkg/zraft/core.(*Raft).proposeMsg (203 samples, 0.02%)</title><rect x="568.0" y="133" width="0.2" height="15.0" fill="rgb(209,22,5)" rx="2" ry="2" />
<text  x="571.00" y="143.5" ></text>
</g>
<g >
<title>net/http.(*http2serverConn).serve.gowrap8 (3,161 samples, 0.31%)</title><rect x="579.2" y="437" width="3.7" height="15.0" fill="rgb(250,209,50)" rx="2" ry="2" />
<text  x="582.21" y="447.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/ui/proxy/handler.SendBreakPointToRaftCluster (299 samples, 0.03%)</title><rect x="577.7" y="165" width="0.4" height="15.0" fill="rgb(213,39,9)" rx="2" ry="2" />
<text  x="580.73" y="175.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/infra/httputil.HttpDoRsp (439 samples, 0.04%)</title><rect x="567.1" y="213" width="0.5" height="15.0" fill="rgb(242,174,41)" rx="2" ry="2" />
<text  x="570.11" y="223.5" ></text>
</g>
<g >
<title>net/http.(*Transport).RoundTrip (19,648 samples, 1.94%)</title><rect x="895.4" y="277" width="22.9" height="15.0" fill="rgb(239,157,37)" rx="2" ry="2" />
<text  x="898.41" y="287.5" >n..</text>
</g>
<g >
<title>k8s.io/klog/v2.(*loggingT).output (777 samples, 0.08%)</title><rect x="948.8" y="325" width="0.9" height="15.0" fill="rgb(229,112,26)" rx="2" ry="2" />
<text  x="951.83" y="335.5" ></text>
</g>
<g >
<title>runtime.selectgo (2,504 samples, 0.25%)</title><rect x="280.2" y="181" width="2.9" height="15.0" fill="rgb(212,35,8)" rx="2" ry="2" />
<text  x="283.23" y="191.5" ></text>
</g>
<g >
<title>net/http.(*Transport).roundTrip (3,623 samples, 0.36%)</title><rect x="274.9" y="117" width="4.3" height="15.0" fill="rgb(232,126,30)" rx="2" ry="2" />
<text  x="277.95" y="127.5" ></text>
</g>
<g >
<title>runtime.selectgo (13,275 samples, 1.31%)</title><rect x="130.3" y="357" width="15.5" height="15.0" fill="rgb(212,35,8)" rx="2" ry="2" />
<text  x="133.33" y="367.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/ui/proxy/handler.SendRaftClusterCmdBreakPoint (717 samples, 0.07%)</title><rect x="283.2" y="293" width="0.8" height="15.0" fill="rgb(240,162,38)" rx="2" ry="2" />
<text  x="286.16" y="303.5" ></text>
</g>
<g >
<title>plat-swr/pkg/zraft/core.(*Raft).proposeMsgWithResult (677 samples, 0.07%)</title><rect x="271.1" y="181" width="0.8" height="15.0" fill="rgb(220,71,17)" rx="2" ry="2" />
<text  x="274.09" y="191.5" ></text>
</g>
<g >
<title>net/http.(*Client).send (19,648 samples, 1.94%)</title><rect x="895.4" y="309" width="22.9" height="15.0" fill="rgb(241,168,40)" rx="2" ry="2" />
<text  x="898.41" y="319.5" >n..</text>
</g>
<g >
<title>plat-swr/pkg/zraft/core.(*Raft).Propose (677 samples, 0.07%)</title><rect x="271.1" y="213" width="0.8" height="15.0" fill="rgb(222,79,19)" rx="2" ry="2" />
<text  x="274.09" y="223.5" ></text>
</g>
<g >
<title>encoding/json.(*Decoder).Decode (501 samples, 0.05%)</title><rect x="10.2" y="421" width="0.5" height="15.0" fill="rgb(216,50,12)" rx="2" ry="2" />
<text  x="13.16" y="431.5" ></text>
</g>
<g >
<title>net/http.(*http2serverConn).serve (14,752 samples, 1.46%)</title><rect x="253.7" y="341" width="17.2" height="15.0" fill="rgb(247,194,46)" rx="2" ry="2" />
<text  x="256.68" y="351.5" ></text>
</g>
<g >
<title>net/http.(*Client).Do (3,623 samples, 0.36%)</title><rect x="274.9" y="197" width="4.3" height="15.0" fill="rgb(237,147,35)" rx="2" ry="2" />
<text  x="277.95" y="207.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/infra/httputil.httpDoRsp (8,299 samples, 0.82%)</title><rect x="618.0" y="261" width="9.7" height="15.0" fill="rgb(236,143,34)" rx="2" ry="2" />
<text  x="621.05" y="271.5" ></text>
</g>
<g >
<title>plat-swr/pkg/zraft/api.(*RaftService).ExecuteCmd (317 samples, 0.03%)</title><rect x="567.6" y="213" width="0.4" height="15.0" fill="rgb(234,135,32)" rx="2" ry="2" />
<text  x="570.63" y="223.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/infra/httputil.HttpDoRsp (1,605 samples, 0.16%)</title><rect x="852.1" y="277" width="1.9" height="15.0" fill="rgb(242,174,41)" rx="2" ry="2" />
<text  x="855.09" y="287.5" ></text>
</g>
<g >
<title>runtime.selectgo (593 samples, 0.06%)</title><rect x="850.2" y="101" width="0.7" height="15.0" fill="rgb(212,35,8)" rx="2" ry="2" />
<text  x="853.19" y="111.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/ui/callbacks.(*RegistryCallback).CommitRead (8,335 samples, 0.82%)</title><rect x="618.0" y="357" width="9.8" height="15.0" fill="rgb(226,97,23)" rx="2" ry="2" />
<text  x="621.05" y="367.5" ></text>
</g>
<g >
<title>runtime.chansend1 (109 samples, 0.01%)</title><rect x="567.9" y="149" width="0.1" height="15.0" fill="rgb(212,33,8)" rx="2" ry="2" />
<text  x="570.88" y="159.5" ></text>
</g>
<g >
<title>net/http.(*persistConn).roundTrip (407 samples, 0.04%)</title><rect x="567.1" y="53" width="0.5" height="15.0" fill="rgb(221,74,17)" rx="2" ry="2" />
<text  x="570.15" y="63.5" ></text>
</g>
<g >
<title>net/http.(*http2serverConn).runHandler.func1 (850 samples, 0.08%)</title><rect x="578.2" y="389" width="1.0" height="15.0" fill="rgb(227,103,24)" rx="2" ry="2" />
<text  x="581.22" y="399.5" ></text>
</g>
<g >
<title>io.copyBuffer (222,274 samples, 21.94%)</title><rect x="285.5" y="341" width="258.9" height="15.0" fill="rgb(220,71,17)" rx="2" ry="2" />
<text  x="288.52" y="351.5" >io.copyBuffer</text>
</g>
<g >
<title>net/http.(*http2Server).serveConn (14,752 samples, 1.46%)</title><rect x="253.7" y="357" width="17.2" height="15.0" fill="rgb(253,225,53)" rx="2" ry="2" />
<text  x="256.68" y="367.5" ></text>
</g>
<g >
<title>net/http.send (1,605 samples, 0.16%)</title><rect x="852.1" y="165" width="1.9" height="15.0" fill="rgb(254,227,54)" rx="2" ry="2" />
<text  x="855.09" y="175.5" ></text>
</g>
<g >
<title>plat-swr/pkg/zraft/core.(*AppWAL).Delete (3,977 samples, 0.39%)</title><rect x="886.0" y="357" width="4.6" height="15.0" fill="rgb(209,21,5)" rx="2" ry="2" />
<text  x="888.98" y="367.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/ui/proxy/handler.SendBreakPointToRaftCluster (717 samples, 0.07%)</title><rect x="283.2" y="245" width="0.8" height="15.0" fill="rgb(213,39,9)" rx="2" ry="2" />
<text  x="286.16" y="255.5" ></text>
</g>
<g >
<title>plat-swr/pkg/zraft/core.(*Exec).sendFeedback (19,658 samples, 1.94%)</title><rect x="895.4" y="357" width="22.9" height="15.0" fill="rgb(234,136,32)" rx="2" ry="2" />
<text  x="898.40" y="367.5" >p..</text>
</g>
<g >
<title>plat-swr/pkg/zraft/core.(*Exec).processWriteMsg (259,433 samples, 25.60%)</title><rect x="627.8" y="373" width="302.1" height="15.0" fill="rgb(241,166,39)" rx="2" ry="2" />
<text  x="630.75" y="383.5" >plat-swr/pkg/zraft/core.(*Exec).processW..</text>
</g>
<g >
<title>crypto/tls.(*Conn).handshakeContext (131 samples, 0.01%)</title><rect x="582.9" y="405" width="0.1" height="15.0" fill="rgb(235,141,33)" rx="2" ry="2" />
<text  x="585.89" y="415.5" ></text>
</g>
<g >
<title>plat-swr/pkg/zraft/api.(*RaftService).ExecuteCmd (717 samples, 0.07%)</title><rect x="283.2" y="213" width="0.8" height="15.0" fill="rgb(234,135,32)" rx="2" ry="2" />
<text  x="286.16" y="223.5" ></text>
</g>
<g >
<title>runtime.selectgo (2,771 samples, 0.27%)</title><rect x="583.1" y="181" width="3.2" height="15.0" fill="rgb(212,35,8)" rx="2" ry="2" />
<text  x="586.05" y="191.5" ></text>
</g>
<g >
<title>plat-swr/pkg/zraft/core.(*Raft).waitForProposalResult (236 samples, 0.02%)</title><rect x="284.0" y="197" width="0.3" height="15.0" fill="rgb(243,179,42)" rx="2" ry="2" />
<text  x="287.00" y="207.5" ></text>
</g>
<g >
<title>net/http.(*Server).Serve.gowrap3 (41,449 samples, 4.09%)</title><rect x="236.0" y="437" width="48.3" height="15.0" fill="rgb(234,135,32)" rx="2" ry="2" />
<text  x="239.00" y="447.5" >net/..</text>
</g>
<g >
<title>plat-swr/pkg/zraft/core.(*Raft).proposeMsgWithResult (203 samples, 0.02%)</title><rect x="566.9" y="181" width="0.2" height="15.0" fill="rgb(220,71,17)" rx="2" ry="2" />
<text  x="569.87" y="191.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/infra/httputil.HttpDoData (440 samples, 0.04%)</title><rect x="567.1" y="229" width="0.5" height="15.0" fill="rgb(247,194,46)" rx="2" ry="2" />
<text  x="570.11" y="239.5" ></text>
</g>
<g >
<title>net.(*netFD).connect.func2 (138 samples, 0.01%)</title><rect x="235.8" y="437" width="0.2" height="15.0" fill="rgb(254,227,54)" rx="2" ry="2" />
<text  x="238.84" y="447.5" ></text>
</g>
<g >
<title>net/http.(*Client).send (1,635 samples, 0.16%)</title><rect x="850.2" y="181" width="1.9" height="15.0" fill="rgb(241,168,40)" rx="2" ry="2" />
<text  x="853.19" y="191.5" ></text>
</g>
<g >
<title>plat-swr/pkg/zraft/core.(*Receiver).runCmdCache (6,285 samples, 0.62%)</title><rect x="1066.5" y="421" width="7.3" height="15.0" fill="rgb(230,117,28)" rx="2" ry="2" />
<text  x="1069.51" y="431.5" ></text>
</g>
<g >
<title>plat-swr/pkg/zraft/core.(*Exec).PushMessage (268 samples, 0.03%)</title><rect x="949.7" y="373" width="0.3" height="15.0" fill="rgb(212,35,8)" rx="2" ry="2" />
<text  x="952.74" y="383.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/ui/callbacks/impl.(*StreamManager).HandleRequest (27,493 samples, 2.71%)</title><rect x="854.0" y="325" width="32.0" height="15.0" fill="rgb(209,19,4)" rx="2" ry="2" />
<text  x="856.96" y="335.5" >pl..</text>
</g>
<g >
<title>net/http.(*persistConn).roundTrip (19,621 samples, 1.94%)</title><rect x="895.4" y="245" width="22.9" height="15.0" fill="rgb(221,74,17)" rx="2" ry="2" />
<text  x="898.44" y="255.5" >n..</text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/ui/callbacks/impl.(*PostUpload).CommitWrite (1,635 samples, 0.16%)</title><rect x="850.2" y="341" width="1.9" height="15.0" fill="rgb(240,163,39)" rx="2" ry="2" />
<text  x="853.19" y="351.5" ></text>
</g>
<g >
<title>runtime.selectgo (288 samples, 0.03%)</title><rect x="253.8" y="309" width="0.3" height="15.0" fill="rgb(212,35,8)" rx="2" ry="2" />
<text  x="256.76" y="319.5" ></text>
</g>
<g >
<title>runtime.chanrecv1 (1,418 samples, 0.14%)</title><rect x="1188.3" y="405" width="1.7" height="15.0" fill="rgb(223,85,20)" rx="2" ry="2" />
<text  x="1191.35" y="415.5" ></text>
</g>
<g >
<title>net/http.(*http2serverConn).writeHeaders (190 samples, 0.02%)</title><rect x="578.4" y="277" width="0.2" height="15.0" fill="rgb(236,145,34)" rx="2" ry="2" />
<text  x="581.40" y="287.5" ></text>
</g>
<g >
<title>net/http.(*Client).do (9,182 samples, 0.91%)</title><rect x="583.0" y="277" width="10.7" height="15.0" fill="rgb(230,117,28)" rx="2" ry="2" />
<text  x="586.05" y="287.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/ui/proxy/handler.SendBreakPointToRaftCluster (677 samples, 0.07%)</title><rect x="271.1" y="261" width="0.8" height="15.0" fill="rgb(213,39,9)" rx="2" ry="2" />
<text  x="274.09" y="271.5" ></text>
</g>
<g >
<title>plat-swr/pkg/zraft/core.(*FeedbackCollector).broadcastMessageToCluster (119 samples, 0.01%)</title><rect x="270.9" y="277" width="0.1" height="15.0" fill="rgb(234,137,32)" rx="2" ry="2" />
<text  x="273.89" y="287.5" ></text>
</g>
<g >
<title>github.com/labstack/echo/v4.(*Echo).add.func1 (198 samples, 0.02%)</title><rect x="270.9" y="373" width="0.2" height="15.0" fill="rgb(245,184,44)" rx="2" ry="2" />
<text  x="273.86" y="383.5" ></text>
</g>
<g >
<title>runtime.selectgo (207 samples, 0.02%)</title><rect x="567.6" y="133" width="0.3" height="15.0" fill="rgb(212,35,8)" rx="2" ry="2" />
<text  x="570.63" y="143.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/infra/httputil.HttpDoData (1,605 samples, 0.16%)</title><rect x="852.1" y="293" width="1.9" height="15.0" fill="rgb(247,194,46)" rx="2" ry="2" />
<text  x="855.09" y="303.5" ></text>
</g>
<g >
<title>plat-swr/pkg/zraft/core.(*FeedbackCollector).broadcastResultMessage (119 samples, 0.01%)</title><rect x="270.9" y="293" width="0.1" height="15.0" fill="rgb(234,133,32)" rx="2" ry="2" />
<text  x="273.89" y="303.5" ></text>
</g>
<g >
<title>plat-swr/pkg/zraft/core.(*AppWAL).Write (4,084 samples, 0.40%)</title><rect x="890.6" y="357" width="4.8" height="15.0" fill="rgb(224,90,21)" rx="2" ry="2" />
<text  x="893.61" y="367.5" ></text>
</g>
<g >
<title>net/http.initALPNRequest.ServeHTTP (9,747 samples, 0.96%)</title><rect x="566.9" y="373" width="11.3" height="15.0" fill="rgb(241,168,40)" rx="2" ry="2" />
<text  x="569.87" y="383.5" ></text>
</g>
<g >
<title>plat-swr/pkg/zraft/core.(*Raft).proposeMsgWithResult (237 samples, 0.02%)</title><rect x="284.0" y="213" width="0.3" height="15.0" fill="rgb(220,71,17)" rx="2" ry="2" />
<text  x="287.00" y="223.5" ></text>
</g>
<g >
<title>io.copyBuffer (25,657 samples, 2.53%)</title><rect x="854.0" y="293" width="29.8" height="15.0" fill="rgb(220,71,17)" rx="2" ry="2" />
<text  x="856.96" y="303.5" >io..</text>
</g>
<g >
<title>net/http.(*Request).write (222,280 samples, 21.94%)</title><rect x="285.5" y="405" width="258.9" height="15.0" fill="rgb(216,54,12)" rx="2" ry="2" />
<text  x="288.52" y="415.5" >net/http.(*Request).write</text>
</g>
<g >
<title>net/http.(*persistConn).roundTrip (2,389 samples, 0.24%)</title><rect x="276.4" y="101" width="2.8" height="15.0" fill="rgb(221,74,17)" rx="2" ry="2" />
<text  x="279.39" y="111.5" ></text>
</g>
<g >
<title>go.etcd.io/etcd/server/v3/etcdserver/api/rafthttp.startPeer.func2 (46,908 samples, 4.63%)</title><rect x="130.3" y="437" width="54.7" height="15.0" fill="rgb(211,31,7)" rx="2" ry="2" />
<text  x="133.33" y="447.5" >go.et..</text>
</g>
<g >
<title>net/http.(*bodyEOFSignal).condfn (501 samples, 0.05%)</title><rect x="10.2" y="357" width="0.5" height="15.0" fill="rgb(231,120,28)" rx="2" ry="2" />
<text  x="13.16" y="367.5" ></text>
</g>
<g >
<title>k8s.io/klog/v2.(*loggingT).output (251 samples, 0.02%)</title><rect x="627.8" y="309" width="0.2" height="15.0" fill="rgb(229,112,26)" rx="2" ry="2" />
<text  x="630.75" y="319.5" ></text>
</g>
<g >
<title>plat-swr/pkg/zraft/core.(*Exec).processReadMsg (8,380 samples, 0.83%)</title><rect x="618.0" y="373" width="9.8" height="15.0" fill="rgb(214,45,10)" rx="2" ry="2" />
<text  x="620.99" y="383.5" ></text>
</g>
<g >
<title>sync.(*RWMutex).Lock (2,096 samples, 0.21%)</title><rect x="1044.1" y="373" width="2.4" height="15.0" fill="rgb(239,158,37)" rx="2" ry="2" />
<text  x="1047.09" y="383.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/infra/httputil.httpDoRsp (9,182 samples, 0.91%)</title><rect x="583.0" y="341" width="10.7" height="15.0" fill="rgb(236,143,34)" rx="2" ry="2" />
<text  x="586.05" y="351.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/infra/httputil.HttpDo (440 samples, 0.04%)</title><rect x="567.1" y="245" width="0.5" height="15.0" fill="rgb(239,156,37)" rx="2" ry="2" />
<text  x="570.11" y="255.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/ui/proxy/handler.SendAllBreakPointToRaftCluster (717 samples, 0.07%)</title><rect x="283.2" y="277" width="0.8" height="15.0" fill="rgb(248,199,47)" rx="2" ry="2" />
<text  x="286.16" y="287.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/ui/proxy/handler.ProxyHandler (885 samples, 0.09%)</title><rect x="279.2" y="341" width="1.0" height="15.0" fill="rgb(243,177,42)" rx="2" ry="2" />
<text  x="282.17" y="351.5" ></text>
</g>
<g >
<title>plat-swr/pkg/zraft/api.(*RaftService).ExecuteCmd (1,320 samples, 0.13%)</title><rect x="271.9" y="197" width="1.5" height="15.0" fill="rgb(234,135,32)" rx="2" ry="2" />
<text  x="274.87" y="207.5" ></text>
</g>
<g >
<title>plat-swr/pkg/zraft/core.(*ExecGroup).run (26,882 samples, 2.65%)</title><rect x="947.9" y="421" width="31.3" height="15.0" fill="rgb(235,142,33)" rx="2" ry="2" />
<text  x="950.91" y="431.5" >pl..</text>
</g>
<g >
<title>plat-swr/pkg/zraft/core.(*Exec).run (283,669 samples, 28.00%)</title><rect x="617.6" y="421" width="330.3" height="15.0" fill="rgb(227,102,24)" rx="2" ry="2" />
<text  x="620.56" y="431.5" >plat-swr/pkg/zraft/core.(*Exec).run</text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/ui/proxy/handler.SendRaftClusterCmd (326 samples, 0.03%)</title><rect x="567.6" y="245" width="0.4" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="570.62" y="255.5" ></text>
</g>
<g >
<title>runtime.selectgo (137 samples, 0.01%)</title><rect x="10.7" y="373" width="0.2" height="15.0" fill="rgb(212,35,8)" rx="2" ry="2" />
<text  x="13.74" y="383.5" ></text>
</g>
<g >
<title>runtime.chansend1 (154 samples, 0.02%)</title><rect x="1046.5" y="357" width="0.2" height="15.0" fill="rgb(212,33,8)" rx="2" ry="2" />
<text  x="1049.53" y="367.5" ></text>
</g>
<g >
<title>k8s.io/klog/v2.(*loggingT).printf (195 samples, 0.02%)</title><rect x="950.0" y="341" width="0.3" height="15.0" fill="rgb(223,83,19)" rx="2" ry="2" />
<text  x="953.05" y="351.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/ui/proxy/handler.Handlers (237 samples, 0.02%)</title><rect x="284.0" y="309" width="0.3" height="15.0" fill="rgb(227,105,25)" rx="2" ry="2" />
<text  x="287.00" y="319.5" ></text>
</g>
<g >
<title>plat-swr/pkg/zraft/api.(*RaftService).ExecuteCmd (2,542 samples, 0.25%)</title><rect x="280.2" y="261" width="3.0" height="15.0" fill="rgb(234,135,32)" rx="2" ry="2" />
<text  x="283.20" y="271.5" ></text>
</g>
<g >
<title>net/http.(*Client).send (9,182 samples, 0.91%)</title><rect x="583.0" y="261" width="10.7" height="15.0" fill="rgb(241,168,40)" rx="2" ry="2" />
<text  x="586.05" y="271.5" ></text>
</g>
<g >
<title>runtime.selectgo (2,389 samples, 0.24%)</title><rect x="276.4" y="85" width="2.8" height="15.0" fill="rgb(212,35,8)" rx="2" ry="2" />
<text  x="279.39" y="95.5" ></text>
</g>
<g >
<title>runtime.chanrecv1 (953 samples, 0.09%)</title><rect x="854.0" y="229" width="1.1" height="15.0" fill="rgb(223,85,20)" rx="2" ry="2" />
<text  x="856.96" y="239.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/ui/proxy/handler.SendRaftClusterCmd (2,544 samples, 0.25%)</title><rect x="280.2" y="293" width="3.0" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="283.20" y="303.5" ></text>
</g>
<g >
<title>runtime.selectgo (14,916 samples, 1.47%)</title><rect x="1073.8" y="373" width="17.4" height="15.0" fill="rgb(212,35,8)" rx="2" ry="2" />
<text  x="1076.83" y="383.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/infra/httputil.httpDoRsp.func1 (1,605 samples, 0.16%)</title><rect x="852.1" y="229" width="1.9" height="15.0" fill="rgb(207,12,2)" rx="2" ry="2" />
<text  x="855.09" y="239.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/ui/proxy/handler.ExecRaftCmd (203 samples, 0.02%)</title><rect x="568.0" y="181" width="0.2" height="15.0" fill="rgb(245,184,44)" rx="2" ry="2" />
<text  x="571.00" y="191.5" ></text>
</g>
<g >
<title>net/http.(*Client).send (143 samples, 0.01%)</title><rect x="235.7" y="357" width="0.1" height="15.0" fill="rgb(241,168,40)" rx="2" ry="2" />
<text  x="238.67" y="367.5" ></text>
</g>
<g >
<title>runtime.selectgo (9,943 samples, 0.98%)</title><rect x="918.3" y="341" width="11.6" height="15.0" fill="rgb(212,35,8)" rx="2" ry="2" />
<text  x="921.29" y="351.5" ></text>
</g>
<g >
<title>plat-swr/pkg/zraft/core.(*Raft).waitForProposalResult (202 samples, 0.02%)</title><rect x="566.9" y="165" width="0.2" height="15.0" fill="rgb(243,179,42)" rx="2" ry="2" />
<text  x="569.87" y="175.5" ></text>
</g>
<g >
<title>plat-swr/pkg/zraft/core.(*Raft).proposeMsgWithResult (715 samples, 0.07%)</title><rect x="283.2" y="165" width="0.8" height="15.0" fill="rgb(220,71,17)" rx="2" ry="2" />
<text  x="286.16" y="175.5" ></text>
</g>
<g >
<title>net/http.(*Transport).RoundTrip (1,635 samples, 0.16%)</title><rect x="850.2" y="149" width="1.9" height="15.0" fill="rgb(239,157,37)" rx="2" ry="2" />
<text  x="853.19" y="159.5" ></text>
</g>
<g >
<title>net/http.(*transferWriter).doBodyCopy (222,274 samples, 21.94%)</title><rect x="285.5" y="373" width="258.9" height="15.0" fill="rgb(246,193,46)" rx="2" ry="2" />
<text  x="288.52" y="383.5" >net/http.(*transferWriter).doBodyC..</text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/infra/httputil.HttpDoData (9,184 samples, 0.91%)</title><rect x="583.0" y="373" width="10.7" height="15.0" fill="rgb(247,194,46)" rx="2" ry="2" />
<text  x="586.05" y="383.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/infra/httputil.retryDo (8,299 samples, 0.82%)</title><rect x="618.0" y="245" width="9.7" height="15.0" fill="rgb(220,69,16)" rx="2" ry="2" />
<text  x="621.05" y="255.5" ></text>
</g>
<g >
<title>net/http/httputil.(*ReverseProxy).ServeHTTP (885 samples, 0.09%)</title><rect x="279.2" y="325" width="1.0" height="15.0" fill="rgb(223,83,20)" rx="2" ry="2" />
<text  x="282.17" y="335.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/ui/proxy/handler.PostUpload (645 samples, 0.06%)</title><rect x="566.9" y="293" width="0.7" height="15.0" fill="rgb(219,67,16)" rx="2" ry="2" />
<text  x="569.87" y="303.5" ></text>
</g>
<g >
<title>net/http.(*Transport).RoundTrip (731 samples, 0.07%)</title><rect x="279.2" y="309" width="0.8" height="15.0" fill="rgb(239,157,37)" rx="2" ry="2" />
<text  x="282.17" y="319.5" ></text>
</g>
<g >
<title>plat-swr/pkg/zraft/etcd-raft.(*raftNode).maybeTriggerSnapshot (20,767 samples, 2.05%)</title><rect x="1106.1" y="405" width="24.1" height="15.0" fill="rgb(245,187,44)" rx="2" ry="2" />
<text  x="1109.06" y="415.5" >p..</text>
</g>
<g >
<title>github.com/xiang90/probing.(*prober).AddHTTP.func1 (1,444 samples, 0.14%)</title><rect x="10.2" y="437" width="1.6" height="15.0" fill="rgb(250,210,50)" rx="2" ry="2" />
<text  x="13.16" y="447.5" ></text>
</g>
<g >
<title>plat-swr/pkg/zraft/core.(*Raft).waitForProposalResult (2,523 samples, 0.25%)</title><rect x="280.2" y="197" width="2.9" height="15.0" fill="rgb(243,179,42)" rx="2" ry="2" />
<text  x="283.20" y="207.5" ></text>
</g>
<g >
<title>runtime.selectgo (219 samples, 0.02%)</title><rect x="284.3" y="389" width="0.2" height="15.0" fill="rgb(212,35,8)" rx="2" ry="2" />
<text  x="287.28" y="399.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/ui/proxy/middleware/blob.GetBlobMiddleware.func1 (326 samples, 0.03%)</title><rect x="567.6" y="293" width="0.4" height="15.0" fill="rgb(238,155,37)" rx="2" ry="2" />
<text  x="570.62" y="303.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/ui/callbacks/impl.SendRegistryReq (8,300 samples, 0.82%)</title><rect x="618.0" y="325" width="9.7" height="15.0" fill="rgb(239,156,37)" rx="2" ry="2" />
<text  x="621.05" y="335.5" ></text>
</g>
<g >
<title>net/http.(*http2responseWriter).FlushError (850 samples, 0.08%)</title><rect x="578.2" y="341" width="1.0" height="15.0" fill="rgb(211,32,7)" rx="2" ry="2" />
<text  x="581.22" y="351.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/ui/proxy/handler.ExecRaftCmd (237 samples, 0.02%)</title><rect x="284.0" y="277" width="0.3" height="15.0" fill="rgb(245,184,44)" rx="2" ry="2" />
<text  x="287.00" y="287.5" ></text>
</g>
<g >
<title>crypto/tls.(*listener).Accept (137 samples, 0.01%)</title><rect x="1105.4" y="357" width="0.2" height="15.0" fill="rgb(239,157,37)" rx="2" ry="2" />
<text  x="1108.42" y="367.5" ></text>
</g>
<g >
<title>net/http.(*http2serverConn).writeDataFromHandler (151 samples, 0.01%)</title><rect x="578.2" y="277" width="0.2" height="15.0" fill="rgb(221,78,18)" rx="2" ry="2" />
<text  x="581.22" y="287.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/infra/httputil.HttpDo (8,300 samples, 0.82%)</title><rect x="618.0" y="309" width="9.7" height="15.0" fill="rgb(239,156,37)" rx="2" ry="2" />
<text  x="621.05" y="319.5" ></text>
</g>
<g >
<title>runtime.selectgo (137 samples, 0.01%)</title><rect x="10.0" y="421" width="0.2" height="15.0" fill="rgb(212,35,8)" rx="2" ry="2" />
<text  x="13.00" y="431.5" ></text>
</g>
<g >
<title>io.(*pipe).read (222,274 samples, 21.94%)</title><rect x="285.5" y="309" width="258.9" height="15.0" fill="rgb(240,161,38)" rx="2" ry="2" />
<text  x="288.52" y="319.5" >io.(*pipe).read</text>
</g>
<g >
<title>net/http.(*persistConn).roundTrip (92 samples, 0.01%)</title><rect x="235.7" y="293" width="0.1" height="15.0" fill="rgb(221,74,17)" rx="2" ry="2" />
<text  x="238.73" y="303.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/ui/proxy/middleware/blob.PutManifestMiddleware.func1 (954 samples, 0.09%)</title><rect x="283.2" y="341" width="1.1" height="15.0" fill="rgb(234,137,32)" rx="2" ry="2" />
<text  x="286.16" y="351.5" ></text>
</g>
<g >
<title>plat-swr/pkg/zraft/core.(*Raft).waitForProposalResult (1,313 samples, 0.13%)</title><rect x="273.4" y="213" width="1.5" height="15.0" fill="rgb(243,179,42)" rx="2" ry="2" />
<text  x="276.41" y="223.5" ></text>
</g>
<g >
<title>net/http/httputil.(*ReverseProxy).copyBuffer (154 samples, 0.02%)</title><rect x="280.0" y="293" width="0.2" height="15.0" fill="rgb(229,113,27)" rx="2" ry="2" />
<text  x="283.02" y="303.5" ></text>
</g>
<g >
<title>net/http.(*Client).Do (1,605 samples, 0.16%)</title><rect x="852.1" y="213" width="1.9" height="15.0" fill="rgb(237,147,35)" rx="2" ry="2" />
<text  x="855.09" y="223.5" ></text>
</g>
<g >
<title>net/http.http2chunkWriter.Write (341 samples, 0.03%)</title><rect x="578.2" y="309" width="0.4" height="15.0" fill="rgb(229,113,27)" rx="2" ry="2" />
<text  x="581.22" y="319.5" ></text>
</g>
<g >
<title>net/http.(*Transport).dialConnFor (137 samples, 0.01%)</title><rect x="566.6" y="421" width="0.2" height="15.0" fill="rgb(253,225,53)" rx="2" ry="2" />
<text  x="569.65" y="431.5" ></text>
</g>
<g >
<title>net/http.(*Client).send (3,623 samples, 0.36%)</title><rect x="274.9" y="165" width="4.3" height="15.0" fill="rgb(241,168,40)" rx="2" ry="2" />
<text  x="277.95" y="175.5" ></text>
</g>
<g >
<title>net/http.(*Client).send (8,299 samples, 0.82%)</title><rect x="618.0" y="181" width="9.7" height="15.0" fill="rgb(241,168,40)" rx="2" ry="2" />
<text  x="621.05" y="191.5" ></text>
</g>
<g >
<title>runtime.selectgo (151 samples, 0.01%)</title><rect x="578.2" y="261" width="0.2" height="15.0" fill="rgb(212,35,8)" rx="2" ry="2" />
<text  x="581.22" y="271.5" ></text>
</g>
<g >
<title>plat-swr/pkg/zraft/core.(*Raft).Propose (1,320 samples, 0.13%)</title><rect x="273.4" y="261" width="1.5" height="15.0" fill="rgb(222,79,19)" rx="2" ry="2" />
<text  x="276.41" y="271.5" ></text>
</g>
<g >
<title>sync.(*Mutex).Lock (800 samples, 0.08%)</title><rect x="1066.5" y="389" width="0.9" height="15.0" fill="rgb(231,119,28)" rx="2" ry="2" />
<text  x="1069.51" y="399.5" ></text>
</g>
<g >
<title>net/http.(*persistConn).addTLS (137 samples, 0.01%)</title><rect x="566.6" y="389" width="0.2" height="15.0" fill="rgb(229,112,26)" rx="2" ry="2" />
<text  x="569.65" y="399.5" ></text>
</g>
<g >
<title>plat-swr/pkg/zraft/core.(*FeedbackCollector).ProcessFeedback (198 samples, 0.02%)</title><rect x="270.9" y="325" width="0.2" height="15.0" fill="rgb(218,59,14)" rx="2" ry="2" />
<text  x="273.86" y="335.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/infra/httputil.httpDoRsp.func1 (9,182 samples, 0.91%)</title><rect x="583.0" y="309" width="10.7" height="15.0" fill="rgb(207,12,2)" rx="2" ry="2" />
<text  x="586.05" y="319.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/ui/proxy/handler.Handlers (326 samples, 0.03%)</title><rect x="567.6" y="261" width="0.4" height="15.0" fill="rgb(227,105,25)" rx="2" ry="2" />
<text  x="570.62" y="271.5" ></text>
</g>
<g >
<title>runtime.selectgo (24,851 samples, 2.45%)</title><rect x="950.3" y="405" width="28.9" height="15.0" fill="rgb(212,35,8)" rx="2" ry="2" />
<text  x="953.28" y="415.5" >ru..</text>
</g>
<g >
<title>runtime.chanrecv1 (154 samples, 0.02%)</title><rect x="280.0" y="229" width="0.2" height="15.0" fill="rgb(223,85,20)" rx="2" ry="2" />
<text  x="283.02" y="239.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/ui/proxy/handler.SendRaftClusterCmd (124 samples, 0.01%)</title><rect x="578.1" y="245" width="0.1" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="581.08" y="255.5" ></text>
</g>
<g >
<title>net/http.(*Transport).RoundTrip (439 samples, 0.04%)</title><rect x="567.1" y="85" width="0.5" height="15.0" fill="rgb(239,157,37)" rx="2" ry="2" />
<text  x="570.11" y="95.5" ></text>
</g>
<g >
<title>net/http.(*bodyEOFSignal).Read (154 samples, 0.02%)</title><rect x="280.0" y="277" width="0.2" height="15.0" fill="rgb(209,19,4)" rx="2" ry="2" />
<text  x="283.02" y="287.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/ui/callbacks/impl.NewRegistryReq (109 samples, 0.01%)</title><rect x="578.1" y="197" width="0.1" height="15.0" fill="rgb(209,19,4)" rx="2" ry="2" />
<text  x="581.10" y="207.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/ui/proxy/handler.SendPostUploadMsg (1,320 samples, 0.13%)</title><rect x="273.4" y="309" width="1.5" height="15.0" fill="rgb(218,63,15)" rx="2" ry="2" />
<text  x="276.41" y="319.5" ></text>
</g>
<g >
<title>plat-swr/pkg/zraft/core.(*Raft).Propose (717 samples, 0.07%)</title><rect x="283.2" y="197" width="0.8" height="15.0" fill="rgb(222,79,19)" rx="2" ry="2" />
<text  x="286.16" y="207.5" ></text>
</g>
<g >
<title>net/http.(*persistConn).readLoop.func4 (154 samples, 0.02%)</title><rect x="280.0" y="245" width="0.2" height="15.0" fill="rgb(209,22,5)" rx="2" ry="2" />
<text  x="283.02" y="255.5" ></text>
</g>
<g >
<title>runtime.selectgo (13,355 samples, 1.32%)</title><rect x="528.8" y="293" width="15.6" height="15.0" fill="rgb(212,35,8)" rx="2" ry="2" />
<text  x="531.82" y="303.5" ></text>
</g>
<g >
<title>k8s.io/klog/v2.(*loggingT).printfDepth (777 samples, 0.08%)</title><rect x="948.8" y="341" width="0.9" height="15.0" fill="rgb(236,146,34)" rx="2" ry="2" />
<text  x="951.83" y="351.5" ></text>
</g>
<g >
<title>runtime.selectgo (1,247 samples, 0.12%)</title><rect x="273.5" y="197" width="1.4" height="15.0" fill="rgb(212,35,8)" rx="2" ry="2" />
<text  x="276.49" y="207.5" ></text>
</g>
<g >
<title>runtime.selectgo (201 samples, 0.02%)</title><rect x="568.0" y="85" width="0.2" height="15.0" fill="rgb(212,35,8)" rx="2" ry="2" />
<text  x="571.00" y="95.5" ></text>
</g>
<g >
<title>plat-swr/pkg/zraft/core.(*Raft).proposeMsgWithResult (2,540 samples, 0.25%)</title><rect x="280.2" y="213" width="3.0" height="15.0" fill="rgb(220,71,17)" rx="2" ry="2" />
<text  x="283.20" y="223.5" ></text>
</g>
<g >
<title>net/http.(*connReader).abortPendingRead (15,172 samples, 1.50%)</title><rect x="236.0" y="389" width="17.7" height="15.0" fill="rgb(245,188,45)" rx="2" ry="2" />
<text  x="239.01" y="399.5" ></text>
</g>
<g >
<title>net/http.(*Transport).RoundTrip (143 samples, 0.01%)</title><rect x="235.7" y="325" width="0.1" height="15.0" fill="rgb(239,157,37)" rx="2" ry="2" />
<text  x="238.67" y="335.5" ></text>
</g>
<g >
<title>net/http.(*persistConn).roundTrip (6,407 samples, 0.63%)</title><rect x="586.3" y="197" width="7.4" height="15.0" fill="rgb(221,74,17)" rx="2" ry="2" />
<text  x="589.28" y="207.5" ></text>
</g>
<g >
<title>plat-swr/pkg/zraft/core.(*Raft).proposeMsgWithResult (316 samples, 0.03%)</title><rect x="567.6" y="165" width="0.4" height="15.0" fill="rgb(220,71,17)" rx="2" ry="2" />
<text  x="570.63" y="175.5" ></text>
</g>
<g >
<title>net/http.(*Transport).dialConn.gowrap2 (1,067 samples, 0.11%)</title><rect x="284.3" y="437" width="1.2" height="15.0" fill="rgb(251,215,51)" rx="2" ry="2" />
<text  x="287.27" y="447.5" ></text>
</g>
<g >
<title>net/http.(*Transport).roundTrip (8,299 samples, 0.82%)</title><rect x="618.0" y="133" width="9.7" height="15.0" fill="rgb(232,126,30)" rx="2" ry="2" />
<text  x="621.05" y="143.5" ></text>
</g>
<g >
<title>runtime.selectgo (274 samples, 0.03%)</title><rect x="11.5" y="421" width="0.3" height="15.0" fill="rgb(212,35,8)" rx="2" ry="2" />
<text  x="14.52" y="431.5" ></text>
</g>
<g >
<title>runtime.selectgo (5,485 samples, 0.54%)</title><rect x="1067.4" y="405" width="6.4" height="15.0" fill="rgb(212,35,8)" rx="2" ry="2" />
<text  x="1070.44" y="415.5" ></text>
</g>
<g >
<title>github.com/labstack/echo/v4/middleware.RecoverWithConfig.func1.1 (9,747 samples, 0.96%)</title><rect x="566.9" y="325" width="11.3" height="15.0" fill="rgb(239,156,37)" rx="2" ry="2" />
<text  x="569.87" y="335.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/ui/proxy/handler.PatchUpload (1,997 samples, 0.20%)</title><rect x="271.1" y="341" width="2.3" height="15.0" fill="rgb(241,168,40)" rx="2" ry="2" />
<text  x="274.09" y="351.5" ></text>
</g>
<g >
<title>k8s.io/klog/v2.(*loggingT).printf (777 samples, 0.08%)</title><rect x="948.8" y="357" width="0.9" height="15.0" fill="rgb(223,83,19)" rx="2" ry="2" />
<text  x="951.83" y="367.5" ></text>
</g>
<g >
<title>plat-swr/pkg/zraft/core.NewExecGroup.gowrap1 (26,882 samples, 2.65%)</title><rect x="947.9" y="437" width="31.3" height="15.0" fill="rgb(253,225,53)" rx="2" ry="2" />
<text  x="950.91" y="447.5" >pl..</text>
</g>
<g >
<title>io.(*PipeReader).Read (222,274 samples, 21.94%)</title><rect x="285.5" y="325" width="258.9" height="15.0" fill="rgb(209,22,5)" rx="2" ry="2" />
<text  x="288.52" y="335.5" >io.(*PipeReader).Read</text>
</g>
<g >
<title>plat-swr/pkg/zraft/core.(*Raft).Propose (1,320 samples, 0.13%)</title><rect x="271.9" y="181" width="1.5" height="15.0" fill="rgb(222,79,19)" rx="2" ry="2" />
<text  x="274.87" y="191.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/ui/proxy/handler.ExecRaftCmd (1,320 samples, 0.13%)</title><rect x="271.9" y="213" width="1.5" height="15.0" fill="rgb(245,184,44)" rx="2" ry="2" />
<text  x="274.87" y="223.5" ></text>
</g>
<g >
<title>plat-swr/pkg/zraft/core.(*AppWAL).Write (2,096 samples, 0.21%)</title><rect x="1044.1" y="389" width="2.4" height="15.0" fill="rgb(224,90,21)" rx="2" ry="2" />
<text  x="1047.09" y="399.5" ></text>
</g>
<g >
<title>runtime.selectgo (414 samples, 0.04%)</title><rect x="1105.6" y="389" width="0.5" height="15.0" fill="rgb(212,35,8)" rx="2" ry="2" />
<text  x="1108.58" y="399.5" ></text>
</g>
<g >
<title>net/http.(*http2pipe).Read (587 samples, 0.06%)</title><rect x="568.2" y="165" width="0.7" height="15.0" fill="rgb(215,46,11)" rx="2" ry="2" />
<text  x="571.24" y="175.5" ></text>
</g>
<g >
<title>net/http.(*http2serverConn).noteBodyReadFromHandler (109 samples, 0.01%)</title><rect x="578.1" y="149" width="0.1" height="15.0" fill="rgb(243,178,42)" rx="2" ry="2" />
<text  x="581.10" y="159.5" ></text>
</g>
<g >
<title>plat-swr/pkg/zraft/core.(*ExecGroup).runCmdCache (55,050 samples, 5.43%)</title><rect x="979.2" y="421" width="64.1" height="15.0" fill="rgb(229,113,27)" rx="2" ry="2" />
<text  x="982.22" y="431.5" >plat-sw..</text>
</g>
<g >
<title>io.(*pipe).write (25,657 samples, 2.53%)</title><rect x="854.0" y="245" width="29.8" height="15.0" fill="rgb(216,52,12)" rx="2" ry="2" />
<text  x="856.96" y="255.5" >io..</text>
</g>
<g >
<title>net/http.(*Client).do (19,648 samples, 1.94%)</title><rect x="895.4" y="325" width="22.9" height="15.0" fill="rgb(230,117,28)" rx="2" ry="2" />
<text  x="898.41" y="335.5" >n..</text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/ui/proxy/handler.ExecRaftCmd (299 samples, 0.03%)</title><rect x="577.7" y="149" width="0.4" height="15.0" fill="rgb(245,184,44)" rx="2" ry="2" />
<text  x="580.73" y="159.5" ></text>
</g>
<g >
<title>go.etcd.io/etcd/raft/v3.(*node).stepWait (14,916 samples, 1.47%)</title><rect x="1073.8" y="405" width="17.4" height="15.0" fill="rgb(243,177,42)" rx="2" ry="2" />
<text  x="1076.83" y="415.5" ></text>
</g>
<g >
<title>runtime.selectgo (201 samples, 0.02%)</title><rect x="566.9" y="149" width="0.2" height="15.0" fill="rgb(212,35,8)" rx="2" ry="2" />
<text  x="569.87" y="159.5" ></text>
</g>
<g >
<title>plat-swr/pkg/zraft/core.(*Raft).processCommits (20,385 samples, 2.01%)</title><rect x="593.8" y="421" width="23.8" height="15.0" fill="rgb(245,184,44)" rx="2" ry="2" />
<text  x="596.82" y="431.5" >p..</text>
</g>
<g >
<title>sync.(*Mutex).Lock (777 samples, 0.08%)</title><rect x="948.8" y="309" width="0.9" height="15.0" fill="rgb(231,119,28)" rx="2" ry="2" />
<text  x="951.83" y="319.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/ui/proxy/handler.processDataChunk (1,320 samples, 0.13%)</title><rect x="271.9" y="261" width="1.5" height="15.0" fill="rgb(246,192,45)" rx="2" ry="2" />
<text  x="274.87" y="271.5" ></text>
</g>
<g >
<title>sync.(*Mutex).Lock (90 samples, 0.01%)</title><rect x="1046.7" y="341" width="0.1" height="15.0" fill="rgb(231,119,28)" rx="2" ry="2" />
<text  x="1049.71" y="351.5" ></text>
</g>
<g >
<title>plat-swr/pkg/zraft/core.(*Raft).proposeMsg (296 samples, 0.03%)</title><rect x="577.7" y="101" width="0.4" height="15.0" fill="rgb(209,22,5)" rx="2" ry="2" />
<text  x="580.73" y="111.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/ui/proxy/handler.processStreamData (1,320 samples, 0.13%)</title><rect x="271.9" y="277" width="1.5" height="15.0" fill="rgb(253,224,53)" rx="2" ry="2" />
<text  x="274.87" y="287.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/ui/proxy/handler.SendRegistryReq (3,623 samples, 0.36%)</title><rect x="274.9" y="309" width="4.3" height="15.0" fill="rgb(212,32,7)" rx="2" ry="2" />
<text  x="277.95" y="319.5" ></text>
</g>
<g >
<title>runtime.selectgo (20,766 samples, 2.05%)</title><rect x="1106.1" y="389" width="24.1" height="15.0" fill="rgb(212,35,8)" rx="2" ry="2" />
<text  x="1109.06" y="399.5" >r..</text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/ui/proxy/handler.SendBreakPointFinishFlagToRaftCluster (203 samples, 0.02%)</title><rect x="568.0" y="213" width="0.2" height="15.0" fill="rgb(237,150,35)" rx="2" ry="2" />
<text  x="571.00" y="223.5" ></text>
</g>
<g >
<title>plat-swr/pkg/zraft/core.(*Raft).Propose (2,542 samples, 0.25%)</title><rect x="280.2" y="245" width="3.0" height="15.0" fill="rgb(222,79,19)" rx="2" ry="2" />
<text  x="283.20" y="255.5" ></text>
</g>
<g >
<title>plat-swr/pkg/zraft/core.NewExecGroup.gowrap2 (55,050 samples, 5.43%)</title><rect x="979.2" y="437" width="64.1" height="15.0" fill="rgb(247,195,46)" rx="2" ry="2" />
<text  x="982.22" y="447.5" >plat-sw..</text>
</g>
<g >
<title>k8s.io/klog/v2.(*loggingT).output (268 samples, 0.03%)</title><rect x="949.7" y="309" width="0.3" height="15.0" fill="rgb(229,112,26)" rx="2" ry="2" />
<text  x="952.74" y="319.5" ></text>
</g>
<g >
<title>runtime.chanrecv1 (501 samples, 0.05%)</title><rect x="10.2" y="325" width="0.5" height="15.0" fill="rgb(223,85,20)" rx="2" ry="2" />
<text  x="13.16" y="335.5" ></text>
</g>
<g >
<title>net/http.(*http2serverConn).scheduleHandler.gowrap1 (10,597 samples, 1.05%)</title><rect x="566.9" y="437" width="12.3" height="15.0" fill="rgb(248,201,48)" rx="2" ry="2" />
<text  x="569.87" y="447.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/infra/httputil.retryDo (1,605 samples, 0.16%)</title><rect x="852.1" y="245" width="1.9" height="15.0" fill="rgb(220,69,16)" rx="2" ry="2" />
<text  x="855.09" y="255.5" ></text>
</g>
<g >
<title>k8s.io/klog/v2.(*loggingT).printf (369 samples, 0.04%)</title><rect x="617.6" y="357" width="0.4" height="15.0" fill="rgb(223,83,19)" rx="2" ry="2" />
<text  x="620.56" y="367.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/ui/proxy/handler.ExecRaftCmd (326 samples, 0.03%)</title><rect x="567.6" y="229" width="0.4" height="15.0" fill="rgb(245,184,44)" rx="2" ry="2" />
<text  x="570.62" y="239.5" ></text>
</g>
<g >
<title>net/http.serverHandler.ServeHTTP (11,523 samples, 1.14%)</title><rect x="270.9" y="405" width="13.4" height="15.0" fill="rgb(234,135,32)" rx="2" ry="2" />
<text  x="273.86" y="415.5" ></text>
</g>
<g >
<title>net/http.(*Client).Do (8,299 samples, 0.82%)</title><rect x="618.0" y="213" width="9.7" height="15.0" fill="rgb(237,147,35)" rx="2" ry="2" />
<text  x="621.05" y="223.5" ></text>
</g>
<g >
<title>plat-swr/pkg/zraft/core.(*Receiver).doCmdCache (800 samples, 0.08%)</title><rect x="1066.5" y="405" width="0.9" height="15.0" fill="rgb(220,70,16)" rx="2" ry="2" />
<text  x="1069.51" y="415.5" ></text>
</g>
<g >
<title>net/http.(*Transport).roundTrip (1,605 samples, 0.16%)</title><rect x="852.1" y="133" width="1.9" height="15.0" fill="rgb(232,126,30)" rx="2" ry="2" />
<text  x="855.09" y="143.5" ></text>
</g>
<g >
<title>plat-swr/pkg/zraft/core.(*Raft).Propose (237 samples, 0.02%)</title><rect x="284.0" y="245" width="0.3" height="15.0" fill="rgb(222,79,19)" rx="2" ry="2" />
<text  x="287.00" y="255.5" ></text>
</g>
<g >
<title>k8s.io/klog/v2.(*loggingT).printfDepth (195 samples, 0.02%)</title><rect x="950.0" y="325" width="0.3" height="15.0" fill="rgb(236,146,34)" rx="2" ry="2" />
<text  x="953.05" y="335.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/ui/proxy/handler.PutManifestOrBlob (8,776 samples, 0.87%)</title><rect x="568.0" y="277" width="10.2" height="15.0" fill="rgb(246,189,45)" rx="2" ry="2" />
<text  x="571.00" y="287.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/infra/httputil.retryDo (439 samples, 0.04%)</title><rect x="567.1" y="181" width="0.5" height="15.0" fill="rgb(220,69,16)" rx="2" ry="2" />
<text  x="570.11" y="191.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/ui/callbacks/impl.SendRegistryReq (1,635 samples, 0.16%)</title><rect x="850.2" y="325" width="1.9" height="15.0" fill="rgb(239,156,37)" rx="2" ry="2" />
<text  x="853.19" y="335.5" ></text>
</g>
<g >
<title>net/http.(*http2responseWriterState).writeChunk (341 samples, 0.03%)</title><rect x="578.2" y="293" width="0.4" height="15.0" fill="rgb(243,174,41)" rx="2" ry="2" />
<text  x="581.22" y="303.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/ui/proxy/handler.ExecRaftCmd (205 samples, 0.02%)</title><rect x="566.9" y="245" width="0.2" height="15.0" fill="rgb(245,184,44)" rx="2" ry="2" />
<text  x="569.87" y="255.5" ></text>
</g>
<g >
<title>plat-swr/pkg/zraft/api.(*RaftService).ExecuteCmd (205 samples, 0.02%)</title><rect x="566.9" y="229" width="0.2" height="15.0" fill="rgb(234,135,32)" rx="2" ry="2" />
<text  x="569.87" y="239.5" ></text>
</g>
<g >
<title>runtime.selectgo (19,621 samples, 1.94%)</title><rect x="895.4" y="229" width="22.9" height="15.0" fill="rgb(212,35,8)" rx="2" ry="2" />
<text  x="898.44" y="239.5" >r..</text>
</g>
<g >
<title>net/http.(*persistConn).roundTrip (5,647 samples, 0.56%)</title><rect x="621.1" y="117" width="6.6" height="15.0" fill="rgb(221,74,17)" rx="2" ry="2" />
<text  x="624.13" y="127.5" ></text>
</g>
<g >
<title>net/http.Handler.ServeHTTP-fm (9,747 samples, 0.96%)</title><rect x="566.9" y="405" width="11.3" height="15.0" fill="rgb(213,40,9)" rx="2" ry="2" />
<text  x="569.87" y="415.5" ></text>
</g>
<g >
<title>net/http.(*http2serverConn).runHandler (10,597 samples, 1.05%)</title><rect x="566.9" y="421" width="12.3" height="15.0" fill="rgb(206,4,1)" rx="2" ry="2" />
<text  x="569.87" y="431.5" ></text>
</g>
<g >
<title>plat-swr/pkg/zraft/core.(*ExecGroup).findExecIndexInExecs (195 samples, 0.02%)</title><rect x="950.0" y="373" width="0.3" height="15.0" fill="rgb(238,153,36)" rx="2" ry="2" />
<text  x="953.05" y="383.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/ui/proxy/handler.PostUploadHandler (645 samples, 0.06%)</title><rect x="566.9" y="277" width="0.7" height="15.0" fill="rgb(234,136,32)" rx="2" ry="2" />
<text  x="569.87" y="287.5" ></text>
</g>
<g >
<title>plat-swr/pkg/zraft/etcd-raft.(*raftNode).serveRaft (137 samples, 0.01%)</title><rect x="1105.4" y="421" width="0.2" height="15.0" fill="rgb(237,149,35)" rx="2" ry="2" />
<text  x="1108.42" y="431.5" ></text>
</g>
<g >
<title>plat-swr/pkg/zraft/core.(*Raft).waitForProposalResult (285 samples, 0.03%)</title><rect x="577.7" y="69" width="0.4" height="15.0" fill="rgb(243,179,42)" rx="2" ry="2" />
<text  x="580.73" y="79.5" ></text>
</g>
<g >
<title>runtime.deferreturn (131 samples, 0.01%)</title><rect x="582.9" y="389" width="0.1" height="15.0" fill="rgb(242,170,40)" rx="2" ry="2" />
<text  x="585.89" y="399.5" ></text>
</g>
<g >
<title>net/http.(*Client).do (1,605 samples, 0.16%)</title><rect x="852.1" y="197" width="1.9" height="15.0" fill="rgb(230,117,28)" rx="2" ry="2" />
<text  x="855.09" y="207.5" ></text>
</g>
<g >
<title>net/http.send (9,182 samples, 0.91%)</title><rect x="583.0" y="245" width="10.7" height="15.0" fill="rgb(254,227,54)" rx="2" ry="2" />
<text  x="586.05" y="255.5" ></text>
</g>
<g >
<title>go.etcd.io/etcd/raft/v3.(*node).run (101,706 samples, 10.04%)</title><rect x="11.8" y="421" width="118.5" height="15.0" fill="rgb(225,93,22)" rx="2" ry="2" />
<text  x="14.84" y="431.5" >go.etcd.io/etc..</text>
</g>
<g >
<title>runtime.selectgo (137 samples, 0.01%)</title><rect x="1105.4" y="309" width="0.2" height="15.0" fill="rgb(212,35,8)" rx="2" ry="2" />
<text  x="1108.42" y="319.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/ui/callbacks/impl.(*PatchUpload).CommitWrite (190,755 samples, 18.83%)</title><rect x="628.0" y="341" width="222.2" height="15.0" fill="rgb(212,34,8)" rx="2" ry="2" />
<text  x="631.04" y="351.5" >plat-swr/pkg/registry-proxy/u..</text>
</g>
<g >
<title>runtime.chanrecv1 (137 samples, 0.01%)</title><rect x="566.6" y="373" width="0.2" height="15.0" fill="rgb(223,85,20)" rx="2" ry="2" />
<text  x="569.65" y="383.5" ></text>
</g>
<g >
<title>plat-swr/pkg/zraft/core.(*Exec).ProcessedMessage (268,182 samples, 26.47%)</title><rect x="617.6" y="405" width="312.3" height="15.0" fill="rgb(253,223,53)" rx="2" ry="2" />
<text  x="620.56" y="415.5" >plat-swr/pkg/zraft/core.(*Exec).ProcessedM..</text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/infra/httputil.httpDoRsp.func1 (8,299 samples, 0.82%)</title><rect x="618.0" y="229" width="9.7" height="15.0" fill="rgb(207,12,2)" rx="2" ry="2" />
<text  x="621.05" y="239.5" ></text>
</g>
<g >
<title>github.com/labstack/echo/v4.(*Echo).ServeHTTP (9,747 samples, 0.96%)</title><rect x="566.9" y="341" width="11.3" height="15.0" fill="rgb(235,139,33)" rx="2" ry="2" />
<text  x="569.87" y="351.5" ></text>
</g>
<g >
<title>plat-swr/pkg/zraft/core.(*Raft).waitForProposalResult (201 samples, 0.02%)</title><rect x="568.0" y="101" width="0.2" height="15.0" fill="rgb(243,179,42)" rx="2" ry="2" />
<text  x="571.00" y="111.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/infra/httputil.HttpDoRsp (3,623 samples, 0.36%)</title><rect x="274.9" y="261" width="4.3" height="15.0" fill="rgb(242,174,41)" rx="2" ry="2" />
<text  x="277.95" y="271.5" ></text>
</g>
<g >
<title>io.(*PipeWriter).Write (25,657 samples, 2.53%)</title><rect x="854.0" y="261" width="29.8" height="15.0" fill="rgb(214,45,10)" rx="2" ry="2" />
<text  x="856.96" y="271.5" >io..</text>
</g>
<g >
<title>k8s.io/klog/v2.Infof (251 samples, 0.02%)</title><rect x="627.8" y="357" width="0.2" height="15.0" fill="rgb(247,194,46)" rx="2" ry="2" />
<text  x="630.75" y="367.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/ui/proxy/middleware/blob.PutManifestMiddleware.func1 (8,776 samples, 0.87%)</title><rect x="568.0" y="293" width="10.2" height="15.0" fill="rgb(234,137,32)" rx="2" ry="2" />
<text  x="571.00" y="303.5" ></text>
</g>
<g >
<title>plat-swr/pkg/zraft/core.(*ExecGroup).DispatchMsg (2,031 samples, 0.20%)</title><rect x="947.9" y="405" width="2.4" height="15.0" fill="rgb(211,31,7)" rx="2" ry="2" />
<text  x="950.91" y="415.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/ui/proxy/handler.HeadManifestOrBlob (326 samples, 0.03%)</title><rect x="567.6" y="277" width="0.4" height="15.0" fill="rgb(228,105,25)" rx="2" ry="2" />
<text  x="570.62" y="287.5" ></text>
</g>
<g >
<title>net/http.send (19,648 samples, 1.94%)</title><rect x="895.4" y="293" width="22.9" height="15.0" fill="rgb(254,227,54)" rx="2" ry="2" />
<text  x="898.41" y="303.5" >n..</text>
</g>
<g >
<title>k8s.io/klog/v2.(*loggingT).printfDepth (268 samples, 0.03%)</title><rect x="949.7" y="325" width="0.3" height="15.0" fill="rgb(236,146,34)" rx="2" ry="2" />
<text  x="952.74" y="335.5" ></text>
</g>
<g >
<title>runtime.selectgo (1,205 samples, 0.12%)</title><rect x="272.0" y="117" width="1.4" height="15.0" fill="rgb(212,35,8)" rx="2" ry="2" />
<text  x="274.95" y="127.5" ></text>
</g>
<g >
<title>runtime.selectgo (19,124 samples, 1.89%)</title><rect x="544.4" y="405" width="22.2" height="15.0" fill="rgb(212,35,8)" rx="2" ry="2" />
<text  x="547.38" y="415.5" >r..</text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/infra/httputil.HttpDoRsp (9,182 samples, 0.91%)</title><rect x="583.0" y="357" width="10.7" height="15.0" fill="rgb(242,174,41)" rx="2" ry="2" />
<text  x="586.05" y="367.5" ></text>
</g>
<g >
<title>plat-swr/pkg/zraft/core.(*Raft).proposeMsgWithResult (296 samples, 0.03%)</title><rect x="577.7" y="85" width="0.4" height="15.0" fill="rgb(220,71,17)" rx="2" ry="2" />
<text  x="580.73" y="95.5" ></text>
</g>
<g >
<title>sync.(*Mutex).Lock (2,096 samples, 0.21%)</title><rect x="1044.1" y="357" width="2.4" height="15.0" fill="rgb(231,119,28)" rx="2" ry="2" />
<text  x="1047.09" y="367.5" ></text>
</g>
<g >
<title>runtime.selectgo (285 samples, 0.03%)</title><rect x="577.7" y="53" width="0.4" height="15.0" fill="rgb(212,35,8)" rx="2" ry="2" />
<text  x="580.73" y="63.5" ></text>
</g>
<g >
<title>go.etcd.io/etcd/server/v3/etcdserver/api/rafthttp.startStreamWriter.gowrap1 (43,448 samples, 4.29%)</title><rect x="185.0" y="437" width="50.6" height="15.0" fill="rgb(233,130,31)" rx="2" ry="2" />
<text  x="187.95" y="447.5" >go.et..</text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/ui/proxy/handler.BreakPointHandler (8,652 samples, 0.85%)</title><rect x="568.0" y="261" width="10.1" height="15.0" fill="rgb(215,48,11)" rx="2" ry="2" />
<text  x="571.00" y="271.5" ></text>
</g>
<g >
<title>go.etcd.io/etcd/raft/v3.RestartNode.gowrap1 (101,706 samples, 10.04%)</title><rect x="11.8" y="437" width="118.5" height="15.0" fill="rgb(238,153,36)" rx="2" ry="2" />
<text  x="14.84" y="447.5" >go.etcd.io/etc..</text>
</g>
<g >
<title>runtime.selectgo (2,652 samples, 0.26%)</title><rect x="618.0" y="101" width="3.1" height="15.0" fill="rgb(212,35,8)" rx="2" ry="2" />
<text  x="621.05" y="111.5" ></text>
</g>
<g >
<title>io.Copy (25,657 samples, 2.53%)</title><rect x="854.0" y="309" width="29.8" height="15.0" fill="rgb(216,50,12)" rx="2" ry="2" />
<text  x="856.96" y="319.5" >io..</text>
</g>
<g >
<title>runtime.selectgo (676 samples, 0.07%)</title><rect x="271.1" y="149" width="0.8" height="15.0" fill="rgb(212,35,8)" rx="2" ry="2" />
<text  x="274.09" y="159.5" ></text>
</g>
<g >
<title>plat-swr/pkg/zraft/core.(*Raft).Propose (317 samples, 0.03%)</title><rect x="567.6" y="197" width="0.4" height="15.0" fill="rgb(222,79,19)" rx="2" ry="2" />
<text  x="570.63" y="207.5" ></text>
</g>
<g >
<title>net/http.http2ConfigureServer.func2 (14,752 samples, 1.46%)</title><rect x="253.7" y="405" width="17.2" height="15.0" fill="rgb(220,71,17)" rx="2" ry="2" />
<text  x="256.68" y="415.5" ></text>
</g>
<g >
<title>k8s.io/klog/v2.(*loggingT).output (369 samples, 0.04%)</title><rect x="617.6" y="325" width="0.4" height="15.0" fill="rgb(229,112,26)" rx="2" ry="2" />
<text  x="620.56" y="335.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/ui/proxy/handler.SendRegistryReq (440 samples, 0.04%)</title><rect x="567.1" y="261" width="0.5" height="15.0" fill="rgb(212,32,7)" rx="2" ry="2" />
<text  x="570.11" y="271.5" ></text>
</g>
<g >
<title>crypto/tls.(*Conn).handshakeContext.func1 (131 samples, 0.01%)</title><rect x="582.9" y="373" width="0.1" height="15.0" fill="rgb(207,10,2)" rx="2" ry="2" />
<text  x="585.89" y="383.5" ></text>
</g>
<g >
<title>runtime.chanrecv2 (20,374 samples, 2.01%)</title><rect x="593.8" y="405" width="23.8" height="15.0" fill="rgb(217,55,13)" rx="2" ry="2" />
<text  x="596.84" y="415.5" >r..</text>
</g>
<g >
<title>k8s.io/klog/v2.Infof (369 samples, 0.04%)</title><rect x="617.6" y="373" width="0.4" height="15.0" fill="rgb(247,194,46)" rx="2" ry="2" />
<text  x="620.56" y="383.5" ></text>
</g>
<g >
<title>bytes.(*Reader).WriteTo (25,657 samples, 2.53%)</title><rect x="854.0" y="277" width="29.8" height="15.0" fill="rgb(233,130,31)" rx="2" ry="2" />
<text  x="856.96" y="287.5" >by..</text>
</g>
<g >
<title>net/http.(*Transport).RoundTrip (1,605 samples, 0.16%)</title><rect x="852.1" y="149" width="1.9" height="15.0" fill="rgb(239,157,37)" rx="2" ry="2" />
<text  x="855.09" y="159.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/ui/proxy/handler.SendAllBreakPointToRaftCluster (8,652 samples, 0.85%)</title><rect x="568.0" y="229" width="10.1" height="15.0" fill="rgb(248,199,47)" rx="2" ry="2" />
<text  x="571.00" y="239.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/ui/proxy/handler.SendRaftClusterCmdBreakPoint (1,997 samples, 0.20%)</title><rect x="271.1" y="309" width="2.3" height="15.0" fill="rgb(240,162,38)" rx="2" ry="2" />
<text  x="274.09" y="319.5" ></text>
</g>
<g >
<title>plat-swr/pkg/zraft/core.(*ExecGroup).refreshGroup (90 samples, 0.01%)</title><rect x="1046.7" y="357" width="0.1" height="15.0" fill="rgb(245,188,44)" rx="2" ry="2" />
<text  x="1049.71" y="367.5" ></text>
</g>
<g >
<title>net/http.(*persistConn).readLoop (1,067 samples, 0.11%)</title><rect x="284.3" y="421" width="1.2" height="15.0" fill="rgb(207,12,3)" rx="2" ry="2" />
<text  x="287.27" y="431.5" ></text>
</g>
<g >
<title>plat-swr/pkg/zraft/core.(*ExecGroup).PushMessage (527 samples, 0.05%)</title><rect x="1046.8" y="373" width="0.6" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="1049.81" y="383.5" ></text>
</g>
<g >
<title>plat-swr/pkg/zraft/core.(*Raft).waitForProposalResult (650 samples, 0.06%)</title><rect x="283.2" y="149" width="0.7" height="15.0" fill="rgb(243,179,42)" rx="2" ry="2" />
<text  x="286.16" y="159.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/ui/proxy/handler.SendAllBreakPointToRaftCluster (1,997 samples, 0.20%)</title><rect x="271.1" y="293" width="2.3" height="15.0" fill="rgb(248,199,47)" rx="2" ry="2" />
<text  x="274.09" y="303.5" ></text>
</g>
<g >
<title>net/http.(*Client).Do (19,648 samples, 1.94%)</title><rect x="895.4" y="341" width="22.9" height="15.0" fill="rgb(237,147,35)" rx="2" ry="2" />
<text  x="898.41" y="351.5" >n..</text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/infra/httputil.httpDoRsp.func1 (1,635 samples, 0.16%)</title><rect x="850.2" y="229" width="1.9" height="15.0" fill="rgb(207,12,2)" rx="2" ry="2" />
<text  x="853.19" y="239.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/infra/httputil.retryDo (9,182 samples, 0.91%)</title><rect x="583.0" y="325" width="10.7" height="15.0" fill="rgb(220,69,16)" rx="2" ry="2" />
<text  x="586.05" y="335.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/ui/proxy/handler.SendRaftClusterCmd (237 samples, 0.02%)</title><rect x="284.0" y="293" width="0.3" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="287.00" y="303.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/ui/proxy/handler.PostUploadHandler (4,943 samples, 0.49%)</title><rect x="273.4" y="325" width="5.8" height="15.0" fill="rgb(234,136,32)" rx="2" ry="2" />
<text  x="276.41" y="335.5" ></text>
</g>
<g >
<title>runtime.unique_runtime_registerUniqueMapCleanup.func2 (1,418 samples, 0.14%)</title><rect x="1188.3" y="421" width="1.7" height="15.0" fill="rgb(210,24,5)" rx="2" ry="2" />
<text  x="1191.35" y="431.5" ></text>
</g>
<g >
<title>k8s.io/klog/v2.(*loggingT).output (195 samples, 0.02%)</title><rect x="950.0" y="309" width="0.3" height="15.0" fill="rgb(229,112,26)" rx="2" ry="2" />
<text  x="953.05" y="319.5" ></text>
</g>
<g >
<title>net/http.send (439 samples, 0.04%)</title><rect x="567.1" y="101" width="0.5" height="15.0" fill="rgb(254,227,54)" rx="2" ry="2" />
<text  x="570.11" y="111.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/ui/proxy/handler.buildCmdParams (109 samples, 0.01%)</title><rect x="578.1" y="213" width="0.1" height="15.0" fill="rgb(208,15,3)" rx="2" ry="2" />
<text  x="581.10" y="223.5" ></text>
</g>
<g >
<title>plat-swr/pkg/zraft/etcd-raft.stoppableListener.Accept (137 samples, 0.01%)</title><rect x="1105.4" y="325" width="0.2" height="15.0" fill="rgb(230,115,27)" rx="2" ry="2" />
<text  x="1108.42" y="335.5" ></text>
</g>
<g >
<title>sync.(*Mutex).Lock (4,084 samples, 0.40%)</title><rect x="890.6" y="325" width="4.8" height="15.0" fill="rgb(231,119,28)" rx="2" ry="2" />
<text  x="893.61" y="335.5" ></text>
</g>
<g >
<title>net/http.(*Transport).getConn (520 samples, 0.05%)</title><rect x="852.1" y="117" width="0.6" height="15.0" fill="rgb(240,165,39)" rx="2" ry="2" />
<text  x="855.09" y="127.5" ></text>
</g>
<g >
<title>bytes.(*Reader).WriteTo (189,432 samples, 18.70%)</title><rect x="628.0" y="277" width="220.6" height="15.0" fill="rgb(233,130,31)" rx="2" ry="2" />
<text  x="631.04" y="287.5" >bytes.(*Reader).WriteTo</text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/ui/proxy/handler.SendBreakPointToRaftCluster (203 samples, 0.02%)</title><rect x="568.0" y="197" width="0.2" height="15.0" fill="rgb(213,39,9)" rx="2" ry="2" />
<text  x="571.00" y="207.5" ></text>
</g>
<g >
<title>runtime.selectgo (12,205 samples, 1.20%)</title><rect x="1091.2" y="421" width="14.2" height="15.0" fill="rgb(212,35,8)" rx="2" ry="2" />
<text  x="1094.20" y="431.5" ></text>
</g>
<g >
<title>plat-swr/pkg/zraft/core.NewReceiver.gowrap2 (6,285 samples, 0.62%)</title><rect x="1066.5" y="437" width="7.3" height="15.0" fill="rgb(248,199,47)" rx="2" ry="2" />
<text  x="1069.51" y="447.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/infra/httputil.HttpDoRsp (8,299 samples, 0.82%)</title><rect x="618.0" y="277" width="9.7" height="15.0" fill="rgb(242,174,41)" rx="2" ry="2" />
<text  x="621.05" y="287.5" ></text>
</g>
<g >
<title>net/http.(*Transport).roundTrip (1,635 samples, 0.16%)</title><rect x="850.2" y="133" width="1.9" height="15.0" fill="rgb(232,126,30)" rx="2" ry="2" />
<text  x="853.19" y="143.5" ></text>
</g>
<g >
<title>crypto/tls.(*Conn).handshakeContext.func2 (137 samples, 0.01%)</title><rect x="10.0" y="437" width="0.2" height="15.0" fill="rgb(250,210,50)" rx="2" ry="2" />
<text  x="13.00" y="447.5" ></text>
</g>
<g >
<title>runtime.selectgo (101,706 samples, 10.04%)</title><rect x="11.8" y="405" width="118.5" height="15.0" fill="rgb(212,35,8)" rx="2" ry="2" />
<text  x="14.84" y="415.5" >runtime.selectgo</text>
</g>
<g >
<title>plat-swr/pkg/zraft/etcd-raft.(*raftNode).serveChannels.func1 (27,121 samples, 2.68%)</title><rect x="1073.8" y="437" width="31.6" height="15.0" fill="rgb(228,106,25)" rx="2" ry="2" />
<text  x="1076.83" y="447.5" >pl..</text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/ui/proxy/handler.Handlers (124 samples, 0.01%)</title><rect x="578.1" y="261" width="0.1" height="15.0" fill="rgb(227,105,25)" rx="2" ry="2" />
<text  x="581.08" y="271.5" ></text>
</g>
<g >
<title>net/http.(*persistConn).roundTrip (731 samples, 0.07%)</title><rect x="279.2" y="277" width="0.8" height="15.0" fill="rgb(221,74,17)" rx="2" ry="2" />
<text  x="282.17" y="287.5" ></text>
</g>
<g >
<title>net/http.(*Transport).roundTrip (9,182 samples, 0.91%)</title><rect x="583.0" y="213" width="10.7" height="15.0" fill="rgb(232,126,30)" rx="2" ry="2" />
<text  x="586.05" y="223.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/ui/callbacks/impl.SendRegistryReq (1,605 samples, 0.16%)</title><rect x="852.1" y="325" width="1.9" height="15.0" fill="rgb(239,156,37)" rx="2" ry="2" />
<text  x="855.09" y="335.5" ></text>
</g>
<g >
<title>plat-swr/pkg/zraft/core.(*ExecGroup).DispatchResultToExec (197 samples, 0.02%)</title><rect x="950.0" y="389" width="0.3" height="15.0" fill="rgb(216,55,13)" rx="2" ry="2" />
<text  x="953.05" y="399.5" ></text>
</g>
<g >
<title>k8s.io/klog/v2.(*loggingT).output (789 samples, 0.08%)</title><rect x="947.9" y="341" width="0.9" height="15.0" fill="rgb(229,112,26)" rx="2" ry="2" />
<text  x="950.91" y="351.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/ui/health.getStatusCode (144 samples, 0.01%)</title><rect x="235.7" y="405" width="0.1" height="15.0" fill="rgb(239,157,37)" rx="2" ry="2" />
<text  x="238.67" y="415.5" ></text>
</g>
<g >
<title>net/http.(*Client).Do (143 samples, 0.01%)</title><rect x="235.7" y="389" width="0.1" height="15.0" fill="rgb(237,147,35)" rx="2" ry="2" />
<text  x="238.67" y="399.5" ></text>
</g>
<g >
<title>net/http.(*Client).Do (1,635 samples, 0.16%)</title><rect x="850.2" y="213" width="1.9" height="15.0" fill="rgb(237,147,35)" rx="2" ry="2" />
<text  x="853.19" y="223.5" ></text>
</g>
<g >
<title>net/http.(*Transport).dialConn.gowrap3 (241,404 samples, 23.82%)</title><rect x="285.5" y="437" width="281.1" height="15.0" fill="rgb(245,185,44)" rx="2" ry="2" />
<text  x="288.52" y="447.5" >net/http.(*Transport).dialConn.gowrap3</text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/ui/callbacks/impl.(*StreamManager).handleStream (9,184 samples, 0.91%)</title><rect x="583.0" y="421" width="10.7" height="15.0" fill="rgb(209,22,5)" rx="2" ry="2" />
<text  x="586.05" y="431.5" ></text>
</g>
<g >
<title>plat-swr/pkg/zraft/core.(*Raft).proposeMsg (237 samples, 0.02%)</title><rect x="284.0" y="229" width="0.3" height="15.0" fill="rgb(209,22,5)" rx="2" ry="2" />
<text  x="287.00" y="239.5" ></text>
</g>
<g >
<title>net/http.(*Transport).getConn (2,775 samples, 0.27%)</title><rect x="583.0" y="197" width="3.3" height="15.0" fill="rgb(240,165,39)" rx="2" ry="2" />
<text  x="586.05" y="207.5" ></text>
</g>
<g >
<title>plat-swr/pkg/zraft/core.(*Exec).waitForFeedback (9,943 samples, 0.98%)</title><rect x="918.3" y="357" width="11.6" height="15.0" fill="rgb(208,13,3)" rx="2" ry="2" />
<text  x="921.29" y="367.5" ></text>
</g>
<g >
<title>net/http.(*Client).do (439 samples, 0.04%)</title><rect x="567.1" y="133" width="0.5" height="15.0" fill="rgb(230,117,28)" rx="2" ry="2" />
<text  x="570.11" y="143.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/infra/httputil.HttpDoRsp (1,635 samples, 0.16%)</title><rect x="850.2" y="277" width="1.9" height="15.0" fill="rgb(242,174,41)" rx="2" ry="2" />
<text  x="853.19" y="287.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/ui/callbacks/impl.CreateStreamRegistryReq (9,184 samples, 0.91%)</title><rect x="583.0" y="405" width="10.7" height="15.0" fill="rgb(229,110,26)" rx="2" ry="2" />
<text  x="586.05" y="415.5" ></text>
</g>
<g >
<title>k8s.io/klog/v2.(*loggingT).printfDepth (369 samples, 0.04%)</title><rect x="617.6" y="341" width="0.4" height="15.0" fill="rgb(236,146,34)" rx="2" ry="2" />
<text  x="620.56" y="351.5" ></text>
</g>
<g >
<title>net/http.(*http2Server).ServeConn (14,752 samples, 1.46%)</title><rect x="253.7" y="373" width="17.2" height="15.0" fill="rgb(210,25,6)" rx="2" ry="2" />
<text  x="256.68" y="383.5" ></text>
</g>
<g >
<title>io.ReadAll (109 samples, 0.01%)</title><rect x="578.1" y="181" width="0.1" height="15.0" fill="rgb(251,211,50)" rx="2" ry="2" />
<text  x="581.10" y="191.5" ></text>
</g>
<g >
<title>go.etcd.io/etcd/raft/v3.(*node).step (13,275 samples, 1.31%)</title><rect x="130.3" y="389" width="15.5" height="15.0" fill="rgb(209,22,5)" rx="2" ry="2" />
<text  x="133.33" y="399.5" ></text>
</g>
<g >
<title>crypto/tls.(*Conn).HandshakeContext (131 samples, 0.01%)</title><rect x="582.9" y="421" width="0.1" height="15.0" fill="rgb(242,172,41)" rx="2" ry="2" />
<text  x="585.89" y="431.5" ></text>
</g>
<g >
<title>plat-swr/pkg/zraft/api.(*RaftService).ExecuteCmd (203 samples, 0.02%)</title><rect x="568.0" y="165" width="0.2" height="15.0" fill="rgb(234,135,32)" rx="2" ry="2" />
<text  x="571.00" y="175.5" ></text>
</g>
<g >
<title>k8s.io/klog/v2.Infof (789 samples, 0.08%)</title><rect x="947.9" y="389" width="0.9" height="15.0" fill="rgb(247,194,46)" rx="2" ry="2" />
<text  x="950.91" y="399.5" ></text>
</g>
<g >
<title>k8s.io/klog/v2.(*loggingT).output (651 samples, 0.06%)</title><rect x="1043.3" y="341" width="0.8" height="15.0" fill="rgb(229,112,26)" rx="2" ry="2" />
<text  x="1046.33" y="351.5" ></text>
</g>
<g >
<title>net/http.(*http2serverConn).writeHeaders (509 samples, 0.05%)</title><rect x="578.6" y="293" width="0.6" height="15.0" fill="rgb(236,145,34)" rx="2" ry="2" />
<text  x="581.62" y="303.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/ui/health.WatchRegistry (144 samples, 0.01%)</title><rect x="235.7" y="421" width="0.1" height="15.0" fill="rgb(229,112,26)" rx="2" ry="2" />
<text  x="238.67" y="431.5" ></text>
</g>
<g >
<title>runtime.selectgo (106 samples, 0.01%)</title><rect x="235.6" y="405" width="0.1" height="15.0" fill="rgb(212,35,8)" rx="2" ry="2" />
<text  x="238.55" y="415.5" ></text>
</g>
<g >
<title>runtime.selectgo (16,389 samples, 1.62%)</title><rect x="1047.4" y="405" width="19.1" height="15.0" fill="rgb(212,35,8)" rx="2" ry="2" />
<text  x="1050.43" y="415.5" ></text>
</g>
<g >
<title>github.com/labstack/echo/v4.(*Echo).add.func1 (9,747 samples, 0.96%)</title><rect x="566.9" y="309" width="11.3" height="15.0" fill="rgb(245,184,44)" rx="2" ry="2" />
<text  x="569.87" y="319.5" ></text>
</g>
<g >
<title>net/http.(*Client).do (3,623 samples, 0.36%)</title><rect x="274.9" y="181" width="4.3" height="15.0" fill="rgb(230,117,28)" rx="2" ry="2" />
<text  x="277.95" y="191.5" ></text>
</g>
<g >
<title>sync.(*Mutex).Lock (268 samples, 0.03%)</title><rect x="949.7" y="293" width="0.3" height="15.0" fill="rgb(231,119,28)" rx="2" ry="2" />
<text  x="952.74" y="303.5" ></text>
</g>
<g >
<title>net/http.send (3,623 samples, 0.36%)</title><rect x="274.9" y="149" width="4.3" height="15.0" fill="rgb(254,227,54)" rx="2" ry="2" />
<text  x="277.95" y="159.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/ui/callbacks/impl.(*StreamManager).CreateStreamRequestTask.func1 (9,184 samples, 0.91%)</title><rect x="583.0" y="437" width="10.7" height="15.0" fill="rgb(220,71,17)" rx="2" ry="2" />
<text  x="586.05" y="447.5" ></text>
</g>
<g >
<title>encoding/json.(*Decoder).readValue (501 samples, 0.05%)</title><rect x="10.2" y="405" width="0.5" height="15.0" fill="rgb(213,37,9)" rx="2" ry="2" />
<text  x="13.16" y="415.5" ></text>
</g>
<g >
<title>net/http.(*Transport).getConn (593 samples, 0.06%)</title><rect x="850.2" y="117" width="0.7" height="15.0" fill="rgb(240,165,39)" rx="2" ry="2" />
<text  x="853.19" y="127.5" ></text>
</g>
<g >
<title>sync.(*RWMutex).Lock (4,084 samples, 0.40%)</title><rect x="890.6" y="341" width="4.8" height="15.0" fill="rgb(239,158,37)" rx="2" ry="2" />
<text  x="893.61" y="351.5" ></text>
</g>
<g >
<title>runtime.selectgo (731 samples, 0.07%)</title><rect x="279.2" y="261" width="0.8" height="15.0" fill="rgb(212,35,8)" rx="2" ry="2" />
<text  x="282.17" y="271.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/infra/httputil.httpDoRsp (3,623 samples, 0.36%)</title><rect x="274.9" y="245" width="4.3" height="15.0" fill="rgb(236,143,34)" rx="2" ry="2" />
<text  x="277.95" y="255.5" ></text>
</g>
<g >
<title>net/http.(*http2requestBody).Read (109 samples, 0.01%)</title><rect x="578.1" y="165" width="0.1" height="15.0" fill="rgb(252,218,52)" rx="2" ry="2" />
<text  x="581.10" y="175.5" ></text>
</g>
<g >
<title>plat-swr/pkg/zraft/api.(*RaftService).ExecuteCmd (1,320 samples, 0.13%)</title><rect x="273.4" y="277" width="1.5" height="15.0" fill="rgb(234,135,32)" rx="2" ry="2" />
<text  x="276.41" y="287.5" ></text>
</g>
<g >
<title>net/http.(*persistConn).roundTrip (1,042 samples, 0.10%)</title><rect x="850.9" y="117" width="1.2" height="15.0" fill="rgb(221,74,17)" rx="2" ry="2" />
<text  x="853.88" y="127.5" ></text>
</g>
<g >
<title>plat-swr/pkg/zraft/core.(*ExecGroup).PushMessage (154 samples, 0.02%)</title><rect x="1046.5" y="373" width="0.2" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="1049.53" y="383.5" ></text>
</g>
<g >
<title>go.etcd.io/etcd/raft/v3.(*node).stepWithWaitOption (13,275 samples, 1.31%)</title><rect x="130.3" y="373" width="15.5" height="15.0" fill="rgb(243,174,41)" rx="2" ry="2" />
<text  x="133.33" y="383.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/infra/httputil.httpDoRsp.func1 (439 samples, 0.04%)</title><rect x="567.1" y="165" width="0.5" height="15.0" fill="rgb(207,12,2)" rx="2" ry="2" />
<text  x="570.11" y="175.5" ></text>
</g>
<g >
<title>net/http.(*Client).send (439 samples, 0.04%)</title><rect x="567.1" y="117" width="0.5" height="15.0" fill="rgb(241,168,40)" rx="2" ry="2" />
<text  x="570.11" y="127.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/infra/httputil.retryDo (3,623 samples, 0.36%)</title><rect x="274.9" y="229" width="4.3" height="15.0" fill="rgb(220,69,16)" rx="2" ry="2" />
<text  x="277.95" y="239.5" ></text>
</g>
<g >
<title>net/http.(*conn).serve (41,449 samples, 4.09%)</title><rect x="236.0" y="421" width="48.3" height="15.0" fill="rgb(240,162,38)" rx="2" ry="2" />
<text  x="239.00" y="431.5" >net/..</text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/ui/proxy/handler.SendBreakPointToRaftCluster (1,320 samples, 0.13%)</title><rect x="271.9" y="229" width="1.5" height="15.0" fill="rgb(213,39,9)" rx="2" ry="2" />
<text  x="274.87" y="239.5" ></text>
</g>
<g >
<title>plat-swr/pkg/zraft/core.(*Receiver).DispatchCmdToGroup (244 samples, 0.02%)</title><rect x="1046.5" y="389" width="0.3" height="15.0" fill="rgb(234,134,32)" rx="2" ry="2" />
<text  x="1049.53" y="399.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/ui/proxy/handler.PostUpload (4,943 samples, 0.49%)</title><rect x="273.4" y="341" width="5.8" height="15.0" fill="rgb(219,67,16)" rx="2" ry="2" />
<text  x="276.41" y="351.5" ></text>
</g>
<g >
<title>go.etcd.io/etcd/server/v3/wal.(*filePipeline).run (106 samples, 0.01%)</title><rect x="235.6" y="421" width="0.1" height="15.0" fill="rgb(245,187,44)" rx="2" ry="2" />
<text  x="238.55" y="431.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/ui/proxy/middleware/blob.GetBlobMiddleware.func1 (2,544 samples, 0.25%)</title><rect x="280.2" y="341" width="3.0" height="15.0" fill="rgb(238,155,37)" rx="2" ry="2" />
<text  x="283.20" y="351.5" ></text>
</g>
<g >
<title>plat-swr/pkg/zraft/core.(*FeedbackCollector).HandleFeedback-fm (198 samples, 0.02%)</title><rect x="270.9" y="357" width="0.2" height="15.0" fill="rgb(213,40,9)" rx="2" ry="2" />
<text  x="273.86" y="367.5" ></text>
</g>
<g >
<title>runtime.selectgo (1,042 samples, 0.10%)</title><rect x="850.9" y="101" width="1.2" height="15.0" fill="rgb(212,35,8)" rx="2" ry="2" />
<text  x="853.88" y="111.5" ></text>
</g>
<g >
<title>plat-swr/pkg/zraft/core.(*Receiver).allocateGroupExec (90 samples, 0.01%)</title><rect x="1046.7" y="373" width="0.1" height="15.0" fill="rgb(228,106,25)" rx="2" ry="2" />
<text  x="1049.71" y="383.5" ></text>
</g>
<g >
<title>plat-swr/pkg/zraft/core.(*Raft).Propose (203 samples, 0.02%)</title><rect x="568.0" y="149" width="0.2" height="15.0" fill="rgb(222,79,19)" rx="2" ry="2" />
<text  x="571.00" y="159.5" ></text>
</g>
<g >
<title>plat-swr/pkg/zraft/core.NewReceiver.gowrap1 (19,909 samples, 1.96%)</title><rect x="1043.3" y="437" width="23.2" height="15.0" fill="rgb(254,228,54)" rx="2" ry="2" />
<text  x="1046.33" y="447.5" >p..</text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/ui/proxy/handler.processDataChunk (8,449 samples, 0.83%)</title><rect x="568.2" y="197" width="9.9" height="15.0" fill="rgb(246,192,45)" rx="2" ry="2" />
<text  x="571.24" y="207.5" ></text>
</g>
<g >
<title>plat-swr/pkg/zraft/core.(*Raft).proposeMsgWithResult (1,315 samples, 0.13%)</title><rect x="271.9" y="149" width="1.5" height="15.0" fill="rgb(220,71,17)" rx="2" ry="2" />
<text  x="274.88" y="159.5" ></text>
</g>
<g >
<title>plat-swr/pkg/zraft/api.(*RaftService).ExecuteCmd (237 samples, 0.02%)</title><rect x="284.0" y="261" width="0.3" height="15.0" fill="rgb(234,135,32)" rx="2" ry="2" />
<text  x="287.00" y="271.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/ui/proxy/handler.Handlers (2,544 samples, 0.25%)</title><rect x="280.2" y="309" width="3.0" height="15.0" fill="rgb(227,105,25)" rx="2" ry="2" />
<text  x="283.20" y="319.5" ></text>
</g>
<g >
<title>net/http.(*bodyEOFSignal).Read (501 samples, 0.05%)</title><rect x="10.2" y="373" width="0.5" height="15.0" fill="rgb(209,19,4)" rx="2" ry="2" />
<text  x="13.16" y="383.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/ui/callbacks/impl.(*PutManifest).CommitWrite (1,605 samples, 0.16%)</title><rect x="852.1" y="341" width="1.9" height="15.0" fill="rgb(238,152,36)" rx="2" ry="2" />
<text  x="855.09" y="351.5" ></text>
</g>
<g >
<title>encoding/json.(*Decoder).refill (501 samples, 0.05%)</title><rect x="10.2" y="389" width="0.5" height="15.0" fill="rgb(241,167,40)" rx="2" ry="2" />
<text  x="13.16" y="399.5" ></text>
</g>
<g >
<title>io.Copy (189,432 samples, 18.70%)</title><rect x="628.0" y="309" width="220.6" height="15.0" fill="rgb(216,50,12)" rx="2" ry="2" />
<text  x="631.04" y="319.5" >io.Copy</text>
</g>
<g >
<title>net/http.serverHandler.ServeHTTP (9,747 samples, 0.96%)</title><rect x="566.9" y="357" width="11.3" height="15.0" fill="rgb(234,135,32)" rx="2" ry="2" />
<text  x="569.87" y="367.5" ></text>
</g>
<g >
<title>io.(*pipe).write (189,432 samples, 18.70%)</title><rect x="628.0" y="245" width="220.6" height="15.0" fill="rgb(216,52,12)" rx="2" ry="2" />
<text  x="631.04" y="255.5" >io.(*pipe).write</text>
</g>
<g >
<title>runtime.selectgo (3,161 samples, 0.31%)</title><rect x="579.2" y="405" width="3.7" height="15.0" fill="rgb(212,35,8)" rx="2" ry="2" />
<text  x="582.21" y="415.5" ></text>
</g>
<g >
<title>plat-swr/pkg/zraft/core.(*Raft).Propose (205 samples, 0.02%)</title><rect x="566.9" y="213" width="0.2" height="15.0" fill="rgb(222,79,19)" rx="2" ry="2" />
<text  x="569.87" y="223.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/ui/callbacks/impl.(*StreamManager).HandleRequest (190,755 samples, 18.83%)</title><rect x="628.0" y="325" width="222.2" height="15.0" fill="rgb(209,19,4)" rx="2" ry="2" />
<text  x="631.04" y="335.5" >plat-swr/pkg/registry-proxy/u..</text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/ui/proxy/handler.ExecRaftCmd (677 samples, 0.07%)</title><rect x="271.1" y="245" width="0.8" height="15.0" fill="rgb(245,184,44)" rx="2" ry="2" />
<text  x="274.09" y="255.5" ></text>
</g>
<g >
<title>plat-swr/pkg/zraft/etcd-raft.(*raftNode).startRaft.gowrap2 (71,077 samples, 7.01%)</title><rect x="1105.6" y="437" width="82.7" height="15.0" fill="rgb(221,75,18)" rx="2" ry="2" />
<text  x="1108.58" y="447.5" >plat-swr/..</text>
</g>
<g >
<title>runtime.chanrecv1 (131 samples, 0.01%)</title><rect x="582.9" y="357" width="0.1" height="15.0" fill="rgb(223,85,20)" rx="2" ry="2" />
<text  x="585.89" y="367.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/infra/httputil.httpDoRsp (1,605 samples, 0.16%)</title><rect x="852.1" y="261" width="1.9" height="15.0" fill="rgb(236,143,34)" rx="2" ry="2" />
<text  x="855.09" y="271.5" ></text>
</g>
<g >
<title>net/http.(*Transport).RoundTrip (3,623 samples, 0.36%)</title><rect x="274.9" y="133" width="4.3" height="15.0" fill="rgb(239,157,37)" rx="2" ry="2" />
<text  x="277.95" y="143.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/ui/proxy/handler.SendPostUploadMsg (205 samples, 0.02%)</title><rect x="566.9" y="261" width="0.2" height="15.0" fill="rgb(218,63,15)" rx="2" ry="2" />
<text  x="569.87" y="271.5" ></text>
</g>
<g >
<title>runtime.chansend1 (527 samples, 0.05%)</title><rect x="1046.8" y="357" width="0.6" height="15.0" fill="rgb(212,33,8)" rx="2" ry="2" />
<text  x="1049.81" y="367.5" ></text>
</g>
<g >
<title>sync.(*Mutex).Lock (651 samples, 0.06%)</title><rect x="1043.3" y="325" width="0.8" height="15.0" fill="rgb(231,119,28)" rx="2" ry="2" />
<text  x="1046.33" y="335.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/infra/httputil.httpDoRsp.func1 (3,623 samples, 0.36%)</title><rect x="274.9" y="213" width="4.3" height="15.0" fill="rgb(207,12,2)" rx="2" ry="2" />
<text  x="277.95" y="223.5" ></text>
</g>
<g >
<title>sync.(*Mutex).Lock (3,975 samples, 0.39%)</title><rect x="886.0" y="325" width="4.6" height="15.0" fill="rgb(231,119,28)" rx="2" ry="2" />
<text  x="888.98" y="335.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/ui/callbacks/impl.(*PutUpload).CommitWrite (27,493 samples, 2.71%)</title><rect x="854.0" y="341" width="32.0" height="15.0" fill="rgb(244,182,43)" rx="2" ry="2" />
<text  x="856.96" y="351.5" >pl..</text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/ui/proxy/handler.ExecRaftCmd (717 samples, 0.07%)</title><rect x="283.2" y="229" width="0.8" height="15.0" fill="rgb(245,184,44)" rx="2" ry="2" />
<text  x="286.16" y="239.5" ></text>
</g>
<g >
<title>plat-swr/pkg/zraft/core.(*Raft).Propose (299 samples, 0.03%)</title><rect x="577.7" y="117" width="0.4" height="15.0" fill="rgb(222,79,19)" rx="2" ry="2" />
<text  x="580.73" y="127.5" ></text>
</g>
<g >
<title>net/http.(*Transport).RoundTrip (9,182 samples, 0.91%)</title><rect x="583.0" y="229" width="10.7" height="15.0" fill="rgb(239,157,37)" rx="2" ry="2" />
<text  x="586.05" y="239.5" ></text>
</g>
<g >
<title>plat-swr/pkg/zraft/core.(*Raft).proposeMsg (203 samples, 0.02%)</title><rect x="566.9" y="197" width="0.2" height="15.0" fill="rgb(209,22,5)" rx="2" ry="2" />
<text  x="569.87" y="207.5" ></text>
</g>
<g >
<title>plat-swr/pkg/zraft/core.NewExec.gowrap1 (283,669 samples, 28.00%)</title><rect x="617.6" y="437" width="330.3" height="15.0" fill="rgb(245,185,44)" rx="2" ry="2" />
<text  x="620.56" y="447.5" >plat-swr/pkg/zraft/core.NewExec.gowrap1</text>
</g>
<g >
<title>runtime.chanrecv1 (1,835 samples, 0.18%)</title><rect x="883.8" y="293" width="2.2" height="15.0" fill="rgb(223,85,20)" rx="2" ry="2" />
<text  x="886.84" y="303.5" ></text>
</g>
<g >
<title>runtime.chanrecv1 (12,301 samples, 1.21%)</title><rect x="628.0" y="229" width="14.4" height="15.0" fill="rgb(223,85,20)" rx="2" ry="2" />
<text  x="631.04" y="239.5" ></text>
</g>
<g >
<title>plat-swr/pkg/zraft/core.(*Raft).proposeMsgWithResult (1,320 samples, 0.13%)</title><rect x="273.4" y="229" width="1.5" height="15.0" fill="rgb(220,71,17)" rx="2" ry="2" />
<text  x="276.41" y="239.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/ui/proxy/handler.buildCmdMessage (109 samples, 0.01%)</title><rect x="578.1" y="229" width="0.1" height="15.0" fill="rgb(231,123,29)" rx="2" ry="2" />
<text  x="581.10" y="239.5" ></text>
</g>
<g >
<title>runtime.chanrecv1 (1,323 samples, 0.13%)</title><rect x="848.6" y="293" width="1.6" height="15.0" fill="rgb(223,85,20)" rx="2" ry="2" />
<text  x="851.65" y="303.5" ></text>
</g>
<g >
<title>net/http.(*Transport).startDialConnForLocked.func1 (143 samples, 0.01%)</title><rect x="566.6" y="437" width="0.2" height="15.0" fill="rgb(240,162,38)" rx="2" ry="2" />
<text  x="569.65" y="447.5" ></text>
</g>
<g >
<title>runtime.selectgo (190 samples, 0.02%)</title><rect x="578.4" y="261" width="0.2" height="15.0" fill="rgb(212,35,8)" rx="2" ry="2" />
<text  x="581.40" y="271.5" ></text>
</g>
<g >
<title>net/http.(*persistConn).roundTrip (532 samples, 0.05%)</title><rect x="10.9" y="389" width="0.6" height="15.0" fill="rgb(221,74,17)" rx="2" ry="2" />
<text  x="13.90" y="399.5" ></text>
</g>
<g >
<title>k8s.io/klog/v2.Infof (195 samples, 0.02%)</title><rect x="950.0" y="357" width="0.3" height="15.0" fill="rgb(247,194,46)" rx="2" ry="2" />
<text  x="953.05" y="367.5" ></text>
</g>
<g >
<title>runtime.selectgo (6,407 samples, 0.63%)</title><rect x="586.3" y="181" width="7.4" height="15.0" fill="rgb(212,35,8)" rx="2" ry="2" />
<text  x="589.28" y="191.5" ></text>
</g>
<g >
<title>net/http.(*Client).send (1,605 samples, 0.16%)</title><rect x="852.1" y="181" width="1.9" height="15.0" fill="rgb(241,168,40)" rx="2" ry="2" />
<text  x="855.09" y="191.5" ></text>
</g>
<g >
<title>k8s.io/klog/v2.(*loggingT).printf (268 samples, 0.03%)</title><rect x="949.7" y="341" width="0.3" height="15.0" fill="rgb(223,83,19)" rx="2" ry="2" />
<text  x="952.74" y="351.5" ></text>
</g>
<g >
<title>net/http.(*initALPNRequest).ServeHTTP (9,747 samples, 0.96%)</title><rect x="566.9" y="389" width="11.3" height="15.0" fill="rgb(247,193,46)" rx="2" ry="2" />
<text  x="569.87" y="399.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/ui/proxy/handler.SendBreakPointFinishFlagToRaftCluster (717 samples, 0.07%)</title><rect x="283.2" y="261" width="0.8" height="15.0" fill="rgb(237,150,35)" rx="2" ry="2" />
<text  x="286.16" y="271.5" ></text>
</g>
<g >
<title>net/http.(*http2responseWriterState).writeChunk (509 samples, 0.05%)</title><rect x="578.6" y="309" width="0.6" height="15.0" fill="rgb(243,174,41)" rx="2" ry="2" />
<text  x="581.62" y="319.5" ></text>
</g>
<g >
<title>net/http.(*transferWriter).writeBody (222,278 samples, 21.94%)</title><rect x="285.5" y="389" width="258.9" height="15.0" fill="rgb(229,114,27)" rx="2" ry="2" />
<text  x="288.52" y="399.5" >net/http.(*transferWriter).writeBody</text>
</g>
<g >
<title>main.main.func2 (144 samples, 0.01%)</title><rect x="235.7" y="437" width="0.1" height="15.0" fill="rgb(242,171,40)" rx="2" ry="2" />
<text  x="238.67" y="447.5" ></text>
</g>
<g >
<title>net/http.(*onceCloseListener).Accept (137 samples, 0.01%)</title><rect x="1105.4" y="373" width="0.2" height="15.0" fill="rgb(214,43,10)" rx="2" ry="2" />
<text  x="1108.42" y="383.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/infra/httputil.httpDoRsp (439 samples, 0.04%)</title><rect x="567.1" y="197" width="0.5" height="15.0" fill="rgb(236,143,34)" rx="2" ry="2" />
<text  x="570.11" y="207.5" ></text>
</g>
<g >
<title>runtime.selectgo (5,647 samples, 0.56%)</title><rect x="621.1" y="101" width="6.6" height="15.0" fill="rgb(212,35,8)" rx="2" ry="2" />
<text  x="624.13" y="111.5" ></text>
</g>
<g >
<title>net/http.(*Server).ServeTLS (137 samples, 0.01%)</title><rect x="1105.4" y="405" width="0.2" height="15.0" fill="rgb(228,110,26)" rx="2" ry="2" />
<text  x="1108.42" y="415.5" ></text>
</g>
<g >
<title>go.etcd.io/etcd/raft/v3.(*node).Step (13,275 samples, 1.31%)</title><rect x="130.3" y="405" width="15.5" height="15.0" fill="rgb(216,53,12)" rx="2" ry="2" />
<text  x="133.33" y="415.5" ></text>
</g>
<g >
<title>plat-swr/pkg/zraft/api.(*RaftService).ExecuteCmd (677 samples, 0.07%)</title><rect x="271.1" y="229" width="0.8" height="15.0" fill="rgb(234,135,32)" rx="2" ry="2" />
<text  x="274.09" y="239.5" ></text>
</g>
<g >
<title>net/http.(*Transport).getConn (1,234 samples, 0.12%)</title><rect x="274.9" y="101" width="1.5" height="15.0" fill="rgb(240,165,39)" rx="2" ry="2" />
<text  x="277.95" y="111.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/infra/httputil.httpDoRsp (1,635 samples, 0.16%)</title><rect x="850.2" y="261" width="1.9" height="15.0" fill="rgb(236,143,34)" rx="2" ry="2" />
<text  x="853.19" y="271.5" ></text>
</g>
<g >
<title>runtime.selectgo (235 samples, 0.02%)</title><rect x="284.0" y="181" width="0.3" height="15.0" fill="rgb(212,35,8)" rx="2" ry="2" />
<text  x="287.00" y="191.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/ui/proxy/handler.sendChunkData (299 samples, 0.03%)</title><rect x="577.7" y="181" width="0.4" height="15.0" fill="rgb(249,205,49)" rx="2" ry="2" />
<text  x="580.73" y="191.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/infra/httputil.HttpDo (1,635 samples, 0.16%)</title><rect x="850.2" y="309" width="1.9" height="15.0" fill="rgb(239,156,37)" rx="2" ry="2" />
<text  x="853.19" y="319.5" ></text>
</g>
<g >
<title>net/http.send (1,635 samples, 0.16%)</title><rect x="850.2" y="165" width="1.9" height="15.0" fill="rgb(254,227,54)" rx="2" ry="2" />
<text  x="853.19" y="175.5" ></text>
</g>
<g >
<title>runtime.selectgo (138 samples, 0.01%)</title><rect x="235.8" y="421" width="0.2" height="15.0" fill="rgb(212,35,8)" rx="2" ry="2" />
<text  x="238.84" y="431.5" ></text>
</g>
<g >
<title>runtime.selectgo (33,633 samples, 3.32%)</title><rect x="145.8" y="421" width="39.2" height="15.0" fill="rgb(212,35,8)" rx="2" ry="2" />
<text  x="148.79" y="431.5" >run..</text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/ui/proxy/handler.ExecRaftCmd (2,544 samples, 0.25%)</title><rect x="280.2" y="277" width="3.0" height="15.0" fill="rgb(245,184,44)" rx="2" ry="2" />
<text  x="283.20" y="287.5" ></text>
</g>
<g >
<title>net/http.(*Transport).getConn (2,652 samples, 0.26%)</title><rect x="618.0" y="117" width="3.1" height="15.0" fill="rgb(240,165,39)" rx="2" ry="2" />
<text  x="621.05" y="127.5" ></text>
</g>
<g >
<title>net/http.(*http2responseWriter).Flush (850 samples, 0.08%)</title><rect x="578.2" y="357" width="1.0" height="15.0" fill="rgb(234,133,31)" rx="2" ry="2" />
<text  x="581.22" y="367.5" ></text>
</g>
<g >
<title>plat-swr/pkg/zraft/core.(*Raft).proposeMsg (1,315 samples, 0.13%)</title><rect x="271.9" y="165" width="1.5" height="15.0" fill="rgb(209,22,5)" rx="2" ry="2" />
<text  x="274.88" y="175.5" ></text>
</g>
<g >
<title>net/http.(*Client).do (143 samples, 0.01%)</title><rect x="235.7" y="373" width="0.1" height="15.0" fill="rgb(230,117,28)" rx="2" ry="2" />
<text  x="238.67" y="383.5" ></text>
</g>
<g >
<title>net/http.(*Transport).roundTrip (19,648 samples, 1.94%)</title><rect x="895.4" y="261" width="22.9" height="15.0" fill="rgb(232,126,30)" rx="2" ry="2" />
<text  x="898.41" y="271.5" >n..</text>
</g>
<g >
<title>runtime.selectgo (109 samples, 0.01%)</title><rect x="578.1" y="133" width="0.1" height="15.0" fill="rgb(212,35,8)" rx="2" ry="2" />
<text  x="581.10" y="143.5" ></text>
</g>
<g >
<title>net/http.send (143 samples, 0.01%)</title><rect x="235.7" y="341" width="0.1" height="15.0" fill="rgb(254,227,54)" rx="2" ry="2" />
<text  x="238.67" y="351.5" ></text>
</g>
<g >
<title>runtime.selectgo (520 samples, 0.05%)</title><rect x="852.1" y="101" width="0.6" height="15.0" fill="rgb(212,35,8)" rx="2" ry="2" />
<text  x="855.09" y="111.5" ></text>
</g>
<g >
<title>net/http.(*Client).do (8,299 samples, 0.82%)</title><rect x="618.0" y="197" width="9.7" height="15.0" fill="rgb(230,117,28)" rx="2" ry="2" />
<text  x="621.05" y="207.5" ></text>
</g>
<g >
<title>net/http.(*persistConn).roundTrip (1,085 samples, 0.11%)</title><rect x="852.7" y="117" width="1.3" height="15.0" fill="rgb(221,74,17)" rx="2" ry="2" />
<text  x="855.70" y="127.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/ui/callbacks/impl.(*Stream).GetResult (1,835 samples, 0.18%)</title><rect x="883.8" y="309" width="2.2" height="15.0" fill="rgb(247,195,46)" rx="2" ry="2" />
<text  x="886.84" y="319.5" ></text>
</g>
<g >
<title>net/http/httputil.(*ReverseProxy).copyResponse (154 samples, 0.02%)</title><rect x="280.0" y="309" width="0.2" height="15.0" fill="rgb(246,192,46)" rx="2" ry="2" />
<text  x="283.02" y="319.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/ui/callbacks/impl.(*Stream).GetResult (1,323 samples, 0.13%)</title><rect x="848.6" y="309" width="1.6" height="15.0" fill="rgb(247,195,46)" rx="2" ry="2" />
<text  x="851.65" y="319.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/ui/proxy/handler.BreakPointHandler (1,997 samples, 0.20%)</title><rect x="271.1" y="325" width="2.3" height="15.0" fill="rgb(215,48,11)" rx="2" ry="2" />
<text  x="274.09" y="335.5" ></text>
</g>
<g >
<title>net/http.(*Client).Do (439 samples, 0.04%)</title><rect x="567.1" y="149" width="0.5" height="15.0" fill="rgb(237,147,35)" rx="2" ry="2" />
<text  x="570.11" y="159.5" ></text>
</g>
<g >
<title>runtime.selectgo (92 samples, 0.01%)</title><rect x="235.7" y="277" width="0.1" height="15.0" fill="rgb(212,35,8)" rx="2" ry="2" />
<text  x="238.73" y="287.5" ></text>
</g>
<g >
<title>net/http.(*Transport).dialConn (137 samples, 0.01%)</title><rect x="566.6" y="405" width="0.2" height="15.0" fill="rgb(209,21,5)" rx="2" ry="2" />
<text  x="569.65" y="415.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/ui/proxy/handler.HeadManifestOrBlob (2,544 samples, 0.25%)</title><rect x="280.2" y="325" width="3.0" height="15.0" fill="rgb(228,105,25)" rx="2" ry="2" />
<text  x="283.20" y="335.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/ui/callbacks/impl.(*HeadBlob).CommitRead (8,304 samples, 0.82%)</title><rect x="618.0" y="341" width="9.7" height="15.0" fill="rgb(236,142,34)" rx="2" ry="2" />
<text  x="621.05" y="351.5" ></text>
</g>
<g >
<title>plat-swr/pkg/zraft/core.(*Raft).proposeMsg (316 samples, 0.03%)</title><rect x="567.6" y="181" width="0.4" height="15.0" fill="rgb(209,22,5)" rx="2" ry="2" />
<text  x="570.63" y="191.5" ></text>
</g>
<g >
<title>runtime.deferreturn (850 samples, 0.08%)</title><rect x="578.2" y="405" width="1.0" height="15.0" fill="rgb(242,170,40)" rx="2" ry="2" />
<text  x="581.22" y="415.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/infra/httputil.HttpDo (1,605 samples, 0.16%)</title><rect x="852.1" y="309" width="1.9" height="15.0" fill="rgb(239,156,37)" rx="2" ry="2" />
<text  x="855.09" y="319.5" ></text>
</g>
<g >
<title>runtime.selectgo (15,487 samples, 1.53%)</title><rect x="929.9" y="405" width="18.0" height="15.0" fill="rgb(212,35,8)" rx="2" ry="2" />
<text  x="932.88" y="415.5" ></text>
</g>
<g >
<title>net/http.(*persistConn).addTLS.func2 (131 samples, 0.01%)</title><rect x="582.9" y="437" width="0.1" height="15.0" fill="rgb(244,181,43)" rx="2" ry="2" />
<text  x="585.89" y="447.5" ></text>
</g>
<g >
<title>runtime.selectgo (509 samples, 0.05%)</title><rect x="578.6" y="277" width="0.6" height="15.0" fill="rgb(212,35,8)" rx="2" ry="2" />
<text  x="581.62" y="287.5" ></text>
</g>
<g >
<title>net/http.send (8,299 samples, 0.82%)</title><rect x="618.0" y="165" width="9.7" height="15.0" fill="rgb(254,227,54)" rx="2" ry="2" />
<text  x="621.05" y="175.5" ></text>
</g>
<g >
<title>plat-swr/pkg/zraft/core.(*Raft).proposeMsg (715 samples, 0.07%)</title><rect x="283.2" y="181" width="0.8" height="15.0" fill="rgb(209,22,5)" rx="2" ry="2" />
<text  x="286.16" y="191.5" ></text>
</g>
<g >
<title>net/http.(*persistConn).wroteRequest (219 samples, 0.02%)</title><rect x="284.3" y="405" width="0.2" height="15.0" fill="rgb(231,120,28)" rx="2" ry="2" />
<text  x="287.28" y="415.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/infra/httputil.HttpDoData (1,635 samples, 0.16%)</title><rect x="850.2" y="293" width="1.9" height="15.0" fill="rgb(247,194,46)" rx="2" ry="2" />
<text  x="853.19" y="303.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/infra/httputil.HttpDo (3,623 samples, 0.36%)</title><rect x="274.9" y="293" width="4.3" height="15.0" fill="rgb(239,156,37)" rx="2" ry="2" />
<text  x="277.95" y="303.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/ui/proxy/handler.processStreamData (8,449 samples, 0.83%)</title><rect x="568.2" y="213" width="9.9" height="15.0" fill="rgb(253,224,53)" rx="2" ry="2" />
<text  x="571.24" y="223.5" ></text>
</g>
<g >
<title>k8s.io/klog/v2.Infof (777 samples, 0.08%)</title><rect x="948.8" y="373" width="0.9" height="15.0" fill="rgb(247,194,46)" rx="2" ry="2" />
<text  x="951.83" y="383.5" ></text>
</g>
<g >
<title>net/http.(*http2serverConn).noteBodyReadFromHandler (7,563 samples, 0.75%)</title><rect x="568.9" y="165" width="8.8" height="15.0" fill="rgb(243,178,42)" rx="2" ry="2" />
<text  x="571.92" y="175.5" ></text>
</g>
<g >
<title>runtime.selectgo (55,000 samples, 5.43%)</title><rect x="979.3" y="405" width="64.0" height="15.0" fill="rgb(212,35,8)" rx="2" ry="2" />
<text  x="982.28" y="415.5" >runtime..</text>
</g>
<g >
<title>runtime.chansend1 (208,919 samples, 20.62%)</title><rect x="285.5" y="293" width="243.3" height="15.0" fill="rgb(212,33,8)" rx="2" ry="2" />
<text  x="288.52" y="303.5" >runtime.chansend1</text>
</g>
<g >
<title>k8s.io/klog/v2.(*loggingT).printfDepth (789 samples, 0.08%)</title><rect x="947.9" y="357" width="0.9" height="15.0" fill="rgb(236,146,34)" rx="2" ry="2" />
<text  x="950.91" y="367.5" ></text>
</g>
<g >
<title>go.etcd.io/etcd/raft/v3.(*node).stepWithWaitOption (14,916 samples, 1.47%)</title><rect x="1073.8" y="389" width="17.4" height="15.0" fill="rgb(243,174,41)" rx="2" ry="2" />
<text  x="1076.83" y="399.5" ></text>
</g>
<g >
<title>github.com/labstack/echo/v4/middleware.RecoverWithConfig.func1.1 (11,323 samples, 1.12%)</title><rect x="271.1" y="373" width="13.2" height="15.0" fill="rgb(239,156,37)" rx="2" ry="2" />
<text  x="274.09" y="383.5" ></text>
</g>
<g >
<title>plat-swr/pkg/zraft/core.(*Raft).waitForProposalResult (207 samples, 0.02%)</title><rect x="567.6" y="149" width="0.3" height="15.0" fill="rgb(243,179,42)" rx="2" ry="2" />
<text  x="570.63" y="159.5" ></text>
</g>
<g >
<title>runtime.selectgo (1,234 samples, 0.12%)</title><rect x="274.9" y="85" width="1.5" height="15.0" fill="rgb(212,35,8)" rx="2" ry="2" />
<text  x="277.95" y="95.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/ui/proxy/handler.PutManifestOrBlob (954 samples, 0.09%)</title><rect x="283.2" y="325" width="1.1" height="15.0" fill="rgb(246,189,45)" rx="2" ry="2" />
<text  x="286.16" y="335.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/ui/proxy/handler.SendBreakPointFinishFlagToRaftCluster (677 samples, 0.07%)</title><rect x="271.1" y="277" width="0.8" height="15.0" fill="rgb(237,150,35)" rx="2" ry="2" />
<text  x="274.09" y="287.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/infra/httputil.HttpDo (9,184 samples, 0.91%)</title><rect x="583.0" y="389" width="10.7" height="15.0" fill="rgb(239,156,37)" rx="2" ry="2" />
<text  x="586.05" y="399.5" ></text>
</g>
<g >
<title>net/http.(*Transport).RoundTrip (669 samples, 0.07%)</title><rect x="10.7" y="421" width="0.8" height="15.0" fill="rgb(239,157,37)" rx="2" ry="2" />
<text  x="13.74" y="431.5" ></text>
</g>
<g >
<title>sync.(*Mutex).Lock (195 samples, 0.02%)</title><rect x="950.0" y="293" width="0.3" height="15.0" fill="rgb(231,119,28)" rx="2" ry="2" />
<text  x="953.05" y="303.5" ></text>
</g>
<g >
<title>all (1,013,259 samples, 100%)</title><rect x="10.0" y="453" width="1180.0" height="15.0" fill="rgb(213,39,9)" rx="2" ry="2" />
<text  x="13.00" y="463.5" ></text>
</g>
<g >
<title>plat-swr/pkg/zraft/etcd-raft.(*stoppableListener).Accept (137 samples, 0.01%)</title><rect x="1105.4" y="341" width="0.2" height="15.0" fill="rgb(235,140,33)" rx="2" ry="2" />
<text  x="1108.42" y="351.5" ></text>
</g>
<g >
<title>plat-swr/pkg/zraft/core.(*Receiver).DispatchResultToGroup (527 samples, 0.05%)</title><rect x="1046.8" y="389" width="0.6" height="15.0" fill="rgb(239,157,37)" rx="2" ry="2" />
<text  x="1049.81" y="399.5" ></text>
</g>
<g >
<title>runtime.selectgo (24,704 samples, 2.44%)</title><rect x="855.1" y="229" width="28.7" height="15.0" fill="rgb(212,35,8)" rx="2" ry="2" />
<text  x="858.07" y="239.5" >ru..</text>
</g>
<g >
<title>k8s.io/klog/v2.(*loggingT).printf (789 samples, 0.08%)</title><rect x="947.9" y="373" width="0.9" height="15.0" fill="rgb(223,83,19)" rx="2" ry="2" />
<text  x="950.91" y="383.5" ></text>
</g>
<g >
<title>net/http.(*Server).Serve (137 samples, 0.01%)</title><rect x="1105.4" y="389" width="0.2" height="15.0" fill="rgb(248,200,48)" rx="2" ry="2" />
<text  x="1108.42" y="399.5" ></text>
</g>
<g >
<title>go.etcd.io/etcd/raft/v3.(*node).Advance (414 samples, 0.04%)</title><rect x="1105.6" y="405" width="0.5" height="15.0" fill="rgb(220,72,17)" rx="2" ry="2" />
<text  x="1108.58" y="415.5" ></text>
</g>
<g >
<title>plat-swr/pkg/zraft/core.(*Raft).Start.gowrap2 (20,385 samples, 2.01%)</title><rect x="593.8" y="437" width="23.8" height="15.0" fill="rgb(222,81,19)" rx="2" ry="2" />
<text  x="596.82" y="447.5" >p..</text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/infra/httputil.HttpDoData (8,300 samples, 0.82%)</title><rect x="618.0" y="293" width="9.7" height="15.0" fill="rgb(247,194,46)" rx="2" ry="2" />
<text  x="621.05" y="303.5" ></text>
</g>
<g >
<title>plat-swr/pkg/zraft/core.(*Raft).proposeMsgWithResult (203 samples, 0.02%)</title><rect x="568.0" y="117" width="0.2" height="15.0" fill="rgb(220,71,17)" rx="2" ry="2" />
<text  x="571.00" y="127.5" ></text>
</g>
<g >
<title>net/http.(*http2responseWriter).handlerDone (850 samples, 0.08%)</title><rect x="578.2" y="373" width="1.0" height="15.0" fill="rgb(243,177,42)" rx="2" ry="2" />
<text  x="581.22" y="383.5" ></text>
</g>
<g >
<title>runtime.selectgo (14,389 samples, 1.42%)</title><rect x="254.1" y="325" width="16.8" height="15.0" fill="rgb(212,35,8)" rx="2" ry="2" />
<text  x="257.10" y="335.5" ></text>
</g>
<g >
<title>net/http.http2ConfigureServer.func1 (14,752 samples, 1.46%)</title><rect x="253.7" y="389" width="17.2" height="15.0" fill="rgb(226,100,24)" rx="2" ry="2" />
<text  x="256.68" y="399.5" ></text>
</g>
<g >
<title>net/http.(*http2requestBody).Read (8,150 samples, 0.80%)</title><rect x="568.2" y="181" width="9.5" height="15.0" fill="rgb(252,218,52)" rx="2" ry="2" />
<text  x="571.24" y="191.5" ></text>
</g>
<g >
<title>k8s.io/klog/v2.Infof (268 samples, 0.03%)</title><rect x="949.7" y="357" width="0.3" height="15.0" fill="rgb(247,194,46)" rx="2" ry="2" />
<text  x="952.74" y="367.5" ></text>
</g>
<g >
<title>plat-swr/pkg/zraft/core.(*Raft).waitForProposalResult (677 samples, 0.07%)</title><rect x="271.1" y="165" width="0.8" height="15.0" fill="rgb(243,179,42)" rx="2" ry="2" />
<text  x="274.09" y="175.5" ></text>
</g>
</g>
</svg>
