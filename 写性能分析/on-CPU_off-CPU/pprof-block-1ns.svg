<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg version="1.1" width="1200" height="502" onload="init(evt)" viewBox="0 0 1200 502" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<!-- Flame graph stack visualization. See https://github.com/brendangregg/FlameGraph for latest version, and http://www.brendangregg.com/flamegraphs.html for examples. -->
<!-- NOTES:  -->
<defs>
	<linearGradient id="background" y1="0" y2="1" x1="0" x2="0" >
		<stop stop-color="#eeeeee" offset="5%" />
		<stop stop-color="#eeeeb0" offset="95%" />
	</linearGradient>
</defs>
<style type="text/css">
	text { font-family:Verdana; font-size:12px; fill:rgb(0,0,0); }
	#search, #ignorecase { opacity:0.1; cursor:pointer; }
	#search:hover, #search.show, #ignorecase:hover, #ignorecase.show { opacity:1; }
	#subtitle { text-anchor:middle; font-color:rgb(160,160,160); }
	#title { text-anchor:middle; font-size:17px}
	#unzoom { cursor:pointer; }
	#frames > *:hover { stroke:black; stroke-width:0.5; cursor:pointer; }
	.hide { display:none; }
	.parent { opacity:0.5; }
</style>
<script type="text/ecmascript">
<![CDATA[
	"use strict";
	var details, searchbtn, unzoombtn, matchedtxt, svg, searching, currentSearchTerm, ignorecase, ignorecaseBtn;
	function init(evt) {
		details = document.getElementById("details").firstChild;
		searchbtn = document.getElementById("search");
		ignorecaseBtn = document.getElementById("ignorecase");
		unzoombtn = document.getElementById("unzoom");
		matchedtxt = document.getElementById("matched");
		svg = document.getElementsByTagName("svg")[0];
		searching = 0;
		currentSearchTerm = null;

		// use GET parameters to restore a flamegraphs state.
		var params = get_params();
		if (params.x && params.y)
			zoom(find_group(document.querySelector('[x="' + params.x + '"][y="' + params.y + '"]')));
                if (params.s) search(params.s);
	}

	// event listeners
	window.addEventListener("click", function(e) {
		var target = find_group(e.target);
		if (target) {
			if (target.nodeName == "a") {
				if (e.ctrlKey === false) return;
				e.preventDefault();
			}
			if (target.classList.contains("parent")) unzoom(true);
			zoom(target);
			if (!document.querySelector('.parent')) {
				// we have basically done a clearzoom so clear the url
				var params = get_params();
				if (params.x) delete params.x;
				if (params.y) delete params.y;
				history.replaceState(null, null, parse_params(params));
				unzoombtn.classList.add("hide");
				return;
			}

			// set parameters for zoom state
			var el = target.querySelector("rect");
			if (el && el.attributes && el.attributes.y && el.attributes._orig_x) {
				var params = get_params()
				params.x = el.attributes._orig_x.value;
				params.y = el.attributes.y.value;
				history.replaceState(null, null, parse_params(params));
			}
		}
		else if (e.target.id == "unzoom") clearzoom();
		else if (e.target.id == "search") search_prompt();
		else if (e.target.id == "ignorecase") toggle_ignorecase();
	}, false)

	// mouse-over for info
	// show
	window.addEventListener("mouseover", function(e) {
		var target = find_group(e.target);
		if (target) details.nodeValue = "Function: " + g_to_text(target);
	}, false)

	// clear
	window.addEventListener("mouseout", function(e) {
		var target = find_group(e.target);
		if (target) details.nodeValue = ' ';
	}, false)

	// ctrl-F for search
	// ctrl-I to toggle case-sensitive search
	window.addEventListener("keydown",function (e) {
		if (e.keyCode === 114 || (e.ctrlKey && e.keyCode === 70)) {
			e.preventDefault();
			search_prompt();
		}
		else if (e.ctrlKey && e.keyCode === 73) {
			e.preventDefault();
			toggle_ignorecase();
		}
	}, false)

	// functions
	function get_params() {
		var params = {};
		var paramsarr = window.location.search.substr(1).split('&');
		for (var i = 0; i < paramsarr.length; ++i) {
			var tmp = paramsarr[i].split("=");
			if (!tmp[0] || !tmp[1]) continue;
			params[tmp[0]]  = decodeURIComponent(tmp[1]);
		}
		return params;
	}
	function parse_params(params) {
		var uri = "?";
		for (var key in params) {
			uri += key + '=' + encodeURIComponent(params[key]) + '&';
		}
		if (uri.slice(-1) == "&")
			uri = uri.substring(0, uri.length - 1);
		if (uri == '?')
			uri = window.location.href.split('?')[0];
		return uri;
	}
	function find_child(node, selector) {
		var children = node.querySelectorAll(selector);
		if (children.length) return children[0];
	}
	function find_group(node) {
		var parent = node.parentElement;
		if (!parent) return;
		if (parent.id == "frames") return node;
		return find_group(parent);
	}
	function orig_save(e, attr, val) {
		if (e.attributes["_orig_" + attr] != undefined) return;
		if (e.attributes[attr] == undefined) return;
		if (val == undefined) val = e.attributes[attr].value;
		e.setAttribute("_orig_" + attr, val);
	}
	function orig_load(e, attr) {
		if (e.attributes["_orig_"+attr] == undefined) return;
		e.attributes[attr].value = e.attributes["_orig_" + attr].value;
		e.removeAttribute("_orig_"+attr);
	}
	function g_to_text(e) {
		var text = find_child(e, "title").firstChild.nodeValue;
		return (text)
	}
	function g_to_func(e) {
		var func = g_to_text(e);
		// if there's any manipulation we want to do to the function
		// name before it's searched, do it here before returning.
		return (func);
	}
	function update_text(e) {
		var r = find_child(e, "rect");
		var t = find_child(e, "text");
		var w = parseFloat(r.attributes.width.value) -3;
		var txt = find_child(e, "title").textContent.replace(/\([^(]*\)$/,"");
		t.attributes.x.value = parseFloat(r.attributes.x.value) + 3;

		// Smaller than this size won't fit anything
		if (w < 2 * 12 * 0.59) {
			t.textContent = "";
			return;
		}

		t.textContent = txt;
		var sl = t.getSubStringLength(0, txt.length);
		// check if only whitespace or if we can fit the entire string into width w
		if (/^ *$/.test(txt) || sl < w)
			return;

		// this isn't perfect, but gives a good starting point
		// and avoids calling getSubStringLength too often
		var start = Math.floor((w/sl) * txt.length);
		for (var x = start; x > 0; x = x-2) {
			if (t.getSubStringLength(0, x + 2) <= w) {
				t.textContent = txt.substring(0, x) + "..";
				return;
			}
		}
		t.textContent = "";
	}

	// zoom
	function zoom_reset(e) {
		if (e.attributes != undefined) {
			orig_load(e, "x");
			orig_load(e, "width");
		}
		if (e.childNodes == undefined) return;
		for (var i = 0, c = e.childNodes; i < c.length; i++) {
			zoom_reset(c[i]);
		}
	}
	function zoom_child(e, x, ratio) {
		if (e.attributes != undefined) {
			if (e.attributes.x != undefined) {
				orig_save(e, "x");
				e.attributes.x.value = (parseFloat(e.attributes.x.value) - x - 10) * ratio + 10;
				if (e.tagName == "text")
					e.attributes.x.value = find_child(e.parentNode, "rect[x]").attributes.x.value + 3;
			}
			if (e.attributes.width != undefined) {
				orig_save(e, "width");
				e.attributes.width.value = parseFloat(e.attributes.width.value) * ratio;
			}
		}

		if (e.childNodes == undefined) return;
		for (var i = 0, c = e.childNodes; i < c.length; i++) {
			zoom_child(c[i], x - 10, ratio);
		}
	}
	function zoom_parent(e) {
		if (e.attributes) {
			if (e.attributes.x != undefined) {
				orig_save(e, "x");
				e.attributes.x.value = 10;
			}
			if (e.attributes.width != undefined) {
				orig_save(e, "width");
				e.attributes.width.value = parseInt(svg.width.baseVal.value) - (10 * 2);
			}
		}
		if (e.childNodes == undefined) return;
		for (var i = 0, c = e.childNodes; i < c.length; i++) {
			zoom_parent(c[i]);
		}
	}
	function zoom(node) {
		var attr = find_child(node, "rect").attributes;
		var width = parseFloat(attr.width.value);
		var xmin = parseFloat(attr.x.value);
		var xmax = parseFloat(xmin + width);
		var ymin = parseFloat(attr.y.value);
		var ratio = (svg.width.baseVal.value - 2 * 10) / width;

		// XXX: Workaround for JavaScript float issues (fix me)
		var fudge = 0.0001;

		unzoombtn.classList.remove("hide");

		var el = document.getElementById("frames").children;
		for (var i = 0; i < el.length; i++) {
			var e = el[i];
			var a = find_child(e, "rect").attributes;
			var ex = parseFloat(a.x.value);
			var ew = parseFloat(a.width.value);
			var upstack;
			// Is it an ancestor
			if (0 == 0) {
				upstack = parseFloat(a.y.value) > ymin;
			} else {
				upstack = parseFloat(a.y.value) < ymin;
			}
			if (upstack) {
				// Direct ancestor
				if (ex <= xmin && (ex+ew+fudge) >= xmax) {
					e.classList.add("parent");
					zoom_parent(e);
					update_text(e);
				}
				// not in current path
				else
					e.classList.add("hide");
			}
			// Children maybe
			else {
				// no common path
				if (ex < xmin || ex + fudge >= xmax) {
					e.classList.add("hide");
				}
				else {
					zoom_child(e, xmin, ratio);
					update_text(e);
				}
			}
		}
		search();
	}
	function unzoom(dont_update_text) {
		unzoombtn.classList.add("hide");
		var el = document.getElementById("frames").children;
		for(var i = 0; i < el.length; i++) {
			el[i].classList.remove("parent");
			el[i].classList.remove("hide");
			zoom_reset(el[i]);
			if(!dont_update_text) update_text(el[i]);
		}
		search();
	}
	function clearzoom() {
		unzoom();

		// remove zoom state
		var params = get_params();
		if (params.x) delete params.x;
		if (params.y) delete params.y;
		history.replaceState(null, null, parse_params(params));
	}

	// search
	function toggle_ignorecase() {
		ignorecase = !ignorecase;
		if (ignorecase) {
			ignorecaseBtn.classList.add("show");
		} else {
			ignorecaseBtn.classList.remove("show");
		}
		reset_search();
		search();
	}
	function reset_search() {
		var el = document.querySelectorAll("#frames rect");
		for (var i = 0; i < el.length; i++) {
			orig_load(el[i], "fill")
		}
		var params = get_params();
		delete params.s;
		history.replaceState(null, null, parse_params(params));
	}
	function search_prompt() {
		if (!searching) {
			var term = prompt("Enter a search term (regexp " +
			    "allowed, eg: ^ext4_)"
			    + (ignorecase ? ", ignoring case" : "")
			    + "\nPress Ctrl-i to toggle case sensitivity", "");
			if (term != null) search(term);
		} else {
			reset_search();
			searching = 0;
			currentSearchTerm = null;
			searchbtn.classList.remove("show");
			searchbtn.firstChild.nodeValue = "Search"
			matchedtxt.classList.add("hide");
			matchedtxt.firstChild.nodeValue = ""
		}
	}
	function search(term) {
		if (term) currentSearchTerm = term;
		if (currentSearchTerm === null) return;

		var re = new RegExp(currentSearchTerm, ignorecase ? 'i' : '');
		var el = document.getElementById("frames").children;
		var matches = new Object();
		var maxwidth = 0;
		for (var i = 0; i < el.length; i++) {
			var e = el[i];
			var func = g_to_func(e);
			var rect = find_child(e, "rect");
			if (func == null || rect == null)
				continue;

			// Save max width. Only works as we have a root frame
			var w = parseFloat(rect.attributes.width.value);
			if (w > maxwidth)
				maxwidth = w;

			if (func.match(re)) {
				// highlight
				var x = parseFloat(rect.attributes.x.value);
				orig_save(rect, "fill");
				rect.attributes.fill.value = "rgb(230,0,230)";

				// remember matches
				if (matches[x] == undefined) {
					matches[x] = w;
				} else {
					if (w > matches[x]) {
						// overwrite with parent
						matches[x] = w;
					}
				}
				searching = 1;
			}
		}
		if (!searching)
			return;
		var params = get_params();
		params.s = currentSearchTerm;
		history.replaceState(null, null, parse_params(params));

		searchbtn.classList.add("show");
		searchbtn.firstChild.nodeValue = "Reset Search";

		// calculate percent matched, excluding vertical overlap
		var count = 0;
		var lastx = -1;
		var lastw = 0;
		var keys = Array();
		for (k in matches) {
			if (matches.hasOwnProperty(k))
				keys.push(k);
		}
		// sort the matched frames by their x location
		// ascending, then width descending
		keys.sort(function(a, b){
			return a - b;
		});
		// Step through frames saving only the biggest bottom-up frames
		// thanks to the sort order. This relies on the tree property
		// where children are always smaller than their parents.
		var fudge = 0.0001;	// JavaScript floating point
		for (var k in keys) {
			var x = parseFloat(keys[k]);
			var w = matches[keys[k]];
			if (x >= lastx + lastw - fudge) {
				count += w;
				lastx = x;
				lastw = w;
			}
		}
		// display matched percent
		matchedtxt.classList.remove("hide");
		var pct = 100 * count / maxwidth;
		if (pct != 100) pct = pct.toFixed(1)
		matchedtxt.firstChild.nodeValue = "Matched: " + pct + "%";
	}
]]>
</script>
<rect x="0.0" y="0" width="1200.0" height="502.0" fill="url(#background)"  />
<text id="title" x="600.00" y="24" >Flame Graph</text>
<text id="details" x="10.00" y="485" > </text>
<text id="unzoom" x="10.00" y="24" class="hide">Reset Zoom</text>
<text id="search" x="1090.00" y="24" >Search</text>
<text id="ignorecase" x="1174.00" y="24" >ic</text>
<text id="matched" x="1090.00" y="485" > </text>
<g id="frames">
<g >
<title>net/http.(*Server).Serve (137 samples, 0.01%)</title><rect x="1104.0" y="389" width="0.2" height="15.0" fill="rgb(248,200,48)" rx="2" ry="2" />
<text  x="1107.03" y="399.5" ></text>
</g>
<g >
<title>net/http.(*persistConn).writeLoop (231,532 samples, 23.10%)</title><rect x="287.3" y="421" width="272.6" height="15.0" fill="rgb(234,133,31)" rx="2" ry="2" />
<text  x="290.33" y="431.5" >net/http.(*persistConn).writeLoop</text>
</g>
<g >
<title>net/http.(*Transport).roundTrip (9,159 samples, 0.91%)</title><rect x="576.4" y="213" width="10.8" height="15.0" fill="rgb(232,126,30)" rx="2" ry="2" />
<text  x="579.40" y="223.5" ></text>
</g>
<g >
<title>net/http.(*conn).serve (42,588 samples, 4.25%)</title><rect x="236.0" y="421" width="50.1" height="15.0" fill="rgb(240,162,38)" rx="2" ry="2" />
<text  x="238.97" y="431.5" >net/h..</text>
</g>
<g >
<title>runtime.selectgo (18,908 samples, 1.89%)</title><rect x="537.6" y="405" width="22.3" height="15.0" fill="rgb(212,35,8)" rx="2" ry="2" />
<text  x="540.63" y="415.5" >r..</text>
</g>
<g >
<title>go.etcd.io/etcd/raft/v3.(*node).Advance (166 samples, 0.02%)</title><rect x="1104.2" y="405" width="0.2" height="15.0" fill="rgb(220,72,17)" rx="2" ry="2" />
<text  x="1107.20" y="415.5" ></text>
</g>
<g >
<title>plat-swr/pkg/zraft/core.(*Raft).proposeMsgWithResult (264 samples, 0.03%)</title><rect x="571.1" y="85" width="0.3" height="15.0" fill="rgb(220,71,17)" rx="2" ry="2" />
<text  x="574.14" y="95.5" ></text>
</g>
<g >
<title>runtime.selectgo (160 samples, 0.02%)</title><rect x="560.6" y="149" width="0.2" height="15.0" fill="rgb(212,35,8)" rx="2" ry="2" />
<text  x="563.62" y="159.5" ></text>
</g>
<g >
<title>net/http.(*http2serverConn).writeHeaders (251 samples, 0.03%)</title><rect x="571.7" y="277" width="0.3" height="15.0" fill="rgb(236,145,34)" rx="2" ry="2" />
<text  x="574.73" y="287.5" ></text>
</g>
<g >
<title>plat-swr/pkg/zraft/core.(*Raft).waitForProposalResult (1,222 samples, 0.12%)</title><rect x="275.3" y="213" width="1.4" height="15.0" fill="rgb(243,179,42)" rx="2" ry="2" />
<text  x="278.28" y="223.5" ></text>
</g>
<g >
<title>k8s.io/klog/v2.(*loggingT).output (190 samples, 0.02%)</title><rect x="281.8" y="133" width="0.2" height="15.0" fill="rgb(229,112,26)" rx="2" ry="2" />
<text  x="284.82" y="143.5" ></text>
</g>
<g >
<title>k8s.io/klog/v2.(*loggingT).printf (296 samples, 0.03%)</title><rect x="949.0" y="357" width="0.4" height="15.0" fill="rgb(223,83,19)" rx="2" ry="2" />
<text  x="952.04" y="367.5" ></text>
</g>
<g >
<title>net/http.(*Transport).roundTrip (3,615 samples, 0.36%)</title><rect x="276.7" y="117" width="4.3" height="15.0" fill="rgb(232,126,30)" rx="2" ry="2" />
<text  x="279.72" y="127.5" ></text>
</g>
<g >
<title>net/http.(*Client).Do (160 samples, 0.02%)</title><rect x="235.6" y="389" width="0.2" height="15.0" fill="rgb(237,147,35)" rx="2" ry="2" />
<text  x="238.57" y="399.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/infra/httputil.httpDoRsp.func1 (1,521 samples, 0.15%)</title><rect x="847.4" y="229" width="1.8" height="15.0" fill="rgb(207,12,2)" rx="2" ry="2" />
<text  x="850.45" y="239.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/ui/callbacks/impl.(*PostUpload).CommitWrite (1,625 samples, 0.16%)</title><rect x="845.5" y="341" width="1.9" height="15.0" fill="rgb(240,163,39)" rx="2" ry="2" />
<text  x="848.53" y="351.5" ></text>
</g>
<g >
<title>runtime.selectgo (313 samples, 0.03%)</title><rect x="286.1" y="389" width="0.4" height="15.0" fill="rgb(212,35,8)" rx="2" ry="2" />
<text  x="289.11" y="399.5" ></text>
</g>
<g >
<title>net/http.(*persistConn).roundTrip (2,359 samples, 0.24%)</title><rect x="278.2" y="101" width="2.8" height="15.0" fill="rgb(221,74,17)" rx="2" ry="2" />
<text  x="281.19" y="111.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/infra/httputil.retryDo (338 samples, 0.03%)</title><rect x="561.0" y="181" width="0.4" height="15.0" fill="rgb(220,69,16)" rx="2" ry="2" />
<text  x="564.01" y="191.5" ></text>
</g>
<g >
<title>k8s.io/klog/v2.(*loggingT).printf (122 samples, 0.01%)</title><rect x="894.2" y="325" width="0.1" height="15.0" fill="rgb(223,83,19)" rx="2" ry="2" />
<text  x="897.18" y="335.5" ></text>
</g>
<g >
<title>net/http.(*Transport).getConn (1,256 samples, 0.13%)</title><rect x="276.7" y="101" width="1.5" height="15.0" fill="rgb(240,165,39)" rx="2" ry="2" />
<text  x="279.72" y="111.5" ></text>
</g>
<g >
<title>net/http.(*Transport).getConn (2,723 samples, 0.27%)</title><rect x="611.7" y="117" width="3.2" height="15.0" fill="rgb(240,165,39)" rx="2" ry="2" />
<text  x="614.66" y="127.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/ui/callbacks/impl.(*Stream).GetResult (1,319 samples, 0.13%)</title><rect x="844.0" y="309" width="1.5" height="15.0" fill="rgb(247,195,46)" rx="2" ry="2" />
<text  x="846.98" y="319.5" ></text>
</g>
<g >
<title>plat-swr/pkg/zraft/core.(*Raft).proposeMsg (201 samples, 0.02%)</title><rect x="561.4" y="181" width="0.2" height="15.0" fill="rgb(209,22,5)" rx="2" ry="2" />
<text  x="564.40" y="191.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/infra/httputil.HttpDo (8,040 samples, 0.80%)</title><rect x="611.7" y="309" width="9.4" height="15.0" fill="rgb(239,156,37)" rx="2" ry="2" />
<text  x="614.66" y="319.5" ></text>
</g>
<g >
<title>plat-swr/pkg/zraft/core.(*Receiver).DispatchCmdToGroup (298 samples, 0.03%)</title><rect x="1046.7" y="389" width="0.4" height="15.0" fill="rgb(234,134,32)" rx="2" ry="2" />
<text  x="1049.72" y="399.5" ></text>
</g>
<g >
<title>plat-swr/pkg/zraft/core.(*ExecGroup).PushMessage (218 samples, 0.02%)</title><rect x="1046.7" y="373" width="0.3" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="1049.72" y="383.5" ></text>
</g>
<g >
<title>plat-swr/pkg/zraft/core.(*Exec).doProcessMessage (270,293 samples, 26.96%)</title><rect x="611.2" y="389" width="318.2" height="15.0" fill="rgb(239,156,37)" rx="2" ry="2" />
<text  x="614.17" y="399.5" >plat-swr/pkg/zraft/core.(*Exec).doProcessM..</text>
</g>
<g >
<title>net/http.(*Client).send (8,036 samples, 0.80%)</title><rect x="611.7" y="181" width="9.4" height="15.0" fill="rgb(241,168,40)" rx="2" ry="2" />
<text  x="614.66" y="191.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/ui/proxy/handler.HeadManifestOrBlob (201 samples, 0.02%)</title><rect x="561.4" y="277" width="0.2" height="15.0" fill="rgb(228,105,25)" rx="2" ry="2" />
<text  x="564.40" y="287.5" ></text>
</g>
<g >
<title>net/http.(*Transport).dial (451 samples, 0.04%)</title><rect x="559.9" y="389" width="0.5" height="15.0" fill="rgb(230,117,28)" rx="2" ry="2" />
<text  x="562.89" y="399.5" ></text>
</g>
<g >
<title>net/http/httputil.(*ReverseProxy).copyBuffer (138 samples, 0.01%)</title><rect x="281.6" y="293" width="0.2" height="15.0" fill="rgb(229,113,27)" rx="2" ry="2" />
<text  x="284.64" y="303.5" ></text>
</g>
<g >
<title>net/http.(*transferWriter).doBodyCopy (212,624 samples, 21.21%)</title><rect x="287.3" y="373" width="250.3" height="15.0" fill="rgb(246,193,46)" rx="2" ry="2" />
<text  x="290.33" y="383.5" >net/http.(*transferWriter).doBody..</text>
</g>
<g >
<title>net/http.(*Client).do (3,615 samples, 0.36%)</title><rect x="276.7" y="181" width="4.3" height="15.0" fill="rgb(230,117,28)" rx="2" ry="2" />
<text  x="279.72" y="191.5" ></text>
</g>
<g >
<title>net/http.http2chunkWriter.Write (478 samples, 0.05%)</title><rect x="572.0" y="325" width="0.6" height="15.0" fill="rgb(229,113,27)" rx="2" ry="2" />
<text  x="575.03" y="335.5" ></text>
</g>
<g >
<title>net/http.(*Transport).RoundTrip (3,615 samples, 0.36%)</title><rect x="276.7" y="133" width="4.3" height="15.0" fill="rgb(239,157,37)" rx="2" ry="2" />
<text  x="279.72" y="143.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/ui/proxy/handler.SendPostUploadMsg (1,248 samples, 0.12%)</title><rect x="275.2" y="309" width="1.5" height="15.0" fill="rgb(218,63,15)" rx="2" ry="2" />
<text  x="278.25" y="319.5" ></text>
</g>
<g >
<title>plat-swr/pkg/zraft/core.(*Raft).waitForProposalResult (630 samples, 0.06%)</title><rect x="285.0" y="149" width="0.8" height="15.0" fill="rgb(243,179,42)" rx="2" ry="2" />
<text  x="288.04" y="159.5" ></text>
</g>
<g >
<title>sync.(*Mutex).Lock (1,325 samples, 0.13%)</title><rect x="947.5" y="325" width="1.5" height="15.0" fill="rgb(231,119,28)" rx="2" ry="2" />
<text  x="950.48" y="335.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/ui/proxy/handler.SendBreakPointFinishFlagToRaftCluster (203 samples, 0.02%)</title><rect x="561.6" y="213" width="0.3" height="15.0" fill="rgb(237,150,35)" rx="2" ry="2" />
<text  x="564.64" y="223.5" ></text>
</g>
<g >
<title>runtime.chansend1 (353 samples, 0.04%)</title><rect x="1047.1" y="357" width="0.4" height="15.0" fill="rgb(212,33,8)" rx="2" ry="2" />
<text  x="1050.07" y="367.5" ></text>
</g>
<g >
<title>go.etcd.io/etcd/raft/v3.(*raftLog).firstIndex (126 samples, 0.01%)</title><rect x="11.4" y="309" width="0.2" height="15.0" fill="rgb(237,151,36)" rx="2" ry="2" />
<text  x="14.44" y="319.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/ui/callbacks/impl.(*HeadBlob).CommitRead (8,040 samples, 0.80%)</title><rect x="611.7" y="341" width="9.4" height="15.0" fill="rgb(236,142,34)" rx="2" ry="2" />
<text  x="614.66" y="351.5" ></text>
</g>
<g >
<title>net/http.(*persistConn).addTLS.func2 (384 samples, 0.04%)</title><rect x="576.0" y="437" width="0.4" height="15.0" fill="rgb(244,181,43)" rx="2" ry="2" />
<text  x="578.95" y="447.5" ></text>
</g>
<g >
<title>net/http.(*http2serverConn).serve (14,950 samples, 1.49%)</title><rect x="254.9" y="341" width="17.6" height="15.0" fill="rgb(247,194,46)" rx="2" ry="2" />
<text  x="257.90" y="351.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/ui/health.getStatusCode (162 samples, 0.02%)</title><rect x="235.6" y="405" width="0.2" height="15.0" fill="rgb(239,157,37)" rx="2" ry="2" />
<text  x="238.57" y="415.5" ></text>
</g>
<g >
<title>plat-swr/pkg/zraft/core.(*Exec).ProcessedMessage (270,293 samples, 26.96%)</title><rect x="611.2" y="405" width="318.2" height="15.0" fill="rgb(253,223,53)" rx="2" ry="2" />
<text  x="614.17" y="415.5" >plat-swr/pkg/zraft/core.(*Exec).ProcessedM..</text>
</g>
<g >
<title>runtime.selectgo (101,033 samples, 10.08%)</title><rect x="11.6" y="405" width="118.9" height="15.0" fill="rgb(212,35,8)" rx="2" ry="2" />
<text  x="14.59" y="415.5" >runtime.selectgo</text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/ui/proxy/handler.SendBreakPointToRaftCluster (203 samples, 0.02%)</title><rect x="561.6" y="197" width="0.3" height="15.0" fill="rgb(213,39,9)" rx="2" ry="2" />
<text  x="564.64" y="207.5" ></text>
</g>
<g >
<title>plat-swr/pkg/zraft/core.(*Raft).proposeMsgWithResult (633 samples, 0.06%)</title><rect x="285.0" y="165" width="0.8" height="15.0" fill="rgb(220,71,17)" rx="2" ry="2" />
<text  x="288.04" y="175.5" ></text>
</g>
<g >
<title>k8s.io/klog/v2.(*loggingT).output (392 samples, 0.04%)</title><rect x="611.2" y="325" width="0.4" height="15.0" fill="rgb(229,112,26)" rx="2" ry="2" />
<text  x="614.17" y="335.5" ></text>
</g>
<g >
<title>runtime.selectgo (1,214 samples, 0.12%)</title><rect x="275.3" y="197" width="1.4" height="15.0" fill="rgb(212,35,8)" rx="2" ry="2" />
<text  x="278.28" y="207.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/infra/httputil.HttpDoData (3,657 samples, 0.36%)</title><rect x="276.7" y="277" width="4.3" height="15.0" fill="rgb(247,194,46)" rx="2" ry="2" />
<text  x="279.72" y="287.5" ></text>
</g>
<g >
<title>k8s.io/klog/v2.(*loggingT).printfDepth (392 samples, 0.04%)</title><rect x="611.2" y="341" width="0.4" height="15.0" fill="rgb(236,146,34)" rx="2" ry="2" />
<text  x="614.17" y="351.5" ></text>
</g>
<g >
<title>net.socket (451 samples, 0.04%)</title><rect x="559.9" y="229" width="0.5" height="15.0" fill="rgb(211,30,7)" rx="2" ry="2" />
<text  x="562.89" y="239.5" ></text>
</g>
<g >
<title>runtime.chanrecv1 (138 samples, 0.01%)</title><rect x="560.4" y="373" width="0.2" height="15.0" fill="rgb(223,85,20)" rx="2" ry="2" />
<text  x="563.42" y="383.5" ></text>
</g>
<g >
<title>plat-swr/pkg/zraft/api.(*RaftService).ExecuteCmd (336 samples, 0.03%)</title><rect x="560.6" y="229" width="0.4" height="15.0" fill="rgb(234,135,32)" rx="2" ry="2" />
<text  x="563.61" y="239.5" ></text>
</g>
<g >
<title>net/http.(*Transport).roundTrip (19,734 samples, 1.97%)</title><rect x="894.3" y="261" width="23.3" height="15.0" fill="rgb(232,126,30)" rx="2" ry="2" />
<text  x="897.33" y="271.5" >n..</text>
</g>
<g >
<title>sync.(*Mutex).Lock (4,051 samples, 0.40%)</title><rect x="889.0" y="325" width="4.8" height="15.0" fill="rgb(231,119,28)" rx="2" ry="2" />
<text  x="892.02" y="335.5" ></text>
</g>
<g >
<title>go.etcd.io/etcd/server/v3/etcdserver/api/rafthttp.startPeer.func2 (45,514 samples, 4.54%)</title><rect x="130.6" y="437" width="53.5" height="15.0" fill="rgb(211,31,7)" rx="2" ry="2" />
<text  x="133.57" y="447.5" >go.et..</text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/ui/proxy/handler.ProxyHandler (664 samples, 0.07%)</title><rect x="281.0" y="341" width="0.8" height="15.0" fill="rgb(243,177,42)" rx="2" ry="2" />
<text  x="284.02" y="351.5" ></text>
</g>
<g >
<title>plat-swr/pkg/zraft/core.(*Raft).Propose (2,747 samples, 0.27%)</title><rect x="281.8" y="245" width="3.2" height="15.0" fill="rgb(222,79,19)" rx="2" ry="2" />
<text  x="284.80" y="255.5" ></text>
</g>
<g >
<title>net/http.(*http2serverConn).readFrames (2,854 samples, 0.28%)</title><rect x="572.6" y="421" width="3.4" height="15.0" fill="rgb(217,57,13)" rx="2" ry="2" />
<text  x="575.59" y="431.5" ></text>
</g>
<g >
<title>plat-swr/pkg/zraft/core.(*FeedbackCollector).HandleFeedback-fm (260 samples, 0.03%)</title><rect x="272.5" y="357" width="0.3" height="15.0" fill="rgb(213,40,9)" rx="2" ry="2" />
<text  x="275.50" y="367.5" ></text>
</g>
<g >
<title>net/http.send (1,521 samples, 0.15%)</title><rect x="847.4" y="165" width="1.8" height="15.0" fill="rgb(254,227,54)" rx="2" ry="2" />
<text  x="850.45" y="175.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/ui/callbacks/impl.SendRegistryReq (1,625 samples, 0.16%)</title><rect x="845.5" y="325" width="1.9" height="15.0" fill="rgb(239,156,37)" rx="2" ry="2" />
<text  x="848.53" y="335.5" ></text>
</g>
<g >
<title>runtime.chanrecv1 (384 samples, 0.04%)</title><rect x="576.0" y="357" width="0.4" height="15.0" fill="rgb(223,85,20)" rx="2" ry="2" />
<text  x="578.95" y="367.5" ></text>
</g>
<g >
<title>runtime.selectgo (14,080 samples, 1.40%)</title><rect x="521.1" y="293" width="16.5" height="15.0" fill="rgb(212,35,8)" rx="2" ry="2" />
<text  x="524.06" y="303.5" ></text>
</g>
<g >
<title>bufio.(*Writer).Flush (480 samples, 0.05%)</title><rect x="571.5" y="325" width="0.5" height="15.0" fill="rgb(246,189,45)" rx="2" ry="2" />
<text  x="574.46" y="335.5" ></text>
</g>
<g >
<title>net/http.(*http2serverConn).writeHeaders (478 samples, 0.05%)</title><rect x="572.0" y="293" width="0.6" height="15.0" fill="rgb(236,145,34)" rx="2" ry="2" />
<text  x="575.03" y="303.5" ></text>
</g>
<g >
<title>net/http.(*Transport).RoundTrip (19,734 samples, 1.97%)</title><rect x="894.3" y="277" width="23.3" height="15.0" fill="rgb(239,157,37)" rx="2" ry="2" />
<text  x="897.33" y="287.5" >n..</text>
</g>
<g >
<title>go.etcd.io/etcd/raft/v3.(*raftLog).term (126 samples, 0.01%)</title><rect x="11.4" y="325" width="0.2" height="15.0" fill="rgb(217,55,13)" rx="2" ry="2" />
<text  x="14.44" y="335.5" ></text>
</g>
<g >
<title>sync.(*Mutex).Lock (684 samples, 0.07%)</title><rect x="1066.9" y="389" width="0.8" height="15.0" fill="rgb(231,119,28)" rx="2" ry="2" />
<text  x="1069.94" y="399.5" ></text>
</g>
<g >
<title>runtime.selectgo (179,521 samples, 17.91%)</title><rect x="632.6" y="229" width="211.4" height="15.0" fill="rgb(212,35,8)" rx="2" ry="2" />
<text  x="635.65" y="239.5" >runtime.selectgo</text>
</g>
<g >
<title>plat-swr/pkg/zraft/core.(*Raft).waitForProposalResult (663 samples, 0.07%)</title><rect x="272.8" y="165" width="0.8" height="15.0" fill="rgb(243,179,42)" rx="2" ry="2" />
<text  x="275.81" y="175.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/infra/httputil.HttpDoRsp (8,036 samples, 0.80%)</title><rect x="611.7" y="277" width="9.4" height="15.0" fill="rgb(242,174,41)" rx="2" ry="2" />
<text  x="614.66" y="287.5" ></text>
</g>
<g >
<title>net/http.(*Transport).roundTrip (1,625 samples, 0.16%)</title><rect x="845.5" y="133" width="1.9" height="15.0" fill="rgb(232,126,30)" rx="2" ry="2" />
<text  x="848.53" y="143.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/ui/proxy/handler.SendBreakPointFinishFlagToRaftCluster (634 samples, 0.06%)</title><rect x="285.0" y="261" width="0.8" height="15.0" fill="rgb(237,150,35)" rx="2" ry="2" />
<text  x="288.04" y="271.5" ></text>
</g>
<g >
<title>k8s.io/klog/v2.(*loggingT).output (95 samples, 0.01%)</title><rect x="272.5" y="245" width="0.1" height="15.0" fill="rgb(229,112,26)" rx="2" ry="2" />
<text  x="275.51" y="255.5" ></text>
</g>
<g >
<title>net/http.(*Transport).roundTrip (770 samples, 0.08%)</title><rect x="10.2" y="405" width="0.9" height="15.0" fill="rgb(232,126,30)" rx="2" ry="2" />
<text  x="13.21" y="415.5" ></text>
</g>
<g >
<title>runtime.selectgo (87 samples, 0.01%)</title><rect x="235.7" y="277" width="0.1" height="15.0" fill="rgb(212,35,8)" rx="2" ry="2" />
<text  x="238.66" y="287.5" ></text>
</g>
<g >
<title>plat-swr/pkg/zraft/api.(*RaftService).ExecuteCmd (663 samples, 0.07%)</title><rect x="272.8" y="229" width="0.8" height="15.0" fill="rgb(234,135,32)" rx="2" ry="2" />
<text  x="275.81" y="239.5" ></text>
</g>
<g >
<title>io.copyBuffer (28,069 samples, 2.80%)</title><rect x="849.2" y="293" width="33.1" height="15.0" fill="rgb(220,71,17)" rx="2" ry="2" />
<text  x="852.24" y="303.5" >io..</text>
</g>
<g >
<title>plat-swr/pkg/zraft/core.(*Raft).proposeMsg (330 samples, 0.03%)</title><rect x="560.6" y="197" width="0.4" height="15.0" fill="rgb(209,22,5)" rx="2" ry="2" />
<text  x="563.62" y="207.5" ></text>
</g>
<g >
<title>runtime.chanrecv1 (138 samples, 0.01%)</title><rect x="281.6" y="229" width="0.2" height="15.0" fill="rgb(223,85,20)" rx="2" ry="2" />
<text  x="284.64" y="239.5" ></text>
</g>
<g >
<title>plat-swr/pkg/zraft/core.(*Raft).proposeMsgWithResult (201 samples, 0.02%)</title><rect x="561.4" y="165" width="0.2" height="15.0" fill="rgb(220,71,17)" rx="2" ry="2" />
<text  x="564.40" y="175.5" ></text>
</g>
<g >
<title>plat-swr/pkg/zraft/etcd-raft.(*raftNode).serveRaft (137 samples, 0.01%)</title><rect x="1104.0" y="421" width="0.2" height="15.0" fill="rgb(237,149,35)" rx="2" ry="2" />
<text  x="1107.03" y="431.5" ></text>
</g>
<g >
<title>runtime.selectgo (16,524 samples, 1.65%)</title><rect x="1047.5" y="405" width="19.4" height="15.0" fill="rgb(212,35,8)" rx="2" ry="2" />
<text  x="1050.48" y="415.5" ></text>
</g>
<g >
<title>runtime.selectgo (55,000 samples, 5.49%)</title><rect x="979.1" y="405" width="64.7" height="15.0" fill="rgb(212,35,8)" rx="2" ry="2" />
<text  x="982.07" y="415.5" >runtime..</text>
</g>
<g >
<title>runtime.chanrecv1 (843 samples, 0.08%)</title><rect x="849.2" y="229" width="1.0" height="15.0" fill="rgb(223,85,20)" rx="2" ry="2" />
<text  x="852.24" y="239.5" ></text>
</g>
<g >
<title>k8s.io/klog/v2.(*loggingT).printf (257 samples, 0.03%)</title><rect x="949.6" y="341" width="0.3" height="15.0" fill="rgb(223,83,19)" rx="2" ry="2" />
<text  x="952.63" y="351.5" ></text>
</g>
<g >
<title>plat-swr/pkg/zraft/core.(*Raft).proposeMsg (633 samples, 0.06%)</title><rect x="285.0" y="181" width="0.8" height="15.0" fill="rgb(209,22,5)" rx="2" ry="2" />
<text  x="288.04" y="191.5" ></text>
</g>
<g >
<title>runtime.chansend1 (170 samples, 0.02%)</title><rect x="560.8" y="165" width="0.2" height="15.0" fill="rgb(212,33,8)" rx="2" ry="2" />
<text  x="563.80" y="175.5" ></text>
</g>
<g >
<title>go.etcd.io/etcd/raft/v3.(*node).stepWithWaitOption (13,152 samples, 1.31%)</title><rect x="1074.2" y="389" width="15.5" height="15.0" fill="rgb(243,174,41)" rx="2" ry="2" />
<text  x="1077.20" y="399.5" ></text>
</g>
<g >
<title>plat-swr/pkg/zraft/core.(*Raft).waitForProposalResult (160 samples, 0.02%)</title><rect x="560.6" y="165" width="0.2" height="15.0" fill="rgb(243,179,42)" rx="2" ry="2" />
<text  x="563.62" y="175.5" ></text>
</g>
<g >
<title>net/http.(*Client).Do (1,521 samples, 0.15%)</title><rect x="847.4" y="213" width="1.8" height="15.0" fill="rgb(237,147,35)" rx="2" ry="2" />
<text  x="850.45" y="223.5" ></text>
</g>
<g >
<title>bytes.(*Reader).WriteTo (188,884 samples, 18.84%)</title><rect x="621.6" y="277" width="222.4" height="15.0" fill="rgb(233,130,31)" rx="2" ry="2" />
<text  x="624.63" y="287.5" >bytes.(*Reader).WriteTo</text>
</g>
<g >
<title>sync.(*Mutex).Lock (3,888 samples, 0.39%)</title><rect x="884.4" y="325" width="4.6" height="15.0" fill="rgb(231,119,28)" rx="2" ry="2" />
<text  x="887.45" y="335.5" ></text>
</g>
<g >
<title>net/http.Handler.ServeHTTP-fm (9,220 samples, 0.92%)</title><rect x="560.6" y="405" width="10.9" height="15.0" fill="rgb(213,40,9)" rx="2" ry="2" />
<text  x="563.61" y="415.5" ></text>
</g>
<g >
<title>runtime.selectgo (7,218 samples, 0.72%)</title><rect x="562.6" y="149" width="8.5" height="15.0" fill="rgb(212,35,8)" rx="2" ry="2" />
<text  x="565.64" y="159.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/ui/callbacks.(*RegistryCallback).CommitRead (8,059 samples, 0.80%)</title><rect x="611.7" y="357" width="9.4" height="15.0" fill="rgb(226,97,23)" rx="2" ry="2" />
<text  x="614.66" y="367.5" ></text>
</g>
<g >
<title>net/http.send (338 samples, 0.03%)</title><rect x="561.0" y="101" width="0.4" height="15.0" fill="rgb(254,227,54)" rx="2" ry="2" />
<text  x="564.01" y="111.5" ></text>
</g>
<g >
<title>net/http.(*Transport).dialConn.gowrap2 (1,047 samples, 0.10%)</title><rect x="286.1" y="437" width="1.2" height="15.0" fill="rgb(251,215,51)" rx="2" ry="2" />
<text  x="289.10" y="447.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/ui/proxy/handler.SendPostUploadMsg (336 samples, 0.03%)</title><rect x="560.6" y="261" width="0.4" height="15.0" fill="rgb(218,63,15)" rx="2" ry="2" />
<text  x="563.61" y="271.5" ></text>
</g>
<g >
<title>runtime.selectgo (27,226 samples, 2.72%)</title><rect x="850.2" y="229" width="32.1" height="15.0" fill="rgb(212,35,8)" rx="2" ry="2" />
<text  x="853.24" y="239.5" >ru..</text>
</g>
<g >
<title>sync.(*Mutex).Lock (401 samples, 0.04%)</title><rect x="621.2" y="293" width="0.4" height="15.0" fill="rgb(231,119,28)" rx="2" ry="2" />
<text  x="624.15" y="303.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/ui/proxy/handler.Handlers (2,749 samples, 0.27%)</title><rect x="281.8" y="309" width="3.2" height="15.0" fill="rgb(227,105,25)" rx="2" ry="2" />
<text  x="284.80" y="319.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/ui/proxy/handler.PutManifestOrBlob (902 samples, 0.09%)</title><rect x="285.0" y="325" width="1.1" height="15.0" fill="rgb(246,189,45)" rx="2" ry="2" />
<text  x="288.04" y="335.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/ui/callbacks/impl.(*Stream).GetResult (1,833 samples, 0.18%)</title><rect x="882.3" y="309" width="2.1" height="15.0" fill="rgb(247,195,46)" rx="2" ry="2" />
<text  x="885.29" y="319.5" ></text>
</g>
<g >
<title>net/http.(*Client).Do (3,615 samples, 0.36%)</title><rect x="276.7" y="197" width="4.3" height="15.0" fill="rgb(237,147,35)" rx="2" ry="2" />
<text  x="279.72" y="207.5" ></text>
</g>
<g >
<title>net/http.(*http2requestBody).Read (7,861 samples, 0.78%)</title><rect x="561.9" y="181" width="9.2" height="15.0" fill="rgb(252,218,52)" rx="2" ry="2" />
<text  x="564.88" y="191.5" ></text>
</g>
<g >
<title>runtime.deferreturn (958 samples, 0.10%)</title><rect x="571.5" y="405" width="1.1" height="15.0" fill="rgb(242,170,40)" rx="2" ry="2" />
<text  x="574.46" y="415.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/ui/proxy/handler.PostUploadHandler (674 samples, 0.07%)</title><rect x="560.6" y="277" width="0.8" height="15.0" fill="rgb(234,136,32)" rx="2" ry="2" />
<text  x="563.61" y="287.5" ></text>
</g>
<g >
<title>runtime.selectgo (2,854 samples, 0.28%)</title><rect x="572.6" y="405" width="3.4" height="15.0" fill="rgb(212,35,8)" rx="2" ry="2" />
<text  x="575.59" y="415.5" ></text>
</g>
<g >
<title>plat-swr/pkg/zraft/core.(*Raft).waitForProposalResult (2,698 samples, 0.27%)</title><rect x="281.8" y="197" width="3.2" height="15.0" fill="rgb(243,179,42)" rx="2" ry="2" />
<text  x="284.82" y="207.5" ></text>
</g>
<g >
<title>net/http.(*bodyEOFSignal).condfn (138 samples, 0.01%)</title><rect x="281.6" y="261" width="0.2" height="15.0" fill="rgb(231,120,28)" rx="2" ry="2" />
<text  x="284.64" y="271.5" ></text>
</g>
<g >
<title>go.etcd.io/etcd/server/v3/wal.newFilePipeline.gowrap1 (106 samples, 0.01%)</title><rect x="235.4" y="437" width="0.2" height="15.0" fill="rgb(213,40,9)" rx="2" ry="2" />
<text  x="238.45" y="447.5" ></text>
</g>
<g >
<title>plat-swr/pkg/zraft/core.(*FeedbackCollector).checkConsensus (225 samples, 0.02%)</title><rect x="272.5" y="309" width="0.3" height="15.0" fill="rgb(207,9,2)" rx="2" ry="2" />
<text  x="275.51" y="319.5" ></text>
</g>
<g >
<title>net/http.(*Server).ServeTLS (137 samples, 0.01%)</title><rect x="1104.0" y="405" width="0.2" height="15.0" fill="rgb(228,110,26)" rx="2" ry="2" />
<text  x="1107.03" y="415.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/ui/proxy/handler.processDataChunk (8,129 samples, 0.81%)</title><rect x="561.9" y="197" width="9.5" height="15.0" fill="rgb(246,192,45)" rx="2" ry="2" />
<text  x="564.88" y="207.5" ></text>
</g>
<g >
<title>plat-swr/pkg/zraft/core.(*Raft).Start.gowrap2 (20,316 samples, 2.03%)</title><rect x="587.3" y="437" width="23.9" height="15.0" fill="rgb(222,81,19)" rx="2" ry="2" />
<text  x="590.26" y="447.5" >p..</text>
</g>
<g >
<title>plat-swr/pkg/zraft/core.(*Exec).processWriteMsg (261,815 samples, 26.12%)</title><rect x="621.2" y="373" width="308.2" height="15.0" fill="rgb(241,166,39)" rx="2" ry="2" />
<text  x="624.15" y="383.5" >plat-swr/pkg/zraft/core.(*Exec).processWr..</text>
</g>
<g >
<title>all (1,002,387 samples, 100%)</title><rect x="10.0" y="453" width="1180.0" height="15.0" fill="rgb(213,39,9)" rx="2" ry="2" />
<text  x="13.00" y="463.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/ui/proxy/handler.Handlers (268 samples, 0.03%)</title><rect x="285.8" y="309" width="0.3" height="15.0" fill="rgb(227,105,25)" rx="2" ry="2" />
<text  x="288.79" y="319.5" ></text>
</g>
<g >
<title>k8s.io/klog/v2.(*loggingT).printf (353 samples, 0.04%)</title><rect x="1043.8" y="373" width="0.4" height="15.0" fill="rgb(223,83,19)" rx="2" ry="2" />
<text  x="1046.81" y="383.5" ></text>
</g>
<g >
<title>runtime.selectgo (3,019 samples, 0.30%)</title><rect x="576.4" y="181" width="3.6" height="15.0" fill="rgb(212,35,8)" rx="2" ry="2" />
<text  x="579.40" y="191.5" ></text>
</g>
<g >
<title>runtime.chanrecv1 (9,363 samples, 0.93%)</title><rect x="621.6" y="229" width="11.0" height="15.0" fill="rgb(223,85,20)" rx="2" ry="2" />
<text  x="624.63" y="239.5" ></text>
</g>
<g >
<title>net/http.(*persistConn).roundTrip (526 samples, 0.05%)</title><rect x="281.0" y="277" width="0.6" height="15.0" fill="rgb(221,74,17)" rx="2" ry="2" />
<text  x="284.02" y="287.5" ></text>
</g>
<g >
<title>plat-swr/pkg/zraft/core.(*Raft).proposeMsgWithResult (663 samples, 0.07%)</title><rect x="272.8" y="181" width="0.8" height="15.0" fill="rgb(220,71,17)" rx="2" ry="2" />
<text  x="275.81" y="191.5" ></text>
</g>
<g >
<title>k8s.io/klog/v2.Infof (353 samples, 0.04%)</title><rect x="1043.8" y="389" width="0.4" height="15.0" fill="rgb(247,194,46)" rx="2" ry="2" />
<text  x="1046.81" y="399.5" ></text>
</g>
<g >
<title>plat-swr/pkg/zraft/core.(*Raft).Propose (336 samples, 0.03%)</title><rect x="560.6" y="213" width="0.4" height="15.0" fill="rgb(222,79,19)" rx="2" ry="2" />
<text  x="563.61" y="223.5" ></text>
</g>
<g >
<title>plat-swr/pkg/zraft/core.(*Raft).proposeMsgWithResult (1,224 samples, 0.12%)</title><rect x="275.3" y="229" width="1.4" height="15.0" fill="rgb(220,71,17)" rx="2" ry="2" />
<text  x="278.28" y="239.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/infra/httputil.httpDoRsp.func1 (338 samples, 0.03%)</title><rect x="561.0" y="165" width="0.4" height="15.0" fill="rgb(207,12,2)" rx="2" ry="2" />
<text  x="564.01" y="175.5" ></text>
</g>
<g >
<title>sync.(*Cond).Wait (623 samples, 0.06%)</title><rect x="561.9" y="149" width="0.7" height="15.0" fill="rgb(227,102,24)" rx="2" ry="2" />
<text  x="564.88" y="159.5" ></text>
</g>
<g >
<title>net/http.(*http2responseWriterState).writeChunk (478 samples, 0.05%)</title><rect x="572.0" y="309" width="0.6" height="15.0" fill="rgb(243,174,41)" rx="2" ry="2" />
<text  x="575.03" y="319.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/ui/proxy/handler.ExecRaftCmd (201 samples, 0.02%)</title><rect x="561.4" y="229" width="0.2" height="15.0" fill="rgb(245,184,44)" rx="2" ry="2" />
<text  x="564.40" y="239.5" ></text>
</g>
<g >
<title>net/http.serverHandler.ServeHTTP (11,553 samples, 1.15%)</title><rect x="272.5" y="405" width="13.6" height="15.0" fill="rgb(234,135,32)" rx="2" ry="2" />
<text  x="275.50" y="415.5" ></text>
</g>
<g >
<title>crypto/tls.(*Conn).handshakeContext.func1 (384 samples, 0.04%)</title><rect x="576.0" y="373" width="0.4" height="15.0" fill="rgb(207,10,2)" rx="2" ry="2" />
<text  x="578.95" y="383.5" ></text>
</g>
<g >
<title>net.(*sysDialer).doDialTCPProto (451 samples, 0.04%)</title><rect x="559.9" y="261" width="0.5" height="15.0" fill="rgb(230,116,27)" rx="2" ry="2" />
<text  x="562.89" y="271.5" ></text>
</g>
<g >
<title>io.(*PipeWriter).Write (188,884 samples, 18.84%)</title><rect x="621.6" y="261" width="222.4" height="15.0" fill="rgb(214,45,10)" rx="2" ry="2" />
<text  x="624.63" y="271.5" >io.(*PipeWriter).Write</text>
</g>
<g >
<title>runtime.selectgo (2,359 samples, 0.24%)</title><rect x="278.2" y="85" width="2.8" height="15.0" fill="rgb(212,35,8)" rx="2" ry="2" />
<text  x="281.19" y="95.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/infra/httputil.HttpDoData (1,625 samples, 0.16%)</title><rect x="845.5" y="293" width="1.9" height="15.0" fill="rgb(247,194,46)" rx="2" ry="2" />
<text  x="848.53" y="303.5" ></text>
</g>
<g >
<title>runtime.chanrecv1 (1,465 samples, 0.15%)</title><rect x="1188.3" y="405" width="1.7" height="15.0" fill="rgb(223,85,20)" rx="2" ry="2" />
<text  x="1191.28" y="415.5" ></text>
</g>
<g >
<title>plat-swr/pkg/zraft/core.(*Raft).proposeMsgWithResult (1,404 samples, 0.14%)</title><rect x="273.6" y="149" width="1.6" height="15.0" fill="rgb(220,71,17)" rx="2" ry="2" />
<text  x="276.59" y="159.5" ></text>
</g>
<g >
<title>net.(*netFD).connect.func2 (171 samples, 0.02%)</title><rect x="235.8" y="437" width="0.2" height="15.0" fill="rgb(254,227,54)" rx="2" ry="2" />
<text  x="238.77" y="447.5" ></text>
</g>
<g >
<title>sync.(*Mutex).Lock (190 samples, 0.02%)</title><rect x="281.8" y="117" width="0.2" height="15.0" fill="rgb(231,119,28)" rx="2" ry="2" />
<text  x="284.82" y="127.5" ></text>
</g>
<g >
<title>net/http.initALPNRequest.ServeHTTP (9,220 samples, 0.92%)</title><rect x="560.6" y="373" width="10.9" height="15.0" fill="rgb(241,168,40)" rx="2" ry="2" />
<text  x="563.61" y="383.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/ui/callbacks/impl.(*PutUpload).CommitWrite (29,903 samples, 2.98%)</title><rect x="849.2" y="341" width="35.2" height="15.0" fill="rgb(244,182,43)" rx="2" ry="2" />
<text  x="852.24" y="351.5" >pl..</text>
</g>
<g >
<title>plat-swr/pkg/zraft/core.(*AppWAL).Delete (3,888 samples, 0.39%)</title><rect x="884.4" y="357" width="4.6" height="15.0" fill="rgb(209,21,5)" rx="2" ry="2" />
<text  x="887.45" y="367.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/infra/httputil.httpDoRsp (3,615 samples, 0.36%)</title><rect x="276.7" y="245" width="4.3" height="15.0" fill="rgb(236,143,34)" rx="2" ry="2" />
<text  x="279.72" y="255.5" ></text>
</g>
<g >
<title>runtime.selectgo (15,398 samples, 1.54%)</title><rect x="929.4" y="405" width="18.1" height="15.0" fill="rgb(212,35,8)" rx="2" ry="2" />
<text  x="932.36" y="415.5" ></text>
</g>
<g >
<title>io.(*pipe).write (188,884 samples, 18.84%)</title><rect x="621.6" y="245" width="222.4" height="15.0" fill="rgb(216,52,12)" rx="2" ry="2" />
<text  x="624.63" y="255.5" >io.(*pipe).write</text>
</g>
<g >
<title>github.com/labstack/echo/v4/middleware.RecoverWithConfig.func1.1 (11,292 samples, 1.13%)</title><rect x="272.8" y="373" width="13.3" height="15.0" fill="rgb(239,156,37)" rx="2" ry="2" />
<text  x="275.81" y="383.5" ></text>
</g>
<g >
<title>runtime.selectgo (138 samples, 0.01%)</title><rect x="10.0" y="421" width="0.2" height="15.0" fill="rgb(212,35,8)" rx="2" ry="2" />
<text  x="13.00" y="431.5" ></text>
</g>
<g >
<title>go.etcd.io/etcd/raft/v3.(*node).Propose (13,152 samples, 1.31%)</title><rect x="1074.2" y="421" width="15.5" height="15.0" fill="rgb(221,77,18)" rx="2" ry="2" />
<text  x="1077.20" y="431.5" ></text>
</g>
<g >
<title>plat-swr/pkg/zraft/core.(*Raft).Propose (268 samples, 0.03%)</title><rect x="571.1" y="117" width="0.3" height="15.0" fill="rgb(222,79,19)" rx="2" ry="2" />
<text  x="574.13" y="127.5" ></text>
</g>
<g >
<title>runtime.selectgo (632 samples, 0.06%)</title><rect x="10.4" y="373" width="0.7" height="15.0" fill="rgb(212,35,8)" rx="2" ry="2" />
<text  x="13.37" y="383.5" ></text>
</g>
<g >
<title>plat-swr/pkg/zraft/core.(*Raft).NotifyProposalResult (121 samples, 0.01%)</title><rect x="929.2" y="357" width="0.2" height="15.0" fill="rgb(233,131,31)" rx="2" ry="2" />
<text  x="932.21" y="367.5" ></text>
</g>
<g >
<title>net/http.send (160 samples, 0.02%)</title><rect x="235.6" y="341" width="0.2" height="15.0" fill="rgb(254,227,54)" rx="2" ry="2" />
<text  x="238.57" y="351.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/infra/httputil.retryDo (1,521 samples, 0.15%)</title><rect x="847.4" y="245" width="1.8" height="15.0" fill="rgb(220,69,16)" rx="2" ry="2" />
<text  x="850.45" y="255.5" ></text>
</g>
<g >
<title>k8s.io/klog/v2.(*loggingT).printfDepth (190 samples, 0.02%)</title><rect x="281.8" y="149" width="0.2" height="15.0" fill="rgb(236,146,34)" rx="2" ry="2" />
<text  x="284.82" y="159.5" ></text>
</g>
<g >
<title>net.(*sysDialer).dialTCP (451 samples, 0.04%)</title><rect x="559.9" y="293" width="0.5" height="15.0" fill="rgb(223,84,20)" rx="2" ry="2" />
<text  x="562.89" y="303.5" ></text>
</g>
<g >
<title>go.etcd.io/etcd/raft/v3.(*MemoryStorage).FirstIndex (126 samples, 0.01%)</title><rect x="11.4" y="293" width="0.2" height="15.0" fill="rgb(216,53,12)" rx="2" ry="2" />
<text  x="14.44" y="303.5" ></text>
</g>
<g >
<title>net/http.http2ConfigureServer.func1 (14,950 samples, 1.49%)</title><rect x="254.9" y="389" width="17.6" height="15.0" fill="rgb(226,100,24)" rx="2" ry="2" />
<text  x="257.90" y="399.5" ></text>
</g>
<g >
<title>sync.(*Mutex).Lock (200 samples, 0.02%)</title><rect x="949.4" y="293" width="0.2" height="15.0" fill="rgb(231,119,28)" rx="2" ry="2" />
<text  x="952.39" y="303.5" ></text>
</g>
<g >
<title>net/http.(*persistConn).roundTrip (5,313 samples, 0.53%)</title><rect x="614.9" y="117" width="6.2" height="15.0" fill="rgb(221,74,17)" rx="2" ry="2" />
<text  x="617.86" y="127.5" ></text>
</g>
<g >
<title>runtime.selectgo (5,313 samples, 0.53%)</title><rect x="614.9" y="101" width="6.2" height="15.0" fill="rgb(212,35,8)" rx="2" ry="2" />
<text  x="617.86" y="111.5" ></text>
</g>
<g >
<title>k8s.io/klog/v2.(*loggingT).printf (331 samples, 0.03%)</title><rect x="893.8" y="325" width="0.4" height="15.0" fill="rgb(223,83,19)" rx="2" ry="2" />
<text  x="896.79" y="335.5" ></text>
</g>
<g >
<title>k8s.io/klog/v2.(*loggingT).output (401 samples, 0.04%)</title><rect x="621.2" y="309" width="0.4" height="15.0" fill="rgb(229,112,26)" rx="2" ry="2" />
<text  x="624.15" y="319.5" ></text>
</g>
<g >
<title>k8s.io/klog/v2.(*loggingT).printfDepth (122 samples, 0.01%)</title><rect x="894.2" y="309" width="0.1" height="15.0" fill="rgb(236,146,34)" rx="2" ry="2" />
<text  x="897.18" y="319.5" ></text>
</g>
<g >
<title>net/http.(*Transport).dialConn (589 samples, 0.06%)</title><rect x="559.9" y="405" width="0.7" height="15.0" fill="rgb(209,21,5)" rx="2" ry="2" />
<text  x="562.89" y="415.5" ></text>
</g>
<g >
<title>net/http.(*Client).do (1,625 samples, 0.16%)</title><rect x="845.5" y="197" width="1.9" height="15.0" fill="rgb(230,117,28)" rx="2" ry="2" />
<text  x="848.53" y="207.5" ></text>
</g>
<g >
<title>net.(*netFD).dial (451 samples, 0.04%)</title><rect x="559.9" y="213" width="0.5" height="15.0" fill="rgb(209,22,5)" rx="2" ry="2" />
<text  x="562.89" y="223.5" ></text>
</g>
<g >
<title>net/http.(*response).finishRequest (16,081 samples, 1.60%)</title><rect x="236.0" y="405" width="18.9" height="15.0" fill="rgb(233,128,30)" rx="2" ry="2" />
<text  x="238.97" y="415.5" ></text>
</g>
<g >
<title>net/http.(*Transport).getConn (536 samples, 0.05%)</title><rect x="845.5" y="117" width="0.7" height="15.0" fill="rgb(240,165,39)" rx="2" ry="2" />
<text  x="848.53" y="127.5" ></text>
</g>
<g >
<title>k8s.io/klog/v2.(*loggingT).printf (392 samples, 0.04%)</title><rect x="611.2" y="357" width="0.4" height="15.0" fill="rgb(223,83,19)" rx="2" ry="2" />
<text  x="614.17" y="367.5" ></text>
</g>
<g >
<title>net/http.send (8,036 samples, 0.80%)</title><rect x="611.7" y="165" width="9.4" height="15.0" fill="rgb(254,227,54)" rx="2" ry="2" />
<text  x="614.66" y="175.5" ></text>
</g>
<g >
<title>plat-swr/pkg/zraft/core.(*Raft).proposeMsgWithResult (196 samples, 0.02%)</title><rect x="561.6" y="117" width="0.3" height="15.0" fill="rgb(220,71,17)" rx="2" ry="2" />
<text  x="564.64" y="127.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/ui/proxy/handler.SendRaftClusterCmd (201 samples, 0.02%)</title><rect x="561.4" y="245" width="0.2" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="564.40" y="255.5" ></text>
</g>
<g >
<title>net/http.(*Transport).roundTrip (1,521 samples, 0.15%)</title><rect x="847.4" y="133" width="1.8" height="15.0" fill="rgb(232,126,30)" rx="2" ry="2" />
<text  x="850.45" y="143.5" ></text>
</g>
<g >
<title>go.etcd.io/etcd/server/v3/wal.(*filePipeline).run (106 samples, 0.01%)</title><rect x="235.4" y="421" width="0.2" height="15.0" fill="rgb(245,187,44)" rx="2" ry="2" />
<text  x="238.45" y="431.5" ></text>
</g>
<g >
<title>plat-swr/pkg/zraft/core.(*Raft).Propose (268 samples, 0.03%)</title><rect x="285.8" y="245" width="0.3" height="15.0" fill="rgb(222,79,19)" rx="2" ry="2" />
<text  x="288.79" y="255.5" ></text>
</g>
<g >
<title>net/http.(*Client).send (3,615 samples, 0.36%)</title><rect x="276.7" y="165" width="4.3" height="15.0" fill="rgb(241,168,40)" rx="2" ry="2" />
<text  x="279.72" y="175.5" ></text>
</g>
<g >
<title>net/http.(*Transport).dialConnFor (589 samples, 0.06%)</title><rect x="559.9" y="421" width="0.7" height="15.0" fill="rgb(253,225,53)" rx="2" ry="2" />
<text  x="562.89" y="431.5" ></text>
</g>
<g >
<title>k8s.io/klog/v2.(*loggingT).output (296 samples, 0.03%)</title><rect x="949.0" y="325" width="0.4" height="15.0" fill="rgb(229,112,26)" rx="2" ry="2" />
<text  x="952.04" y="335.5" ></text>
</g>
<g >
<title>sync.(*Cond).Wait (16,081 samples, 1.60%)</title><rect x="236.0" y="373" width="18.9" height="15.0" fill="rgb(227,102,24)" rx="2" ry="2" />
<text  x="238.97" y="383.5" ></text>
</g>
<g >
<title>sync.(*Mutex).Lock (2,114 samples, 0.21%)</title><rect x="1044.2" y="357" width="2.5" height="15.0" fill="rgb(231,119,28)" rx="2" ry="2" />
<text  x="1047.23" y="367.5" ></text>
</g>
<g >
<title>plat-swr/pkg/zraft/core.(*Raft).proposeMsg (1,224 samples, 0.12%)</title><rect x="275.3" y="245" width="1.4" height="15.0" fill="rgb(209,22,5)" rx="2" ry="2" />
<text  x="278.28" y="255.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/ui/proxy/handler.ExecRaftCmd (336 samples, 0.03%)</title><rect x="560.6" y="245" width="0.4" height="15.0" fill="rgb(245,184,44)" rx="2" ry="2" />
<text  x="563.61" y="255.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/ui/proxy/handler.SendRaftClusterCmdBreakPoint (8,332 samples, 0.83%)</title><rect x="561.6" y="245" width="9.8" height="15.0" fill="rgb(240,162,38)" rx="2" ry="2" />
<text  x="564.64" y="255.5" ></text>
</g>
<g >
<title>runtime.selectgo (1,395 samples, 0.14%)</title><rect x="273.6" y="117" width="1.6" height="15.0" fill="rgb(212,35,8)" rx="2" ry="2" />
<text  x="276.60" y="127.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/infra/httputil.HttpDo (338 samples, 0.03%)</title><rect x="561.0" y="245" width="0.4" height="15.0" fill="rgb(239,156,37)" rx="2" ry="2" />
<text  x="564.01" y="255.5" ></text>
</g>
<g >
<title>net/http.(*connReader).abortPendingRead (16,081 samples, 1.60%)</title><rect x="236.0" y="389" width="18.9" height="15.0" fill="rgb(245,188,45)" rx="2" ry="2" />
<text  x="238.97" y="399.5" ></text>
</g>
<g >
<title>plat-swr/pkg/zraft/core.(*Raft).Propose (1,248 samples, 0.12%)</title><rect x="275.2" y="261" width="1.5" height="15.0" fill="rgb(222,79,19)" rx="2" ry="2" />
<text  x="278.25" y="271.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/ui/proxy/handler.PostUploadHandler (4,905 samples, 0.49%)</title><rect x="275.2" y="325" width="5.8" height="15.0" fill="rgb(234,136,32)" rx="2" ry="2" />
<text  x="278.25" y="335.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/infra/httputil.HttpDoRsp (338 samples, 0.03%)</title><rect x="561.0" y="213" width="0.4" height="15.0" fill="rgb(242,174,41)" rx="2" ry="2" />
<text  x="564.01" y="223.5" ></text>
</g>
<g >
<title>plat-swr/pkg/zraft/core.(*Raft).proposeMsg (264 samples, 0.03%)</title><rect x="571.1" y="101" width="0.3" height="15.0" fill="rgb(209,22,5)" rx="2" ry="2" />
<text  x="574.14" y="111.5" ></text>
</g>
<g >
<title>runtime.chanrecv2 (20,316 samples, 2.03%)</title><rect x="587.3" y="405" width="23.9" height="15.0" fill="rgb(217,55,13)" rx="2" ry="2" />
<text  x="590.26" y="415.5" >r..</text>
</g>
<g >
<title>net/http.(*persistConn).readLoop (1,047 samples, 0.10%)</title><rect x="286.1" y="421" width="1.2" height="15.0" fill="rgb(207,12,3)" rx="2" ry="2" />
<text  x="289.10" y="431.5" ></text>
</g>
<g >
<title>k8s.io/klog/v2.(*loggingT).printf (1,325 samples, 0.13%)</title><rect x="947.5" y="373" width="1.5" height="15.0" fill="rgb(223,83,19)" rx="2" ry="2" />
<text  x="950.48" y="383.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/ui/proxy/handler.ExecRaftCmd (268 samples, 0.03%)</title><rect x="285.8" y="277" width="0.3" height="15.0" fill="rgb(245,184,44)" rx="2" ry="2" />
<text  x="288.79" y="287.5" ></text>
</g>
<g >
<title>plat-swr/pkg/zraft/core.(*Raft).waitForProposalResult (1,396 samples, 0.14%)</title><rect x="273.6" y="133" width="1.6" height="15.0" fill="rgb(243,179,42)" rx="2" ry="2" />
<text  x="276.59" y="143.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/ui/proxy/handler.ExecRaftCmd (663 samples, 0.07%)</title><rect x="272.8" y="245" width="0.8" height="15.0" fill="rgb(245,184,44)" rx="2" ry="2" />
<text  x="275.81" y="255.5" ></text>
</g>
<g >
<title>plat-swr/pkg/zraft/etcd-raft.(*raftNode).serveChannels.func1 (25,347 samples, 2.53%)</title><rect x="1074.2" y="437" width="29.8" height="15.0" fill="rgb(228,106,25)" rx="2" ry="2" />
<text  x="1077.20" y="447.5" >pl..</text>
</g>
<g >
<title>runtime.selectgo (9,902 samples, 0.99%)</title><rect x="917.6" y="341" width="11.6" height="15.0" fill="rgb(212,35,8)" rx="2" ry="2" />
<text  x="920.56" y="351.5" ></text>
</g>
<g >
<title>runtime.selectgo (316 samples, 0.03%)</title><rect x="561.0" y="37" width="0.4" height="15.0" fill="rgb(212,35,8)" rx="2" ry="2" />
<text  x="564.03" y="47.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/infra/httputil.httpDoRsp (9,159 samples, 0.91%)</title><rect x="576.4" y="341" width="10.8" height="15.0" fill="rgb(236,143,34)" rx="2" ry="2" />
<text  x="579.40" y="351.5" ></text>
</g>
<g >
<title>net/http.(*Client).Do (338 samples, 0.03%)</title><rect x="561.0" y="149" width="0.4" height="15.0" fill="rgb(237,147,35)" rx="2" ry="2" />
<text  x="564.01" y="159.5" ></text>
</g>
<g >
<title>plat-swr/pkg/zraft/core.(*Receiver).runCmdCache (6,168 samples, 0.62%)</title><rect x="1066.9" y="421" width="7.3" height="15.0" fill="rgb(230,117,28)" rx="2" ry="2" />
<text  x="1069.94" y="431.5" ></text>
</g>
<g >
<title>go.etcd.io/etcd/raft/v3.(*raft).handleAppendEntries (126 samples, 0.01%)</title><rect x="11.4" y="373" width="0.2" height="15.0" fill="rgb(236,142,34)" rx="2" ry="2" />
<text  x="14.44" y="383.5" ></text>
</g>
<g >
<title>go.etcd.io/etcd/raft/v3.(*raftLog).matchTerm (126 samples, 0.01%)</title><rect x="11.4" y="341" width="0.2" height="15.0" fill="rgb(232,126,30)" rx="2" ry="2" />
<text  x="14.44" y="351.5" ></text>
</g>
<g >
<title>net/http.http2ConfigureServer.func2 (14,950 samples, 1.49%)</title><rect x="254.9" y="405" width="17.6" height="15.0" fill="rgb(220,71,17)" rx="2" ry="2" />
<text  x="257.90" y="415.5" ></text>
</g>
<g >
<title>net/http.(*Transport).RoundTrip (526 samples, 0.05%)</title><rect x="281.0" y="309" width="0.6" height="15.0" fill="rgb(239,157,37)" rx="2" ry="2" />
<text  x="284.02" y="319.5" ></text>
</g>
<g >
<title>go.etcd.io/etcd/raft/v3.(*node).Step (11,902 samples, 1.19%)</title><rect x="130.6" y="405" width="14.0" height="15.0" fill="rgb(216,53,12)" rx="2" ry="2" />
<text  x="133.57" y="415.5" ></text>
</g>
<g >
<title>runtime.selectgo (24,720 samples, 2.47%)</title><rect x="949.9" y="405" width="29.1" height="15.0" fill="rgb(212,35,8)" rx="2" ry="2" />
<text  x="952.93" y="415.5" >ru..</text>
</g>
<g >
<title>net/http.(*Client).Do (8,036 samples, 0.80%)</title><rect x="611.7" y="213" width="9.4" height="15.0" fill="rgb(237,147,35)" rx="2" ry="2" />
<text  x="614.66" y="223.5" ></text>
</g>
<g >
<title>net.(*sysDialer).dialSingle (451 samples, 0.04%)</title><rect x="559.9" y="309" width="0.5" height="15.0" fill="rgb(225,92,22)" rx="2" ry="2" />
<text  x="562.89" y="319.5" ></text>
</g>
<g >
<title>plat-swr/pkg/zraft/core.(*Raft).Propose (634 samples, 0.06%)</title><rect x="285.0" y="197" width="0.8" height="15.0" fill="rgb(222,79,19)" rx="2" ry="2" />
<text  x="288.04" y="207.5" ></text>
</g>
<g >
<title>net/http.(*Transport).roundTrip (526 samples, 0.05%)</title><rect x="281.0" y="293" width="0.6" height="15.0" fill="rgb(232,126,30)" rx="2" ry="2" />
<text  x="284.02" y="303.5" ></text>
</g>
<g >
<title>go.etcd.io/etcd/raft/v3.(*node).stepWithWaitOption (11,902 samples, 1.19%)</title><rect x="130.6" y="373" width="14.0" height="15.0" fill="rgb(243,174,41)" rx="2" ry="2" />
<text  x="133.57" y="383.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/ui/proxy/handler.sendChunkData (1,409 samples, 0.14%)</title><rect x="273.6" y="245" width="1.6" height="15.0" fill="rgb(249,205,49)" rx="2" ry="2" />
<text  x="276.59" y="255.5" ></text>
</g>
<g >
<title>runtime.chanrecv1 (451 samples, 0.04%)</title><rect x="559.9" y="149" width="0.5" height="15.0" fill="rgb(223,85,20)" rx="2" ry="2" />
<text  x="562.89" y="159.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/ui/proxy/handler.ExecRaftCmd (203 samples, 0.02%)</title><rect x="561.6" y="181" width="0.3" height="15.0" fill="rgb(245,184,44)" rx="2" ry="2" />
<text  x="564.64" y="191.5" ></text>
</g>
<g >
<title>k8s.io/klog/v2.(*loggingT).output (200 samples, 0.02%)</title><rect x="949.4" y="309" width="0.2" height="15.0" fill="rgb(229,112,26)" rx="2" ry="2" />
<text  x="952.39" y="319.5" ></text>
</g>
<g >
<title>plat-swr/pkg/zraft/core.(*Raft).proposeMsg (663 samples, 0.07%)</title><rect x="272.8" y="197" width="0.8" height="15.0" fill="rgb(209,22,5)" rx="2" ry="2" />
<text  x="275.81" y="207.5" ></text>
</g>
<g >
<title>net/http.(*Client).do (160 samples, 0.02%)</title><rect x="235.6" y="373" width="0.2" height="15.0" fill="rgb(230,117,28)" rx="2" ry="2" />
<text  x="238.57" y="383.5" ></text>
</g>
<g >
<title>plat-swr/pkg/zraft/api.(*RaftService).ExecuteCmd (634 samples, 0.06%)</title><rect x="285.0" y="213" width="0.8" height="15.0" fill="rgb(234,135,32)" rx="2" ry="2" />
<text  x="288.04" y="223.5" ></text>
</g>
<g >
<title>net/http.(*persistConn).roundTrip (1,089 samples, 0.11%)</title><rect x="846.2" y="117" width="1.2" height="15.0" fill="rgb(221,74,17)" rx="2" ry="2" />
<text  x="849.16" y="127.5" ></text>
</g>
<g >
<title>runtime.deferreturn (384 samples, 0.04%)</title><rect x="576.0" y="389" width="0.4" height="15.0" fill="rgb(242,170,40)" rx="2" ry="2" />
<text  x="578.95" y="399.5" ></text>
</g>
<g >
<title>net/http.(*Client).send (9,159 samples, 0.91%)</title><rect x="576.4" y="261" width="10.8" height="15.0" fill="rgb(241,168,40)" rx="2" ry="2" />
<text  x="579.40" y="271.5" ></text>
</g>
<g >
<title>net/http/httputil.(*ReverseProxy).copyResponse (138 samples, 0.01%)</title><rect x="281.6" y="309" width="0.2" height="15.0" fill="rgb(246,192,46)" rx="2" ry="2" />
<text  x="284.64" y="319.5" ></text>
</g>
<g >
<title>net.(*netFD).connect (451 samples, 0.04%)</title><rect x="559.9" y="197" width="0.5" height="15.0" fill="rgb(239,158,37)" rx="2" ry="2" />
<text  x="562.89" y="207.5" ></text>
</g>
<g >
<title>k8s.io/klog/v2.(*loggingT).printfDepth (121 samples, 0.01%)</title><rect x="929.2" y="309" width="0.2" height="15.0" fill="rgb(236,146,34)" rx="2" ry="2" />
<text  x="932.21" y="319.5" ></text>
</g>
<g >
<title>net/http.(*Transport).RoundTrip (338 samples, 0.03%)</title><rect x="561.0" y="85" width="0.4" height="15.0" fill="rgb(239,157,37)" rx="2" ry="2" />
<text  x="564.01" y="95.5" ></text>
</g>
<g >
<title>k8s.io/klog/v2.(*loggingT).printf (200 samples, 0.02%)</title><rect x="949.4" y="341" width="0.2" height="15.0" fill="rgb(223,83,19)" rx="2" ry="2" />
<text  x="952.39" y="351.5" ></text>
</g>
<g >
<title>net/http.send (1,625 samples, 0.16%)</title><rect x="845.5" y="165" width="1.9" height="15.0" fill="rgb(254,227,54)" rx="2" ry="2" />
<text  x="848.53" y="175.5" ></text>
</g>
<g >
<title>runtime.selectgo (43,581 samples, 4.35%)</title><rect x="184.1" y="405" width="51.3" height="15.0" fill="rgb(212,35,8)" rx="2" ry="2" />
<text  x="187.15" y="415.5" >runti..</text>
</g>
<g >
<title>plat-swr/pkg/zraft/api.(*RaftService).ExecuteCmd (268 samples, 0.03%)</title><rect x="571.1" y="133" width="0.3" height="15.0" fill="rgb(234,135,32)" rx="2" ry="2" />
<text  x="574.13" y="143.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/infra/httputil.httpDoRsp.func1 (8,036 samples, 0.80%)</title><rect x="611.7" y="229" width="9.4" height="15.0" fill="rgb(207,12,2)" rx="2" ry="2" />
<text  x="614.66" y="239.5" ></text>
</g>
<g >
<title>plat-swr/pkg/zraft/api.(*RaftService).ExecuteCmd (2,747 samples, 0.27%)</title><rect x="281.8" y="261" width="3.2" height="15.0" fill="rgb(234,135,32)" rx="2" ry="2" />
<text  x="284.80" y="271.5" ></text>
</g>
<g >
<title>github.com/labstack/echo/v4.(*Echo).ServeHTTP (11,552 samples, 1.15%)</title><rect x="272.5" y="389" width="13.6" height="15.0" fill="rgb(235,139,33)" rx="2" ry="2" />
<text  x="275.50" y="399.5" ></text>
</g>
<g >
<title>net/http.(*Transport).RoundTrip (9,159 samples, 0.91%)</title><rect x="576.4" y="229" width="10.8" height="15.0" fill="rgb(239,157,37)" rx="2" ry="2" />
<text  x="579.40" y="239.5" ></text>
</g>
<g >
<title>io.copyBuffer (212,624 samples, 21.21%)</title><rect x="287.3" y="341" width="250.3" height="15.0" fill="rgb(220,71,17)" rx="2" ry="2" />
<text  x="290.33" y="351.5" >io.copyBuffer</text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/ui/proxy/handler.SendRaftClusterCmd (2,749 samples, 0.27%)</title><rect x="281.8" y="293" width="3.2" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="284.80" y="303.5" ></text>
</g>
<g >
<title>net/http.(*Transport).RoundTrip (1,625 samples, 0.16%)</title><rect x="845.5" y="149" width="1.9" height="15.0" fill="rgb(239,157,37)" rx="2" ry="2" />
<text  x="848.53" y="159.5" ></text>
</g>
<g >
<title>k8s.io/klog/v2.(*loggingT).printfDepth (95 samples, 0.01%)</title><rect x="272.5" y="261" width="0.1" height="15.0" fill="rgb(236,146,34)" rx="2" ry="2" />
<text  x="275.51" y="271.5" ></text>
</g>
<g >
<title>runtime.selectgo (478 samples, 0.05%)</title><rect x="572.0" y="277" width="0.6" height="15.0" fill="rgb(212,35,8)" rx="2" ry="2" />
<text  x="575.03" y="287.5" ></text>
</g>
<g >
<title>net/http.(*Transport).RoundTrip (160 samples, 0.02%)</title><rect x="235.6" y="325" width="0.2" height="15.0" fill="rgb(239,157,37)" rx="2" ry="2" />
<text  x="238.57" y="335.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/infra/httputil.HttpDoRsp (1,625 samples, 0.16%)</title><rect x="845.5" y="277" width="1.9" height="15.0" fill="rgb(242,174,41)" rx="2" ry="2" />
<text  x="848.53" y="287.5" ></text>
</g>
<g >
<title>net/http.(*Client).do (338 samples, 0.03%)</title><rect x="561.0" y="133" width="0.4" height="15.0" fill="rgb(230,117,28)" rx="2" ry="2" />
<text  x="564.01" y="143.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/infra/httputil.HttpDo (9,160 samples, 0.91%)</title><rect x="576.4" y="389" width="10.8" height="15.0" fill="rgb(239,156,37)" rx="2" ry="2" />
<text  x="579.40" y="399.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/infra/httputil.httpDoRsp (338 samples, 0.03%)</title><rect x="561.0" y="197" width="0.4" height="15.0" fill="rgb(236,143,34)" rx="2" ry="2" />
<text  x="564.01" y="207.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/ui/proxy/handler.SendBreakPointToRaftCluster (634 samples, 0.06%)</title><rect x="285.0" y="245" width="0.8" height="15.0" fill="rgb(213,39,9)" rx="2" ry="2" />
<text  x="288.04" y="255.5" ></text>
</g>
<g >
<title>runtime.selectgo (162 samples, 0.02%)</title><rect x="255.0" y="309" width="0.2" height="15.0" fill="rgb(212,35,8)" rx="2" ry="2" />
<text  x="257.97" y="319.5" ></text>
</g>
<g >
<title>net.internetSocket (451 samples, 0.04%)</title><rect x="559.9" y="245" width="0.5" height="15.0" fill="rgb(228,109,26)" rx="2" ry="2" />
<text  x="562.89" y="255.5" ></text>
</g>
<g >
<title>plat-swr/pkg/zraft/api.(*RaftService).ExecuteCmd (1,408 samples, 0.14%)</title><rect x="273.6" y="197" width="1.6" height="15.0" fill="rgb(234,135,32)" rx="2" ry="2" />
<text  x="276.59" y="207.5" ></text>
</g>
<g >
<title>plat-swr/pkg/zraft/etcd-raft.(*raftNode).Process (11,902 samples, 1.19%)</title><rect x="130.6" y="421" width="14.0" height="15.0" fill="rgb(250,209,50)" rx="2" ry="2" />
<text  x="133.57" y="431.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/ui/proxy/handler.SendRaftClusterCmdBreakPoint (634 samples, 0.06%)</title><rect x="285.0" y="293" width="0.8" height="15.0" fill="rgb(240,162,38)" rx="2" ry="2" />
<text  x="288.04" y="303.5" ></text>
</g>
<g >
<title>plat-swr/pkg/zraft/core.(*Raft).waitForProposalResult (173 samples, 0.02%)</title><rect x="561.4" y="149" width="0.2" height="15.0" fill="rgb(243,179,42)" rx="2" ry="2" />
<text  x="564.40" y="159.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/ui/proxy/handler.SendRaftClusterCmdBreakPoint (2,072 samples, 0.21%)</title><rect x="272.8" y="309" width="2.4" height="15.0" fill="rgb(240,162,38)" rx="2" ry="2" />
<text  x="275.81" y="319.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/ui/proxy/handler.SendAllBreakPointToRaftCluster (8,332 samples, 0.83%)</title><rect x="561.6" y="229" width="9.8" height="15.0" fill="rgb(248,199,47)" rx="2" ry="2" />
<text  x="564.64" y="239.5" ></text>
</g>
<g >
<title>plat-swr/pkg/zraft/core.(*Exec).PushMessage (200 samples, 0.02%)</title><rect x="949.4" y="373" width="0.2" height="15.0" fill="rgb(212,35,8)" rx="2" ry="2" />
<text  x="952.39" y="383.5" ></text>
</g>
<g >
<title>net/http.(*Client).Do (19,734 samples, 1.97%)</title><rect x="894.3" y="341" width="23.3" height="15.0" fill="rgb(237,147,35)" rx="2" ry="2" />
<text  x="897.33" y="351.5" >n..</text>
</g>
<g >
<title>crypto/tls.(*Conn).handshakeContext (384 samples, 0.04%)</title><rect x="576.0" y="405" width="0.4" height="15.0" fill="rgb(235,141,33)" rx="2" ry="2" />
<text  x="578.95" y="415.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/infra/httputil.httpDoRsp.func1 (1,625 samples, 0.16%)</title><rect x="845.5" y="229" width="1.9" height="15.0" fill="rgb(207,12,2)" rx="2" ry="2" />
<text  x="848.53" y="239.5" ></text>
</g>
<g >
<title>net.(*Dialer).DialContext (451 samples, 0.04%)</title><rect x="559.9" y="357" width="0.5" height="15.0" fill="rgb(252,220,52)" rx="2" ry="2" />
<text  x="562.89" y="367.5" ></text>
</g>
<g >
<title>io.(*PipeReader).Read (212,623 samples, 21.21%)</title><rect x="287.3" y="325" width="250.3" height="15.0" fill="rgb(209,22,5)" rx="2" ry="2" />
<text  x="290.33" y="335.5" >io.(*PipeReader).Read</text>
</g>
<g >
<title>net/http.(*http2responseWriter).Flush (958 samples, 0.10%)</title><rect x="571.5" y="357" width="1.1" height="15.0" fill="rgb(234,133,31)" rx="2" ry="2" />
<text  x="574.46" y="367.5" ></text>
</g>
<g >
<title>net/http.(*Client).do (1,521 samples, 0.15%)</title><rect x="847.4" y="197" width="1.8" height="15.0" fill="rgb(230,117,28)" rx="2" ry="2" />
<text  x="850.45" y="207.5" ></text>
</g>
<g >
<title>net/http.(*Client).do (9,159 samples, 0.91%)</title><rect x="576.4" y="277" width="10.8" height="15.0" fill="rgb(230,117,28)" rx="2" ry="2" />
<text  x="579.40" y="287.5" ></text>
</g>
<g >
<title>plat-swr/pkg/zraft/etcd-raft.(*raftNode).startRaft.gowrap2 (71,424 samples, 7.13%)</title><rect x="1104.2" y="437" width="84.1" height="15.0" fill="rgb(221,75,18)" rx="2" ry="2" />
<text  x="1107.20" y="447.5" >plat-swr/..</text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/ui/proxy/handler.processStreamData (8,129 samples, 0.81%)</title><rect x="561.9" y="213" width="9.5" height="15.0" fill="rgb(253,224,53)" rx="2" ry="2" />
<text  x="564.88" y="223.5" ></text>
</g>
<g >
<title>net.(*netFD).connect.func1 (451 samples, 0.04%)</title><rect x="559.9" y="165" width="0.5" height="15.0" fill="rgb(210,26,6)" rx="2" ry="2" />
<text  x="562.89" y="175.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/ui/proxy/handler.BreakPointHandler (2,072 samples, 0.21%)</title><rect x="272.8" y="325" width="2.4" height="15.0" fill="rgb(215,48,11)" rx="2" ry="2" />
<text  x="275.81" y="335.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/infra/httputil.httpDoRsp (8,036 samples, 0.80%)</title><rect x="611.7" y="261" width="9.4" height="15.0" fill="rgb(236,143,34)" rx="2" ry="2" />
<text  x="614.66" y="271.5" ></text>
</g>
<g >
<title>go.etcd.io/etcd/server/v3/etcdserver/api/rafthttp.startStreamWriter.gowrap1 (43,582 samples, 4.35%)</title><rect x="184.1" y="437" width="51.3" height="15.0" fill="rgb(233,130,31)" rx="2" ry="2" />
<text  x="187.14" y="447.5" >go.et..</text>
</g>
<g >
<title>plat-swr/pkg/zraft/core.(*Raft).waitForProposalResult (161 samples, 0.02%)</title><rect x="561.6" y="101" width="0.2" height="15.0" fill="rgb(243,179,42)" rx="2" ry="2" />
<text  x="564.64" y="111.5" ></text>
</g>
<g >
<title>io.(*PipeWriter).Write (28,069 samples, 2.80%)</title><rect x="849.2" y="261" width="33.1" height="15.0" fill="rgb(214,45,10)" rx="2" ry="2" />
<text  x="852.24" y="271.5" >io..</text>
</g>
<g >
<title>plat-swr/pkg/zraft/core.(*Raft).proposeMsg (1,404 samples, 0.14%)</title><rect x="273.6" y="165" width="1.6" height="15.0" fill="rgb(209,22,5)" rx="2" ry="2" />
<text  x="276.59" y="175.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/infra/httputil.httpDoRsp.func1 (3,615 samples, 0.36%)</title><rect x="276.7" y="213" width="4.3" height="15.0" fill="rgb(207,12,2)" rx="2" ry="2" />
<text  x="279.72" y="223.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/ui/proxy/handler.PostUpload (4,905 samples, 0.49%)</title><rect x="275.2" y="341" width="5.8" height="15.0" fill="rgb(219,67,16)" rx="2" ry="2" />
<text  x="278.25" y="351.5" ></text>
</g>
<g >
<title>plat-swr/pkg/zraft/core.NewReceiver.gowrap1 (19,644 samples, 1.96%)</title><rect x="1043.8" y="437" width="23.1" height="15.0" fill="rgb(254,228,54)" rx="2" ry="2" />
<text  x="1046.81" y="447.5" >p..</text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/ui/proxy/handler.processDataChunk (1,409 samples, 0.14%)</title><rect x="273.6" y="261" width="1.6" height="15.0" fill="rgb(246,192,45)" rx="2" ry="2" />
<text  x="276.59" y="271.5" ></text>
</g>
<g >
<title>net/http.(*persistConn).roundTrip (316 samples, 0.03%)</title><rect x="561.0" y="53" width="0.4" height="15.0" fill="rgb(221,74,17)" rx="2" ry="2" />
<text  x="564.03" y="63.5" ></text>
</g>
<g >
<title>sync.(*RWMutex).Lock (3,888 samples, 0.39%)</title><rect x="884.4" y="341" width="4.6" height="15.0" fill="rgb(239,158,37)" rx="2" ry="2" />
<text  x="887.45" y="351.5" ></text>
</g>
<g >
<title>k8s.io/klog/v2.Infof (190 samples, 0.02%)</title><rect x="281.8" y="181" width="0.2" height="15.0" fill="rgb(247,194,46)" rx="2" ry="2" />
<text  x="284.82" y="191.5" ></text>
</g>
<g >
<title>k8s.io/klog/v2.Infof (200 samples, 0.02%)</title><rect x="949.4" y="357" width="0.2" height="15.0" fill="rgb(247,194,46)" rx="2" ry="2" />
<text  x="952.39" y="367.5" ></text>
</g>
<g >
<title>runtime.selectgo (14,729 samples, 1.47%)</title><rect x="255.2" y="325" width="17.3" height="15.0" fill="rgb(212,35,8)" rx="2" ry="2" />
<text  x="258.16" y="335.5" ></text>
</g>
<g >
<title>github.com/labstack/echo/v4/middleware.RecoverWithConfig.func1.1 (9,220 samples, 0.92%)</title><rect x="560.6" y="325" width="10.9" height="15.0" fill="rgb(239,156,37)" rx="2" ry="2" />
<text  x="563.61" y="335.5" ></text>
</g>
<g >
<title>net/http.http2chunkWriter.Write (480 samples, 0.05%)</title><rect x="571.5" y="309" width="0.5" height="15.0" fill="rgb(229,113,27)" rx="2" ry="2" />
<text  x="574.46" y="319.5" ></text>
</g>
<g >
<title>runtime.selectgo (138 samples, 0.01%)</title><rect x="10.2" y="373" width="0.2" height="15.0" fill="rgb(212,35,8)" rx="2" ry="2" />
<text  x="13.21" y="383.5" ></text>
</g>
<g >
<title>plat-swr/pkg/zraft/core.(*Receiver).doCmdCache (684 samples, 0.07%)</title><rect x="1066.9" y="405" width="0.8" height="15.0" fill="rgb(220,70,16)" rx="2" ry="2" />
<text  x="1069.94" y="415.5" ></text>
</g>
<g >
<title>k8s.io/klog/v2.Infof (257 samples, 0.03%)</title><rect x="949.6" y="357" width="0.3" height="15.0" fill="rgb(247,194,46)" rx="2" ry="2" />
<text  x="952.63" y="367.5" ></text>
</g>
<g >
<title>runtime.selectgo (160 samples, 0.02%)</title><rect x="561.6" y="85" width="0.2" height="15.0" fill="rgb(212,35,8)" rx="2" ry="2" />
<text  x="564.65" y="95.5" ></text>
</g>
<g >
<title>go.etcd.io/etcd/server/v3/etcdserver/api/rafthttp.(*streamWriter).run (43,582 samples, 4.35%)</title><rect x="184.1" y="421" width="51.3" height="15.0" fill="rgb(238,152,36)" rx="2" ry="2" />
<text  x="187.14" y="431.5" >go.et..</text>
</g>
<g >
<title>net/http.send (3,615 samples, 0.36%)</title><rect x="276.7" y="149" width="4.3" height="15.0" fill="rgb(254,227,54)" rx="2" ry="2" />
<text  x="279.72" y="159.5" ></text>
</g>
<g >
<title>plat-swr/pkg/zraft/core.(*Receiver).dispatch (3,120 samples, 0.31%)</title><rect x="1043.8" y="405" width="3.7" height="15.0" fill="rgb(211,29,7)" rx="2" ry="2" />
<text  x="1046.81" y="415.5" ></text>
</g>
<g >
<title>net.(*sysDialer).dialSerial (451 samples, 0.04%)</title><rect x="559.9" y="325" width="0.5" height="15.0" fill="rgb(237,151,36)" rx="2" ry="2" />
<text  x="562.89" y="335.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/ui/callbacks/impl.(*StreamManager).HandleRequest (190,204 samples, 18.98%)</title><rect x="621.6" y="325" width="223.9" height="15.0" fill="rgb(209,19,4)" rx="2" ry="2" />
<text  x="624.63" y="335.5" >plat-swr/pkg/registry-proxy/u..</text>
</g>
<g >
<title>k8s.io/klog/v2.(*loggingT).printfDepth (257 samples, 0.03%)</title><rect x="949.6" y="325" width="0.3" height="15.0" fill="rgb(236,146,34)" rx="2" ry="2" />
<text  x="952.63" y="335.5" ></text>
</g>
<g >
<title>net/http.(*persistConn).readLoop.func4 (138 samples, 0.01%)</title><rect x="281.6" y="245" width="0.2" height="15.0" fill="rgb(209,22,5)" rx="2" ry="2" />
<text  x="284.64" y="255.5" ></text>
</g>
<g >
<title>k8s.io/klog/v2.(*loggingT).printfDepth (1,325 samples, 0.13%)</title><rect x="947.5" y="357" width="1.5" height="15.0" fill="rgb(236,146,34)" rx="2" ry="2" />
<text  x="950.48" y="367.5" ></text>
</g>
<g >
<title>net/http.(*Client).send (1,521 samples, 0.15%)</title><rect x="847.4" y="181" width="1.8" height="15.0" fill="rgb(241,168,40)" rx="2" ry="2" />
<text  x="850.45" y="191.5" ></text>
</g>
<g >
<title>k8s.io/klog/v2.Infof (1,325 samples, 0.13%)</title><rect x="947.5" y="389" width="1.5" height="15.0" fill="rgb(247,194,46)" rx="2" ry="2" />
<text  x="950.48" y="399.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/infra/httputil.retryDo (1,625 samples, 0.16%)</title><rect x="845.5" y="245" width="1.9" height="15.0" fill="rgb(220,69,16)" rx="2" ry="2" />
<text  x="848.53" y="255.5" ></text>
</g>
<g >
<title>sync.(*Mutex).Lock (121 samples, 0.01%)</title><rect x="894.2" y="277" width="0.1" height="15.0" fill="rgb(231,119,28)" rx="2" ry="2" />
<text  x="897.18" y="287.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/ui/proxy/handler.SendBreakPointToRaftCluster (1,409 samples, 0.14%)</title><rect x="273.6" y="229" width="1.6" height="15.0" fill="rgb(213,39,9)" rx="2" ry="2" />
<text  x="276.59" y="239.5" ></text>
</g>
<g >
<title>net/http.(*Server).Serve.gowrap3 (42,588 samples, 4.25%)</title><rect x="236.0" y="437" width="50.1" height="15.0" fill="rgb(234,135,32)" rx="2" ry="2" />
<text  x="238.97" y="447.5" >net/h..</text>
</g>
<g >
<title>io.(*pipe).write (28,069 samples, 2.80%)</title><rect x="849.2" y="245" width="33.1" height="15.0" fill="rgb(216,52,12)" rx="2" ry="2" />
<text  x="852.24" y="255.5" >io..</text>
</g>
<g >
<title>net/http.(*http2serverConn).scheduleHandler.gowrap1 (10,178 samples, 1.02%)</title><rect x="560.6" y="437" width="12.0" height="15.0" fill="rgb(248,201,48)" rx="2" ry="2" />
<text  x="563.61" y="447.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/ui/callbacks/impl.(*PutManifest).CommitWrite (1,528 samples, 0.15%)</title><rect x="847.4" y="341" width="1.8" height="15.0" fill="rgb(238,152,36)" rx="2" ry="2" />
<text  x="850.44" y="351.5" ></text>
</g>
<g >
<title>github.com/labstack/echo/v4.(*Echo).add.func1 (11,292 samples, 1.13%)</title><rect x="272.8" y="357" width="13.3" height="15.0" fill="rgb(245,184,44)" rx="2" ry="2" />
<text  x="275.81" y="367.5" ></text>
</g>
<g >
<title>net/http.(*Client).Do (9,159 samples, 0.91%)</title><rect x="576.4" y="293" width="10.8" height="15.0" fill="rgb(237,147,35)" rx="2" ry="2" />
<text  x="579.40" y="303.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/ui/proxy/handler.processStreamData (1,409 samples, 0.14%)</title><rect x="273.6" y="277" width="1.6" height="15.0" fill="rgb(253,224,53)" rx="2" ry="2" />
<text  x="276.59" y="287.5" ></text>
</g>
<g >
<title>net.(*Dialer).DialContext-fm (451 samples, 0.04%)</title><rect x="559.9" y="373" width="0.5" height="15.0" fill="rgb(249,203,48)" rx="2" ry="2" />
<text  x="562.89" y="383.5" ></text>
</g>
<g >
<title>k8s.io/klog/v2.(*loggingT).printfDepth (200 samples, 0.02%)</title><rect x="949.4" y="325" width="0.2" height="15.0" fill="rgb(236,146,34)" rx="2" ry="2" />
<text  x="952.39" y="335.5" ></text>
</g>
<g >
<title>k8s.io/klog/v2.(*loggingT).printf (190 samples, 0.02%)</title><rect x="281.8" y="165" width="0.2" height="15.0" fill="rgb(223,83,19)" rx="2" ry="2" />
<text  x="284.82" y="175.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/ui/proxy/handler.BreakPointHandler (634 samples, 0.06%)</title><rect x="285.0" y="309" width="0.8" height="15.0" fill="rgb(215,48,11)" rx="2" ry="2" />
<text  x="288.04" y="319.5" ></text>
</g>
<g >
<title>plat-swr/pkg/zraft/core.(*Raft).proposeMsgWithResult (330 samples, 0.03%)</title><rect x="560.6" y="181" width="0.4" height="15.0" fill="rgb(220,71,17)" rx="2" ry="2" />
<text  x="563.62" y="191.5" ></text>
</g>
<g >
<title>net/http.(*http2responseWriter).FlushError (958 samples, 0.10%)</title><rect x="571.5" y="341" width="1.1" height="15.0" fill="rgb(211,32,7)" rx="2" ry="2" />
<text  x="574.46" y="351.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/ui/proxy/handler.SendRaftClusterCmd (268 samples, 0.03%)</title><rect x="285.8" y="293" width="0.3" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="288.79" y="303.5" ></text>
</g>
<g >
<title>runtime.selectgo (251 samples, 0.03%)</title><rect x="571.7" y="261" width="0.3" height="15.0" fill="rgb(212,35,8)" rx="2" ry="2" />
<text  x="574.73" y="271.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/ui/callbacks/impl.SendRegistryReq (1,526 samples, 0.15%)</title><rect x="847.4" y="325" width="1.8" height="15.0" fill="rgb(239,156,37)" rx="2" ry="2" />
<text  x="850.45" y="335.5" ></text>
</g>
<g >
<title>plat-swr/pkg/zraft/core.NewExec.gowrap1 (285,691 samples, 28.50%)</title><rect x="611.2" y="437" width="336.3" height="15.0" fill="rgb(245,185,44)" rx="2" ry="2" />
<text  x="614.17" y="447.5" >plat-swr/pkg/zraft/core.NewExec.gowrap1</text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/infra/httputil.retryDo (3,615 samples, 0.36%)</title><rect x="276.7" y="229" width="4.3" height="15.0" fill="rgb(220,69,16)" rx="2" ry="2" />
<text  x="279.72" y="239.5" ></text>
</g>
<g >
<title>net/http.(*persistConn).roundTrip (6,139 samples, 0.61%)</title><rect x="580.0" y="197" width="7.2" height="15.0" fill="rgb(221,74,17)" rx="2" ry="2" />
<text  x="582.96" y="207.5" ></text>
</g>
<g >
<title>runtime.selectgo (630 samples, 0.06%)</title><rect x="285.0" y="133" width="0.8" height="15.0" fill="rgb(212,35,8)" rx="2" ry="2" />
<text  x="288.04" y="143.5" ></text>
</g>
<g >
<title>runtime.selectgo (256 samples, 0.03%)</title><rect x="571.1" y="53" width="0.3" height="15.0" fill="rgb(212,35,8)" rx="2" ry="2" />
<text  x="574.14" y="63.5" ></text>
</g>
<g >
<title>go.etcd.io/etcd/raft/v3.(*raftLog).maybeAppend (126 samples, 0.01%)</title><rect x="11.4" y="357" width="0.2" height="15.0" fill="rgb(235,140,33)" rx="2" ry="2" />
<text  x="14.44" y="367.5" ></text>
</g>
<g >
<title>runtime.selectgo (33,612 samples, 3.35%)</title><rect x="144.6" y="421" width="39.5" height="15.0" fill="rgb(212,35,8)" rx="2" ry="2" />
<text  x="147.58" y="431.5" >run..</text>
</g>
<g >
<title>plat-swr/pkg/zraft/core.(*Raft).Propose (201 samples, 0.02%)</title><rect x="561.4" y="197" width="0.2" height="15.0" fill="rgb(222,79,19)" rx="2" ry="2" />
<text  x="564.40" y="207.5" ></text>
</g>
<g >
<title>net/http.(*http2serverConn).runHandler (10,178 samples, 1.02%)</title><rect x="560.6" y="421" width="12.0" height="15.0" fill="rgb(206,4,1)" rx="2" ry="2" />
<text  x="563.61" y="431.5" ></text>
</g>
<g >
<title>runtime.chansend1 (218 samples, 0.02%)</title><rect x="1046.7" y="357" width="0.3" height="15.0" fill="rgb(212,33,8)" rx="2" ry="2" />
<text  x="1049.72" y="367.5" ></text>
</g>
<g >
<title>k8s.io/klog/v2.(*loggingT).output (257 samples, 0.03%)</title><rect x="949.6" y="309" width="0.3" height="15.0" fill="rgb(229,112,26)" rx="2" ry="2" />
<text  x="952.63" y="319.5" ></text>
</g>
<g >
<title>runtime.selectgo (11,902 samples, 1.19%)</title><rect x="130.6" y="357" width="14.0" height="15.0" fill="rgb(212,35,8)" rx="2" ry="2" />
<text  x="133.57" y="367.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/ui/callbacks/impl.(*StreamManager).CreateStreamRequestTask.func1 (9,160 samples, 0.91%)</title><rect x="576.4" y="437" width="10.8" height="15.0" fill="rgb(220,71,17)" rx="2" ry="2" />
<text  x="579.40" y="447.5" ></text>
</g>
<g >
<title>net/http.(*http2serverConn).runHandler.func1 (958 samples, 0.10%)</title><rect x="571.5" y="389" width="1.1" height="15.0" fill="rgb(227,103,24)" rx="2" ry="2" />
<text  x="574.46" y="399.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/ui/callbacks/impl.(*PatchUpload).CommitWrite (190,204 samples, 18.98%)</title><rect x="621.6" y="341" width="223.9" height="15.0" fill="rgb(212,34,8)" rx="2" ry="2" />
<text  x="624.63" y="351.5" >plat-swr/pkg/registry-proxy/u..</text>
</g>
<g >
<title>net/http.(*http2responseWriter).handlerDone (958 samples, 0.10%)</title><rect x="571.5" y="373" width="1.1" height="15.0" fill="rgb(243,177,42)" rx="2" ry="2" />
<text  x="574.46" y="383.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/infra/httputil.HttpDoData (8,040 samples, 0.80%)</title><rect x="611.7" y="293" width="9.4" height="15.0" fill="rgb(247,194,46)" rx="2" ry="2" />
<text  x="614.66" y="303.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/ui/proxy/middleware/blob.GetBlobMiddleware.func1 (201 samples, 0.02%)</title><rect x="561.4" y="293" width="0.2" height="15.0" fill="rgb(238,155,37)" rx="2" ry="2" />
<text  x="564.40" y="303.5" ></text>
</g>
<g >
<title>net/http.(*Transport).RoundTrip (1,521 samples, 0.15%)</title><rect x="847.4" y="149" width="1.8" height="15.0" fill="rgb(239,157,37)" rx="2" ry="2" />
<text  x="850.45" y="159.5" ></text>
</g>
<g >
<title>plat-swr/pkg/zraft/core.(*Raft).proposeMsgWithResult (2,730 samples, 0.27%)</title><rect x="281.8" y="213" width="3.2" height="15.0" fill="rgb(220,71,17)" rx="2" ry="2" />
<text  x="284.82" y="223.5" ></text>
</g>
<g >
<title>plat-swr/pkg/zraft/core.(*Raft).waitForProposalResult (258 samples, 0.03%)</title><rect x="571.1" y="69" width="0.3" height="15.0" fill="rgb(243,179,42)" rx="2" ry="2" />
<text  x="574.14" y="79.5" ></text>
</g>
<g >
<title>net/http.(*Client).send (19,734 samples, 1.97%)</title><rect x="894.3" y="309" width="23.3" height="15.0" fill="rgb(241,168,40)" rx="2" ry="2" />
<text  x="897.33" y="319.5" >n..</text>
</g>
<g >
<title>go.etcd.io/etcd/raft/v3.stepFollower (126 samples, 0.01%)</title><rect x="11.4" y="389" width="0.2" height="15.0" fill="rgb(238,153,36)" rx="2" ry="2" />
<text  x="14.44" y="399.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/ui/proxy/handler.HeadManifestOrBlob (2,749 samples, 0.27%)</title><rect x="281.8" y="325" width="3.2" height="15.0" fill="rgb(228,105,25)" rx="2" ry="2" />
<text  x="284.80" y="335.5" ></text>
</g>
<g >
<title>crypto/tls.(*Conn).handshakeContext.func2 (138 samples, 0.01%)</title><rect x="10.0" y="437" width="0.2" height="15.0" fill="rgb(250,210,50)" rx="2" ry="2" />
<text  x="13.00" y="447.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/infra/httputil.HttpDoRsp (1,521 samples, 0.15%)</title><rect x="847.4" y="277" width="1.8" height="15.0" fill="rgb(242,174,41)" rx="2" ry="2" />
<text  x="850.45" y="287.5" ></text>
</g>
<g >
<title>net/http.(*Client).do (8,036 samples, 0.80%)</title><rect x="611.7" y="197" width="9.4" height="15.0" fill="rgb(230,117,28)" rx="2" ry="2" />
<text  x="614.66" y="207.5" ></text>
</g>
<g >
<title>net/http.(*Client).do (19,734 samples, 1.97%)</title><rect x="894.3" y="325" width="23.3" height="15.0" fill="rgb(230,117,28)" rx="2" ry="2" />
<text  x="897.33" y="335.5" >n..</text>
</g>
<g >
<title>runtime.selectgo (656 samples, 0.07%)</title><rect x="272.8" y="149" width="0.8" height="15.0" fill="rgb(212,35,8)" rx="2" ry="2" />
<text  x="275.82" y="159.5" ></text>
</g>
<g >
<title>plat-swr/pkg/zraft/etcd-raft.(*raftNode).maybeTriggerSnapshot (20,629 samples, 2.06%)</title><rect x="1104.4" y="405" width="24.3" height="15.0" fill="rgb(245,187,44)" rx="2" ry="2" />
<text  x="1107.39" y="415.5" >p..</text>
</g>
<g >
<title>net/http.(*bodyEOFSignal).Read (138 samples, 0.01%)</title><rect x="281.6" y="277" width="0.2" height="15.0" fill="rgb(209,19,4)" rx="2" ry="2" />
<text  x="284.64" y="287.5" ></text>
</g>
<g >
<title>runtime.selectgo (5,484 samples, 0.55%)</title><rect x="1067.7" y="405" width="6.5" height="15.0" fill="rgb(212,35,8)" rx="2" ry="2" />
<text  x="1070.74" y="415.5" ></text>
</g>
<g >
<title>k8s.io/klog/v2.Infof (122 samples, 0.01%)</title><rect x="894.2" y="341" width="0.1" height="15.0" fill="rgb(247,194,46)" rx="2" ry="2" />
<text  x="897.18" y="351.5" ></text>
</g>
<g >
<title>net/http.(*http2serverConn).noteBodyReadFromHandler (7,218 samples, 0.72%)</title><rect x="562.6" y="165" width="8.5" height="15.0" fill="rgb(243,178,42)" rx="2" ry="2" />
<text  x="565.64" y="175.5" ></text>
</g>
<g >
<title>plat-swr/pkg/zraft/core.NewReceiver.gowrap2 (6,168 samples, 0.62%)</title><rect x="1066.9" y="437" width="7.3" height="15.0" fill="rgb(248,199,47)" rx="2" ry="2" />
<text  x="1069.94" y="447.5" ></text>
</g>
<g >
<title>plat-swr/pkg/zraft/core.(*ExecGroup).run (26,801 samples, 2.67%)</title><rect x="947.5" y="421" width="31.5" height="15.0" fill="rgb(235,142,33)" rx="2" ry="2" />
<text  x="950.48" y="431.5" >pl..</text>
</g>
<g >
<title>sync.(*RWMutex).Lock (2,114 samples, 0.21%)</title><rect x="1044.2" y="373" width="2.5" height="15.0" fill="rgb(239,158,37)" rx="2" ry="2" />
<text  x="1047.23" y="383.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/ui/proxy/handler.ExecRaftCmd (634 samples, 0.06%)</title><rect x="285.0" y="229" width="0.8" height="15.0" fill="rgb(245,184,44)" rx="2" ry="2" />
<text  x="288.04" y="239.5" ></text>
</g>
<g >
<title>sync.(*Mutex).Lock (331 samples, 0.03%)</title><rect x="893.8" y="277" width="0.4" height="15.0" fill="rgb(231,119,28)" rx="2" ry="2" />
<text  x="896.79" y="287.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/ui/callbacks/impl.SendRegistryReq (8,040 samples, 0.80%)</title><rect x="611.7" y="325" width="9.4" height="15.0" fill="rgb(239,156,37)" rx="2" ry="2" />
<text  x="614.66" y="335.5" ></text>
</g>
<g >
<title>plat-swr/pkg/zraft/core.(*Exec).run (285,691 samples, 28.50%)</title><rect x="611.2" y="421" width="336.3" height="15.0" fill="rgb(227,102,24)" rx="2" ry="2" />
<text  x="614.17" y="431.5" >plat-swr/pkg/zraft/core.(*Exec).run</text>
</g>
<g >
<title>github.com/labstack/echo/v4.(*Echo).add.func1 (9,220 samples, 0.92%)</title><rect x="560.6" y="309" width="10.9" height="15.0" fill="rgb(245,184,44)" rx="2" ry="2" />
<text  x="563.61" y="319.5" ></text>
</g>
<g >
<title>k8s.io/klog/v2.(*loggingT).output (331 samples, 0.03%)</title><rect x="893.8" y="293" width="0.4" height="15.0" fill="rgb(229,112,26)" rx="2" ry="2" />
<text  x="896.79" y="303.5" ></text>
</g>
<g >
<title>sync.(*Mutex).Lock (95 samples, 0.01%)</title><rect x="272.5" y="229" width="0.1" height="15.0" fill="rgb(231,119,28)" rx="2" ry="2" />
<text  x="275.51" y="239.5" ></text>
</g>
<g >
<title>net.(*sysDialer).doDialTCP (451 samples, 0.04%)</title><rect x="559.9" y="277" width="0.5" height="15.0" fill="rgb(217,55,13)" rx="2" ry="2" />
<text  x="562.89" y="287.5" ></text>
</g>
<g >
<title>runtime.unique_runtime_registerUniqueMapCleanup.func2 (1,465 samples, 0.15%)</title><rect x="1188.3" y="421" width="1.7" height="15.0" fill="rgb(210,24,5)" rx="2" ry="2" />
<text  x="1191.28" y="431.5" ></text>
</g>
<g >
<title>runtime.chanrecv1 (1,833 samples, 0.18%)</title><rect x="882.3" y="293" width="2.1" height="15.0" fill="rgb(223,85,20)" rx="2" ry="2" />
<text  x="885.29" y="303.5" ></text>
</g>
<g >
<title>crypto/tls.(*Conn).HandshakeContext (384 samples, 0.04%)</title><rect x="576.0" y="421" width="0.4" height="15.0" fill="rgb(242,172,41)" rx="2" ry="2" />
<text  x="578.95" y="431.5" ></text>
</g>
<g >
<title>k8s.io/klog/v2.(*loggingT).printfDepth (403 samples, 0.04%)</title><rect x="621.2" y="325" width="0.4" height="15.0" fill="rgb(236,146,34)" rx="2" ry="2" />
<text  x="624.15" y="335.5" ></text>
</g>
<g >
<title>runtime.selectgo (171 samples, 0.02%)</title><rect x="235.8" y="421" width="0.2" height="15.0" fill="rgb(212,35,8)" rx="2" ry="2" />
<text  x="238.77" y="431.5" ></text>
</g>
<g >
<title>net/http.(*persistConn).roundTrip (632 samples, 0.06%)</title><rect x="10.4" y="389" width="0.7" height="15.0" fill="rgb(221,74,17)" rx="2" ry="2" />
<text  x="13.37" y="399.5" ></text>
</g>
<g >
<title>io.CopyBuffer (212,624 samples, 21.21%)</title><rect x="287.3" y="357" width="250.3" height="15.0" fill="rgb(227,102,24)" rx="2" ry="2" />
<text  x="290.33" y="367.5" >io.CopyBuffer</text>
</g>
<g >
<title>runtime.chansend1 (198,543 samples, 19.81%)</title><rect x="287.3" y="293" width="233.8" height="15.0" fill="rgb(212,33,8)" rx="2" ry="2" />
<text  x="290.33" y="303.5" >runtime.chansend1</text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/infra/httputil.HttpDo (3,657 samples, 0.36%)</title><rect x="276.7" y="293" width="4.3" height="15.0" fill="rgb(239,156,37)" rx="2" ry="2" />
<text  x="279.72" y="303.5" ></text>
</g>
<g >
<title>plat-swr/pkg/zraft/api.(*RaftService).ExecuteCmd (203 samples, 0.02%)</title><rect x="561.6" y="165" width="0.3" height="15.0" fill="rgb(234,135,32)" rx="2" ry="2" />
<text  x="564.64" y="175.5" ></text>
</g>
<g >
<title>plat-swr/pkg/zraft/core.(*FeedbackCollector).HandleFeedback (260 samples, 0.03%)</title><rect x="272.5" y="341" width="0.3" height="15.0" fill="rgb(217,58,13)" rx="2" ry="2" />
<text  x="275.50" y="351.5" ></text>
</g>
<g >
<title>github.com/xiang90/probing.(*prober).AddHTTP.func1 (1,085 samples, 0.11%)</title><rect x="10.2" y="437" width="1.2" height="15.0" fill="rgb(250,210,50)" rx="2" ry="2" />
<text  x="13.16" y="447.5" ></text>
</g>
<g >
<title>net/http.(*http2responseWriterState).writeChunk (480 samples, 0.05%)</title><rect x="571.5" y="293" width="0.5" height="15.0" fill="rgb(243,174,41)" rx="2" ry="2" />
<text  x="574.46" y="303.5" ></text>
</g>
<g >
<title>plat-swr/pkg/zraft/core.(*Raft).Propose (663 samples, 0.07%)</title><rect x="272.8" y="213" width="0.8" height="15.0" fill="rgb(222,79,19)" rx="2" ry="2" />
<text  x="275.81" y="223.5" ></text>
</g>
<g >
<title>k8s.io/klog/v2.Infof (121 samples, 0.01%)</title><rect x="929.2" y="341" width="0.2" height="15.0" fill="rgb(247,194,46)" rx="2" ry="2" />
<text  x="932.21" y="351.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/ui/proxy/handler.SendRegistryReq (338 samples, 0.03%)</title><rect x="561.0" y="261" width="0.4" height="15.0" fill="rgb(212,32,7)" rx="2" ry="2" />
<text  x="564.01" y="271.5" ></text>
</g>
<g >
<title>net/http.(*persistConn).roundTrip (19,699 samples, 1.97%)</title><rect x="894.4" y="245" width="23.2" height="15.0" fill="rgb(221,74,17)" rx="2" ry="2" />
<text  x="897.37" y="255.5" >n..</text>
</g>
<g >
<title>plat-swr/pkg/zraft/core.(*Exec).sendFeedback (19,859 samples, 1.98%)</title><rect x="894.2" y="357" width="23.4" height="15.0" fill="rgb(234,136,32)" rx="2" ry="2" />
<text  x="897.18" y="367.5" >p..</text>
</g>
<g >
<title>plat-swr/pkg/zraft/core.(*ExecGroup).findExecIndexInExecs (257 samples, 0.03%)</title><rect x="949.6" y="373" width="0.3" height="15.0" fill="rgb(238,153,36)" rx="2" ry="2" />
<text  x="952.63" y="383.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/ui/callbacks/impl.(*StreamManager).handleStream (9,160 samples, 0.91%)</title><rect x="576.4" y="421" width="10.8" height="15.0" fill="rgb(209,22,5)" rx="2" ry="2" />
<text  x="579.40" y="431.5" ></text>
</g>
<g >
<title>plat-swr/pkg/zraft/core.(*Raft).proposeMsg (258 samples, 0.03%)</title><rect x="285.8" y="229" width="0.3" height="15.0" fill="rgb(209,22,5)" rx="2" ry="2" />
<text  x="288.79" y="239.5" ></text>
</g>
<g >
<title>plat-swr/pkg/zraft/etcd-raft.stoppableListener.Accept (137 samples, 0.01%)</title><rect x="1104.0" y="325" width="0.2" height="15.0" fill="rgb(230,115,27)" rx="2" ry="2" />
<text  x="1107.03" y="335.5" ></text>
</g>
<g >
<title>github.com/labstack/echo/v4.(*Echo).ServeHTTP (9,220 samples, 0.92%)</title><rect x="560.6" y="341" width="10.9" height="15.0" fill="rgb(235,139,33)" rx="2" ry="2" />
<text  x="563.61" y="351.5" ></text>
</g>
<g >
<title>k8s.io/klog/v2.Infof (296 samples, 0.03%)</title><rect x="949.0" y="373" width="0.4" height="15.0" fill="rgb(247,194,46)" rx="2" ry="2" />
<text  x="952.04" y="383.5" ></text>
</g>
<g >
<title>net/http.(*persistConn).roundTrip (1,033 samples, 0.10%)</title><rect x="848.0" y="117" width="1.2" height="15.0" fill="rgb(221,74,17)" rx="2" ry="2" />
<text  x="851.02" y="127.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/infra/httputil.retryDo (9,159 samples, 0.91%)</title><rect x="576.4" y="325" width="10.8" height="15.0" fill="rgb(220,69,16)" rx="2" ry="2" />
<text  x="579.40" y="335.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/infra/httputil.HttpDoRsp (3,615 samples, 0.36%)</title><rect x="276.7" y="261" width="4.3" height="15.0" fill="rgb(242,174,41)" rx="2" ry="2" />
<text  x="279.72" y="271.5" ></text>
</g>
<g >
<title>net/http.(*persistConn).roundTrip (87 samples, 0.01%)</title><rect x="235.7" y="293" width="0.1" height="15.0" fill="rgb(221,74,17)" rx="2" ry="2" />
<text  x="238.66" y="303.5" ></text>
</g>
<g >
<title>net/http.serverHandler.ServeHTTP (9,220 samples, 0.92%)</title><rect x="560.6" y="357" width="10.9" height="15.0" fill="rgb(234,135,32)" rx="2" ry="2" />
<text  x="563.61" y="367.5" ></text>
</g>
<g >
<title>plat-swr/pkg/zraft/core.(*Exec).waitForFeedback (9,902 samples, 0.99%)</title><rect x="917.6" y="357" width="11.6" height="15.0" fill="rgb(208,13,3)" rx="2" ry="2" />
<text  x="920.56" y="367.5" ></text>
</g>
<g >
<title>plat-swr/pkg/zraft/core.(*Raft).processCommits (20,316 samples, 2.03%)</title><rect x="587.3" y="421" width="23.9" height="15.0" fill="rgb(245,184,44)" rx="2" ry="2" />
<text  x="590.26" y="431.5" >p..</text>
</g>
<g >
<title>runtime.unique_runtime_registerUniqueMapCleanup.gowrap1 (1,465 samples, 0.15%)</title><rect x="1188.3" y="437" width="1.7" height="15.0" fill="rgb(243,178,42)" rx="2" ry="2" />
<text  x="1191.28" y="447.5" ></text>
</g>
<g >
<title>go.etcd.io/etcd/raft/v3.(*raft).Step (126 samples, 0.01%)</title><rect x="11.4" y="405" width="0.2" height="15.0" fill="rgb(221,75,18)" rx="2" ry="2" />
<text  x="14.44" y="415.5" ></text>
</g>
<g >
<title>io.Copy (28,069 samples, 2.80%)</title><rect x="849.2" y="309" width="33.1" height="15.0" fill="rgb(216,50,12)" rx="2" ry="2" />
<text  x="852.24" y="319.5" >io..</text>
</g>
<g >
<title>sync.(*Mutex).Lock (353 samples, 0.04%)</title><rect x="1043.8" y="325" width="0.4" height="15.0" fill="rgb(231,119,28)" rx="2" ry="2" />
<text  x="1046.81" y="335.5" ></text>
</g>
<g >
<title>k8s.io/klog/v2.(*loggingT).output (121 samples, 0.01%)</title><rect x="894.2" y="293" width="0.1" height="15.0" fill="rgb(229,112,26)" rx="2" ry="2" />
<text  x="897.18" y="303.5" ></text>
</g>
<g >
<title>go.etcd.io/etcd/raft/v3.(*node).step (11,902 samples, 1.19%)</title><rect x="130.6" y="389" width="14.0" height="15.0" fill="rgb(209,22,5)" rx="2" ry="2" />
<text  x="133.57" y="399.5" ></text>
</g>
<g >
<title>runtime.selectgo (2,508 samples, 0.25%)</title><rect x="282.0" y="181" width="3.0" height="15.0" fill="rgb(212,35,8)" rx="2" ry="2" />
<text  x="285.05" y="191.5" ></text>
</g>
<g >
<title>runtime.chanrecv1 (1,319 samples, 0.13%)</title><rect x="844.0" y="293" width="1.5" height="15.0" fill="rgb(223,85,20)" rx="2" ry="2" />
<text  x="846.98" y="303.5" ></text>
</g>
<g >
<title>net/http.(*transferWriter).writeBody (212,624 samples, 21.21%)</title><rect x="287.3" y="389" width="250.3" height="15.0" fill="rgb(229,114,27)" rx="2" ry="2" />
<text  x="290.33" y="399.5" >net/http.(*transferWriter).writeB..</text>
</g>
<g >
<title>plat-swr/pkg/zraft/etcd-raft.(*stoppableListener).Accept (137 samples, 0.01%)</title><rect x="1104.0" y="341" width="0.2" height="15.0" fill="rgb(235,140,33)" rx="2" ry="2" />
<text  x="1107.03" y="351.5" ></text>
</g>
<g >
<title>net/http.(*Transport).RoundTrip (770 samples, 0.08%)</title><rect x="10.2" y="421" width="0.9" height="15.0" fill="rgb(239,157,37)" rx="2" ry="2" />
<text  x="13.21" y="431.5" ></text>
</g>
<g >
<title>net/http.(*http2Server).serveConn (14,950 samples, 1.49%)</title><rect x="254.9" y="357" width="17.6" height="15.0" fill="rgb(253,225,53)" rx="2" ry="2" />
<text  x="257.90" y="367.5" ></text>
</g>
<g >
<title>net/http.(*http2Server).ServeConn (14,950 samples, 1.49%)</title><rect x="254.9" y="373" width="17.6" height="15.0" fill="rgb(210,25,6)" rx="2" ry="2" />
<text  x="257.90" y="383.5" ></text>
</g>
<g >
<title>plat-swr/pkg/zraft/core.(*Receiver).DispatchResultToGroup (353 samples, 0.04%)</title><rect x="1047.1" y="389" width="0.4" height="15.0" fill="rgb(239,157,37)" rx="2" ry="2" />
<text  x="1050.07" y="399.5" ></text>
</g>
<g >
<title>net/http.(*Transport).roundTrip (338 samples, 0.03%)</title><rect x="561.0" y="69" width="0.4" height="15.0" fill="rgb(232,126,30)" rx="2" ry="2" />
<text  x="564.01" y="79.5" ></text>
</g>
<g >
<title>k8s.io/klog/v2.Infof (392 samples, 0.04%)</title><rect x="611.2" y="373" width="0.4" height="15.0" fill="rgb(247,194,46)" rx="2" ry="2" />
<text  x="614.17" y="383.5" ></text>
</g>
<g >
<title>runtime.selectgo (137 samples, 0.01%)</title><rect x="1104.0" y="309" width="0.2" height="15.0" fill="rgb(212,35,8)" rx="2" ry="2" />
<text  x="1107.03" y="319.5" ></text>
</g>
<g >
<title>net/http.(*onceCloseListener).Accept (137 samples, 0.01%)</title><rect x="1104.0" y="373" width="0.2" height="15.0" fill="rgb(214,43,10)" rx="2" ry="2" />
<text  x="1107.03" y="383.5" ></text>
</g>
<g >
<title>k8s.io/klog/v2.(*loggingT).output (353 samples, 0.04%)</title><rect x="1043.8" y="341" width="0.4" height="15.0" fill="rgb(229,112,26)" rx="2" ry="2" />
<text  x="1046.81" y="351.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/ui/proxy/handler.SendAllBreakPointToRaftCluster (634 samples, 0.06%)</title><rect x="285.0" y="277" width="0.8" height="15.0" fill="rgb(248,199,47)" rx="2" ry="2" />
<text  x="288.04" y="287.5" ></text>
</g>
<g >
<title>runtime.selectgo (664 samples, 0.07%)</title><rect x="286.6" y="405" width="0.7" height="15.0" fill="rgb(212,35,8)" rx="2" ry="2" />
<text  x="289.55" y="415.5" ></text>
</g>
<g >
<title>sync.(*Mutex).Lock (126 samples, 0.01%)</title><rect x="11.4" y="277" width="0.2" height="15.0" fill="rgb(231,119,28)" rx="2" ry="2" />
<text  x="14.44" y="287.5" ></text>
</g>
<g >
<title>go.etcd.io/etcd/raft/v3.(*node).run (101,159 samples, 10.09%)</title><rect x="11.4" y="421" width="119.1" height="15.0" fill="rgb(225,93,22)" rx="2" ry="2" />
<text  x="14.44" y="431.5" >go.etcd.io/etc..</text>
</g>
<g >
<title>k8s.io/klog/v2.(*loggingT).printfDepth (353 samples, 0.04%)</title><rect x="1043.8" y="357" width="0.4" height="15.0" fill="rgb(236,146,34)" rx="2" ry="2" />
<text  x="1046.81" y="367.5" ></text>
</g>
<g >
<title>k8s.io/klog/v2.Infof (95 samples, 0.01%)</title><rect x="272.5" y="293" width="0.1" height="15.0" fill="rgb(247,194,46)" rx="2" ry="2" />
<text  x="275.51" y="303.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/infra/httputil.httpDoRsp (1,521 samples, 0.15%)</title><rect x="847.4" y="261" width="1.8" height="15.0" fill="rgb(236,143,34)" rx="2" ry="2" />
<text  x="850.45" y="271.5" ></text>
</g>
<g >
<title>plat-swr/pkg/zraft/api.(*RaftService).ExecuteCmd (201 samples, 0.02%)</title><rect x="561.4" y="213" width="0.2" height="15.0" fill="rgb(234,135,32)" rx="2" ry="2" />
<text  x="564.40" y="223.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/ui/proxy/handler.PostUpload (674 samples, 0.07%)</title><rect x="560.6" y="293" width="0.8" height="15.0" fill="rgb(219,67,16)" rx="2" ry="2" />
<text  x="563.61" y="303.5" ></text>
</g>
<g >
<title>runtime.selectgo (276 samples, 0.03%)</title><rect x="11.1" y="421" width="0.3" height="15.0" fill="rgb(212,35,8)" rx="2" ry="2" />
<text  x="14.11" y="431.5" ></text>
</g>
<g >
<title>net/http.(*http2serverConn).readPreface (162 samples, 0.02%)</title><rect x="255.0" y="325" width="0.2" height="15.0" fill="rgb(249,202,48)" rx="2" ry="2" />
<text  x="257.97" y="335.5" ></text>
</g>
<g >
<title>net/http.(*Transport).startDialConnForLocked.func1 (599 samples, 0.06%)</title><rect x="559.9" y="437" width="0.7" height="15.0" fill="rgb(240,162,38)" rx="2" ry="2" />
<text  x="562.89" y="447.5" ></text>
</g>
<g >
<title>plat-swr/pkg/zraft/core.(*Raft).Propose (1,408 samples, 0.14%)</title><rect x="273.6" y="181" width="1.6" height="15.0" fill="rgb(222,79,19)" rx="2" ry="2" />
<text  x="276.59" y="191.5" ></text>
</g>
<g >
<title>runtime.selectgo (1,256 samples, 0.13%)</title><rect x="276.7" y="85" width="1.5" height="15.0" fill="rgb(212,35,8)" rx="2" ry="2" />
<text  x="279.72" y="95.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/ui/proxy/handler.sendChunkData (268 samples, 0.03%)</title><rect x="571.1" y="181" width="0.3" height="15.0" fill="rgb(249,205,49)" rx="2" ry="2" />
<text  x="574.13" y="191.5" ></text>
</g>
<g >
<title>plat-swr/pkg/zraft/core.(*Raft).proposeMsg (2,730 samples, 0.27%)</title><rect x="281.8" y="229" width="3.2" height="15.0" fill="rgb(209,22,5)" rx="2" ry="2" />
<text  x="284.82" y="239.5" ></text>
</g>
<g >
<title>runtime.selectgo (1,089 samples, 0.11%)</title><rect x="846.2" y="101" width="1.2" height="15.0" fill="rgb(212,35,8)" rx="2" ry="2" />
<text  x="849.16" y="111.5" ></text>
</g>
<g >
<title>net/http.(*http2serverConn).writeDataFromHandler (229 samples, 0.02%)</title><rect x="571.5" y="277" width="0.2" height="15.0" fill="rgb(221,78,18)" rx="2" ry="2" />
<text  x="574.46" y="287.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/infra/httputil.HttpDoData (1,525 samples, 0.15%)</title><rect x="847.4" y="293" width="1.8" height="15.0" fill="rgb(247,194,46)" rx="2" ry="2" />
<text  x="850.45" y="303.5" ></text>
</g>
<g >
<title>runtime.selectgo (106 samples, 0.01%)</title><rect x="235.4" y="405" width="0.2" height="15.0" fill="rgb(212,35,8)" rx="2" ry="2" />
<text  x="238.45" y="415.5" ></text>
</g>
<g >
<title>runtime.selectgo (19,699 samples, 1.97%)</title><rect x="894.4" y="229" width="23.2" height="15.0" fill="rgb(212,35,8)" rx="2" ry="2" />
<text  x="897.37" y="239.5" >r..</text>
</g>
<g >
<title>plat-swr/pkg/zraft/core.(*ExecGroup).runCmdCache (55,027 samples, 5.49%)</title><rect x="979.0" y="421" width="64.8" height="15.0" fill="rgb(229,113,27)" rx="2" ry="2" />
<text  x="982.03" y="431.5" >plat-sw..</text>
</g>
<g >
<title>k8s.io/klog/v2.(*loggingT).printfDepth (296 samples, 0.03%)</title><rect x="949.0" y="341" width="0.4" height="15.0" fill="rgb(236,146,34)" rx="2" ry="2" />
<text  x="952.04" y="351.5" ></text>
</g>
<g >
<title>runtime.selectgo (20,629 samples, 2.06%)</title><rect x="1104.4" y="389" width="24.3" height="15.0" fill="rgb(212,35,8)" rx="2" ry="2" />
<text  x="1107.39" y="399.5" >r..</text>
</g>
<g >
<title>k8s.io/klog/v2.(*loggingT).output (120 samples, 0.01%)</title><rect x="929.2" y="293" width="0.2" height="15.0" fill="rgb(229,112,26)" rx="2" ry="2" />
<text  x="932.22" y="303.5" ></text>
</g>
<g >
<title>net/http/httputil.(*ReverseProxy).ServeHTTP (664 samples, 0.07%)</title><rect x="281.0" y="325" width="0.8" height="15.0" fill="rgb(223,83,20)" rx="2" ry="2" />
<text  x="284.02" y="335.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/ui/callbacks/impl.CreateStreamRegistryReq (9,160 samples, 0.91%)</title><rect x="576.4" y="405" width="10.8" height="15.0" fill="rgb(229,110,26)" rx="2" ry="2" />
<text  x="579.40" y="415.5" ></text>
</g>
<g >
<title>plat-swr/pkg/zraft/core.(*Exec).processReadMsg (8,086 samples, 0.81%)</title><rect x="611.6" y="373" width="9.6" height="15.0" fill="rgb(214,45,10)" rx="2" ry="2" />
<text  x="614.63" y="383.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/ui/proxy/handler.ExecRaftCmd (1,408 samples, 0.14%)</title><rect x="273.6" y="213" width="1.6" height="15.0" fill="rgb(245,184,44)" rx="2" ry="2" />
<text  x="276.59" y="223.5" ></text>
</g>
<g >
<title>plat-swr/pkg/zraft/core.(*Raft).waitForProposalResult (256 samples, 0.03%)</title><rect x="285.8" y="197" width="0.3" height="15.0" fill="rgb(243,179,42)" rx="2" ry="2" />
<text  x="288.79" y="207.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/infra/httputil.HttpDo (1,625 samples, 0.16%)</title><rect x="845.5" y="309" width="1.9" height="15.0" fill="rgb(239,156,37)" rx="2" ry="2" />
<text  x="848.53" y="319.5" ></text>
</g>
<g >
<title>net/http.(*Transport).getConn (488 samples, 0.05%)</title><rect x="847.4" y="117" width="0.6" height="15.0" fill="rgb(240,165,39)" rx="2" ry="2" />
<text  x="850.45" y="127.5" ></text>
</g>
<g >
<title>k8s.io/klog/v2.(*loggingT).printfDepth (331 samples, 0.03%)</title><rect x="893.8" y="309" width="0.4" height="15.0" fill="rgb(236,146,34)" rx="2" ry="2" />
<text  x="896.79" y="319.5" ></text>
</g>
<g >
<title>net/http.send (9,159 samples, 0.91%)</title><rect x="576.4" y="245" width="10.8" height="15.0" fill="rgb(254,227,54)" rx="2" ry="2" />
<text  x="579.40" y="255.5" ></text>
</g>
<g >
<title>net/http.(*Transport).getConn (3,020 samples, 0.30%)</title><rect x="576.4" y="197" width="3.6" height="15.0" fill="rgb(240,165,39)" rx="2" ry="2" />
<text  x="579.40" y="207.5" ></text>
</g>
<g >
<title>plat-swr/pkg/zraft/core.(*Raft).proposeMsg (196 samples, 0.02%)</title><rect x="561.6" y="133" width="0.3" height="15.0" fill="rgb(209,22,5)" rx="2" ry="2" />
<text  x="564.64" y="143.5" ></text>
</g>
<g >
<title>net/http.(*Transport).roundTrip (160 samples, 0.02%)</title><rect x="235.6" y="309" width="0.2" height="15.0" fill="rgb(232,126,30)" rx="2" ry="2" />
<text  x="238.57" y="319.5" ></text>
</g>
<g >
<title>runtime.selectgo (536 samples, 0.05%)</title><rect x="845.5" y="101" width="0.7" height="15.0" fill="rgb(212,35,8)" rx="2" ry="2" />
<text  x="848.53" y="111.5" ></text>
</g>
<g >
<title>crypto/tls.(*listener).Accept (137 samples, 0.01%)</title><rect x="1104.0" y="357" width="0.2" height="15.0" fill="rgb(239,157,37)" rx="2" ry="2" />
<text  x="1107.03" y="367.5" ></text>
</g>
<g >
<title>net/http.(*Client).Do (1,625 samples, 0.16%)</title><rect x="845.5" y="213" width="1.9" height="15.0" fill="rgb(237,147,35)" rx="2" ry="2" />
<text  x="848.53" y="223.5" ></text>
</g>
<g >
<title>plat-swr/pkg/zraft/core.NewExecGroup.gowrap2 (55,027 samples, 5.49%)</title><rect x="979.0" y="437" width="64.8" height="15.0" fill="rgb(247,195,46)" rx="2" ry="2" />
<text  x="982.03" y="447.5" >plat-sw..</text>
</g>
<g >
<title>go.etcd.io/etcd/raft/v3.(*node).stepWait (13,152 samples, 1.31%)</title><rect x="1074.2" y="405" width="15.5" height="15.0" fill="rgb(243,177,42)" rx="2" ry="2" />
<text  x="1077.20" y="415.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/infra/httputil.HttpDo (1,525 samples, 0.15%)</title><rect x="847.4" y="309" width="1.8" height="15.0" fill="rgb(239,156,37)" rx="2" ry="2" />
<text  x="850.45" y="319.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/ui/proxy/middleware/blob.PutManifestMiddleware.func1 (8,345 samples, 0.83%)</title><rect x="561.6" y="293" width="9.9" height="15.0" fill="rgb(234,137,32)" rx="2" ry="2" />
<text  x="564.64" y="303.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/ui/health.WatchRegistry (162 samples, 0.02%)</title><rect x="235.6" y="421" width="0.2" height="15.0" fill="rgb(229,112,26)" rx="2" ry="2" />
<text  x="238.57" y="431.5" ></text>
</g>
<g >
<title>bytes.(*Reader).WriteTo (28,069 samples, 2.80%)</title><rect x="849.2" y="277" width="33.1" height="15.0" fill="rgb(233,130,31)" rx="2" ry="2" />
<text  x="852.24" y="287.5" >by..</text>
</g>
<g >
<title>runtime.selectgo (6,139 samples, 0.61%)</title><rect x="580.0" y="181" width="7.2" height="15.0" fill="rgb(212,35,8)" rx="2" ry="2" />
<text  x="582.96" y="191.5" ></text>
</g>
<g >
<title>io.copyBuffer (188,884 samples, 18.84%)</title><rect x="621.6" y="293" width="222.4" height="15.0" fill="rgb(220,71,17)" rx="2" ry="2" />
<text  x="624.63" y="303.5" >io.copyBuffer</text>
</g>
<g >
<title>k8s.io/klog/v2.(*loggingT).printf (95 samples, 0.01%)</title><rect x="272.5" y="277" width="0.1" height="15.0" fill="rgb(223,83,19)" rx="2" ry="2" />
<text  x="275.51" y="287.5" ></text>
</g>
<g >
<title>plat-swr/pkg/zraft/core.(*Raft).Propose (203 samples, 0.02%)</title><rect x="561.6" y="149" width="0.3" height="15.0" fill="rgb(222,79,19)" rx="2" ry="2" />
<text  x="564.64" y="159.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/ui/proxy/handler.PutManifestOrBlob (8,345 samples, 0.83%)</title><rect x="561.6" y="277" width="9.9" height="15.0" fill="rgb(246,189,45)" rx="2" ry="2" />
<text  x="564.64" y="287.5" ></text>
</g>
<g >
<title>go.etcd.io/etcd/raft/v3.RestartNode.gowrap1 (101,159 samples, 10.09%)</title><rect x="11.4" y="437" width="119.1" height="15.0" fill="rgb(238,153,36)" rx="2" ry="2" />
<text  x="14.44" y="447.5" >go.etcd.io/etc..</text>
</g>
<g >
<title>runtime.selectgo (13,152 samples, 1.31%)</title><rect x="1074.2" y="373" width="15.5" height="15.0" fill="rgb(212,35,8)" rx="2" ry="2" />
<text  x="1077.20" y="383.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/ui/proxy/handler.SendBreakPointToRaftCluster (663 samples, 0.07%)</title><rect x="272.8" y="261" width="0.8" height="15.0" fill="rgb(213,39,9)" rx="2" ry="2" />
<text  x="275.81" y="271.5" ></text>
</g>
<g >
<title>net/http.send (19,734 samples, 1.97%)</title><rect x="894.3" y="293" width="23.3" height="15.0" fill="rgb(254,227,54)" rx="2" ry="2" />
<text  x="897.33" y="303.5" >n..</text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/ui/proxy/handler.SendRegistryReq (3,657 samples, 0.36%)</title><rect x="276.7" y="309" width="4.3" height="15.0" fill="rgb(212,32,7)" rx="2" ry="2" />
<text  x="279.72" y="319.5" ></text>
</g>
<g >
<title>plat-swr/pkg/zraft/etcd-raft.(*raftNode).startRaft.gowrap1 (137 samples, 0.01%)</title><rect x="1104.0" y="437" width="0.2" height="15.0" fill="rgb(227,105,25)" rx="2" ry="2" />
<text  x="1107.03" y="447.5" ></text>
</g>
<g >
<title>plat-swr/pkg/zraft/core.(*ExecGroup).DispatchMsg (2,081 samples, 0.21%)</title><rect x="947.5" y="405" width="2.4" height="15.0" fill="rgb(211,31,7)" rx="2" ry="2" />
<text  x="950.48" y="415.5" ></text>
</g>
<g >
<title>plat-swr/pkg/zraft/core.(*ExecGroup).DispatchCmdToExec (496 samples, 0.05%)</title><rect x="949.0" y="389" width="0.6" height="15.0" fill="rgb(211,31,7)" rx="2" ry="2" />
<text  x="952.04" y="399.5" ></text>
</g>
<g >
<title>sync.(*RWMutex).Lock (4,051 samples, 0.40%)</title><rect x="889.0" y="341" width="4.8" height="15.0" fill="rgb(239,158,37)" rx="2" ry="2" />
<text  x="892.02" y="351.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/infra/httputil.httpDoRsp (1,625 samples, 0.16%)</title><rect x="845.5" y="261" width="1.9" height="15.0" fill="rgb(236,143,34)" rx="2" ry="2" />
<text  x="848.53" y="271.5" ></text>
</g>
<g >
<title>net/http.(*http2serverConn).serve.gowrap8 (2,854 samples, 0.28%)</title><rect x="572.6" y="437" width="3.4" height="15.0" fill="rgb(250,209,50)" rx="2" ry="2" />
<text  x="575.59" y="447.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/ui/proxy/handler.SendBreakPointToRaftCluster (268 samples, 0.03%)</title><rect x="571.1" y="165" width="0.3" height="15.0" fill="rgb(213,39,9)" rx="2" ry="2" />
<text  x="574.13" y="175.5" ></text>
</g>
<g >
<title>net/http.(*Transport).RoundTrip (8,036 samples, 0.80%)</title><rect x="611.7" y="149" width="9.4" height="15.0" fill="rgb(239,157,37)" rx="2" ry="2" />
<text  x="614.66" y="159.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/ui/proxy/handler.BreakPointHandler (8,332 samples, 0.83%)</title><rect x="561.6" y="261" width="9.8" height="15.0" fill="rgb(215,48,11)" rx="2" ry="2" />
<text  x="564.64" y="271.5" ></text>
</g>
<g >
<title>plat-swr/pkg/zraft/core.(*FeedbackCollector).ProcessFeedback (260 samples, 0.03%)</title><rect x="272.5" y="325" width="0.3" height="15.0" fill="rgb(218,59,14)" rx="2" ry="2" />
<text  x="275.50" y="335.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/ui/proxy/handler.Handlers (201 samples, 0.02%)</title><rect x="561.4" y="261" width="0.2" height="15.0" fill="rgb(227,105,25)" rx="2" ry="2" />
<text  x="564.40" y="271.5" ></text>
</g>
<g >
<title>plat-swr/pkg/zraft/core.(*AppWAL).Write (4,051 samples, 0.40%)</title><rect x="889.0" y="357" width="4.8" height="15.0" fill="rgb(224,90,21)" rx="2" ry="2" />
<text  x="892.02" y="367.5" ></text>
</g>
<g >
<title>k8s.io/klog/v2.(*loggingT).printf (121 samples, 0.01%)</title><rect x="929.2" y="325" width="0.2" height="15.0" fill="rgb(223,83,19)" rx="2" ry="2" />
<text  x="932.21" y="335.5" ></text>
</g>
<g >
<title>runtime.selectgo (173 samples, 0.02%)</title><rect x="561.4" y="133" width="0.2" height="15.0" fill="rgb(212,35,8)" rx="2" ry="2" />
<text  x="564.40" y="143.5" ></text>
</g>
<g >
<title>net/http.(*Client).send (338 samples, 0.03%)</title><rect x="561.0" y="117" width="0.4" height="15.0" fill="rgb(241,168,40)" rx="2" ry="2" />
<text  x="564.01" y="127.5" ></text>
</g>
<g >
<title>plat-swr/pkg/zraft/core.(*Receiver).run (19,644 samples, 1.96%)</title><rect x="1043.8" y="421" width="23.1" height="15.0" fill="rgb(236,145,34)" rx="2" ry="2" />
<text  x="1046.81" y="431.5" >p..</text>
</g>
<g >
<title>io.(*pipe).read (212,623 samples, 21.21%)</title><rect x="287.3" y="309" width="250.3" height="15.0" fill="rgb(240,161,38)" rx="2" ry="2" />
<text  x="290.33" y="319.5" >io.(*pipe).read</text>
</g>
<g >
<title>runtime.selectgo (2,723 samples, 0.27%)</title><rect x="611.7" y="101" width="3.2" height="15.0" fill="rgb(212,35,8)" rx="2" ry="2" />
<text  x="614.66" y="111.5" ></text>
</g>
<g >
<title>sync.(*Mutex).Lock (257 samples, 0.03%)</title><rect x="949.6" y="293" width="0.3" height="15.0" fill="rgb(231,119,28)" rx="2" ry="2" />
<text  x="952.63" y="303.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/ui/callbacks.(*RegistryCallback).CommitWrite (223,260 samples, 22.27%)</title><rect x="621.6" y="357" width="262.8" height="15.0" fill="rgb(252,218,52)" rx="2" ry="2" />
<text  x="624.63" y="367.5" >plat-swr/pkg/registry-proxy/ui/call..</text>
</g>
<g >
<title>github.com/labstack/echo/v4.(*Echo).add.func1 (260 samples, 0.03%)</title><rect x="272.5" y="373" width="0.3" height="15.0" fill="rgb(245,184,44)" rx="2" ry="2" />
<text  x="275.50" y="383.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/ui/proxy/middleware/blob.GetBlobMiddleware.func1 (2,749 samples, 0.27%)</title><rect x="281.8" y="341" width="3.2" height="15.0" fill="rgb(238,155,37)" rx="2" ry="2" />
<text  x="284.80" y="351.5" ></text>
</g>
<g >
<title>net/http.(*initALPNRequest).ServeHTTP (9,220 samples, 0.92%)</title><rect x="560.6" y="389" width="10.9" height="15.0" fill="rgb(247,193,46)" rx="2" ry="2" />
<text  x="563.61" y="399.5" ></text>
</g>
<g >
<title>plat-swr/pkg/zraft/core.(*Raft).proposeMsgWithResult (258 samples, 0.03%)</title><rect x="285.8" y="213" width="0.3" height="15.0" fill="rgb(220,71,17)" rx="2" ry="2" />
<text  x="288.79" y="223.5" ></text>
</g>
<g >
<title>net/http.(*Transport).roundTrip (8,036 samples, 0.80%)</title><rect x="611.7" y="133" width="9.4" height="15.0" fill="rgb(232,126,30)" rx="2" ry="2" />
<text  x="614.66" y="143.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/ui/proxy/handler.ExecRaftCmd (2,749 samples, 0.27%)</title><rect x="281.8" y="277" width="3.2" height="15.0" fill="rgb(245,184,44)" rx="2" ry="2" />
<text  x="284.80" y="287.5" ></text>
</g>
<g >
<title>runtime.selectgo (1,033 samples, 0.10%)</title><rect x="848.0" y="101" width="1.2" height="15.0" fill="rgb(212,35,8)" rx="2" ry="2" />
<text  x="851.02" y="111.5" ></text>
</g>
<g >
<title>plat-swr/pkg/zraft/core.(*ExecGroup).DispatchResultToExec (260 samples, 0.03%)</title><rect x="949.6" y="389" width="0.3" height="15.0" fill="rgb(216,55,13)" rx="2" ry="2" />
<text  x="952.63" y="399.5" ></text>
</g>
<g >
<title>plat-swr/pkg/zraft/etcd-raft.(*raftNode).serveChannels (71,424 samples, 7.13%)</title><rect x="1104.2" y="421" width="84.1" height="15.0" fill="rgb(206,7,1)" rx="2" ry="2" />
<text  x="1107.20" y="431.5" >plat-swr/..</text>
</g>
<g >
<title>sync.(*Mutex).Lock (392 samples, 0.04%)</title><rect x="611.2" y="309" width="0.4" height="15.0" fill="rgb(231,119,28)" rx="2" ry="2" />
<text  x="614.17" y="319.5" ></text>
</g>
<g >
<title>net/http.(*persistConn).wroteRequest (313 samples, 0.03%)</title><rect x="286.1" y="405" width="0.4" height="15.0" fill="rgb(231,120,28)" rx="2" ry="2" />
<text  x="289.11" y="415.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/ui/proxy/handler.SendAllBreakPointToRaftCluster (2,072 samples, 0.21%)</title><rect x="272.8" y="293" width="2.4" height="15.0" fill="rgb(248,199,47)" rx="2" ry="2" />
<text  x="275.81" y="303.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/ui/proxy/middleware/blob.PutManifestMiddleware.func1 (902 samples, 0.09%)</title><rect x="285.0" y="341" width="1.1" height="15.0" fill="rgb(234,137,32)" rx="2" ry="2" />
<text  x="288.04" y="351.5" ></text>
</g>
<g >
<title>k8s.io/klog/v2.(*loggingT).output (1,325 samples, 0.13%)</title><rect x="947.5" y="341" width="1.5" height="15.0" fill="rgb(229,112,26)" rx="2" ry="2" />
<text  x="950.48" y="351.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/infra/httputil.HttpDoData (338 samples, 0.03%)</title><rect x="561.0" y="229" width="0.4" height="15.0" fill="rgb(247,194,46)" rx="2" ry="2" />
<text  x="564.01" y="239.5" ></text>
</g>
<g >
<title>runtime.selectgo (229 samples, 0.02%)</title><rect x="571.5" y="261" width="0.2" height="15.0" fill="rgb(212,35,8)" rx="2" ry="2" />
<text  x="574.46" y="271.5" ></text>
</g>
<g >
<title>net/http.(*Request).write (212,624 samples, 21.21%)</title><rect x="287.3" y="405" width="250.3" height="15.0" fill="rgb(216,54,12)" rx="2" ry="2" />
<text  x="290.33" y="415.5" >net/http.(*Request).write</text>
</g>
<g >
<title>runtime.selectgo (12,195 samples, 1.22%)</title><rect x="1089.7" y="421" width="14.3" height="15.0" fill="rgb(212,35,8)" rx="2" ry="2" />
<text  x="1092.68" y="431.5" ></text>
</g>
<g >
<title>net/http.(*Client).send (1,625 samples, 0.16%)</title><rect x="845.5" y="181" width="1.9" height="15.0" fill="rgb(241,168,40)" rx="2" ry="2" />
<text  x="848.53" y="191.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/ui/proxy/handler.SendBreakPointFinishFlagToRaftCluster (663 samples, 0.07%)</title><rect x="272.8" y="277" width="0.8" height="15.0" fill="rgb(237,150,35)" rx="2" ry="2" />
<text  x="275.81" y="287.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/ui/proxy/handler.ExecRaftCmd (1,248 samples, 0.12%)</title><rect x="275.2" y="293" width="1.5" height="15.0" fill="rgb(245,184,44)" rx="2" ry="2" />
<text  x="278.25" y="303.5" ></text>
</g>
<g >
<title>runtime.selectgo (526 samples, 0.05%)</title><rect x="281.0" y="261" width="0.6" height="15.0" fill="rgb(212,35,8)" rx="2" ry="2" />
<text  x="284.02" y="271.5" ></text>
</g>
<g >
<title>runtime.selectgo (166 samples, 0.02%)</title><rect x="1104.2" y="389" width="0.2" height="15.0" fill="rgb(212,35,8)" rx="2" ry="2" />
<text  x="1107.20" y="399.5" ></text>
</g>
<g >
<title>main.main.func2 (162 samples, 0.02%)</title><rect x="235.6" y="437" width="0.2" height="15.0" fill="rgb(242,171,40)" rx="2" ry="2" />
<text  x="238.57" y="447.5" ></text>
</g>
<g >
<title>sync.(*Mutex).Lock (296 samples, 0.03%)</title><rect x="949.0" y="309" width="0.4" height="15.0" fill="rgb(231,119,28)" rx="2" ry="2" />
<text  x="952.04" y="319.5" ></text>
</g>
<g >
<title>plat-swr/pkg/zraft/core.NewExecGroup.gowrap1 (26,801 samples, 2.67%)</title><rect x="947.5" y="437" width="31.5" height="15.0" fill="rgb(253,225,53)" rx="2" ry="2" />
<text  x="950.48" y="447.5" >pl..</text>
</g>
<g >
<title>plat-swr/pkg/zraft/core.(*AppWAL).Write (2,116 samples, 0.21%)</title><rect x="1044.2" y="389" width="2.5" height="15.0" fill="rgb(224,90,21)" rx="2" ry="2" />
<text  x="1047.23" y="399.5" ></text>
</g>
<g >
<title>plat-swr/pkg/zraft/api.(*RaftService).ExecuteCmd (268 samples, 0.03%)</title><rect x="285.8" y="261" width="0.3" height="15.0" fill="rgb(234,135,32)" rx="2" ry="2" />
<text  x="288.79" y="271.5" ></text>
</g>
<g >
<title>net/http.(*http2pipe).Read (643 samples, 0.06%)</title><rect x="561.9" y="165" width="0.7" height="15.0" fill="rgb(215,46,11)" rx="2" ry="2" />
<text  x="564.88" y="175.5" ></text>
</g>
<g >
<title>runtime.selectgo (256 samples, 0.03%)</title><rect x="285.8" y="181" width="0.3" height="15.0" fill="rgb(212,35,8)" rx="2" ry="2" />
<text  x="288.79" y="191.5" ></text>
</g>
<g >
<title>net/http.(*persistConn).addTLS (138 samples, 0.01%)</title><rect x="560.4" y="389" width="0.2" height="15.0" fill="rgb(229,112,26)" rx="2" ry="2" />
<text  x="563.42" y="399.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/ui/proxy/handler.ExecRaftCmd (268 samples, 0.03%)</title><rect x="571.1" y="149" width="0.3" height="15.0" fill="rgb(245,184,44)" rx="2" ry="2" />
<text  x="574.13" y="159.5" ></text>
</g>
<g >
<title>net/http.(*Transport).getConn (138 samples, 0.01%)</title><rect x="10.2" y="389" width="0.2" height="15.0" fill="rgb(240,165,39)" rx="2" ry="2" />
<text  x="13.21" y="399.5" ></text>
</g>
<g >
<title>runtime.deferreturn (451 samples, 0.04%)</title><rect x="559.9" y="181" width="0.5" height="15.0" fill="rgb(242,170,40)" rx="2" ry="2" />
<text  x="562.89" y="191.5" ></text>
</g>
<g >
<title>net/http.(*Transport).dialConn.gowrap3 (231,532 samples, 23.10%)</title><rect x="287.3" y="437" width="272.6" height="15.0" fill="rgb(245,185,44)" rx="2" ry="2" />
<text  x="290.33" y="447.5" >net/http.(*Transport).dialConn.gowrap3</text>
</g>
<g >
<title>k8s.io/klog/v2.(*loggingT).printf (403 samples, 0.04%)</title><rect x="621.2" y="341" width="0.4" height="15.0" fill="rgb(223,83,19)" rx="2" ry="2" />
<text  x="624.15" y="351.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/infra/httputil.retryDo (8,036 samples, 0.80%)</title><rect x="611.7" y="245" width="9.4" height="15.0" fill="rgb(220,69,16)" rx="2" ry="2" />
<text  x="614.66" y="255.5" ></text>
</g>
<g >
<title>net.(*sysDialer).dialParallel (451 samples, 0.04%)</title><rect x="559.9" y="341" width="0.5" height="15.0" fill="rgb(213,40,9)" rx="2" ry="2" />
<text  x="562.89" y="351.5" ></text>
</g>
<g >
<title>io.Copy (188,884 samples, 18.84%)</title><rect x="621.6" y="309" width="222.4" height="15.0" fill="rgb(216,50,12)" rx="2" ry="2" />
<text  x="624.63" y="319.5" >io.Copy</text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/infra/httputil.HttpDoRsp (9,159 samples, 0.91%)</title><rect x="576.4" y="357" width="10.8" height="15.0" fill="rgb(242,174,41)" rx="2" ry="2" />
<text  x="579.40" y="367.5" ></text>
</g>
<g >
<title>sync.(*Mutex).Lock (120 samples, 0.01%)</title><rect x="929.2" y="277" width="0.2" height="15.0" fill="rgb(231,119,28)" rx="2" ry="2" />
<text  x="932.22" y="287.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/infra/httputil.HttpDoData (9,160 samples, 0.91%)</title><rect x="576.4" y="373" width="10.8" height="15.0" fill="rgb(247,194,46)" rx="2" ry="2" />
<text  x="579.40" y="383.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/ui/callbacks/impl.(*StreamManager).HandleRequest (29,902 samples, 2.98%)</title><rect x="849.2" y="325" width="35.2" height="15.0" fill="rgb(209,19,4)" rx="2" ry="2" />
<text  x="852.24" y="335.5" >pl..</text>
</g>
<g >
<title>k8s.io/klog/v2.Infof (331 samples, 0.03%)</title><rect x="893.8" y="341" width="0.4" height="15.0" fill="rgb(247,194,46)" rx="2" ry="2" />
<text  x="896.79" y="351.5" ></text>
</g>
<g >
<title>plat-swr/pkg/zraft/core.(*ExecGroup).PushMessage (353 samples, 0.04%)</title><rect x="1047.1" y="373" width="0.4" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="1050.07" y="383.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/ui/proxy/handler.PatchUpload (2,072 samples, 0.21%)</title><rect x="272.8" y="341" width="2.4" height="15.0" fill="rgb(241,168,40)" rx="2" ry="2" />
<text  x="275.81" y="351.5" ></text>
</g>
<g >
<title>runtime.selectgo (488 samples, 0.05%)</title><rect x="847.4" y="101" width="0.6" height="15.0" fill="rgb(212,35,8)" rx="2" ry="2" />
<text  x="850.45" y="111.5" ></text>
</g>
<g >
<title>k8s.io/klog/v2.Infof (403 samples, 0.04%)</title><rect x="621.2" y="357" width="0.4" height="15.0" fill="rgb(247,194,46)" rx="2" ry="2" />
<text  x="624.15" y="367.5" ></text>
</g>
<g >
<title>net/http.(*Client).send (160 samples, 0.02%)</title><rect x="235.6" y="357" width="0.2" height="15.0" fill="rgb(241,168,40)" rx="2" ry="2" />
<text  x="238.57" y="367.5" ></text>
</g>
<g >
<title>plat-swr/pkg/registry-proxy/infra/httputil.httpDoRsp.func1 (9,159 samples, 0.91%)</title><rect x="576.4" y="309" width="10.8" height="15.0" fill="rgb(207,12,2)" rx="2" ry="2" />
<text  x="579.40" y="319.5" ></text>
</g>
<g >
<title>runtime.selectgo (50,629 samples, 5.05%)</title><rect x="1128.7" y="405" width="59.6" height="15.0" fill="rgb(212,35,8)" rx="2" ry="2" />
<text  x="1131.68" y="415.5" >runtim..</text>
</g>
<g >
<title>plat-swr/pkg/zraft/api.(*RaftService).ExecuteCmd (1,248 samples, 0.12%)</title><rect x="275.2" y="277" width="1.5" height="15.0" fill="rgb(234,135,32)" rx="2" ry="2" />
<text  x="278.25" y="287.5" ></text>
</g>
<g >
<title>plat-swr/pkg/zraft/core.(*Exec).processResult (331 samples, 0.03%)</title><rect x="893.8" y="357" width="0.4" height="15.0" fill="rgb(247,195,46)" rx="2" ry="2" />
<text  x="896.79" y="367.5" ></text>
</g>
</g>
</svg>
