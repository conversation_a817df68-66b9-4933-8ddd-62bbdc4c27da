import os
import csv
from glob import glob

# 定义结果目录和输出文件路径
result_dir = "/tmp/swr-pull-test/result/"
output_csv = os.path.join(result_dir, "swr_pull_time.csv")

# 收集所有 _time.txt 文件
artifact_files = glob(os.path.join(result_dir, "*_time.txt"))

if not artifact_files:
    print("Error: No time.txt files found.")
    exit(1)

# 初始化汇总数据结构
aggregated_data = {}

# 遍历每个时间记录文件并解析数据
for file in artifact_files:
    filename = os.path.basename(file)
    parts = filename.split("_", maxsplit=2)  # 分割前两个下划线
    node = parts[0]  # 节点名称
    component = parts[1].replace(".txt", "")  # 组件名称

    if node not in aggregated_data:
        aggregated_data[node] = {}

    # 读取文件内容并解析开始时间、结束时间、耗时
    with open(file, 'r') as f:
        lines = f.readlines()
        
        # 跳过格式不正常的文件
        if len(lines) < 3:
            print(f"Warning: skipping malformed file {os.path.basename(file)}")
            continue
            
        try:
            start_time = lines[0].strip().split(": ", 1)[1]
            end_time = lines[1].strip().split(": ", 1)[1]
            elapsed_time = float(lines[2].strip().split(": ", 1)[1])
        except (IndexError, ValueError):
            print(f"Warning: skipping malformed file {os.path.basename(file)}")
            continue

        # 存储组件的时间数据
        aggregated_data[node][component] = [start_time, end_time, round(elapsed_time, 3)]

# 准备 CSV 标题行
components = set(comp for data in aggregated_data.values() for comp in data)
header = ["Node"]
for component in components:
    header.extend([f"{component} Start Time", f"{component} End Time", f"{component} Duration (s)"])
header.append("Total Pull Duration (s)")

# 写入汇总数据到 CSV 文件
with open(output_csv, 'w', newline='') as f:
    writer = csv.writer(f)
    writer.writerow(header)

    # 为每个节点写入数据
    for node, data in aggregated_data.items():
        row = [node]
        total_duration = 0.0

        # 为每个组件添加时间数据
        for component in components:
            if component in data:
                start_time, end_time, duration = data[component]
                row.extend([start_time, end_time, duration])
                total_duration += duration
            else:
                row.extend(["", "", ""])

        row.append(round(total_duration, 3))  # 总时长保留3位小数
        writer.writerow(row)

print(f"CSV 文件已生成: {output_csv}")

