- hosts: nodes
  remote_user: ubuntu
  become: yes
  become_method: sudo
  gather_facts: no
  vars_files:
    - artifacts.yml
  vars:
    # vnodes_per_node passed from start.sh
    # removed default virtual_nodes_per_host

  tasks:
    - name: Set virtual nodes for this host
      set_fact:
        my_virtual_nodes: "{{ vnodes_per_node }}"

    - name: Create workspace
      shell: |
        rm -rf "/tmp/swr-pull-test"
        mkdir -p "/tmp/swr-pull-test/result"
        mkdir -p "/tmp/swr-pull-test/scripts"
      become: yes

    - name: Create concurrent pull script for each virtual node
      copy:
        content: |
          #!/bin/bash
          VIRTUAL_NODE_ID=$1
          PHYSICAL_HOST={{ inventory_hostname }}
          
          # 虚拟节点标识符
          VNODE_NAME="${PHYSICAL_HOST}-vnode${VIRTUAL_NODE_ID}"
          
          echo "Virtual Node ${VNODE_NAME} starting CONCURRENT pull process..."
          
          # 为每个虚拟节点创建独立的工作目录
          WORK_DIR="/tmp/swr-pull-test/vnode_${VIRTUAL_NODE_ID}"
          mkdir -p "${WORK_DIR}/bin"
          
          # 并行拉取所有artifacts（同时启动所有拉取任务）
          declare -a PIDS=()
          
          {% for artifact in artifacts %}
          (
            echo "Virtual Node ${VNODE_NAME} pulling {{ artifact.name }}:{{ artifact.tag }} [CONCURRENT]"
            start_time=$(date "+%Y-%m-%d %H:%M:%S.%3N")
            
            if oras pull swr-plat:2524/admin/oci/{{ artifact.name }}:{{ artifact.tag }} -o "${WORK_DIR}/bin/{{ artifact.name }}" 2>/dev/null; then
              pull_status="SUCCESS"
            else
              pull_status="FAILED"
            fi
            
            end_time=$(date "+%Y-%m-%d %H:%M:%S.%3N")
            elapsed_time=$(echo "$(date -d "$end_time" +%s.%3N) - $(date -d "$start_time" +%s.%3N)" | bc)
            
            # 为每个artifact生成单独的time.txt文件
            {
                echo "Start Time: $start_time"
                echo "End Time: $end_time"
                echo "Elapsed Time: $elapsed_time"
            } > "/tmp/swr-pull-test/result/${VNODE_NAME}_{{ artifact.name }}_time.txt"
          ) &
          PIDS+=($!)
          {% endfor %}
          
          # 等待所有并行任务完成
          for pid in "${PIDS[@]}"; do
            wait $pid
          done
          
          echo "Virtual Node ${VNODE_NAME} completed all CONCURRENT pulls"
        dest: "/tmp/swr-pull-test/scripts/concurrent_pull_script.sh"
        mode: '0755'

    - name: Launch virtual nodes concurrently (each doing concurrent pulls internally)
      shell: |
        bash /tmp/swr-pull-test/scripts/concurrent_pull_script.sh {{ item }}
      loop: "{{ range(1, my_virtual_nodes | int + 1) | list }}"
      async: 1800
      poll: 0
      register: virtual_node_jobs

    - name: Wait for all virtual nodes to complete
      async_status:
        jid: "{{ item.ansible_job_id }}"
      loop: "{{ virtual_node_jobs.results }}"
      register: job_results
      until: job_results.finished
      retries: 360
      delay: 5

    - name: Find time.txt files on remote nodes
      find:
        paths: /tmp/swr-pull-test/result
        patterns: '*_time.txt'
      register: time_files

    - name: Collect time.txt files from nodes
      fetch:
        src: "{{ item.path }}"
        dest: "/tmp/swr-pull-test/result/"
        flat: true
      loop: "{{ time_files.files }}"
      ignore_errors: yes

