import pandas as pd
import glob

# 获取当前目录下所有 scale-*.csv 文件
files = glob.glob("./scale-*.csv")

# 初始化结果存储
results = []

# 遍历所有文件，找到每个文件中最长的拉取组件
for file in files:
    # 读取 CSV 文件
    data = pd.read_csv(file)

    # 提取规模信息（文件名中的数字部分）
    scale = int(file.split('-')[1].split('.')[0])

    # 去除非组件的列，保留组件的 Pull Duration 列，并将其转换为 float
    components = data.iloc[:, 3:-1]
    components = components.apply(pd.to_numeric, errors='coerce')

    # 找到当前文件中最长的 Pull Duration 及其对应的组件和节点
    max_duration_idx = components.stack().idxmax()
    node_name = data.loc[max_duration_idx[0], 'Node']
    component_name = max_duration_idx[1]
    max_duration = components.loc[max_duration_idx[0], component_name]

    # 将结果存储到列表中
    results.append([scale, node_name, component_name, max_duration])

# 将结果转换为 DataFrame
output = pd.DataFrame(results, columns=['Scale', 'Node', 'Component', 'Duration (s)'])

# 按照规模从小到大排序
output = output.sort_values(by='Scale')

# 保存为 CSV 文件
output_file = "./longest_pull_per_scale.csv"
output.to_csv(output_file, index=False)

print(f"结果已保存到 {output_file}")

