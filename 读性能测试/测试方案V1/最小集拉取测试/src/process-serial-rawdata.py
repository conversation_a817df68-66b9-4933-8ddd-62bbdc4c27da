import pandas as pd
import glob

def extract_longest_and_shortest(df, scale):
    """根据 Total Pull Duration (s) 提取最长和最短行数据，并保留 3 位小数。"""
    longest_row = df.loc[df['Total Pull Duration (s)'].idxmax()].round(3)
    shortest_row = df.loc[df['Total Pull Duration (s)'].idxmin()].round(3)

    # 转换为列表格式，包含 Scale 和 Type 信息
    longest_data = [scale, 'longest'] + longest_row.tolist()
    shortest_data = [scale, 'shortest'] + shortest_row.tolist()

    return [longest_data, shortest_data]

def compute_average(df, scale):
    """计算每个规模的平均耗时，并保留 3 位小数。"""
    average_row = df.mean().round(3)  # 计算平均值并保留3位小数
    return [scale, 'average'] + average_row.apply(lambda x: format(x, '.3f')).tolist()

def process_files(file_pattern):
    """处理所有符合文件模式的文件，并生成汇总数据。"""
    summary_data = []
    file_paths = sorted(glob.glob(file_pattern), key=lambda x: int(x.split('-')[-1].split('.')[0]))

    for file_path in file_paths:
        # 提取文件名中的规模数值
        scale = int(file_path.split('-')[-1].split('.')[0])

        # 读取 CSV 文件并过滤出相关的数值列
        df = pd.read_csv(file_path)
        duration_df = df.filter(regex=r"Duration.*\(s\)", axis=1)

        # 提取最长、最短和平均行数据
        summary_data.extend(extract_longest_and_shortest(duration_df, scale))
        summary_data.append(compute_average(duration_df, scale))

    # 返回汇总数据和列名
    return summary_data, duration_df.columns.tolist()

def save_summary_to_csv(summary_data, columns, output_path):
    """将汇总数据保存为 CSV 文件。"""
    # 创建完整的列名
    summary_columns = ['Scale', 'Type'] + columns

    # 将数据转换为 DataFrame 并保存为 CSV
    summary_df = pd.DataFrame(summary_data, columns=summary_columns)
    summary_df.to_csv(output_path, index=False)

# 主程序入口
if __name__ == "__main__":
    # 文件路径模式，可根据需要调整路径和文件名模式
    file_pattern = "scale-*.csv"
    output_path = "summary_duration_statistics.csv"

    # 处理文件并生成汇总数据
    summary_data, columns = process_files(file_pattern)

    # 保存汇总结果为 CSV 文件
    save_summary_to_csv(summary_data, columns, output_path)

    print(f"统计数据已保存至: {output_path}")

