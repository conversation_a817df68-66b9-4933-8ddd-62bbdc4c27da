import pandas as pd
import matplotlib.pyplot as plt
import matplotlib.cm as cm

# 读取 CSV 文件
data = pd.read_csv('longest_pull_per_scale.csv')

# 为不同组件分配颜色
unique_components = data['Component'].unique()
colors = cm.get_cmap('tab20', len(unique_components)).colors
component_color_map = {component: colors[i] for i, component in enumerate(unique_components)}

# 创建柱状图，调整宽度
plt.figure(figsize=(12, 8))
bars = plt.bar(
    data['Scale'],
    data['Duration (s)'],
    color=[component_color_map[component] for component in data['Component']],
    edgecolor='black',
    width=3  # 增加柱子的宽度
)

# 添加标题和标签
plt.title('Longest Pull Duration by Scale', fontsize=18)
plt.xlabel('Scale', fontsize=12)
plt.ylabel('Duration (s)', fontsize=12)

# 设置 X 轴刻度间距
plt.xticks(data['Scale'])

# 计算每个柱状图顶部的中心点，用于折线图
center_points_x = [bar.get_x() + bar.get_width() / 2 for bar in bars]
center_points_y = data['Duration (s)'].values

# 绘制折线图，连接柱状图的顶部中心点
plt.plot(center_points_x, center_points_y, marker='o', linestyle='-', color='black', linewidth=2, label='Trend Line')

# 在每个柱子顶部显示具体的耗时
for bar, duration in zip(bars, data['Duration (s)']):
    height = bar.get_height()
    plt.text(bar.get_x() + bar.get_width() / 2, height + 2, f'{duration:.2f}', 
             ha='center', va='bottom', fontsize=10)

# 创建图例并调整其位置到图表内
handles = [plt.Rectangle((0, 0), 1, 1, color=component_color_map[comp]) for comp in unique_components]
plt.legend(handles, unique_components, title='Component', loc='upper left')

# 保存图表为 PNG 和 SVG 文件
plt.savefig('longest_pull_duration.png', bbox_inches='tight')
plt.savefig('longest_pull_duration.svg', bbox_inches='tight')

# 显示图表
plt.show()
