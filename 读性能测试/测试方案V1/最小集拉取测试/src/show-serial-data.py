import pandas as pd
import matplotlib.pyplot as plt
import numpy as np

# 读取 CSV 数据
data = pd.read_csv('summary_duration_statistics.csv')

# 按照 Scale 和 Type 进行排序，确保绘图顺序正确
data = data.sort_values(by=['Scale', 'Type'], ascending=[True, True])

# 检查数据是否有缺失值，并填充缺失值为 0
data = data.fillna(0)

# 动态识别组件列（除了 Scale, Type 和 Total Pull Duration (s) 之外的列）
duration_columns = [col for col in data.columns if col not in ['Scale', 'Type', 'Total Pull Duration (s)']]

# 修复 cmap 的使用，将生成的颜色映射限制为组件数量
cmap = plt.get_cmap('tab10')
colors = [cmap(i % cmap.N) for i in range(len(duration_columns))]

# 创建一个新的图像
fig, ax = plt.subplots(figsize=(16, 12))

# 定义每个规模的柱子宽度
bar_width = 0.25  # 调整宽度以减少重叠

# 定义 X 轴位置，用于每个规模的三组柱子（shortest, average, longest）
scales = sorted(data['Scale'].unique())
x = np.arange(len(scales))

# 标签释义映射
type_labels = {'shortest': 'Short', 'average': 'Avg', 'longest': 'Long'}

# 保存 average 柱子顶部中心点的位置（用于折线图）
avg_points = []

# 循环绘制每组（shortest, average, longest）对应的柱子
for i, t in enumerate(['shortest', 'average', 'longest']):
    subset = data[data['Type'] == t]

    # 用于记录堆叠柱子的底部高度
    bottom = np.zeros(len(scales))

    # 逐个绘制每个组件的堆叠部分
    for j, col in enumerate(duration_columns):
        bars = ax.bar(
            x + i * bar_width, 
            subset[col], 
            width=bar_width, 
            bottom=bottom, 
            color=colors[j], 
            edgecolor='black', 
            label=col if i == 0 else ""  # 仅在第一次循环时添加图例
        )
        # 更新底部高度，便于下一组件堆叠
        bottom += subset[col].values

    # 记录 average 柱子顶部中心点的位置
    if t == 'average':
        avg_points = [(pos + i * bar_width, height) for pos, height in zip(x, bottom)]

    # 为每个柱子添加标签
    for j, pos in enumerate(x):
        total_height = bottom[j]
        ax.text(
            pos + i * bar_width, 
            total_height + 0.5,  # 在柱子顶部上方显示标签，偏移量为0.5
            type_labels[t], 
            ha='center', 
            va='bottom', 
            fontsize=9
        )

# 添加折线图，连接所有 avg 柱子顶部的中心点
avg_x, avg_y = zip(*avg_points)  # 拆分 x 和 y 坐标
ax.plot(avg_x, avg_y, marker='o', linestyle='-', color='black', linewidth=2, label='Avg Trend')

# 设置 X 轴标签和刻度
ax.set_xlabel('Scale', fontsize=12)
ax.set_xticks(x + bar_width)  # 确保 X 轴标签在正确位置
ax.set_xticklabels(scales, fontsize=10)

# 设置 Y 轴标签
ax.set_ylabel('Duration (s)', fontsize=12)

# 添加图例，并放置在左上角（upper left）
ax.legend(loc='upper left')

# 设置标题
ax.set_title('Pull Duration by Scale (Stacked) with Avg Trend Line', fontsize=14)

# 调整布局
fig.tight_layout()

# 保存和显示图片
plt.savefig('swr-test-plot-with-trend.png')
plt.savefig('swr-test-plot-with-trend.svg')
plt.show()
