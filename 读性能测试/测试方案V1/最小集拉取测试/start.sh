#!/bin/bash
current_dir=$(cd "$(dirname "$0")" || exit; pwd)

workspace="/tmp/swr-pull-test"
output_dir="$current_dir/output/$(date +"%Y%m%d%H%M")"
rm -rf "$workspace" && mkdir -p "$workspace"
rm -rf "$output_dir" && mkdir -p "$output_dir"
cp -f $current_dir/src/* "$workspace"
cp -f $current_dir/conf/* "$workspace"
all_nodes="$workspace/all_nodes.info"
target_nodes="$workspace/nodes"
serial_task_file="$workspace/serial-pull-test.yaml"
concurrent_task_file="$workspace/concurrent-pull-test.yaml"
serial_all_task_file="$workspace/serial-pull-all-test.yaml"
concurrent_all_task_file="$workspace/concurrent-pull-all-test.yaml"
analyze_tool="$workspace/analyze-pull-time.py"
result_file="$workspace/result/swr_pull_time.csv"

# 检查输入参数是否提供
if [ $# -ne 2 ]; then
  echo "Usage: $0 <number_of_nodes> <vnodes_per_node>"
  exit 1
fi

# 获取用户指定的节点数量
target_count=$1
vnodes_per_node=$2

# 检查 all_nodes.info 文件是否存在
if [ ! -f "$all_nodes" ]; then
  echo "Error: all_nodes.info file not found!"
  exit 1
fi

# 读取 all_nodes.info 文件中的所有节点，并随机挑选目标数量的节点
total_nodes=$(wc -l < "$all_nodes")

if [ $target_count -gt $total_nodes ]; then
  echo "Error: Requested number of nodes exceeds total available nodes ($total_nodes)."
  exit 1
fi

# 使用 shuf 随机打乱节点并选择指定数量的节点
selected_nodes=$(shuf -n $target_count $all_nodes)

# 创建 ansible inventory 文件并写入选中的节点
echo "[nodes]" > "$target_nodes"
echo "$selected_nodes" >> "$target_nodes"

# 节点内串行拉取最小集
ansible-playbook -i "$target_nodes" "$serial_task_file" -e "vnodes_per_node=$vnodes_per_node"

# 节点内并行拉取最小集
# ansible-playbook -i "$target_nodes" "$concurrent_task_file" -e "vnodes_per_node=$vnodes_per_node"

python "$analyze_tool"
cp -f "$result_file" "$output_dir/scale-$((target_count * vnodes_per_node)).csv"
# find ./ -mindepth 2 -type f -exec mv -t ./ {} +
