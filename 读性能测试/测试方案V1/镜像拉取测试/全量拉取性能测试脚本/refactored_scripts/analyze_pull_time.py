#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
性能测试结果分析脚本 (简化版)

功能:
1. 扫描指定的结果目录，查找所有 `_total_time.txt` 结尾的原始数据文件。
2. 解析每个文件名，提取节点名称。
3. 从每个文件中读取总耗时。
4. 将所有节点的总耗时数据汇总成一个简单的 CSV 报告。

使用方法:
python3 analyze_pull_time.py <结果目录路径>

例如:
python3 analyze_pull_time.py /tmp/swr-pull-test-results-1678886400
"""

import os
import csv
import sys
from glob import glob

def is_valid_duration(duration):
    """
    验证持续时间是否合理（应该是正数且小于24小时）
    过滤掉时间戳等异常数据
    """
    try:
        duration_float = float(duration)
        # 合理的拉取时间应该在0.1秒到24小时(86400秒)之间
        return 0.1 <= duration_float <= 86400
    except (ValueError, TypeError):
        return False

def normalize_node_name(node_name):
    """
    标准化节点名称，处理IPv6地址等特殊格式
    """
    # 如果看起来像IPv6地址（包含多个冒号），进行特殊处理
    if node_name.count(':') >= 2:
        # 可能是IPv6地址，转换为更友好的格式
        normalized = node_name.replace(':', '-')
        # 如果全是十六进制字符和连字符，添加前缀
        if all(c in '0123456789abcdefABCDEF-' for c in normalized):
            return f"ipv6-node-{normalized}"
    
    # 移除或替换其他可能有问题的字符
    normalized = node_name.replace(':', '-').replace('/', '-')
    
    # 确保名称不为空
    if not normalized.strip():
        normalized = "unknown-node"
    
    return normalized

def analyze_results(result_dir):
    """
    分析指定目录中的测试结果并生成 CSV 报告。
    """
    if not os.path.isdir(result_dir):
        print(f"错误: 结果目录 '{result_dir}' 不存在。")
        sys.exit(1)

    output_csv = os.path.join(result_dir, "swr_pull_time_summary.csv")
    
    # 查找所有 *_total_time.txt 文件
    artifact_files = glob(os.path.join(result_dir, "*_total_time.txt"))

    if not artifact_files:
        print(f"错误: 在目录 '{result_dir}' 中没有找到 *_total_time.txt 结果文件。")
        sys.exit(1)

    # 准备写入 CSV 的数据
    # 结构: [ ["Node", "Total_Pull_Duration(s)"], ["node1", 123.45], ... ]
    rows_to_write = [["Node", "Total_Pull_Duration(s)"]]
    
    total_duration_all_nodes = 0.0
    node_count = 0
    skipped_files = []

    # 遍历每个结果文件
    for file_path in sorted(artifact_files):
        filename = os.path.basename(file_path)
        
        # 解析节点名称 (兼容旧版 Python)
        suffix = '_total_time.txt'
        if filename.endswith(suffix):
            node = filename[:-len(suffix)]
        else:
            print(f"警告: 文件名 '{filename}' 格式不正确，已跳过。")
            continue

        # 标准化节点名称
        node = normalize_node_name(node)

        # 读取并解析总耗时
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                line = f.readline()
                # 文件内容格式: "Total Elapsed Time: 123.45"
                duration_str = line.strip().split(": ", 1)[1]
                duration = float(duration_str)
                
                # 验证持续时间是否合理
                if is_valid_duration(duration):
                    duration_rounded = round(duration, 3)
                    rows_to_write.append([node, duration_rounded])
                    total_duration_all_nodes += duration_rounded
                    node_count += 1
                else:
                    print(f"警告: 节点 '{node}' 的时间数据异常 ({duration:.3f}s)，已跳过。")
                    skipped_files.append(filename)
                    
        except (IOError, IndexError, ValueError) as e:
            print(f"警告: 读取或解析文件 '{filename}' 时出错: {e}，已跳过。")
            skipped_files.append(filename)
            continue
    
    # 计算并添加平均值行
    if node_count > 0:
        average_duration = round(total_duration_all_nodes / node_count, 3)
        rows_to_write.append(["Average", average_duration])

    # 写入汇总数据到 CSV 文件
    try:
        with open(output_csv, 'w', newline='', encoding='utf-8') as f:
            writer = csv.writer(f)
            writer.writerows(rows_to_write)
    except IOError as e:
        print(f"错误: 无法写入 CSV 文件 '{output_csv}': {e}")
        sys.exit(1)

    print("\n" + "="*50)
    print("结果分析完成！")
    print(f"汇总报告已生成: {output_csv}")
    if skipped_files:
        print(f"跳过的异常文件: {len(skipped_files)} 个")
        for skipped_file in skipped_files:
            print(f"  - {skipped_file}")
    print(f"有效节点数量: {node_count}")
    print("="*50)

if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("使用方法: python3 analyze_pull_time.py <结果目录路径>")
        sys.exit(1)
    
    results_directory = sys.argv[1]
    analyze_results(results_directory) 