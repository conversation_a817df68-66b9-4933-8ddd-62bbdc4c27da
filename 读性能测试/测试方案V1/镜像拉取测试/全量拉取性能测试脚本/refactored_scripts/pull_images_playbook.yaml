#================================================================================================
# Ansible Playbook: pull_images_playbook.yaml
#
# 说明:
# 这个 Playbook 用于在多个目标节点上并发执行镜像拉取性能测试。
# 它被 start.sh 脚本调用的。
#
# 变量 (由 start.sh 通过 --extra-vars 传递):
# - remote_user: 用于 SSH 连接和执行命令的用户名。
# - result_dir: 本地机器上用于存放最终结果的目录路径。
# - image_list_file: 本地机器上包含待拉取镜像列表的文件路径。
#
# 工作流程 (已优化):
# 1. 在所有目标节点上创建并清理临时工作目录。
# 2. 将本地的 image_list.txt 文件和 pull_images_test.sh 脚本分发到所有目标节点。
# 3. (核心) 在每个节点上执行 pull_images_test.sh 脚本：
#    - 并发清理和并发拉取镜像（恢复原始高性能逻辑）
#    - 记录总体拉取时间而不是每个镜像的时间
#    - 不进行性能监控，专注于时间统计
#    - 生成与分析脚本兼容的总体时间结果文件
# 4. 使用 `fetch` 模块将每个节点上的总体时间结果文件统一收集到本地。
#================================================================================================

- hosts: nodes
  # 从 start.sh 获取远程用户名
  remote_user: "{{ remote_user }}"
  become: yes
  become_method: sudo
  gather_facts: no

  tasks:
    - name: 1. 在目标节点上准备临时目录
      file:
        path: "/tmp/swr-pull-test/result"
        state: directory
        mode: '0755'

    - name: 1.1. 清理旧的测试结果文件
      shell: |
        rm -f /tmp/swr-pull-test/result/*_total_time.txt
        rm -f /tmp/swr-pull-test/result/*.csv
      # ignore_errors: yes

    - name: 2. 将镜像列表文件分发到各节点
      copy:
        src: "{{ image_list_file }}"
        dest: "/tmp/swr-pull-test/image_list.txt"

    - name: 2.1. 将测试脚本分发到各节点
      copy:
        src: "pull_images_test.sh"
        dest: "/tmp/swr-pull-test/pull_images_test.sh"
        mode: '0755'

    - name: 3. 执行镜像拉取测试脚本
      shell: |
        cd /tmp/swr-pull-test
        bash ./pull_images_test.sh
      environment:
        NODE_NAME: "{{ inventory_hostname }}"
        ANSIBLE_HOSTNAME: "{{ inventory_hostname }}"
      async: 7200 # 任务最长运行时间（秒），例如2小时
      poll: 0     # 立即返回，不等待任务完成
      register: pull_tasks
      no_log: true  # 隐藏异步任务的详细输出

    - name: 4. 等待所有节点的测试任务完成
      async_status:
        jid: "{{ item.ansible_job_id }}"
      loop: "{{ pull_tasks.results | default([pull_tasks]) }}"
      register: job_results
      until: job_results.finished
      retries: 300
      delay: 10
      no_log: false  # 简化输出，避免显示重复的轮询信息

    - name: 5. 从所有节点收集所有结果文件
      find:
        paths: /tmp/swr-pull-test/result
        patterns: "*_total_time.txt"
      register: time_files_on_node
      no_log: true  # 简化输出，避免显示详细的文件列表

    - name: 6. 将所有时间文件统一收集到本地
      fetch:
        src: "{{ item.1.path }}"
        dest: "{{ result_dir }}/"
        flat: yes
      with_subelements:
        - "{{ time_files_on_node.results | default([time_files_on_node]) }}"
        - files
      ignore_errors: yes # 如果某个节点测试失败，文件可能不存在，忽略错误继续执行
      # no_log: true  # 简化输出，避免显示每个文件的传输详情

    - name: 7. 显示任务完成摘要
      debug:
        msg: 
          - "==============================================="
          - "镜像拉取性能测试完成！"
          - "- 参与节点数量: {{ ansible_play_hosts | length }}"
          - "- 结果文件已收集到: {{ result_dir }}"
          - "===============================================" 