#!/bin/bash
###############################################################################
# 通过纯 curl 模拟 docker pull，用于 SWR Registry 性能压测
# 步骤：
#   1. HEAD manifests/<tag>
#   2. GET  manifests/<tag>
#   3. 并发 HEAD blobs/<digest>
#   4. 并发 GET  blobs/<digest>（数据直接丢弃到 /dev/null）
###############################################################################

# ------------------ 配置 ------------------
IMAGE_LIST_FILE="image_list.txt"                 # 镜像列表
RESULT_OUTPUT_DIR="/tmp/swr-pull-test/result"    # 结果输出目录
MAX_CONCURRENT_JOBS=10000                          # 全局最大并发

# ------------------ 路径兜底 ------------------
if [[ ! -f $IMAGE_LIST_FILE && $IMAGE_LIST_FILE == "image_list.txt" ]]; then
    IMAGE_LIST_FILE="/tmp/swr-pull-test/image_list.txt"
fi

# ------------------ 解析主机名 ------------------
if [[ -n $NODE_NAME ]];          then HOSTNAME="$NODE_NAME"
elif [[ -n $ANSIBLE_HOSTNAME ]]; then HOSTNAME="$ANSIBLE_HOSTNAME"
else                                  HOSTNAME="$(hostname)"
fi
HOSTNAME=$(echo "$HOSTNAME" | sed 's/:/-/g' | sed 's/[^a-zA-Z0-9._-]/-/g' | cut -c1-50)
[[ -z $HOSTNAME ]] && HOSTNAME="node-$(date +%s)"

# ------------------ 依赖检查 ------------------
for cmd in curl jq; do
    if ! command -v "$cmd" &>/dev/null; then
        echo "错误: 未找到 '$cmd' 命令，请先安装。" ; exit 1
    fi
done
[[ ! -f $IMAGE_LIST_FILE ]] && { echo "错误: 镜像列表不存在: $IMAGE_LIST_FILE"; exit 1; }

mkdir -p "$RESULT_OUTPUT_DIR"

###############################################################################
# 并发控制工具函数
###############################################################################
job_pids=()          # 当前批次的 PID 列表
job_count=0          # 当前批次数量
wait_batch() {       # 等待当前批次全部结束
    if ((${#job_pids[@]})); then
        wait "${job_pids[@]}"
        job_pids=()
        job_count=0
    fi
}

add_job() {          # 记录新后台任务
    job_pids+=("$1")
    ((job_count++))
    if (( job_count >= MAX_CONCURRENT_JOBS )); then
        wait_batch
    fi
}

###############################################################################
# 处理单个镜像
# 参数：镜像字符串 host:port/namespace/repo:tag
###############################################################################
process_image() {
    local image="$1"

    # 拆分：registry 和 repo:tag
    local registry="${image%%/*}"          # host:port
    local rest="${image#*/}"               # namespace/.../repo:tag
    local repo="${rest%%:*}"               # 去掉 :tag
    local tag="${rest##*:}"

    local manifest_accept="application/vnd.docker.distribution.manifest.v2+json"
    local blob_accept="application/vnd.docker.image.rootfs.diff.tar.gzip"
    local manifest_url="https://${registry}/v2/${repo}/manifests/${tag}"

    # 1) HEAD manifest
    local code
    code=$(curl -ksSI -H "Accept: ${manifest_accept}" "$manifest_url" -w '%{http_code}' -o /dev/null)
    [[ $code != 200 ]] && { echo "[WARN] Manifest HEAD 失败 $image ($code)"; return; }

    # 2) GET manifest
    local manifest_json
    manifest_json=$(curl -ksS -H "Accept: ${manifest_accept}" "$manifest_url") || {
        echo "[WARN] Manifest GET 失败 $image"
        return
    }

    # 3) 提取 layer digests
    local digests
    digests=$(echo "$manifest_json" | jq -r '.layers[].digest' 2>/dev/null) || {
        echo "[WARN] 解析 manifest 失败 $image"
        return
    }

    # 4) 并发 HEAD+GET blob
    while read -r digest; do
        [[ -z $digest ]] && continue
        (
            local blob_url="https://${registry}/v2/${repo}/blobs/${digest}"
            local c
            c=$(curl -ksSI -H "Accept: ${blob_accept}" "$blob_url" -w '%{http_code}' -o /dev/null)
            [[ $c != 200 ]] && { echo "[WARN] Blob HEAD $digest ($c)"; exit 0; }
            # GET blob，数据直接丢弃
            curl -ksS -H "Accept: ${blob_accept}" "$blob_url" -o /dev/null
        ) &
        add_job $!
    done <<< "$digests"
}

###############################################################################
# 主流程
###############################################################################
echo "节点 [$HOSTNAME] 开始测试 ..."
TOTAL_IMAGES=$(grep -c . "$IMAGE_LIST_FILE")
echo "总镜像数: $TOTAL_IMAGES，最大并发: $MAX_CONCURRENT_JOBS"
PULL_START_TIME=$(date +%s.%N)

# 逐行读取镜像列表，按镜像级别并发
while IFS= read -r img; do
    [[ -z $img ]] && continue
    ( process_image "$img" ) &         # 每个镜像一个子进程
    add_job $!
done < "$IMAGE_LIST_FILE"

wait_batch   # 等最后残余任务
echo "所有 HTTP 请求已完成"

PULL_END_TIME=$(date +%s.%N)
TOTAL_PULL_TIME=$(awk -v s="$PULL_START_TIME" -v e="$PULL_END_TIME" 'BEGIN{printf "%.3f", e-s}')

OUTPUT_FILE="${RESULT_OUTPUT_DIR}/${HOSTNAME}_total_time.txt"
echo "Total Elapsed Time: ${TOTAL_PULL_TIME}" > "$OUTPUT_FILE"
echo "耗时 ${TOTAL_PULL_TIME}s，结果已写入 $OUTPUT_FILE"
echo "脚本完成时间: $(date '+%Y-%m-%d %H:%M:%S.%3N')" >> "$OUTPUT_FILE"
echo "脚本PID: $$, 父进程: $PPID" >> "$OUTPUT_FILE"
exit 0