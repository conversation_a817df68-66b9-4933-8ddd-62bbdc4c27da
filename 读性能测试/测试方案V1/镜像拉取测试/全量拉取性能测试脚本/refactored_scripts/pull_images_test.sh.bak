#!/bin/bash

# --- 配置 ---
# 镜像列表文件
IMAGE_LIST_FILE="image_list.txt"

RESULT_OUTPUT_DIR="/tmp/swr-pull-test/result"

# 如果当前目录没有image_list.txt，尝试使用绝对路径
if [ ! -f "$IMAGE_LIST_FILE" ] && [ "$IMAGE_LIST_FILE" = "image_list.txt" ]; then
    IMAGE_LIST_FILE="/tmp/swr-pull-test/image_list.txt"
fi

# 获取主机名 - 优先使用环境变量中的节点标识符
if [ -n "$NODE_NAME" ]; then
    HOSTNAME="$NODE_NAME"
elif [ -n "$ANSIBLE_HOSTNAME" ]; then
    HOSTNAME="$ANSIBLE_HOSTNAME"
else
    HOSTNAME=$(hostname)
fi

# 清理主机名，确保它适合用作文件名
# 移除IPv6地址中的冒号，替换为连字符
# 移除其他可能导致文件名问题的特殊字符
HOSTNAME=$(echo "$HOSTNAME" | sed 's/:/-/g' | sed 's/[^a-zA-Z0-9._-]/-/g')

# 如果主机名为空，使用备用方案
if [ -z "$HOSTNAME" ]; then
    # 最后备用方案：使用IP地址的最后一段或随机字符
    HOSTNAME="node-$(hostname -I 2>/dev/null | awk '{print $1}' | sed 's/\./-/g' || echo "$(date +%s)")"
fi

# 确保主机名不超过合理长度
HOSTNAME=$(echo "$HOSTNAME" | cut -c1-50)

# 最大并发任务数，可根据节点性能调整
MAX_CONCURRENT_JOBS=10000

# --- 依赖检查 ---
# 检查 docker 命令
if ! command -v docker &> /dev/null; then
    echo "错误: 未找到 'docker' 命令。请先安装 Docker。"
    exit 1
fi
# 检查镜像列表文件是否存在
if [ ! -f "$IMAGE_LIST_FILE" ]; then
    echo "错误: 镜像列表文件未找到: $IMAGE_LIST_FILE"
    exit 1
fi

# 创建结果输出目录
mkdir -p "$RESULT_OUTPUT_DIR"

# --- 主逻辑 ---
echo "测试脚本开始执行..."
echo "节点 [$HOSTNAME] 开始批量拉取镜像..."

# 过滤掉空行，获取准确的镜像总数
TOTAL_IMAGES=$(grep -c . "$IMAGE_LIST_FILE")

# 1. 并发清理镜像
echo "正在并发清理 ${TOTAL_IMAGES} 个本地镜像（如果存在）..."
job_count=0
pids_to_wait_for=()
while IFS= read -r image; do
    [ -z "$image" ] && continue # 跳过空行

    docker rmi -f "$image" > /dev/null 2>&1 &
    pids_to_wait_for+=($!) # 记录 docker rmi 任务的PID

    job_count=$((job_count + 1))
    if [ "$job_count" -ge "$MAX_CONCURRENT_JOBS" ]; then
        wait "${pids_to_wait_for[@]}" # 只等待我们记录的PID
        pids_to_wait_for=() # 清空数组以备下一批
        job_count=0
    fi
done < "$IMAGE_LIST_FILE"

# 等待所有剩余的后台清理任务完成
if [ ${#pids_to_wait_for[@]} -gt 0 ]; then
    wait "${pids_to_wait_for[@]}"
fi
echo "镜像清理完成。"
echo

# 2. 并发拉取镜像
echo "开始从 $IMAGE_LIST_FILE 并发拉取 ${TOTAL_IMAGES} 个镜像..."
PULL_START_TIME=$(date +%s.%N)

job_count=0
pids_to_wait_for=()
while IFS= read -r image; do
    [ -z "$image" ] && continue
    
    docker pull "$image" > /dev/null 2>&1 &
    pids_to_wait_for+=($!) # 记录 docker pull 任务的PID
    
    job_count=$((job_count + 1))
    if [ "$job_count" -ge "$MAX_CONCURRENT_JOBS" ]; then
        wait "${pids_to_wait_for[@]}" # 只等待我们记录的PID
        pids_to_wait_for=() # 清空数组以备下一批
        job_count=0
    fi
done < "$IMAGE_LIST_FILE"

# 等待最后一批（或唯一一批）拉取任务完成
if [ ${#pids_to_wait_for[@]} -gt 0 ]; then
    wait "${pids_to_wait_for[@]}"
fi

PULL_END_TIME=$(date +%s.%N)
echo "所有拉取任务已完成，正在检查结果..."

# 计算总耗时 - 使用更稳定的计算方法
if command -v bc &> /dev/null; then
    # 如果有bc命令，使用bc进行精确计算
    TOTAL_PULL_TIME=$(echo "scale=3; $PULL_END_TIME - $PULL_START_TIME" | bc)
else
    # 使用python进行计算（更可靠）
    TOTAL_PULL_TIME=$(python3 -c "print(f'{float('$PULL_END_TIME') - float('$PULL_START_TIME'):.3f}')" 2>/dev/null)
    
    # 如果python也不可用，则使用awk作为最后的备选方案
    if [ $? -ne 0 ]; then
        TOTAL_PULL_TIME=$(awk -v end="$PULL_END_TIME" -v start="$PULL_START_TIME" 'BEGIN {printf "%.3f", end - start}')
        
        # 验证结果是否合理（应该是正数且小于合理的上限）
        if ! echo "$TOTAL_PULL_TIME" | grep -q '^[0-9]\+\.[0-9]\+$' || [ "$(echo "$TOTAL_PULL_TIME > 86400" | bc 2>/dev/null || echo "0")" = "1" ]; then
            echo "警告: 时间计算可能有误，使用简化计算"
            TOTAL_PULL_TIME=$(echo "$PULL_END_TIME $PULL_START_TIME" | awk '{printf "%.3f", $1 - $2}')
        fi
    fi
fi

# 生成总体时间结果文件（兼容分析脚本格式）
OUTPUT_FILE="${RESULT_OUTPUT_DIR}/${HOSTNAME}_total_time.txt"
echo "Total Elapsed Time: ${TOTAL_PULL_TIME}" > "$OUTPUT_FILE"

# 脚本正常结束
exit 0