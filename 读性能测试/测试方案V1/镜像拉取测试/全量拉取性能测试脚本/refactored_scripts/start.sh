#!/bin/bash

#================================================================================================
# 脚本说明:
# 该脚本是镜像并发拉取性能测试的主控制脚本。
#
# 功能:
# 1. 准备测试环境: 从 nodes.txt 和 image_list.txt 文件中读取配置，并动态生成 Ansible inventory 文件。
# 2. 运行测试: 调用 Ansible Playbook，在多个节点上并发执行镜像拉取测试并收集结果。
# 3. 分析结果: 调用 Python 脚本对结果进行汇总分析，并生成 CSV 报告。
# 4. 清理环境: 测试结束后自动删除生成的临时文件。
#
# 使用方法:
# 1. 在本脚本所在目录准备好以下文件:
#    - image_list.txt (包含要拉取的镜像列表，每行一个)
#    - nodes.txt (包含目标节点列表，每行一个节点名或IP)
# 2. 确保 ansible 已安装，并且当前用户有权限通过 SSH 免密登录到所有目标节点。
# 3. 直接运行 `./start.sh`。
#================================================================================================

set -eo pipefail

#===============================
# 全局配置
#===============================
# 本地存放测试结果的目录
RESULT_DIR="/tmp/swr-pull-test-results-$(date +%s)"
# 【重要】镜像清单文件，需要手动提供
IMAGE_LIST_FILE="image_list.txt"
# 【重要】节点列表文件，需要手动提供
NODE_LIST_FILE="nodes.txt"
# Ansible Playbook 文件
ANSIBLE_PLAYBOOK="pull_images_playbook.yaml"
# 动态生成的 Ansible Inventory 文件
ANSIBLE_INVENTORY="inventory.ini"
# 测试结果分析脚本
ANALYZE_SCRIPT="analyze_pull_time.py"
# 【重要】执行远程命令的用户名
REMOTE_USER="ubuntu"

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" &>/dev/null && pwd)"

#===============================
# 工具函数
#===============================
log() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - INFO - $1"
}

log_error() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - ERROR - $1" >&2
}

#===============================
# 测试流程函数
#===============================

# 准备测试环境
prepare_test() {
    log "开始准备测试环境..."

    # 1. 检查并创建本地结果目录
    mkdir -p "$RESULT_DIR"
    log "结果将保存在: $RESULT_DIR"
    
    # 2. 检查依赖文件
    if [ ! -f "$SCRIPT_DIR/$IMAGE_LIST_FILE" ]; then
        log_error "缺少镜像列表文件: '$IMAGE_LIST_FILE' 未在脚本目录中找到。"
        log "请提供一个名为 'image_list.txt' 的文件，其中包含要拉取的镜像列表。"
        exit 1
    fi
    log "已找到镜像列表文件: $IMAGE_LIST_FILE"

    if [ ! -f "$SCRIPT_DIR/$NODE_LIST_FILE" ]; then
        log_error "缺少节点列表文件: '$NODE_LIST_FILE' 未在脚本目录中找到。"
        log "请创建一个名为 'nodes.txt' 的文件，每行写一个节点名或IP地址。"
        exit 1
    fi
    log "已找到节点列表文件: $NODE_LIST_FILE"

    # 3. 动态生成 Ansible inventory 文件
    log "正在从 '$NODE_LIST_FILE' 生成 Ansible inventory 文件: $ANSIBLE_INVENTORY"
    echo "[nodes]" > "$SCRIPT_DIR/$ANSIBLE_INVENTORY"
    # 读取 nodes.txt 的内容并附加到 inventory 文件中
    cat "$SCRIPT_DIR/$NODE_LIST_FILE" >> "$SCRIPT_DIR/$ANSIBLE_INVENTORY"
    log "Inventory 文件生成完毕。"

    log "测试环境准备就绪。"
}

# 运行测试
run_test() {
    log "开始运行 Ansible Playbook 进行性能测试..."
    if [ ! -f "$SCRIPT_DIR/$ANSIBLE_PLAYBOOK" ]; then
        log_error "Ansible Playbook 文件 '$ANSIBLE_PLAYBOOK' 未找到。"
        exit 1
    fi

    # 调用 ansible-playbook
    # -i: 指定 inventory 文件
    # --extra-vars: 传递额外变量给 playbook
    ansible-playbook -i "$SCRIPT_DIR/$ANSIBLE_INVENTORY" "$SCRIPT_DIR/$ANSIBLE_PLAYBOOK" \
        --extra-vars "remote_user=$REMOTE_USER" \
        --extra-vars "result_dir=$RESULT_DIR" \
        --extra-vars "image_list_file=$SCRIPT_DIR/$IMAGE_LIST_FILE"

    log "Ansible Playbook 执行完毕。"
}

# 分析结果
analyze_results() {
    log "开始分析测试结果..."
    if [ ! -f "$SCRIPT_DIR/$ANALYZE_SCRIPT" ]; then
        log_error "分析脚本 $ANALYZE_SCRIPT 未找到。"
        exit 1
    fi

    if [ -z "$(ls -A $RESULT_DIR)" ]; then
        log_error "结果目录为空，没有从节点收集到任何数据，跳过分析步骤。"
        return 1
    fi
    
    # 调用 Python 脚本进行分析
    if ! python3 "$SCRIPT_DIR/$ANALYZE_SCRIPT" "$RESULT_DIR"; then
        log_error "结果分析脚本执行失败。"
        exit 1
    fi
    
    log "结果分析完成！"
    log "详细报告已生成: $RESULT_DIR/swr_pull_time_summary.csv"
}

# 清理环境
cleanup() {
    log "开始清理临时文件..."
    
    rm -f "$SCRIPT_DIR/$ANSIBLE_INVENTORY"
    
    log "临时 Inventory 文件已清理。"
    log "测试结果完整保存在: $RESULT_DIR"
}

#===============================
# 主函数
#===============================
main() {
    # 注册 trap，确保脚本退出时执行 cleanup 函数
    trap cleanup EXIT
    
    prepare_test
    run_test
    analyze_results
    
    log "性能测试流程全部完成！"
}

# 执行主函数
main 