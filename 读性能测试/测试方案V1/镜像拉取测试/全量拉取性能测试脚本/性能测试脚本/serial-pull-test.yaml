- hosts: nodes
  remote_user: ubuntu
  become: yes
  become_method: sudo
  gather_facts: no
  vars_files:
    - artifacts.yml  # 加载制品配置文件

  tasks:
    - name: Create workspace
      shell: |
        rm -rf "/tmp/swr-pull-test"
        mkdir -p "/tmp/swr-pull-test/result"
      become: yes

    - name: Pull artifact using oras with timing
      shell: |
        sleep $((RANDOM % 5))
        start_time=$(date "+%Y-%m-%d %H:%M:%S.%3N")
        oras pull swr-plat:2524/admin/oci/{{ item.name }}:{{ item.tag }} -o /tmp/swr-pull-test/bin/{{ item.name }}
        end_time=$(date "+%Y-%m-%d %H:%M:%S.%3N")
        elapsed_time=$(echo "$(date -d "$end_time" +%s.%3N) - $(date -d "$start_time" +%s.%3N)" | bc)
        {
            echo "Start Time: $start_time"
            echo "End Time: $end_time"
            echo "Elapsed Time: $elapsed_time"
        } > /tmp/swr-pull-test/result/{{ inventory_hostname }}_{{ item.name }}_time.txt
      loop: "{{ artifacts }}"
      register: result

    - name: Collect time.txt files from nodes
      fetch:
        src: "/tmp/swr-pull-test/result/{{ inventory_hostname }}_{{ item.name }}_time.txt"
        dest: "/tmp/swr-pull-test/result/"
        flat: true  # 平铺存放文件，不创建节点子目录
      loop: "{{ artifacts }}"
      loop_control:
        loop_var: item
      ignore_errors: yes  # 忽略文件不存在的错误