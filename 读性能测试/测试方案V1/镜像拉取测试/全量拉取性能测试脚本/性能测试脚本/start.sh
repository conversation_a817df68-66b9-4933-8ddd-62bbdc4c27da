#!/bin/bash
current_dir=$(cd "$(dirname "$0")" || exit; pwd)

workspace="/tmp/swr-pull-test"
output_dir="$current_dir/output/$(date +"%Y%m%d%H%M")"
rm -rf "$workspace" && mkdir -p "$workspace"
rm -rf "$output_dir" && mkdir -p "$output_dir"
cp -f $current_dir/src/* "$workspace"
cp -f $current_dir/conf/* "$workspace"
all_nodes="$workspace/all_nodes.info"
s100_nodes="$workspace/100.node.info"
target_nodes="$workspace/nodes"
serial_task_file="$workspace/serial-pull-test.yaml"
concurrent_task_file="$workspace/concurrent-pull-test.yaml"
analyze_tool="$workspace/analyze-pull-time.py"
result_file="$workspace/result/swr_pull_time.csv"

# 检查输入参数是否提供
if [ $# -ne 1 ]; then
  echo "Usage: $0 <number_of_nodes>"
  exit 1
fi

# 获取用户指定的节点数量
target_count=$1

# 检查 all_nodes.info 文件是否存在
if [ ! -f "$all_nodes" ]; then
  echo "Error: all_nodes.info file not found!"
  exit 1
fi

# 读取 all_nodes.info 文件中的所有节点，并随机挑选目标数量的节点
total_nodes=$(wc -l < "$all_nodes")

if [ $target_count -gt $total_nodes ]; then
  echo "Error: Requested number of nodes exceeds total available nodes ($total_nodes)."
  exit 1
fi

# 使用 shuf 随机打乱节点并选择指定数量的节点
#selected_nodes=$(shuf -n $target_count $all_nodes)

#固化100节点测试
selected_nodes=$(cat $s100_nodes)

# 创建 ansible inventory 文件并写入选中的节点
echo "[nodes]" > "$target_nodes"
echo "$selected_nodes" >> "$target_nodes"

ansible-playbook -i "$target_nodes" "$serial_task_file"
#ansible-playbook -i "$target_nodes" "$concurrent_task_file"
python "$analyze_tool"
cp -f "$result_file" "$output_dir/scale-$target_count.csv"