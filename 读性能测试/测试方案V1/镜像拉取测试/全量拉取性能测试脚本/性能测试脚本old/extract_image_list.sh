#!/bin/bash

# 定义输入和输出文件
INPUT_FILE="pdm-cli_deploylist_KubeallPackageList.yaml"
OUTPUT_FILE="image_list.txt"

# 检查 yq 命令是否存在
if ! command -v yq &> /dev/null
then
    echo "错误: 未找到 'yq' 命令。"
    echo "请先安装 yq (https://github.com/mikefarah/yq) 再运行此脚本。"
    exit 1
fi

# 检查输入文件是否存在
if [ ! -f "$INPUT_FILE" ]; then
    echo "错误: 输入文件未找到: $INPUT_FILE"
    exit 1
fi

# 使用 yq 提取并格式化镜像列表
# .spec[].pkgs[] - 遍历 spec 数组中的每个元素的 pkgs 数组
# select(.type == "image") - 筛选出类型为 "image" 的条目
# "..." - 构建输出字符串，引用 .cp_name 和 .cp_version 字段
echo "正在从 $INPUT_FILE 中提取镜像列表..."

yq e '.spec[].pkgs[] | select(.type == "image") | "swr-plat:2524/admin/image/\(.cp_name):\(.cp_version)"' "$INPUT_FILE" > "$OUTPUT_FILE"

echo "镜像列表已成功提取到 $OUTPUT_FILE 文件中。"
echo "总共提取了 $(wc -l < "$OUTPUT_FILE") 个镜像。"
