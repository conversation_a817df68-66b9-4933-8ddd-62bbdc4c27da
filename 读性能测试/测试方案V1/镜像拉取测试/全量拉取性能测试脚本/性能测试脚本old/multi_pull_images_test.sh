#!/bin/bash

# ==============================================================================
#           Multi-Node Docker Image Pull & Profiling Test Orchestrator
# ==============================================================================
#
# Description:
#   This script orchestrates the concurrent execution of 'pull_images_test.sh'
#   on multiple remote nodes. It distributes the necessary files, runs the
#   tests in parallel, and then collects and summarizes the results.
#
# Pre-requisites:
#   1. Passwordless SSH access from the master node to all worker nodes.
#      (Use 'ssh-copy-id user@hostname' to set it up).
#   2. 'pull_images_test.sh' and 'image_list.txt' must be in the same
#      directory as this script.
#
# ==============================================================================


# --- (!!!) User Configuration (!!!) ---
# Please edit this list with the SSH addresses of all worker nodes.
# Example: REMOTE_NODES=("root@************" "root@************")
REMOTE_NODES=(
    "ubuntu@ae00:112::1fd"
    "ubuntu@ae00:112::346"
    "ubuntu@ae00:112::14f"
    "ubuntu@ae00:112::30f"
    "ubuntu@ae00:112::77"
    "ubuntu@ae00:112::359"
    "ubuntu@ae00:112::141"
    "ubuntu@ae00:112::3d"
    "ubuntu@ae00:112::120"
)

# 新增：配置需要进行性能分析的节点列表
# 只有在此列表中的节点才会尝试监控 'kubeall-swr' 进程。
# 请只填写 IP 地址或主机名，不要包含 'user@' 前缀。
NODES_WITH_PROFILING=(
    "ae00:112::1fd"
    "ae00:112::346"
    "ae00:112::14f"
)

# --- Script Configuration ---
set -e  # Exit immediately if a command exits with a non-zero status.
# Options to bypass host key verification for automated runs
SSH_OPTIONS="-o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null -o LogLevel=ERROR"
WORKER_SCRIPT="pull_images_test.sh"
IMAGE_LIST="image_list.txt"
TIMESTAMP=$(date +%Y%m%d_%H%M%S)
RESULTS_DIR="pull_results_${TIMESTAMP}"
REMOTE_WORKDIR="image_pull_test_${TIMESTAMP}"


# --- Prerequisite Checks ---
echo "--- Running Prerequisite Checks ---"
if ! command -v ssh &> /dev/null || ! command -v scp &> /dev/null; then
    echo "错误: 'ssh' 或 'scp' 命令未找到。请确保 OpenSSH 客户端已安装。" >&2
    exit 1
fi

if [ ! -f "$WORKER_SCRIPT" ]; then
    echo "错误: 工作脚本 '$WORKER_SCRIPT' 未找到。" >&2
    exit 1
fi

if [ ! -f "$IMAGE_LIST" ]; then
    echo "错误: 镜像列表 '$IMAGE_LIST' 未找到。" >&2
    exit 1
fi

if [ ${#REMOTE_NODES[@]} -eq 0 ] || [[ "${REMOTE_NODES[0]}" == "user@host1" ]]; then
    echo "错误: 请编辑此脚本并配置 'REMOTE_NODES' 列表。" >&2
    exit 1
fi

echo "所有检查通过。"
echo


# --- Main Logic ---
mkdir -p "$RESULTS_DIR"
echo "结果将保存在本地目录: $RESULTS_DIR"
echo "将在远程节点上使用临时工作目录: $REMOTE_WORKDIR"
echo

pids=()
node_hostnames=()

# 1. Distribute files to all nodes first
echo "--- 1. 分发文件至所有节点 ---"
for node in "${REMOTE_NODES[@]}"; do
    if [ "$node" = "local" ]; then
        node_host=$(hostname)
        sanitized_hostname=$(echo "$node_host" | tr '[:.]' '_')
        node_hostnames+=("$sanitized_hostname")
        echo "  -> 准备在本地节点上执行 (文件名标识: $sanitized_hostname)"
        mkdir -p "$REMOTE_WORKDIR"
        cp "$WORKER_SCRIPT" "$IMAGE_LIST" "$REMOTE_WORKDIR/"
        echo "     文件已复制到本地: $REMOTE_WORKDIR"
        continue
    fi

    node_user=$(echo "$node" | cut -d'@' -f1)
    node_host=$(echo "$node" | cut -d'@' -f2)
    
    # Sanitize hostname for use in filenames (replace : and . with _)
    sanitized_hostname=$(echo "$node_host" | tr '[:.]' '_')
    node_hostnames+=("$sanitized_hostname")
    
    # Use user@host for ssh, and user@[host] for scp with IPv6
    ssh_target="$node"
    scp_target="${node_user}@[${node_host}]"
    
    echo "  -> 正在分发文件至节点: $node (文件名标识: $sanitized_hostname)"

    # Create remote directory and copy files
    # shellcheck disable=SC2029
    ssh -n ${SSH_OPTIONS} "$ssh_target" "mkdir -p $REMOTE_WORKDIR"
    scp ${SSH_OPTIONS} "$WORKER_SCRIPT" "$IMAGE_LIST" "${scp_target}:${REMOTE_WORKDIR}/"
    echo "     文件已分发至 $node"
done
echo

# 2. Start tests concurrently on all nodes
echo "--- 2. 在所有节点上并发启动测试 ---"
for i in "${!REMOTE_NODES[@]}"; do
    node=${REMOTE_NODES[$i]}
    node_hostname=${node_hostnames[$i]}
    
    # 提取主机地址部分用于匹配
    node_host_part=$(echo "$node" | cut -d'@' -f2)

    # 检查当前节点是否需要启用性能监控
    profiling_enabled=false
    for profiling_node in "${NODES_WITH_PROFILING[@]}"; do
        if [[ "$node_host_part" == "$profiling_node" ]]; then
            profiling_enabled=true
            break
        fi
    done

    # 根据是否启用监控来构建远程命令和日志信息
    remote_command="cd $REMOTE_WORKDIR && sudo sh $WORKER_SCRIPT"
    if $profiling_enabled; then
        echo "     在 $node 上启动测试 (启用性能监控)... 日志将保存至 ${RESULTS_DIR}/${node_hostname}.log"
    else
        remote_command+=" --skip-monitoring"
        echo "     在 $node 上启动测试 (禁用性能监控)... 日志将保存至 ${RESULTS_DIR}/${node_hostname}.log"
    fi
    
    ssh_target="$node"
    # 执行远程命令
    ssh -n ${SSH_OPTIONS} "$ssh_target" "$remote_command" > "${RESULTS_DIR}/${node_hostname}.log" 2>&1 &
    pids+=($!)
done
echo

# 3. Wait for all remote jobs to complete
echo "--- 3. 等待所有远程任务完成 ---"
echo "所有远程任务已启动。 PIDs: ${pids[*]}"
FAILED_JOBS=0
for pid in "${pids[@]}"; do
    if ! wait "$pid"; then
        echo "警告: PID 为 $pid 的远程任务执行失败或以非零状态码退出。" >&2
        ((FAILED_JOBS++))
    fi
done

if [ "$FAILED_JOBS" -gt 0 ]; then
    echo "警告: 有 $FAILED_JOBS 个远程任务执行失败。请检查 '${RESULTS_DIR}' 中的日志文件。" >&2
else
    echo "所有远程任务均已成功完成。"
fi
echo

# 4. Collect profiling data
echo "--- 4. 收集性能分析数据 ---"
for i in "${!REMOTE_NODES[@]}"; do
    node=${REMOTE_NODES[$i]}
    local_hostname_for_results=${node_hostnames[$i]}
    local_profile_file_path="${RESULTS_DIR}/${local_hostname_for_results}_profiling_data.csv"

    if [ "$node" = "local" ]; then
        remote_actual_hostname=$(hostname)
        remote_profile_file_path="${REMOTE_WORKDIR}/${remote_actual_hostname}_profiling_data.csv"
        echo "  -> 正在从 local 收集数据..."
        if [ -f "$remote_profile_file_path" ]; then
            sudo cp "$remote_profile_file_path" "$local_profile_file_path"
            echo "     成功收集: ${local_hostname_for_results}_profiling_data.csv"
        else
            echo "     警告: 未能在本地找到性能数据文件 ($remote_profile_file_path)。" >&2
        fi
        continue
    fi

    ssh_target="$node"
    echo "  -> 正在从 $node 收集数据..."
    if remote_actual_hostname=$(ssh -n ${SSH_OPTIONS} "$ssh_target" "hostname" 2>/dev/null); then
        remote_profile_file_path="${REMOTE_WORKDIR}/${remote_actual_hostname}_profiling_data.csv"
        if ssh -n ${SSH_OPTIONS} "$ssh_target" "sudo cat '$remote_profile_file_path'" > "$local_profile_file_path" 2>/dev/null; then
            echo "     成功收集: ${local_hostname_for_results}_profiling_data.csv"
        else
            echo "     警告: 未能从 $node 读取到性能数据文件 ($remote_profile_file_path)。" >&2
        fi
    else
        echo "     警告: 未能从 $node 获取主机名。" >&2
    fi
done
echo

# 5. Clean up remote nodes
echo "--- 5. 清理远程节点上的临时文件 ---"
for node in "${REMOTE_NODES[@]}"; do
    if [ "$node" = "local" ]; then
        echo "  -> 正在清理节点: local"
        sudo rm -rf "$REMOTE_WORKDIR"
        continue
    fi

    ssh_target="$node"
    echo "  -> 正在清理节点: $node"
    # shellcheck disable=SC2029
    ssh -n ${SSH_OPTIONS} "$ssh_target" "sudo rm -rf $REMOTE_WORKDIR"
done
echo "远程清理完成。"
echo


# 6. Final Summary
echo "=================================================="
echo "           多节点镜像拉取测试完成"
echo "=================================================="
echo "所有日志和性能数据已保存在: $RESULTS_DIR"
echo
echo "--- 各节点结果摘要 ---"
for node_hostname in "${node_hostnames[@]}"; do
    echo
    echo "  [节点: $node_hostname]"
    # Use grep to extract summary lines and indent them
    grep -E '总计:|总耗时:|错误:|警告:' "${RESULTS_DIR}/${node_hostname}.log" | sed 's/^/    /' || echo "    未找到摘要信息，请检查完整日志。"
done
echo "=================================================="


# 7. Aggregate and Average Results
echo
echo "--- 7. 汇总和计算平均值 ---"
SUMMARY_FILE="${RESULTS_DIR}/_summary_report.txt"
echo "生成汇总报告: $SUMMARY_FILE"

{
    echo "=================================================="
    echo "           多节点镜像拉取测试汇总报告"
    echo "=================================================="
    echo "执行时间: $(date)"
    echo
} > "$SUMMARY_FILE"

total_duration=0
processed_nodes=0
total_images_pulled=0
total_images_failed=0

# For performance metrics
total_cpu_usage=0.0
total_mem_kb_usage=0
perf_data_nodes_count=0

for node_hostname in "${node_hostnames[@]}"; do
    log_file="${RESULTS_DIR}/${node_hostname}.log"
    {
        echo "--- 节点: $node_hostname ---"
        
        if [ ! -f "$log_file" ]; then
            echo "错误: 未找到日志文件。"
            echo
            continue
        fi
        
        # Extract Total time
        duration_line=$(grep '总耗时:' "$log_file" || echo "")
        if [ -n "$duration_line" ]; then
            duration=$(echo "$duration_line" | awk -F'[ :]+' '{print $2}')
            echo "总耗时: ${duration} 秒"
            total_duration=$((total_duration + duration))
            processed_nodes=$((processed_nodes + 1))
        else
            echo "总耗时: 未找到"
        fi
        
        # Extract counts
        counts_line=$(grep '总计:' "$log_file" || echo "")
        if [ -n "$counts_line" ]; then
            success_count=$(echo "$counts_line" | awk -F'[ ,:]+' '{print $4}')
            fail_count=$(echo "$counts_line" | awk -F'[ ,:]+' '{print $6}')
            echo "拉取统计: $counts_line"
            total_images_pulled=$((total_images_pulled + success_count))
            total_images_failed=$((total_images_failed + fail_count))
        else
            echo "拉取统计: 未找到"
        fi

        # Extract average performance metrics
        profile_csv="${RESULTS_DIR}/${node_hostname}_profiling_data.csv"
        if [ -f "$profile_csv" ] && [ -s "$profile_csv" ]; then
            # --- More Robust CSV Parsing ---
            # Reads header and converts to lowercase to find column indexes automatically,
            # making the matching case-insensitive and robust to naming variations.
            original_header=$(head -n 1 "$profile_csv")
            header_lower=$(echo "$original_header" | tr '[:upper:]' '[:lower:]')

            # Find column numbers by header name using broader, case-insensitive matching.
            cpu_col=$(echo "$header_lower" | awk -F, '{for(i=1;i<=NF;i++) if($i ~ /cpu/) print i; exit}')
            mem_col=$(echo "$header_lower" | awk -F, '{for(i=1;i<=NF;i++) if($i ~ /memory|mem_rss/) print i; exit}')

            # Check if all required columns were found in the header
            if [ -z "$cpu_col" ] || [ -z "$mem_col" ]; then
                echo "性能数据文件: 无法从表头确定所有必需的列: $original_header"
            else
                # AWK script now uses a simplified logic with the dynamically found column indexes.
                avg_stats=$(awk -F, -v ccol="$cpu_col" -v mcol="$mem_col" '
                    NR > 1 {
                        cpu_sum += $ccol;
                        mem_sum += $mcol;
                        count++;
                    }
                    END {
                        if (count > 0) {
                            printf "%.2f,%.0f", cpu_sum / count, mem_sum / count
                        }
                    }' "$profile_csv")

                if [ -n "$avg_stats" ]; then
                    avg_cpu=$(echo "$avg_stats" | cut -d, -f1)
                    avg_mem_kb=$(echo "$avg_stats" | cut -d, -f2)
                    
                    echo "平均CPU使用率: ${avg_cpu}%"
                    echo "平均内存使用量: ${avg_mem_kb} KB"

                    # Add to totals for overall average calculation
                    total_cpu_usage=$(awk -v total="$total_cpu_usage" -v current="$avg_cpu" 'BEGIN{printf "%.2f", total + current}')
                    total_mem_kb_usage=$((total_mem_kb_usage + avg_mem_kb))
                    perf_data_nodes_count=$((perf_data_nodes_count + 1))
                fi
            fi
        else
            echo "性能数据文件: 未找到或文件为空"
        fi
        echo
    } >> "$SUMMARY_FILE"
done

{
    echo "=================================================="
    echo "                  总体摘要"
    echo "=================================================="

    if [ "$processed_nodes" -gt 0 ]; then
        average_duration=$(awk "BEGIN {printf \"%.2f\", $total_duration / $processed_nodes}")
        echo "已处理日志节点数: $processed_nodes"
        echo "总成功拉取镜像数: $total_images_pulled"
        echo "总失败拉取镜像数: $total_images_failed"
        echo "所有节点平均耗时: ${average_duration} 秒"
    else
        echo "未能从任何节点处理日志数据。"
    fi
    
    echo
    echo "--- 整体平均性能 ---"
    if [ "$perf_data_nodes_count" -gt 0 ]; then
        # Calculate overall average performance metrics
        avg_cpu_overall=$(awk -v total="$total_cpu_usage" -v count="$perf_data_nodes_count" 'BEGIN{printf "%.2f", total / count}')
        avg_mem_kb_overall=$((total_mem_kb_usage / perf_data_nodes_count))

        echo "已处理性能数据节点数: $perf_data_nodes_count"
        echo "平均CPU使用率: ${avg_cpu_overall}%"
        echo "平均内存使用量: ${avg_mem_kb_overall} KB"
    else
        echo "未能从任何节点处理性能数据。"
    fi

    echo "=================================================="
} >> "$SUMMARY_FILE"

echo "汇总报告已生成完毕。"
echo

exit 0