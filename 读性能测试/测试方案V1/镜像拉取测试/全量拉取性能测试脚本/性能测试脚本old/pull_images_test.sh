#!/bin/bash

# --- 配置 ---
# 镜像列表文件
IMAGE_LIST_FILE="image_list.txt"

# 检查是否需要跳过性能监控
ENABLE_MONITORING=true
if [ "$1" == "--skip-monitoring" ]; then
    ENABLE_MONITORING=false
fi

# 获取主机名
HOSTNAME=$(hostname)
# 监控数据输出文件
PROFILE_FILE="${HOSTNAME}_profiling_data.csv"
# 监控采样间隔（秒）
SAMPLE_INTERVAL=5
# 最大并发任务数，可根据节点性能调整
MAX_CONCURRENT_JOBS=10000


# --- 依赖检查 ---
# 检查 docker 命令
if ! command -v docker &> /dev/null; then
    echo "错误: 未找到 'docker' 命令。请先安装 Docker。"
    exit 1
fi
# 检查 sysstat 包中的 iostat 和 mpstat 命令
if ! command -v iostat &> /dev/null || ! command -v mpstat &> /dev/null; then
    echo "错误: 未找到 'iostat' 或 'mpstat' 命令。请先安装 'sysstat' 包。"
    exit 1
fi
# 检查镜像列表文件是否存在
if [ ! -f "$IMAGE_LIST_FILE" ]; then
    echo "错误: 镜像列表文件未找到: $IMAGE_LIST_FILE"
    exit 1
fi

TARGET_PID=""
if [ "$ENABLE_MONITORING" = true ]; then
    # 查找目标进程 "kubeall-swr" 的PID
    TARGET_PROCESS_PATTERN="kubeall-swr"
    # 根据您的要求，简化PID的查找方式。
    # 使用pgrep，并用head -n 1确保即使找到多个也只取第一个。
    TARGET_PID=$(pgrep "$TARGET_PROCESS_PATTERN" | head -n 1)

    if [ -z "$TARGET_PID" ]; then
        echo "错误: 无法找到目标进程 '$TARGET_PROCESS_PATTERN' 的PID。请确保该进程正在运行。"
        exit 1
    fi
fi


# --- 监控功能 ---
PROFILING_PID=

start_profiling() {
    echo "开始系统资源监控，数据将保存至: $PROFILE_FILE"
    echo "Timestamp,CPU_Usage(%),Memory_RSS(KB)" > "$PROFILE_FILE"

    (
        local sleep_time=$((SAMPLE_INTERVAL > 2 ? SAMPLE_INTERVAL - 2 : 1))
        while true; do
            TIMESTAMP=$(date +"%Y-%m-%d %H:%M:%S")
            
            CPU_USAGE=$(mpstat 1 1 | awk '/Average:/ {print 100-$NF}')
            # 使用 pidstat 监控目标进程的 RSS (物理内存使用量, KB)
            MEM_RSS_KB=$(pidstat -r -p "$TARGET_PID" 1 1 | tail -n 1 | awk '{print $7}')
            
            echo "$TIMESTAMP,$CPU_USAGE,${MEM_RSS_KB:-0}" >> "$PROFILE_FILE"
            sleep "$sleep_time"
        done
    ) &
    PROFILING_PID=$!
    echo "监控进程已启动，PID: $PROFILING_PID (Target Process Pattern: '$TARGET_PROCESS_PATTERN', Found PID: $TARGET_PID)"
}

stop_profiling() {
    if [ -n "$PROFILING_PID" ]; then
        echo "正在停止系统资源监控 (PID: $PROFILING_PID)..."
        # 使用 kill 命令正常终止后台进程
        # kill 的 -s 0 参数可以检查进程是否存在
        if kill -s 0 "$PROFILING_PID" 2>/dev/null; then
            kill "$PROFILING_PID"
            echo "监控已停止。"
        fi
    fi
}

# 设置 trap，确保脚本退出时（正常或异常）都能执行 stop_profiling 函数
# 仅在启用监控时设置
if [ "$ENABLE_MONITORING" = true ]; then
    trap stop_profiling EXIT
fi


# --- 主逻辑 ---
echo "测试脚本开始执行..."
# 启动监控
if [ "$ENABLE_MONITORING" = true ]; then
    echo "性能监控已启用。"
    start_profiling
else
    echo "性能监控已禁用。"
fi

# 过滤掉空行，获取准确的镜像总数
TOTAL_IMAGES=$(grep -c . "$IMAGE_LIST_FILE")

# 1. 并发清理镜像
echo "正在并发清理 ${TOTAL_IMAGES} 个本地镜像（如果存在）..."
job_count=0
pids_to_wait_for=()
while IFS= read -r image; do
    [ -z "$image" ] && continue # 跳过空行

    docker rmi -f "$image" > /dev/null 2>&1 &
    pids_to_wait_for+=($!) # 记录 docker rmi 任务的PID

    job_count=$((job_count + 1))
    if [ "$job_count" -ge "$MAX_CONCURRENT_JOBS" ]; then
        wait "${pids_to_wait_for[@]}" # 只等待我们记录的PID
        pids_to_wait_for=() # 清空数组以备下一批
        job_count=0
    fi
done < "$IMAGE_LIST_FILE"

# 等待所有剩余的后台清理任务完成
if [ ${#pids_to_wait_for[@]} -gt 0 ]; then
    wait "${pids_to_wait_for[@]}"
fi
echo "镜像清理完成。"
echo

# 2. 并发拉取镜像
echo "开始从 $IMAGE_LIST_FILE 并发拉取 ${TOTAL_IMAGES} 个镜像..."
PULL_START_TIME=$(date +%s)
TMP_LOG_DIR=$(mktemp -d)

job_count=0
pids_to_wait_for=()
while IFS= read -r image; do
    [ -z "$image" ] && continue

    # 为日志文件名创建一个安全的版本
    sanitized_name=$(echo "$image" | tr '/:' '_')
    log_file="${TMP_LOG_DIR}/${sanitized_name}.log"
    
    docker pull "$image" > "$log_file" 2>&1 &
    pids_to_wait_for+=($!) # 记录 docker pull 任务的PID
    
    job_count=$((job_count + 1))
    if [ "$job_count" -ge "$MAX_CONCURRENT_JOBS" ]; then
        wait "${pids_to_wait_for[@]}" # 只等待我们记录的PID
        pids_to_wait_for=() # 清空数组以备下一批
        job_count=0
    fi
done < "$IMAGE_LIST_FILE"

# 等待最后一批（或唯一一批）拉取任务完成
if [ ${#pids_to_wait_for[@]} -gt 0 ]; then
    wait "${pids_to_wait_for[@]}"
fi
echo "所有拉取任务已完成，正在检查结果..."

# 3. 检查结果
PULLED_SUCCESS=0
PULLED_FAILED=0
while IFS= read -r image; do
    [ -z "$image" ] && continue

    sanitized_name=$(echo "$image" | tr '/:' '_')
    log_file="${TMP_LOG_DIR}/${sanitized_name}.log"
    
    # 检查成功标志
    if grep -q -E "Status: Downloaded newer image|Image is up to date" "$log_file"; then
        PULLED_SUCCESS=$((PULLED_SUCCESS + 1))
    else
        echo "--------------------------------------------------"
        echo "错误: 拉取镜像失败: $image"
        # 为了简洁，只显示日志的最后几行
        tail -n 3 "$log_file"
        echo "--------------------------------------------------"
        PULLED_FAILED=$((PULLED_FAILED + 1))
    fi
done < "$IMAGE_LIST_FILE"

# 清理临时日志目录
rm -rf "$TMP_LOG_DIR"

PULL_END_TIME=$(date +%s)
TOTAL_PULL_TIME=$((PULL_END_TIME - PULL_START_TIME))
echo "=================================================="
echo "所有镜像拉取完成！"
echo "总计: $TOTAL_IMAGES, 成功: $PULLED_SUCCESS, 失败: $PULLED_FAILED"
echo "总耗时: $TOTAL_PULL_TIME 秒。"

# 脚本正常结束，trap 会自动调用 stop_profiling
exit 0