{"database_user_map_list": [{"cp_name": "cf-pnode", "db_name": "pnode", "db_user": "pnode", "db_password": ""}, {"cp_name": "cf-pdman", "db_name": "pdman", "db_user": "pdman", "db_password": ""}, {"cp_name": "cf-pdeploy", "db_name": "pdeploy", "db_user": "pdeploy", "db_password": ""}, {"cp_name": "cf-pcluster", "db_name": "pcluster", "db_user": "pcluster", "db_password": ""}, {"cp_name": "posd", "db_name": "posd", "db_user": "posd", "db_password": ""}, {"cp_name": "vnm", "db_name": "pvnm", "db_user": "pvnm", "db_password": ""}, {"cp_name": "inetmanager", "db_name": "inetmanager", "db_user": "inetmanager", "db_password": ""}, {"cp_name": "utm", "db_name": "utm", "db_user": "utm", "db_password": ""}, {"cp_name": "blockstorage", "db_name": "blockstorage", "db_user": "blockstorage", "db_password": ""}, {"cp_name": "zenap_modeldesign", "db_name": "modeldesign", "db_user": "modeldesign", "db_password": ""}, {"cp_name": "opsapiserver", "db_name": "ops_opsapiserver", "db_user": "ops_opsapiserver", "db_password": ""}, {"cp_name": "pmc", "db_name": "ops_pmc", "db_user": "ops_pmc", "db_password": ""}, {"cp_name": "pronoea", "db_name": "ops_pronoea", "db_user": "ops_pronoea", "db_password": ""}, {"cp_name": "prometheus", "db_name": "ops_pronoea", "db_user": "ops_pronoea", "db_password": ""}, {"cp_name": "sqm", "db_name": "ops_sqm", "db_user": "ops_sqm", "db_password": ""}, {"cp_name": "op-ops-log-logapiserver-bp", "db_name": "paas_ops", "db_user": "paas_ops", "db_password": ""}, {"cp_name": "op-ops-log-collectormgt-bp", "db_name": "paas_ops", "db_user": "paas_ops", "db_password": ""}, {"cp_name": "opsapiserver", "db_name": "paas_ops", "db_user": "paas_ops", "db_password": ""}, {"cp_name": "goindexer", "db_name": "paas_ops", "db_user": "paas_ops", "db_password": ""}, {"cp_name": "fm_mgt", "db_name": "paas_ops", "db_user": "paas_ops", "db_password": ""}, {"cp_name": "zart", "db_name": "pswr", "db_user": "pswr", "db_password": ""}, {"cp_name": "br", "db_name": "br<PERSON>r", "db_user": "br<PERSON>r", "db_password": ""}, {"cp_name": "eventmgt", "db_name": "paas_ops", "db_user": "paas_ops", "db_password": ""}, {"cp_name": "cf-csm", "db_name": "pvcsm", "db_user": "pvcsm", "db_password": ""}, {"cp_name": "cf-csm-api", "db_name": "pvcsm", "db_user": "pvcsm", "db_password": ""}, {"cp_name": "cf-vnpm", "db_name": "pvdeploy", "db_user": "pvdeploy", "db_password": ""}, {"cp_name": "cf-vnpm-api", "db_name": "pvdeploy", "db_user": "pvdeploy", "db_password": ""}, {"cp_name": "cf-nbm", "db_name": "pvnbm", "db_user": "pvnbm", "db_password": ""}, {"cp_name": "eps", "db_name": "paas_ops", "db_user": "paas_ops", "db_password": ""}, {"cp_name": "<PERSON><PERSON><PERSON>", "db_name": "paas_ops", "db_user": "paas_ops", "db_password": ""}, {"cp_name": "storage", "db_name": "storage", "db_user": "storage", "db_password": ""}, {"cp_name": "zenap_cos", "db_name": "cos", "db_user": "cos", "db_password": ""}, {"cp_name": "zenap_kms", "db_name": "kms", "db_user": "kms", "db_password": ""}]}