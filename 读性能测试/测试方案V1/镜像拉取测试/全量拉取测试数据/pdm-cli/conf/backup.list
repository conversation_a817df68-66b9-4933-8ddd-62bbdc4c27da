{"mysql": [{"component": "cf-pcluster", "db2tables": {"pcluster": ""}}, {"component": "cf-pdman", "db2tables": {"pdman": ""}}, {"component": "cf-pdeploy", "db2tables": {"pdeploy": ""}}, {"component": "cf-pnode", "db2tables": {"pnode": ""}}, {"component": "cf-csm", "db2tables": {"pvcsm": ""}}, {"component": "cf-nbm", "db2tables": {"pvnbm": ""}}, {"component": "cf-vnpm", "db2tables": {"pvdeploy": ""}}, {"component": "op-ops-log-collectormgt-bp", "db2tables": {"paas_ops": ""}}, {"component": "op-ops-log-logapiserver-bp", "db2tables": {"paas_ops": ""}}, {"component": "pronoea", "db2tables": {"ops_pronoea": ""}}, {"component": "pmc", "db2tables": {"ops_pmc": ""}}, {"component": "sqm", "db2tables": {"ops_sqm": ""}}, {"component": "opsapiserver", "db2tables": {"ops_opsapiserver": ""}}, {"component": "vnm", "db2tables": {"pvnm": ""}}, {"component": "inetmanager", "db2tables": {"inetmanager": ""}}, {"component": "op-inet-manager-xlbmanager", "db2tables": {"xlb": ""}}, {"component": "storage", "db2tables": {"storage": ""}}, {"component": "utm", "db2tables": {"utm": ""}}, {"component": "manager-cert", "db2tables": {"manager_cert": ""}}, {"component": "posd", "db2tables": {"posd": ""}}, {"component": "zenap_modeldesign", "db2tables": {"modeldesign": ""}}, {"component": "br", "db2tables": {"brserver": ""}}, {"component": "blockstorage", "db2tables": {"blockstorage": ""}}, {"component": "nwmaster", "db2tables": {"knitter": ""}}, {"component": "nwmonitor", "db2tables": {"knitter": ""}}, {"component": "cnrm-manager", "db2tables": {"cnrm": ""}}, {"component": "eps", "db2tables": {"eps": ""}}, {"component": "op-confcenter-pconf", "db2tables": {"pconf": ""}}, {"component": "vnf-lcm-bp", "db2tables": {"vnf_lcm": ""}}, {"component": "op-security-project-bp", "db2tables": {"utm": ""}}, {"component": "op-security-certcenter-bp", "db2tables": {"manager_cert": ""}}, {"component": "op-asd-liteappm-bp", "db2tables": {"liteappm": ""}}, {"component": "op-psd-keystone-bp", "db2tables": {"keystone": ""}}, {"component": "op-asm-operator", "db2tables": {"asmoperator": ""}}, {"component": "op-storage-sp_nfs_agent", "db2tables": {"nfscluster": ""}}, {"component": "zart", "db2tables": {"pswr": ""}}, {"component": "op-asd-swr", "db2tables": {"pswr": ""}}, {"component": "oes_dexcloud_msg_apim-bp", "db2tables": {"apim": ""}}, {"component": "op-asd-dragonfly", "db2tables": {"swrp2p": ""}}, {"component": "op-br-br-bp", "db2tables": {"freezer": ""}}]}