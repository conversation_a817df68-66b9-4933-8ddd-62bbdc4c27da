[DEFAULT]
backend = rabbit
my_ip = {{ cinder_my_ip }}
# enabled_backends A list of backend names to use. These backend names should be backed by a
# unique [CONFIG] group with its options (list value)
enabled_backends =
auth_strategy = noauth
osapi_volume_workers = 3

# Seconds to wait for a response from a call. (integer value)
#rpc_response_timeout=60
rpc_response_timeout=300

#
# Options defined in cinder.quota
#
 
# Number of volumes allowed per project (integer value)
quota_volumes=-1
 
# Number of volume snapshots allowed per project (integer
# value)
quota_snapshots=-1
 
# Number of consistencygroups allowed per project (integer
# value)
quota_consistencygroups=-1
 
# Total amount of storage, in gigabytes, allowed for volumes
# and snapshots per project (integer value)
quota_gigabytes=-1
 
# Number of volume backups allowed per project (integer value)
#quota_backups=-1
 
# Total amount of storage, in gigabytes, allowed for backups
# per project (integer value)
quota_backup_gigabytes=-1
 
# Number of seconds until a reservation expires (integer
# value)
#reservation_expire=86400
 
# Count of reservations until usage is refreshed (integer
# value)
#until_refresh=0
 
# Number of seconds between subsequent usage refreshes
# (integer value)
#max_age=0
 
# Default driver to use for quota checks (string value)
#quota_driver=cinder.quota.DbQuotaDriver
 
# Enables or disables use of default quota class with default
# quota. (boolean value)
#use_default_quota_class=true

[BACKEND]

[BRCD_FABRIC_EXAMPLE]

[CISCO_FABRIC_EXAMPLE]

[COORDINATION]

[FC-ZONE-MANAGER]

[KEYMGR]

[cors]

[cors.subdomain]

[database]
connection =  {{ db_connection }}

[keystone_authtoken]

[matchmaker_redis]

[oslo_concurrency]
lock_path = /var/lib/cinder/tmp

[oslo_messaging_amqp]

[oslo_messaging_notifications]

[oslo_messaging_rabbit]
rabbit_host = {{ rabbit_ip }}
rabbit_port = {{ ubu_rabbit_port }}
rabbit_userid = cloud
rabbit_password =

[oslo_middleware]

[oslo_policy]

[oslo_reports]

[oslo_versionedobjects]

[ssl]

[KS3200_IPSAN]
volume_driver=cinder.volume.drivers.zte.zte_ks.ZteISCSIDriver
cinder_zte_conf_file=/etc/cinder/cinder_zte_conf.xml
volume_backend_name=
use_multipath_for_image_xfer=true

[KU5200_FCSAN]
volume_driver=cinder.volume.drivers.zte.zte_ks.ZteFCDriver
cinder_zte_conf_file=/etc/cinder/cinder_zte_conf.xml
volume_backend_name=
use_multipath_for_image_xfer=true

[EMC_VNXe3200]
storage_protocol = iSCSI   # FC or iSCSI
storage_pool_names =
san_ip =
san_login =
san_password =
volume_backend_name =
volume_driver = cinder.volume.drivers.dell_emc.emc_vnxe.EMCVNXeDriver

[IBM_STORWIZE5000]
volume_driver = cinder.volume.drivers.ibm.storwize_svc.storwize_svc_fc.StorwizeSVCFCDriver
san_ip =
san_ssh_port = 22
san_login =
san_password =
storwize_svc_volpool_name =
volume_backend_name =
storwize_svc_iscsi_chap_enabled = False

[IBM_STORWIZE3700]
volume_driver = cinder.volume.drivers.ibm.storwize_svc.storwize_svc_fc.StorwizeSVCFCDriver
san_ip =
san_ssh_port = 22
san_login =
san_password =
storwize_svc_volpool_name =
volume_backend_name =
storwize_svc_iscsi_chap_enabled = False

[KS10000_IPSAN]
volume_driver = cinder.volume.drivers.cephiscsi.ks10000_driver.Ks10000ISCSIDriver
cinder_zte_ks10000_conf_file = /etc/cinder/cinder_zte_ks10000_conf_file.xml
volume_backend_name =
use_multipath_for_image_xfer = true
