[{"file_name": "check_pinit_conf", "interface": "check_pinit_conf", "para": ["/etc/pdm/conf/paas.conf", "/etc/pdm/conf/conf.json", "/root/nodes"]}, {"file_name": "check_pinit_paas", "interface": "check_pinit_paas", "para": ["/etc/pdm/conf/paas.conf", "/etc/pdm/conf/conf.json", "/root/nodes"]}, {"file_name": "check_nodepools", "interface": "NodePoolsCheck", "para": ["/etc/pdm/conf/conf.json", "/etc/pdm/conf/paas.conf", "/root/nodes"]}, {"file_name": "check_nodes", "interface": "check_node_network", "para": ["/etc/pdm/conf/conf.json", "/etc/pdm/conf/paas.conf", "/root/nodes"]}, {"file_name": "check_clusterworker", "interface": "clusterCheck", "para": ["/etc/pdm/conf/conf.json", "/root/nodes", "/etc/pdm/conf/paas.conf"]}, {"file_name": "check_clusters", "interface": "storageConfCheck", "para": ["/etc/pdm/conf/conf.json", "/etc/pdm/conf/storage.json"]}, {"file_name": "check_k8s", "interface": "check_k8s", "para": ["/etc/pdm/conf/conf.json", "/etc/pdm/conf/paas.conf"]}, {"file_name": "check_docker_extra_conf", "interface": "check_docker_extra_conf", "para": ["/etc/pdm/conf/os_sysconfig.conf"]}, {"file_name": "vnm_cfg_check", "interface": "vnm_cfg_check", "para": []}, {"file_name": "xlb_config_check", "interface": "xlb_instance_config_check", "para": []}, {"file_name": "loadbalance_config_check", "interface": "loadbalancer_global_check", "para": []}, {"file_name": "check_nw_conf", "interface": "check_nw_conf", "para": ["/etc/pdm/conf/paas.conf", "/etc/pdm/conf/conf.json", "/root/nodes", "/etc/pdm/conf/vnm_network.conf", "/etc/pdm/conf/knitter.json"]}, {"file_name": "check_network", "interface": "check_network", "para": ["/etc/pdm/conf/conf.json", "/etc/pdm/conf/paas.conf", "/etc/network/Inet_deploy.conf.tmpl", "/etc/pdm/netconfig_templates", "/etc/pdm/conf/vnm_network.conf", "/root/nodes"]}, {"file_name": "check_blockstorage", "interface": "cinderConfCheck", "para": ["/etc/pdm/conf/conf.json", "/etc/pdm/conf/blockstorage/cinder_conf/cinder.conf"]}, {"file_name": "check_cnrm_conf", "interface": "check_cnrm_conf", "para": ["/etc/pdm/conf/cnrm.json", "/etc/pdm/conf/conf.json"]}, {"file_name": "check_paas_dns_servers", "interface": "check_dns", "para": ["/etc/pdm/conf/paas.conf"]}, {"file_name": "check_csi", "interface": "checkCSIConf", "para": ["/etc/pdm/conf/csi.conf"]}, {"file_name": "check_nfs_sp", "interface": "checkExternalConf", "para": ["/etc/pdm/conf/conf.json", "/etc/pdm/conf/external_storage.json"]}, {"file_name": "check_tcf_csidisk", "interface": "checkCsiDiskConf", "para": ["/etc/pdm/conf/conf.json", "/etc/pdm/conf/paas.conf", "/etc/pdm/conf/tcf_csidisk.conf", "/etc/pdm/conf/dynamic_blockstorage_pool.conf"]}, {"file_name": "check_dynamicpool", "interface": "checkDynamicPool", "para": []}, {"file_name": "check_csi_local_storage", "interface": "check_csi_local_storage_conf", "para": ["/etc/pdm/conf/paas.conf", "/etc/pdm/conf/tcf_csi_local_storage.conf"]}]