{"global": [{"tcf_scale_id": 1234, "target_cim_scale": "scale_1"}], "region": {"scenariotype": "IaaS", "region_detail": ""}, "iaas": {"url": "http://************:5000/v2.0/", "tenantName": "paas1", "username": "paas1", "password": ""}, "nodepools": [{"name": "default-np", "min_num": 3, "max_num": 10, "step": 1, "upper_limit": 100, "lower_limit": 0, "hostname": "", "timezone": "", "vm_conf": {"flavor_name": "flavor_node", "image_name": "node", "boot_mode": "", "snapshot_id": "", "volume_size": "", "volume_az": "", "volume_type": "", "available_zone": ""}, "storage_info": [], "node_identities": []}], "roles": [{"name": "elk", "num": 3, "nodepool": "default-np"}, {"name": "glusterfs_server", "num": 3, "nodepool": "default-np"}, {"name": "nfs_server", "num": 0, "nodepool": "default-np"}, {"name": "soft-repo", "num": 3, "nodepool": "default-np"}, {"name": "monitor_pm", "num": 3, "nodepool": "default-np"}, {"name": "nfs_server_sp", "num": 0, "nodepool": "default-np"}], "roles_relation": [], "clusters": [{"name": "cluster1", "type": "kubernetes", "addon_plugin": "off", "cluster_config": {"kube_service_addresses": "", "reserved_res_prf": [{"roles": {"minion": {"cpu": 1, "mem": 1}, "allinone": {"cpu": 1, "mem": 1}}}]}, "nodes": [{"roles": ["master"], "node_num": 1, "node_source": {"np_name": "default-np", "node_role": []}, "labels": {}}, {"roles": ["minion"], "node_num": 3, "node_source": {"np_name": "default-np", "node_role": []}, "node_config": {"app_exclusive_count": 0, "hugepage_2m_total_size": 0, "hugepage_1g_total_size": 0, "cpu_allocation": []}, "labels": {}}]}], "networks": [{"name": "control", "public": true, "desc": "PaaS control network for tcfs", "gateway": "**********", "cidr": "**********/16", "cidr_ipv6": "", "gateway_ipv6": "", "ip_stack": ""}, {"name": "media", "public": true, "desc": "PaaS media network for tcfs", "gateway": "**********", "cidr": "**********/16", "cidr_ipv6": "", "gateway_ipv6": "", "ip_stack": ""}], "overlay_networks": [{"name": "control", "public": true, "desc": "PaaS control network for tcfs", "gateway": "**********", "cidr": "**********/16", "cidr_ipv6": "", "gateway_ipv6": "", "ip_stack": ""}, {"name": "media", "public": true, "desc": "PaaS media network for tcfs", "gateway": "**********", "cidr": "**********/16", "cidr_ipv6": "", "gateway_ipv6": "", "ip_stack": ""}, {"name": "net_api", "public": true, "desc": "PaaS network for pod", "gateway": "**********", "cidr": "**********/16"}], "pim": {"pim_address": "***********", "pim_port": "2043"}, "general_mode": {"api_ip": ""}, "bm_network": {"default_physnet": "physnet1", "paas_controller_ip": [{"api_ip": "**********", "admin_ip": "**********", "ipmi_ip": ""}], "api_vlan_id": "1016", "net_api": {"subnet": [{"name": "subnet_api", "enable_dhcp": true, "gateway_ip": "**********", "allocation_pools": [{"start": "**********", "end": "*************"}], "ip_version": 4, "cidr": "**********/16"}, {"name": "subnet_api_v6", "enable_dhcp": true, "gateway_ip": "3ffe:ffff:0:f101::1", "allocation_pools": [{"start": "3ffe:ffff:0:f101::100", "end": "3ffe:ffff:0:f101::1ff"}], "ip_version": 6, "cidr": "3ffe:ffff:0:f101::/112"}], "provider:physical_network": "physnet1"}, "net_iapi": {"subnet": [{"name": "subnet_iapi", "enable_dhcp": false, "gateway_ip": "************", "allocation_pools": [{"start": "***********", "end": "***********"}], "ip_version": 4, "cidr": "**********/24", "action": "register"}, {"name": "subnet_iapi_v6", "enable_dhcp": false, "gateway_ip": "4ffe:ffff:0:f101::1", "allocation_pools": [{"start": "4ffe:ffff:0:f101::10e", "end": "4ffe:ffff:0:f101::10f"}], "ip_version": 6, "cidr": "4ffe:ffff:0:f101::/112", "action": "register"}], "provider:physical_network": "physnet0", "provider:network_type": "flat", "provider:segmentation_id": ""}, "net_ctrl": {"provider_network": {"public": true, "name": "control", "desc": "PaaS control network for tcfs", "gateway": "**********", "cidr": "**********/16", "cidr_ipv6": "", "gateway_ipv6": "", "ip_stack": "ipv4", "provider:network_type": "vlan", "provider:physical_network": "physnet2", "provider:segmentation_id": "1021"}}, "net_media": {"provider_network": {"public": true, "name": "media", "desc": "PaaS media network for tcfs", "gateway": "**********", "cidr": "**********/16", "cidr_ipv6": "", "gateway_ipv6": "", "ip_stack": "ipv4", "provider:network_type": "vlan", "provider:physical_network": "physnet2", "provider:segmentation_id": "1022"}}}}