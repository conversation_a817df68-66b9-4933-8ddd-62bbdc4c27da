[DEFAULT]
# enabled_csi_driver A list of ceph cluster names to use. These ceph cluster names should be backed by a
# unique [CONFIG] group with its options (list value)
enabled_csi_driver =

# distinguish k8s native scenarios or pict scenarios
is_paas=true

# the following fields list ceph cluster informations with key values
# clusterID is the value of fsid in /etc/ceph/ceph.conf
# monitors are the value of mon_host in /etc/ceph/ceph.conf
# adminKey is the value of key in /etc/ceph/ceph.client.admin.keyring
# fsName is the value of commanding ceph fs ls
# pool is the value of commanding ceph osd lspools
# the value of ceph_manage_ip is the network plane of ZTE_Management_NET IP
# the default value of ceph_manage_port is 34100

[ceph]
cluster_type = ceph
enable_fs = true
enable_rbd = false
ceph_manage_ip =
ceph_manage_port = 34100
username =
password =
clusterID=
monitors=
adminID = admin
adminKey =
fsName =
fspool =
fsprovisioner = cephfs.csi.ceph.com
fsmounter = fuse
imageFormat = 2
imageFeatures = layering
rbdmounter =
rbdpool =
rbdprovisioner = rbd.csi.ceph.com
