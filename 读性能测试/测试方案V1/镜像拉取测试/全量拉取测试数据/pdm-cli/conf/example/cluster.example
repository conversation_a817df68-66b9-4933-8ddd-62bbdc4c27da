{"clusters": [{"name": "cluster1", "type": "kubernetes", "addon_plugin": "off", "cluster_config": {"kube_service_addresses": "", "reserved_res_switch": "off"}, "nodes": [{"roles": ["master"], "node_num": 1, "node_source": {"np_name": "default-np", "node_role": []}, "labels": {}}, {"roles": ["minion"], "node_num": 1, "node_source": {"np_name": "default-np", "node_role": []}, "node_config": {"app_exclusive_count": 0, "hugepage_2m_total_size": 0, "hugepage_1g_total_size": 0, "cpu_allocation": []}, "labels": {}}]}]}