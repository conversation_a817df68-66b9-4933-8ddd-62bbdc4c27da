{"region": {"scenariotype": "baremetal", "region_detail": ""}, "iaas": {"url": "http://10.62.97.112:5000/v2.0/", "tenantName": "paas1", "username": "paas1", "password": ""}, "roles": [{"name": "elk", "num": 1, "nodepool": "default-np"}, {"name": "glusterfs_server", "num": 1, "nodepool": "default-np"}, {"name": "cf-srepo", "num": 1, "nodepool": "default-np"}, {"name": "cassandra", "num": 0, "nodepool": "default-np"}, {"name": "com_srv_share", "num": 1, "nodepool": "default-np"}, {"name": "com_srv_alone", "num": 1, "nodepool": "default-np"}], "roles_relation": [], "clusters": [{"name": "cluster1", "type": "kubernetes", "nodes": [{"roles": ["master", "minion"], "node_num": 1, "node_source": {"np_name": "", "node_role": ["paas_controller"]}, "node_config": {"app_exclusive_count": 0, "hugepage_2m_total_size": 0}, "labels": {"dpdk": "true"}}]}], "bm_network": {"api_vlan_id": "1016", "mgt_vlan_id": "1017", "net_api": {"subnet": {"name": "subnet001", "enable_dhcp": true, "gateway_ip": "**********", "allocation_pools": [{"start": "*********", "end": "*********"}], "ip_version": 4, "cidr": "********/24"}}, "net_mgt": {"subnet": {"name": "subnet001", "enable_dhcp": true, "gateway_ip": null, "allocation_pools": [{"start": "*********", "end": "*********"}], "ip_version": 4, "cidr": "********/24"}}, "net_iapi": {"subnet": {"name": "sub_iapi", "enable_dhcp": false, "gateway_ip": "************", "allocation_pools": [{"start": "***********", "end": "***********"}], "ip_version": 4, "cidr": "**********/24"}}, "net_ctrl": {"provider_network": {"public": true, "name": "control", "gateway": "**********", "cidr": "**********/16", "provider:network_type": "vlan", "provider:physical_network": "physnet2", "provider:segmentation_id": "1021"}}, "net_media": {"provider_network": {"public": true, "name": "media", "gateway": "**********", "cidr": "**********/16", "provider:network_type": "vlan", "provider:physical_network": "physnet2", "provider:segmentation_id": "1022"}}}}