{"region": {"scenariotype": "IaaS", "region_detail": ""}, "iaas": {"url": "http://************:5000/v2.0/", "tenantName": "paas1", "username": "paas1", "password": ""}, "nodepools": [{"name": "default-np", "min_num": 4, "max_num": 10, "step": 1, "upper_limit": 100, "lower_limit": 0, "hostname": "", "timezone": "", "vm_conf": {"flavor_name": "flavor_node", "image_name": "node", "boot_mode": "", "snapshot_id": "", "volume_size": "", "volume_az": "", "volume_type": "", "available_zone": ""}, "storage_info": [{"volume_size": null, "volume_type": null, "volume_az": null, "guestformat": null, "mountpoint": null}]}], "roles": [{"name": "elk", "num": 1, "nodepool": "default-np"}, {"name": "glusterfs_server", "num": 2, "nodepool": "default-np"}, {"name": "cf-srepo", "num": 1, "nodepool": "default-np"}, {"name": "cassandra", "num": 0, "nodepool": "default-np"}, {"name": "com_srv_share", "num": 1, "nodepool": "default-np"}, {"name": "com_srv_alone", "num": 1, "nodepool": "default-np"}], "roles_relation": [], "clusters": [{"name": "cluster1", "type": "kubernetes", "nodes": [{"roles": ["master"], "node_num": 1, "node_source": {"np_name": "default-np", "node_role": []}, "labels": {}}, {"roles": ["minion"], "node_num": 1, "node_source": {"np_name": "default-np", "node_role": []}, "node_config": {"app_exclusive_count": 0, "hugepage_2m_total_size": 0}, "labels": {"dpdk": "true", "privilege": "true"}}]}], "networks": [{"name": "control", "public": true, "gateway": "172.28.0.1", "cidr": "172.28.0.0/16"}, {"name": "media", "public": true, "gateway": "172.29.0.1", "cidr": "172.29.0.0/16"}]}