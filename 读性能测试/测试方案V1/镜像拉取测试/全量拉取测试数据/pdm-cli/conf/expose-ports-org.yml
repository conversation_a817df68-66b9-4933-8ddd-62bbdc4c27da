---
### 可关闭端口， 用户根据需要选择关闭 ####
- registry_authserver_port: 2510
  proto: TCP
  msbservice: true
- provider_35507_port: 35507
  proto: TCP  
  msbservice: true
- provider_vim_port: 9131
  proto: TCP
  msbservice: true
- provider_cim_port: 9151
  proto: TCP
  msbservice: true
- netinsight_analyzer_listen_port: 8082
  proto: TCP
  msbservice: true
- provider_director_port: 44300
  proto: TCP
  msbservice: true
- provider_directortwa_port: 44301
  proto: TCP
  msbservice: true

### 重要端口，不建议关闭， 关闭会用重要功能使用####
- fm_mgt_snmp_port: 2511
  proto: UDP
- nwnode_vxlan_port: 6789
  proto: UDP
- inetmanager_port:
  proto: TCP
  msbservice: true
- rpc_mount_port: 665-1021
  proto: TCP/UDP
- zenap_msb_router_port: 80
  proto: TCP
  msbservice: true
- zenap_msb_router_https_port: 443  
  proto: TCP
  msbservice: true
- zenap_msb_router_https_mutual_auth_port: 1443  
  proto: TCP
  msbservice: true
- zenap_msb_router_om_port: 80  
  proto: TCP  
  msbservice: true
- zenap_msb_router_om_https_port: 443  
  proto: TCP
  msbservice: true
- zenap_msb_router_om_https_mutual_auth_port: 1443  
  proto: TCP
  msbservice: true
- zenap_cos_port: 2480  
  proto: TCP
- zenap_cos_https_port: 2443  
  proto: TCP
- kube_master_api_port: 6443
  proto: TCP
- master_cluster_port:
  proto: TCP
- k8s_external_port: 2601-2605
  proto: TCP
- provider_kafka_ssl_port: 9093
  proto: TCP
- tecs_itools_port: 8001
  proto: TCP
- vnf_lcm_server_port: 2328  
  proto: TCP
- vnf_lcm_server_oneauth_port: 2329  
  proto: TCP
- vnf_lcm_server_twoauth_port: 2330  
  proto: TCP
- nw_flannel_port: 
  proto: UDP
- keystone_public_port: 2520
  proto: TCP
  msbservice: true
- keystone_admin_port: 35357
  proto: TCP
- keystone_twoway_port: 5005
  proto: TCP
  msbservice: true
- mbr_fixed_internal_data_https_port: 3013
  proto: TCP
  msbservice: true
- dsm_fixed_publish_data_port: 3014
  proto: TCP
  msbservice: true
- ovs_listen_port: 6637
  proto: TCP
- dvs_listen1_port: 6640
  proto: TCP
- dvs_listen2_port: 6643
  proto: TCP
- provider_kafka_port: 9092
  proto: TCP
- provider_snmp_v3_port: 35508
  proto: UDP  
- provider_uaf_https_port: 35554
  proto: TCP
- provider_pim_port: 9141
  proto: TCP
- provider_uaf_snmp1_port: 162
  proto: UDP
- provider_kafkassl_2_port: 35593
  proto: TCP
- provider_hwmsyslog_port: 35900
  proto: TCP/UDP
- snmp_trap_port: 
  proto: UDP
- nw_cilium_vxlan_port: 8472
  proto: UDP
- nw_vpn_ike_port: 500
  proto: UDP
- provider_ftp_port: 29952
  proto: TCP
- sbfd_listen_port: 7784-7785
  proto: UDP
- nw_knitter_vpn_vxlan_port: 1283
  proto: UDP
- os_pkg_repo_port: 1041
  proto: TCP
- infranw_tunnel_port: 8789
  proto: UDP
- posd_port: 1133
  proto: TCP
- posd_image_port: 1134
  proto: TCP
- posd_tftp_port: 1135
  proto: UDP
- plat_fix_tcp_port: 22,80,443,2121,2514,2523,8849,9090,9190,9511,9611,10080,10443,15050,18080,18990,18998,19393,19898,29292,29952,30000:31998,32000:32767,40443,45550,54321,18123,19123,29123
  proto: TCP 
- plat_fix_udp_port: 67,69,514,547,1161,2514,4500,12441,30000:31998,32000:32767
  proto: UDP
