config_path: .

sources:

- name: conf
  path: conf.json

- name: com_vars
  path: ../../../root/common/com_vars.yml

- name: inet_deploy
  path: ../../network/Inet_deploy.conf
  parser: ini

- name: paas
  path: paas.conf
  parser: ini

- name: paas_bak
  path: ../../pdm_bak/conf/paas.conf
  parser: ini

- name: install
  path: ../deploylist/install.list
  parser: json

- name: pkg_ver
  path: ../deploylist/pkg_ver.list
  parser: json
  scope: package
  to_context: pkg
  converter: pkg_ver

- name: pkg_ver_bak
  path: ../deploylist/pkg_ver.list_bak
  parser: json
  scope: package
  to_context: pkg_bak
  converter: pkg_ver

- name: pkg_ver_new
  path: ../deploylist/pkg_ver.list_new
  parser: json
  scope: package
  to_context: pkg_new
  converter: pkg_ver

- name: port_vars
  path: ../../../root/common/port_vars.yml

- name: vnm_network
  path: vnm_network.conf
  parser: ini

- name: upgrade
  path: ../upgrade.data
  parser: json

- name: nodegroups
  path: ../model/scenario/{{model_scenario}}/nodegroups.yml

- name: roles
  path: ../model/scenario/{{model_scenario}}/roles.yml

# Convert var key from dash to underline
- name: os_sysconfig
  path: os_sysconfig.conf
  parser: ini

- name: database_user_data
  path: Database_user_data.json
  scope: package
  to_context: db
  converter: db_map

- name: nodes
  path: ../../../root/nodes
  parser: json

- name: sync
  path: ../deploylist/sync.list
  parser: json

- name: module2msb
  path: module2MSB.json
  parser: json

- name: feature_domain
  path: feature_domain.yml
