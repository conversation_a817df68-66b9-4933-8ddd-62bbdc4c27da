ndf:
  version: 1.0
  metadata:
    type: production
    lab: PaaS Lab
    lab_owner: PaaS
    contact: infrastructrue_team@paas
    link: ''
    location: Shanghai
  networks:
    neutron:
      {% for key,value in vnm_network.neutron.items() %}
      {{ key }}: '{{ value }}'
      {% endfor %}
    vip:
      - network: net_api
        subnets:
        {% for key,value in conf.bm_network.items() %}
        {% if key == "net_api" %}
        {% if value.subnet is mapping %}
        - name: {{ value.subnet.name }}
          num: 20
          ip_version: {{ value.subnet.ip_version|string }}
        {% else %}
        {% for subnet_info in value.subnet %}
        - name: {{ subnet_info.name }}
          ip_version: {{ subnet_info.ip_version|string }}
        {% if subnet_info.ip_version|string in paas.network.netapi_default_ip_version %}
          num: 20
        {% else %}
          num: 1
        {% endif %}
        {% endfor %}
        {% endif %}
        {% endif %}
        {% endfor %}
    network_flavors:
      {% for flavor_name,flavor_info in inetdeploy_info.items() %}
      {{ flavor_name }}:
        {% for key1,value1 in flavor_info.items() %}
        {% if value1 is string %}
        {# fix yaml bug consider [] in string is list #}
        {{ key1 }}: {{ value1|replace('[','AAA')|replace(']','BBB') }}
        {% else %}
        {{ key1 }}:
          {% for key2,value2 in value1.items() %}
          {{ key2 }}:
            {% for key3,value3 in value2.items() %}
            {% if value3 is string %}
            {{ key3 }}: '{{ value3|replace('[','AAA')|replace(']','BBB') }}'
            {% else %}
            {{ key3 }}:
              {% for key4,value4 in value3.items() %}
              {{ key4 }}: '{{ value4|replace('[','AAA')|replace(']','BBB') }}'
              {% endfor %}
            {% endif %}
            {% endfor %}
          {% endfor %}
        {% endif %}
        {% endfor %}
      {% endfor %}
    controller_ip_info:
      {% for ip_info in conf.bm_network.paas_controller_ip %}
      {% for key,value in ip_info.items() %}
      {% if loop.index == 1 %}
      - {{ key }}: {{ value }}
      {% else %}
        {{ key }}: {{ value }}
      {% endif %}
      {% endfor %}
      {% endfor %}
    network_config:
    {% for key,value in conf.bm_network.items() %}
    {% if key.startswith("net_") and "provider_network" not in value %}
      {{ key }}:
        name: {{ key }}
        physical_network: {{ value["provider:physical_network"] }}
        {% if "provider:network_type" in value %}
          {% set net_type=value["provider:network_type"] %}
        {% else %}
          {% set net_type="vlan"  %}
        {% endif %}
        network_type: {{ net_type }}
        {% if net_type == "vlan" %}
        {% if "provider:segmentation_id" in value %}
        segmentation_id: {{ value["provider:segmentation_id"] }}
        {% else %}
        {% set vlan_id_key=key[4:]+"_vlan_id" %}
        segmentation_id: {{ conf.bm_network[vlan_id_key] }}
        {% endif %}
        {% endif %}
        mtu: 1500
        subnet:
        {% if value.subnet is mapping %}
        - name: {{ value.subnet.name }}
          enable_dhcp: false
          {% if value.subnet.gateway_ip %}
          gateway_ip: {{ value.subnet.gateway_ip }}
          {% endif %}
          ip_version: {{ value.subnet.ip_version }}
          cidr: {{ value.subnet.cidr }}
          allocation_pools:
          {% for allocation in value.subnet.allocation_pools %}
          - start: {{ allocation.start }}
            end: {{ allocation.end }}
          {% endfor %}
        {% else %}
        {% for subnet_info in value.subnet %}
        - name: {{ subnet_info.name }}
          enable_dhcp: false
          {% if subnet_info.gateway_ip %}
          gateway_ip: {{ subnet_info.gateway_ip }}
          {% endif %}
          ip_version: {{ subnet_info.ip_version }}
          cidr: {{ subnet_info.cidr }}
          allocation_pools:
          {% for allocation in subnet_info.allocation_pools %}
          - start: {{ allocation.start }}
            end: {{ allocation.end }}
          {% endfor %}
        {% endfor %}
        {% endif %}
    {% endif %}
    {% endfor %}
