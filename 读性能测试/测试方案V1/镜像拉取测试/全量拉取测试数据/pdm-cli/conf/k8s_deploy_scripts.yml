
base_dir: /paasdata/op-data/

scripts:
{% if envs.system_operation %}
- path: op-node-volume/tasks/{{envs.operator}}.sh
  desc: {{envs.operator}} op-node-volume on k8s
  type: {{envs.operator}}
  parameters:
    {% if paas.storage and paas.storage.csi_block_driver %}
    - csi_block_driver: '{{paas.storage.csi_block_driver}}'
    {% else %}
    - csi_block_driver: ''
    {% endif %}

- path: op-storage-ceph_csi_driver/tasks/{{envs.operator}}.sh
  desc: {{envs.operator}} op-storage-ceph_csi_driver on k8s
  type: {{envs.operator}}
  parameters:
    {% if paas.storage and paas.storage.ceph_csi_driver %}
    - ceph_csi_driver: '{{paas.storage.ceph_csi_driver}}'
    {% else %}
    - ceph_csi_driver: 'off'
    {% endif %}

- path: op-gpu-scripts/tasks/{{envs.operator}}.sh
  desc: {{envs.operator}} op-gpu-scheduler on k8s
  type: {{envs.operator}}
  parameters:
    - enable_gpu: '{{envs.enable_gpu}}'

- path: op-adrm-deploy/chart/{{envs.operator}}.sh
  desc: {{envs.operator}} op-adrm-deploy on k8s
  type: {{envs.operator}}
  parameters:
    - enable_gpu: '{{envs.enable_gpu}}'

- path: op-storage-csi_disk/tcf_tasks/{{envs.operator}}.sh
  desc: {{envs.operator}} op-storage-csi_disk on k8s
  type: {{envs.operator}}
  parameters:
    {% if paas.storage and paas.storage.csi_block_driver %}
    - csi_block_driver: '{{paas.storage.csi_block_driver}}'
    {% else %}
    - csi_block_driver: ''
    {% endif %}

- path: op-storage-csi_opdisk/tasks/{{envs.operator}}.sh
  desc: {{envs.operator}} op-storage-csi_opdisk on k8s
  type: {{envs.operator}}
  parameters:
    {% if paas.storage and paas.storage.csi_block_driver %}
    - csi_block_driver: '{{paas.storage.csi_block_driver}}'
    {% else %}
    - csi_block_driver: ''
    {% endif %}

- path: op-storage-csi_local_storage/tcf_tasks/{{envs.operator}}.sh
  desc: {{envs.operator}} op-storage-csi_local_storage on k8s
  type: {{envs.operator}}
  parameters:
    {% if paas.storage and paas.storage.csi_local_driver %}
    - csi_local_driver: '{{paas.storage.csi_local_driver}}'
    {% else %}
    - csi_local_driver: ''
    {% endif %}

{% else %}

{% if envs.component_name == 'op-node-volume' %}
- path: op-node-volume/tasks/{{envs.operator}}.sh
  desc: {{envs.operator}} op-node-volume on k8s
  type: {{envs.operator}}
  parameters:
    {% if paas.storage and paas.storage.csi_block_driver %}
    - csi_block_driver: '{{paas.storage.csi_block_driver}}'
    {% else %}
    - csi_block_driver: ''
    {% endif %}
{% endif %}

{% if envs.component_name == 'op-storage-ceph_csi_driver' %}
- path: op-storage-ceph_csi_driver/tasks/{{envs.operator}}.sh
  desc: single component {{envs.operator}} op-storage-ceph_csi_driver on k8s
  type: {{envs.operator}}
  parameters:
    {% if paas.storage and paas.storage.ceph_csi_driver %}
    - ceph_csi_driver: '{{paas.storage.ceph_csi_driver}}'
    {% else %}
    - ceph_csi_driver: 'off'
    {% endif %}
{% endif %}

{% if envs.component_name == 'op-gpu-scripts' %}
- path: op-gpu-scripts/tasks/{{envs.operator}}.sh
  desc: single component {{envs.operator}} op-gpu-scheduler on k8s
  type: {{envs.operator}}
  parameters:
    - enable_gpu: '{{envs.enable_gpu}}'
{% endif %}

{% if envs.component_name == 'op-adrm-deploy' %}
- path: op-adrm-deploy/chart/{{envs.operator}}.sh
  desc: single component {{envs.operator}} op-adrm-deploy on k8s
  type: {{envs.operator}}
  parameters:
    - enable_gpu: '{{envs.enable_gpu}}'
{% endif %}

{% if envs.component_name == 'op-storage-csi_disk' %}
- path: op-storage-csi_disk/tcf_tasks/{{envs.operator}}.sh
  desc: single component {{envs.operator}} op-storage-csi_disk on k8s
  type: {{envs.operator}}
  parameters:
    {% if paas.storage and paas.storage.csi_block_driver %}
    - csi_block_driver: '{{paas.storage.csi_block_driver}}'
    {% else %}
    - csi_block_driver: ''
    {% endif %}
{% endif %}


{% if envs.component_name == 'op-storage-csi_opdisk' %}
- path: op-storage-csi_opdisk/tasks/{{envs.operator}}.sh
  desc: single component {{envs.operator}} op-storage-csi_opdisk on k8s
  type: {{envs.operator}}
  parameters:
    {% if paas.storage and paas.storage.csi_block_driver %}
    - csi_block_driver: '{{paas.storage.csi_block_driver}}'
    {% else %}
    - csi_block_driver: ''
    {% endif %}
{% endif %}

{% if envs.component_name == 'op-containers-kubevirt' %}
- path: op-containers-kubevirt/tasks/{{envs.operator}}.sh
  desc: {{envs.operator}} op-containers-kubevirt on k8s
  type: {{envs.operator}}
{% endif %}

{% if envs.component_name == 'op-storage-csi_local_storage' %}
- path: op-storage-csi_local_storage/tcf_tasks/{{envs.operator}}.sh
  desc: single component {{envs.operator}} op-storage-csi_local_storage on k8s
  type: {{envs.operator}}
  parameters:
    {% if paas.storage and paas.storage.csi_local_driver %}
    - csi_local_driver: '{{paas.storage.csi_local_driver}}'
    {% else %}
    - csi_local_driver: ''
    {% endif %}
{% endif %}

{% endif %}
