[{"source_key": "clusters.enable_gpu", "funcset_name": "gpu", "rules": {"key": ["false", "False", ""], "operator": "in", "value": "false"}}, {"source_key": "global.is_deploy_cos", "funcset_name": "cosg", "capability": "FunctionSet_cosg", "rules": {"key": ["false"], "operator": "in", "value": "false"}}, {"source_key": "global.is_deploy_kata", "funcset_name": "kata", "rules": {"key": ["true", "True"], "operator": "in", "value": "true"}}, {"source_key": "default_app_network_config.default_plugin", "funcset_name": "default_plugin_cilium", "rules": {"key": ["cilium"], "operator": "in", "value": "true"}}, {"source_key": "default_app_network_config.default_plugin", "funcset_name": "default_plugin_flannel", "rules": {"key": ["flannel", ""], "operator": "in", "value": "true"}}, {"source_key": "features.FunctionSet_omm_model", "funcset_name": "omm_model_general", "rules": {"key": ["general"], "operator": "in", "value": "true"}}, {"source_key": "clusters.used_model", "funcset_name": "dns_operator", "rules": {"key": ["model2"], "operator": "in", "value": "true"}}, {"source_key": "global.tcf_scale_id", "funcset_name": "image_accelerator", "rules": {"key": ["100", "200", "300", "500", "1000", "1500", "2000"], "operator": "in", "value": "true"}}, {"source_key": "features.FunctionSet_metric_collect_interval", "funcset_name": "metric_collect_interval_30s", "rules": {"key": ["30s"], "operator": "in", "value": "true"}}, {"source_key": "features.FunctionSet_metric_collect_interval", "funcset_name": "metric_collect_interval_5min", "rules": {"key": ["5min", ""], "operator": "in", "value": "true"}}, {"source_key": "clusters.used_model", "funcset_name": "plat_ingress_ctrl", "rules": {"key": ["model2"], "operator": "in", "value": "false"}}, {"source_key": "global.k8s_container_runtime", "funcset_name": "containerd", "rules": {"key": ["containerd"], "operator": "in", "value": "true"}}, {"source_key": "global.filestorage_type", "funcset_name": "glusterfs", "rules": {"key": ["GlusterFS"], "operator": "in", "value": "true"}}, {"source_key": "global.blockstorage_type", "funcset_name": "block_storage_opdisk", "rules": {"key": ["OP_disk"], "operator": "in", "value": "true"}}, {"source_key": "global.blockstorage_type", "funcset_name": "block_storage_csidisk", "rules": {"key": ["TCF_disk"], "operator": "in", "value": "true"}}, {"source_key": "global.local_storage_type", "funcset_name": "local_storage", "rules": {"key": ["OP_local_storage"], "operator": "in", "value": "true"}}, {"source_key": "global.filestorage_type", "funcset_name": "file_storage_csinfs", "rules": {"key": ["3rdParty_NAS"], "operator": "in", "value": "true"}}, {"source_key": "global.filestorage_type", "funcset_name": "file_storage_csicephfs", "rules": {"key": ["CephFS"], "operator": "in", "value": "true"}}, {"source_key": "features.FunctionSet_gpu", "funcset_name": "gpu_enhanced", "rules": {"key": ["enhanced"], "operator": "in", "value": "true"}}, {"source_key": "clusters.enable_gpu", "funcset_name": "gpu_normal", "rules": {"key": ["True", "true"], "operator": "in", "value": "true"}}]