features:
- capability: FunctionSet_cn_light_vnfm
  comment: |-
    是否支持核心网的专用设备VNF管理功能；
    若配置为true，会安装vnf-lcm组件；若配置为false，则不安装；
    默认为false；
  comment_en: |-
    Whether it supports CN light VNFM function;
    If configured to true, vnf-lcm related components will be installed; Otherwise,
    they will not be installed;
    Default is false.
  domain: Platform Services Domain
  value: 'false'
  enum:
  - 'true'
  - 'false'
- capability: FunctionSet_gpu
  comment: |-
    是否支持GPU功能，取值none、normal或enhanced，默认none。
    若配置为normal，会安装非智算GPU相关组件；若配置为enhanced，会安装智算GPU相关组件；若配置为none，则不安装。
    该特性与“集群配置”中“使能GPU”有关联约束。
    若配置为none或enhanced，“集群配置”中“使能GPU”必须为false；若配置为normal，“集群配置”中“使能GPU”必须为true。
  comment_en: |-
    Whether to support GPU function. The value can be none, normal, enhanced. The default value is none.
    If configured as normal, non-iCF scene GPU related components will be installed; if configured as enhanced, iCF scene GPU related components will be installed; if configured as none, no installation will be done.
    This feature is associated with "EnableGPU" in "Cluster Config" .If configured as none or enhanced, "EnableGPU" in "Cluster Config" must be false; if configured as normal, "EnableGPU" in "Cluster Config" must be true.
  domain: Compute Domain
  value: 'none'
  enum:
  - 'none'
  - 'normal'
  - 'enhanced'
  modifiable: 'true'

- capability: FunctionSet_cosg
  comment: |-
    是否支持API能力开放网关，需与“全局配置”中“是否部署能力开放网关”保持一致；
    若配置为true，会安装COSG相关组件；若配置为false，则不安装；
    默认为true
  comment_en: |-
    Whether it supports API Capability Open Service, and it should be consistent with whether to deploy COSG in "Global Configuration";
    If configured to true, COSG related components will be installed; Otherwise, they will not be installed;
    Default is true.
  domain: Application Services Domain
  value: 'true'
  enum:
  - 'true'
  - 'false'
- capability: Feature_QoS_alarm
  comment: |-
    是否支持QoS告警功能；默认为true。
    若配置为true,支持基于指标的QoS告警功能；若配置为false，则不支持，多适用于资源受限场景。
  comment_en: |-
    Whether it supports QoS (Quality of Service) alerting feature;
    If configured to true, it supports the QoS alerting feature based on metrics; if configured to false, it does not support this, which is more suitable for resource-constrained scenarios; 
    Default is true.
  domain: Operation and Maintenance Management Domain
  value: 'true'
  enum:
  - 'true'
  - 'false'
- capability: FunctionSet_metric_collect_interval
  comment: |-
    性能数据基础采集周期；默认为5min。
    若配置为30s，平台性能数据的基础采集周期为30秒，并部署prometheus组件作为性能数据库。
    若配置为5min，平台性能数据的基础采集周期为5分钟，并部署轻量化victoriametrics组件作为性能数据库，多适用于资源受限场景。
  comment_en: |-
    Basic collection cycle for performance data;
    If configured to 30 seconds, the basic collection cycle for platform performance data is 30 seconds, and the Prometheus component is deployed as the performance database.
    If configured to 5 minutes, the basic collection cycle for platform performance data is 5 minutes, and the lightweight VictoriaMetrics component is deployed as the performance database, which is more suitable for resource-constrained scenarios; 
    Default is 5 minutes.
  domain: Operation and Maintenance Management Domain
  value: 5min
  enum:
  - 30s
  - 5min
- capability: FunctionSet_ops_node_heartbeat_monitor
  comment: |-
    控制是否提供节点心跳丢失告警及节点濒死自愈功能,默认配置为false
    节点心跳告警：检测节点与其中一个paas controller节点（monitor组件主实例所在节点）的网络连通性，异常时上报告警
    节点濒死自愈：基于系统负荷持续冲高的条件进行判决，当节点负荷过高时，触发节点复位
    若配置为true，会安装op-ops-monitor-heartbeat组件提供上述功能，适用于资源非受限场景；
    若配置为false，则不安装组件，不提供对应的上述功能，适用于资源受限场景；
  comment_en: |-
    Control of Node Heartbeat Loss Alarm and Node Near-Death Self-Healing Functionality:
    Node Heartbeat Alarm: Monitors network connectivity between the node and one of the PaaS controller nodes (where the main instance of the monitor component resides), reporting an alarm in case of anomalies.
    Node Near-Death Self-Healing: Determines actions based on sustained high system load conditions, triggering a node reset when the node load is excessively high.
    If set to true, the op-ops-monitor-heartbeat component will be installed to provide the above functionalities, suitable for scenarios with no resource constraints.
    If set to false, the component will not be installed, and the corresponding functionalities will not be available, suitable for resource-constrained scenarios.
    The default setting is false.
  domain: Operation and Maintenance Management Domain
  value: 'false'
  enum:
  - 'true'
  - 'false'
- capability: FunctionSet_persistent_volume_monitor
  comment: |-
    是否支持持久化卷监控功能，持久化卷监控功能主要包括云盘断链检测及用户文件系统可用检测,默认配置为false
    若配置为true，会安装op-ops-monitor-pvm组件提供持久化卷监控功能，适用于资源非受限场景；
    若配置为false，则不安装组件，不提供对应的持久化卷监控功能，适用于资源受限场景；
  comment_en: |-
    Persistent Volume Monitoring Support:
    The persistent volume monitoring feature includes cloud disk disconnection detection and user file system availability checks.
    If set to true, the op-ops-monitor-pvm component will be installed to provide persistent volume monitoring capabilities, suitable for scenarios with no resource constraints.
    If set to false, the component will not be installed, and the corresponding persistent volume monitoring feature will not be available, suitable for resource-constrained scenarios.
    The default setting is false.
  domain: Operation and Maintenance Management Domain
  value: 'false'
  enum:
  - 'true'
  - 'false'
- capability: FunctionSet_streaming_log
  comment: 若配置为true，打开实时日志采集上报功能，但是会增加资源消耗，若配置为false则没有该功能，多适用资源受限场景，默认为false。
  comment_en: |-
    Whether it supports real-time log collection;
    If configured as true, enable real-time log collection and reporting, but it will increase resource consumption; If configured as false, this feature is not available, which is more suitable for resource-constrained scenarios; 
    Default is false.
  domain: Operation and Maintenance Management Domain
  value: 'false'
  enum:
  - 'true'
  - 'false'
- capability: FunctionSet_enhanced_container_application_log
  comment: 若配置为true，打开应用日志增强功能，包括应用日志下载，租户HostPath日志清理，但是会增加资源消耗。若配置为false则没有该功能，多适用资源受限场景，默认为false。
  comment_en: |-
    Whether it supports enhanced application logging features;
    If configured as true, enable enhanced application logging features, including application log downloads and hostpath of tenants log cleaning, but it will increase resource consumption; If configured as false, this feature is not available, which is more suitable for resource-constrained scenarios. 
    Default is false.
  domain: Operation and Maintenance Management Domain
  value: 'false'
  enum:
  - 'true'
  - 'false'
- capability: FunctionSet_omm_model
  comment: |-
    运维模型，默认为simple。
    simple为极简模型，其对非关键指标及告警进行裁剪；
    general为通用模型，其支持所有性能指标及告警；
    integrated_UPF为专U定制模型，其针对特定网元的指标及告警进行定制。
  comment_en: |-
    Operational model, "Simple" is a minimalist model, which trims non-critical indicators and alerts;
    "General" is a universal model, which supports all performance indicators and alerts;
    "Integrated_UPF" is a custom model for specific network elements, which customizes indicators and alerts for specific elements;
    Default is "simple".
  domain: Operation and Maintenance Management Domain
  value: simple
  enum:
  - simple
  - general
  - integrated_UPF
- capability: FunctionSet_topo_enhance
  comment: |-
    运维域特性：是否支持TOPO增强功能，包括TOPO对象树的查看，topo跨级查询，以及中移CIM配置北向。取值如下，默认值false：
    false：不安装nwd组件和dgraph数据库，适用资源受限场景；
    true：安装nwd组件和dgraph数据库，适用资源非受限场景；
  comment_en: |-
    O&M domain feature: Whether to support the enhanced TOPO function, including viewing the TOPO object tree, topo cross-level query, and northbound CIM configuration of China Mobile. Options (default value: false): 
    false: The nwd component and dgraph database are not installed, which is applicable to the scenario where resources are limited.
    true: Install the nwd component and dgraph database, which is applicable to the scenario where resources are not limited.
  domain: Operation and Maintenance Management Domain
  value: 'false'
  enum:
  - 'false'
  - 'true'
- capability: FunctionSet_pim
  comment: |-
    是否支持pim硬件管理、硬件告警、性能、日志、北向数据上报功能；默认为true。
    若配置为true,支持pim硬件管理、硬件告警、性能、日志、北向数据上报功能；若配置为false，则不支持，适用于不需要pim硬件管理、硬件告警、性能、日志、北向数据上报功能的场景。
    专U、i5GC和UME是需要pim功能的。
  comment_en: |-
    O&M domain feature:Whether to support pim hardware management, hardware alarm, performance, log, and northbound data reporting. Options (default value: true): 
    true: The pim hardware management, hardware alarm, performance, log, and northbound data reporting functions are supported.
    false: The pim hardware management, hardware alarm, performance, log, and northbound data reporting functions are not supported. It is applicable to the scenario where the pim hardware management, hardware alarm, performance, log, and northbound data reporting functions are not required.
    The integrated-UPF, i5GC, and UME need the pim function.
  domain: Operation and Maintenance Management Domain
  value: 'true'
  enum:
  - 'true'
  - 'false'
- capability: FunctionSet_OM_and_Management_DataStorage_is_Separated
  comment: |-
    若配置为true，运维数据（告警/性能/日志）使用独立的PG数据库，安装provider_pg组件，提高系统可靠性，适用于资源非受限场景。
    若配置为false，运维数据（告警/性能/日志） 同管理库共用一个PG数据库，不安装provider_pg组件，仅用于资源受限场景。
  comment_en: |-
    If configured as true, operational data (alerts/performance/logs) will use a separate PG database. The provider_pg component will be installed to enhance system reliability, suitable for scenarios with unrestricted resources.

    If configured as false, operational data (alerts/performance/logs) will share the same PG database as the management database. The provider_pg component will not be installed and is only intended for scenarios with limited resources.
  domain: Operation and Maintenance Management Domain
  value: 'false'
  enum:
  - 'true'
  - 'false'
- capability: Feature_support_tcf_license
  comment: |-
    运维域特性：是否支持License，取值如下，默认值false：
    内嵌平台场景由产品控制，建议平台不打开。作为独立产品场景，建议打开。
    false：不支持License功能，系统无License控制；
    true：支持License功能，有License控制，支持系统有效期控制。
  comment_en: |-
    O&M domain feature：Does it support License? The values are as follows, with a default value of false：

    The embedded platform scene is controlled by the product, and it is recommended not to open the platform. As an independent product scenario, it is recommended to open it.

    false：Not supporting License function, the system has no License control；
    true：Support License function, with License control and system validity period control.
  domain: Operation and Maintenance Management Domain
  value: 'false'
  enum:
  - 'false'
  - 'true'
- capability: FunctionSet_k8s_native_ui
  comment: |-
    运维域特性：是否支持k8原生界面相关菜单（集群级原生对象、工作负载、配置与存储、服务发现和负载均衡等），取值如下，默认值false：
    false：不安装k8sopapiserver组件，前端不注册k8s原生界面，适用于资源受限场景；
    true：安装k8sopapiserver组件，前端注册k8s原生相关界面，适用于资源不受限场景。
  comment_en: |-
    O&M domain feature: Whether to support the menus related to the k8 native UI (Kubernetes Cluster Resources, Workload, Configuration and Storage, Service Discovery and Load Balancing,etc). Options(default value: false):
    false: The k8sopapiserver component is not installed, and the k8s native UI is not registered at the front end, which is applicable to the scenario where resources are limited.
    true: The k8sopapiserver component is installed, and the k8s native UI is registered at the front end, which is applicable to the scenario where resources are not limited.
  domain: Operation and Maintenance Management Domain
  value: 'false'
  enum:
  - 'false'
  - 'true'

- capability: FunctionSet_apts
  comment: |-
    运维域特性：是否支持智算场景的巡检压测，预检，训中监控，故障定界等增强功能，取值如下，默认值false：
    false：不安装op-aif-wsm组件，适用于资源受限场景；
    true：安装op-aif-wsm组件，前端注册k8s原生相关界面，适用于资源不受限场景。
  comment_en: |-
    O&M domain feature: Whether to support the menus related to the k8 native UI (Kubernetes Cluster Resources, Workload, Configuration and Storage, Service Discovery and Load Balancing,etc). Options(default value: false):
    false: The k8sopapiserver component is not installed, and the k8s native UI is not registered at the front end, which is applicable to the scenario where resources are limited.
    true: The k8sopapiserver component is installed, and the k8s native UI is registered at the front end, which is applicable to the scenario where resources are not limited.
  domain: Operation and Maintenance Management Domain
  value: 'false'
  enum:
  - 'false'
  - 'true'
  modifiable: 'true'

- capability: FunctionSet_swr_vulnerability_scanning
  comment: |-
    软件仓库是否支持漏洞扫描功能，取值如下，默认值false：
    若配置为true，则安装漏洞扫描相关组件（trivy），软件仓库中镜像支持漏洞扫描功能，适用于资源非受限场景
    若配置为false，软件仓库不安装漏洞扫描相关组件（trivy），不支持漏洞扫描功能，适用于资源受限场景
  comment_en: |-
    Whether the software repository supports vulnerability scanning depends on the configuration, with the default value being false:

    If set to true, the vulnerability scanning component (trivy) will be installed, and images in the SWR will support vulnerability scanning. This is suitable for scenarios where resources are not limited.

    If set to false, the vulnerability scanning component (trivy) will not be installed, and vulnerability scanning will not be supported. This is suitable for resource-limited scenarios.
  domain: Security
  value: 'false'
  enum:
  - 'false'
  - 'true'

- capability: FunctionSet_image_build
  comment: |-
    软件仓库是否支持镜像构建功能，取值如下，默认值false：
    若配置为true，则安装镜像构建组件（buildkit），软件仓库支持镜像构建功能，适用于资源非受限场景
    若配置为false，软件仓库不安装镜像构建组件（buildkit），不支持镜像构建功能，适用于资源受限场景
  comment_en: |-
    Whether the SWR supports image building functionality depends on the configuration, with the default value being false:

    If set to true, the image building component (buildkit) will be installed, enabling image building functionality. This is suitable for scenarios where resources are not limited.

    If set to false, the image building component (buildkit) will not be installed, and image building functionality will not be supported. This is suitable for resource-limited scenarios.
  domain: Compute Domain
  value: 'false'
  enum:
  - 'false'
  - 'true'
- capability: FunctionSet_op_applicationmanager_appdesign
  comment: |-
    应用管理域特性：是否支持Helm Chart包编排功能,取值如下，默认值false：
    false：不安装op-applicationmanager-appdesign组件，TCF-portal无Chart草稿Tab页；适用于资源受限场景。
    true：安装op-applicationmanager-appdesign组件，TCF-portal有Chart草稿Tab页；适用于非资源受限场景。
  comment_en: |-
    Application Services domain feature: whether to support the orchestration of Helm Chart packages.
    Default value: false
    Options:
      false: The op-applicationmanager-appdesign component is not installed, and the TCF-portal has no Chart draft tab. This option is applicable to scenarios where resources are limited.
      true: The op-applicationmanager-appdesign component is installed, and the TCF-portal has a Chart draft tab. This option is applicable to scenarios where resources are not limited.
  domain: Application Services Domain
  value: 'false'
  enum:
  - 'false'
  - 'true'
- capability: FunctionSet_Elastic_Scaling_Service
  comment: |-
    容器计算域特性：是否安装ess(弹性伸缩服务，Elastic Scaling Service)相关组件，ess功能由prometheus-adapter组件构成。取值如下，默认值false：
    false：不安装ess相关组件，适用于资源受限场景；
    true：安装ess相关组件，适用于资源不受限场景。
  comment_en: |-
    Container Compute Domain Feature: Decide whether to install ESS (Elastic Scaling Service) components. ESS includes prometheus-adapter.
    Options (default is false):
    false: Do not install ESS components, suitable for resource-limited scenarios.
    true: Install ESS components, suitable for resource-unlimited scenarios.
  domain: Compute Domain
  value: 'false'
  enum:
  - 'false'
  - 'true'
- capability: FunctionSet_Elastic_Scaling_Service_VPA
  comment: |-
    容器计算域特性：是否支持垂直pod弹缩（Vertical Pod Autoscaler )，该功能主要由vpa-updater、vpa-recommender、vpa-admission-controller、prometheus-adapter组件构成。平台指标（容器、节点的cpu、memory指标）依赖平台prometheus；应用自定义指标依赖技术服务的prometheus。取值如下，默认值false
    false：不安装vpa相关组件，适用于资源受限场景；
    true：安装vpa相关组件，适用于资源不受限场景。
  comment_en: |-
    Container computing domain characteristics: Does it support Vertical Pod Autoscaler? This feature mainly consists of vpa updater, vpa recommender, vpa admission controller and prometheus-adapter components. Platform metrics (container, node CPU, memory metrics) depend on platform prometheus; The custom metrics of the application depend on the prometheus of the technical service. The values are as follows, with a default value of false
    False: Do not install VPA related components, suitable for resource constrained scenarios;
    True: Install VPA related components, suitable for resource unrestricted scenarios.
  domain: Compute Domain
  value: 'false'
  enum:
  - 'false'
  - 'true'
- capability: FunctionSet_plat_component_mgr
  comment: |-
    是否支持平台组件管理功能（组件和组件实例信息呈现、性能数据采集、告警功能）。设置为true将安装op-kubeall-psm组件。
    取值如下，默认为false。
    false：不支持平台组件管理功能（组件和组件实例信息呈现、性能数据采集、告警功能），适用于资源受限场景。
    true：支持平台组件管理功能（组件和组件实例信息呈现、性能数据采集、告警功能），适用于非资源受限场景。
  comment_en: |-
    Whether the platform component management functions (component and component instance information presentation, performance data collection, and alarm functions) are supported. Set to true to install the op-kubeall-psm component.
    The following values can be used, and the default value is false.
    false: The platform component management features (component and component instance information display, performance data collection, and alarm functions) are not supported, and are suitable for resource-constrained scenarios.
    true: supports the platform component management functions (component and component instance information display, performance data collection, and alarm functions), which is suitable for non-resource-constrained scenarios.
  domain: Platform Services Domain
  value: 'false'
  enum:
  - 'false'
  - 'true'
- capability: FunctionSet_enhance_k8s_eviction
  comment: |-
    容器计算域特性：是否安装op-containers-eviction(自研增强驱逐)组件，自研增强驱逐组件提供根据节点CPU、内存及CPU负载门限配置，按照已部署pod的优先级由低到高执行驱逐回收资源功能。取值如下，默认值false：
    false：不安装自研增强驱逐组件，不提供增强驱逐功能；
    true：安装自研增强驱逐组件，提供增强驱逐功能。
  comment_en: |-
    Container computing domain features: Whether to install the op-containers-eviction (self-developed enhanced eviction) component. The self-developed enhanced eviction component provides configuration based on node CPU, memory and CPU load threshold, and performs eviction according to the priority of deployed pods from low to high. Resource recycling function. The values are as follows, the default value is false：
    false：Do not install enhanced eviction components and do not provide enhanced eviction functions；
    true：Install enhanced eviction components to provide enhanced eviction functions。
  domain: Compute Domain
  value: 'false'
  enum:
  - 'false'
  - 'true'
- capability: FunctionSet_nw_lark
  comment: |-
    网络域特性：是否安装op-nw-lark组件，默认值false。
    false：不安装op-nw-lark组件；适用于资源受限场景。
    true：安装op-nw-lark组件；适用于非资源受限场景。
  comment_en: |-
    Network domain feature: whether container network support dynamic association of SDN networking (dynamic association of VLANs), default value is false.
    if true, install op-nw-lark component: support dynamic association for SDN networking, suitable for resource unconstrained scenarios.
    if false, not install op-nw-lark component: not provide dynamic association for SDN networking, suitable for resource constrained scenarios.
  domain: Network Domain
  value: 'false'
  enum:
  - 'false'
  - 'true'
- capability: FunctionSet_nw_ops_alita
  comment: |-
    网络域特性：TCF网络运维（网络诊断与可观测等），如果支持该功能会安装op-nw-alita组件，默认值false。
    false：不支持网络运维功能，不安装op-nw-alita组件；适用于资源受限场景。
    true：支持网络运维功能，安装op-nw-alita组件；适用于非资源受限场景。
  comment_en: |-
    Network domain feature: TCF network operation and maintenance (network diagnosis and observability, etc.), if this feature is supported, op-nw-alita component will be installed.
    Default value is false.
    false: not support network operation and maintenance feature and not install op-nw-alita component, suitable for resource constrained scenarios.
    true: support network operation and maintenance feature and install op-nw-alita components, suitable for resource unconstrained scenarios.
  domain: Network Domain
  value: 'false'
  enum:
  - 'false'
  - 'true'
- capability: FunctionSet_nw_ops_moose
  comment: |-
    网络域特性：TCF网络运维工具（网络诊断工具），如果支持该功能会安装op-nw-moose-chart组件，默认值true。
    false：不支持网络运维工具功能，不安装op-nw-moose-chart组件；适用于资源受限场景。
    true：支持网络运维工具功能，安装op-nw-moose-chart组件；适用于非资源受限场景。
  comment_en: |-
    Network domain feature: TCF network operation and maintenance tool (network diagnosis tool), if this feature is supported, op-nw-moose component will be installed.
    Default value is true.
    false: not support network operation and maintenance feature tool and not install op-nw-moose component, suitable for resource constrained scenarios.
    true: support network operation and maintenance feature tool and install op-nw-moose components, suitable for resource unconstrained scenarios.
  domain: Network Domain
  value: 'true'
  enum:
  - 'false'
  - 'true'
- capability: FunctionSet_op_nw_knitter_vpn
  comment: |-
    网络域特性：是否安装op-nw-knitter-vpn组件，该组件在TCF 拉远场景下支持云侧和边侧节点的微服务使用VPN隧道加密通信，默认值为true。
    false：不安装op-nw-knitter-vpn组件；适用于资源受限场景。
    true：安装op-nw-knitter-vpn组件；适用于非资源受限场景。
  comment_en: |-
    Network Domain Feature: Determines whether the op-nw-knitter-vpn component is installed. This component supports VPN tunnel encryption for communication between cloud and edge nodes in the TCF remote field scenario. The default value is true:
    false: The op-nw-knitter-vpn component is not installed; suitable for resource-constrained scenarios.
    true: The op-nw-knitter-vpn component is installed; suitable for non-resource-constrained scenarios.
  domain: Network Domain
  value: 'true'
  enum:
  - 'false'
  - 'true'
- capability: FunctionSet_op_nw_policy
  comment: |-
    网络域特性：是否安装op-nw-policy组件，该组件支持POD的默认网络(flannel)平面和扩展网络平面下的容器网络微隔离服务，默认值为true。
    false：不安装op-nw-policy组件；适用于资源受限场景。
    true：安装op-nw-policy组件；适用于非资源受限场景。
  comment_en: |-
    Network Domain Feature: Determines whether the op-nw-policy component is installed. This component supports container network micro-segmentation services for both the default network (flannel) plane and the extended network plane for PODs. The default value is true:
    false: The op-nw-policy component is not installed; suitable for resource-constrained scenarios.
    true: The op-nw-policy component is installed; suitable for non-resource-constrained scenarios.
  domain: Network Domain
  value: 'true'
  enum:
  - 'false'
  - 'true'
- capability: FunctionSet_kruise
  comment: |-
    是否支持Advanced StatefulSet（增强StatefulSet）和PersistentPodState（有状态POD的节点绑定）功能；
    若配置为true，会安装kruise组件，适用于资源非受限场景；
    若配置为false，则不安装，适用于资源受限场景；
    默认为false；
  comment_en: |-
    Whether to support the Advanced StatefulSet (Enhanced StatefulSet) and PersistentPodState (Node Binding for Stateful Pods)features:

    If set to true, the kruise component will be installed, which is suitable for scenarios with no resource constraints.

    If set to false, the kruise component will not be installed, which is suitable for resource-constrained scenarios.
    The default value is false.
  domain: Compute Domain
  value: 'false'
  enum:
  - 'false'
  - 'true'
- capability: FunctionSet_k8sconfig_webhook
  comment: |-
    是否支持按租户隔离功能；
    若配置为true，会安装k8sconfig_webhook组件，适用于中移云共享模式1、模式2；
    若配置为false，则不安装；
    默认为false；
  comment_en: |-
    Whether to support the Tenant Isolation:

    If set to true, the k8sconfig_webhook component will be installed, which is suitable for scenarios in China Mobile Cloud Sharing Mode 1 and Mode 2.
    If set to false, the component will not be installed.
    The default value is false.
  domain: Compute Domain
  value: 'false'
  enum:
  - 'false'
  - 'true'
- capability: FunctionSet_sysperf_perfgenius
  comment: |-
    TCF系统性能分析服务（诊断分析、性能调优等），如果支持该功能会安装op-sysperf-perfgenius组件，默认值false。
    false：不支持系统性能分析功能，不安装op-sysperf-perfgenius组件；适用于资源受限场景。
    true：支持系统性能分析功能，安装op-sysperf-perfgenius组件；适用于非资源受限场景。
  comment_en: |-
    TCF system performance analysis service (diagnostic analysis, performance tuning, etc.), if this function is supported, the op-sysperf-perfgenius component will be installed, the default value is false.
    false: The system performance analysis function is not supported, and the op-sysperf-perfgenius component is not installed; it is suitable for resource-constrained scenarios.
    true: The system performance analysis function is supported, and the op-sysperf-perfgenius component is installed; it is suitable for non-resource-constrained scenarios.
  domain: Platform Services Domain
  value: 'false'
  enum:
  - 'false'
  - 'true'
- capability: FunctionSet_k8sextender
  comment: |-
    容器计算域特性：是否安装op-containers-k8sextender组件，部署组件后支持pod指定节点重生，自定义域名配置功能。取值如下，默认值false：
    false：不安装op-containers-k8sextender组件；适用于资源受限场景。
    true：安装op-containers-k8sextender组件；适用于非资源受限场景。
  comment_en: |-
    Whether to install the op-containers-k8sextender component. After deploying the component, it supports the rebirth of the pod specified node and the custom domain name configuration function.
    The values are as follows, the default value is false：
    false：Do not install the op-containers-k8sextender component; suitable for resource-constrained scenarios.
    true：Install the op-containers-k8sextender component; suitable for non-resource-constrained scenarios.
  domain: Compute Domain
  value: 'false'
  enum:
  - 'false'
  - 'true'

- capability: FunctionSet_rdma_network
  comment: |-
    网络域特性：用于提供RDMA 网络管理功能，如果打开此功能，会安装op-nw-network-operator 组件，默认值false。
    false：不需要 RDMA 网络的场景， 不安装op-nw-network-operator组件。
    true：需要 RDMA 网络的场景，安装op-nw-network-operator组件。
  comment_en: |-
    Network Domain Feature: Provides RDMA network management capabilities. If enabled, the op-nw-network-operator component will be installed. The default value is false.
    false: For scenarios where RDMA networks are not needed, the op-nw-network-operator component is not installed.
    true: For scenarios where RDMA networks are needed, the op-nw-network-operator component is installed.
  domain: Network Domain
  value: 'false'
  enum:
  - 'false'
  - 'true'
  modifiable: 'true'

- capability: FunctionSet_op_applicationmanager_applcm
  comment: |-
    应用管理域特性：TCF-Portal前端是否支持Helm Chart应用管理功能
    取值如下，默认值false：
    false：不安装op-applicationmanager-applcm组件，TCF-portal无 “资源管理-应用管理-Helm Release管理” 菜单，“资源管理-Helm Chart”页面无“部署”按钮；适用于资源受限场景。
    true：安装op-applicationmanager-applcm组件，TCF-portal有“资源管理-应用管理-Helm Release管理”菜单，“资源管理-Helm Chart”页面有“部署”按钮； 适用于非资源受限场景。
  comment_en: |-
    Application management domain feature: whether the TCF-Portal front end supports Helm Chart application management.
    Default value: false
    Options:
      false: The op-applicationmanager-applcm component is not installed, and the TCF-portal has no "Resource Management-Application Management-Helm Release Management" menu, and the "Resource Management-Helm Chart" page has no "Deploy" button. This option is applicable to scenarios where resources are limited.
      true: The op-applicationmanager-applcm component is installed, and the TCF-portal has a "Resource Management-Application Management-Helm Release Management" menu, and the "Resource Management-Helm Chart" page has a "Deploy" button. This option is applicable to scenarios where resources are not limited.
  domain: Application Services Domain
  value: 'false'
  enum:
  - 'false'
  - 'true'

- capability: FunctionSet_helm_app_deploy_enhanced
  comment: |-
    应用管理域特性：用于提供helm app部署管理功能，如果打开此功能，会安装op-asm-helm组件，默认值true：
    false：不需要 CaaS提供helm应用部署能力 的场景， 不安装op-asm-helm组件。
    true：需要 CaaS提供helm应用部署能力 的场景， 安装op-asm-helm组件。
  comment_en: |-
    Application Management Domain Features: Used to provide Helm app deployment management capabilities. If this feature is enabled, it will install the op-asm-helm component. The default value is true:
    false: Scenarios where CaaS does not need to provide Helm application deployment capabilities, the op-asm-helm component will not be installed.
    true: Scenarios where CaaS needs to provide Helm application deployment capabilities, the op-asm-helm component will be installed.
  domain: Application Services Domain
  value: 'true'
  enum:
  - 'false'
  - 'true'

- capability: FunctionSet_operator_app_deploy_enhanced
  comment: |-
    应用管理域特性：用于提供helm app部署管理功能，如果打开此功能，会安装op-asm-helm及op-asm-operator 组件，默认值true：
    false：不需要 CaaS提供operator类型应用部署能力 的场景， 不安装op-asm-helm及op-asm-operator组件。
    true：需要 CaaS提供operator类型应用部署能力 的场景， 安装op-asm-helm及op-asm-operator组件。
  comment_en: |-
    Application Management Domain Features: Used to provide Helm app and Operator-based app deployment management capabilities. If this feature is enabled, it will install the op-asm-helm and op-asm-operator components. The default value is true:
      false: Scenarios where CaaS does not need to provide Operator-based application deployment capabilities, the op-asm-helm and op-asm-operator components will not be installed.
      true: Scenarios where CaaS needs to provide Operator-based application deployment capabilities, the op-asm-helm and op-asm-operator components will be installed.
  domain: Application Services Domain
  value: 'true'
  enum:
  - 'false'
  - 'true'

- capability: FunctionSet_cie_coredns
  comment: |-
    是否在CIE角色节点（非paas_controller节点）部署daemonset形态的备用coredns。
    取值如下，默认值false：
      false：不在CIE角色节点（非paas_controller节点）部署daemonset形态的备用coredns；默认不部署。
      true：CIE角色节点（非paas_controller节点）部署daemonset形态的备用coredns；适用于5GC标准场景。
    联动情况：
      "集群配置"sheet页中配置项"CoreDNS运行节点角色"取值为"Minion"时，新增特性开关"FunctionSet_cie_coredns"只能配置为"false"。
  comment_en: |-
    Whether to deploy standby coredns in the form of daemonset on the CIE role node (non-paas_controller node).
    The values are as follows, the default value is false：
      false：Do not deploy standby coredns in the form of daemonset on CIE role nodes (non-paas_controller nodes); not deployed by default.
      true：CIE role nodes (non-paas_controller nodes) deploy standby coredns in the form of daemonset; suitable for 5GC standard scenarios.
    Linkage situation：
      When the value of the configuration item "CoreDNS running node role" in the "Cluster Configuration" sheet is "Minion", the new feature switch "FunctionSet_cie_coredns" can only be configured as "false".
  domain: Compute Domain
  value: 'false'
  enum:
  - 'false'
  - 'true'

- capability: FunctionSet_aifdl_generic
  comment: |-
    是否支持智算AIFDL通用功能；默认为false。
    若配置为true，会安装智算基本组件；若配置为false，则不安装。
    FunctionSet_gpu=normal时，本功能集不能设置为true
  comment_en: |-
    Whether to support AIFDL generic function.
    If configured as true, the basic components of intelligent computing will be installed; If configured as false, they will not be installed.
    When FunctionalSet_gpu=normal, FunctionSet_aifdl_generic cannot be set to true.
  domain: Aifdl Domain
  value: 'false'
  enum:
  - 'false'
  - 'true'
  modifiable: 'true'

- capability: FunctionSet_aifio
  comment: |-
    是否支持透明缓存功能；默认为false。
    若配置为true，会安装透明缓存aifio组件,并且会增加资源消耗；若配置为false，则不安装。
  comment_en: |-
    Whether to support aifio function.
    If configured as true, the aifio component will be installed and memory resource consumption will be increased; If configured as false, it will not be installed
  domain: Aifdl Domain
  value: 'false'
  enum:
  - 'false'
  - 'true'
  modifiable: 'true'

- capability: Feature_etcd_auto_detect_and_recover
  comment: |-
    容器计算域特性：是否支持etcd自动识别与恢复损坏数据的开关，仅在单节点、无外部备份服务器、频繁暴力下电的场景时可以开启，不适用于集群运行期资源频繁变化的场景。非单节点场景下，不要打开该特性，即使打开也不会生效。
    取值如下，默认值false：
    false：不开启etcd自动识别与恢复损坏数据。
    true：开启etcd自动识别与恢复损坏数据。
  comment_en: |-
    Compute Domain Features: A switch that supports whether the etcd automatically identifies and recovers damaged data. This switch can only be enabled in scenarios with a single node, no external backup server, and frequent violent power failures. It is not suitable for scenarios where resources change frequently during cluster operation.In non-single-node scenarios, do not enable this feature. Even if it is enabled, it will not take effect.
    The values are as follows, and the default value is false:
    false: Do not enable the etcd to automatically identify and recover damaged data.
    true: Enable the etcd to automatically identify and recover damaged data.
  domain: Compute Domain
  value: 'false'
  enum:
  - 'false'
  - 'true'
  modifiable: 'true'
