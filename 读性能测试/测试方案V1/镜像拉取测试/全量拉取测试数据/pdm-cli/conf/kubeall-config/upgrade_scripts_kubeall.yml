base_dir: /etc/pdm/workspace/upgrade_scripts

scripts:

- path: common/upgrade_component_config.sh
  desc: common/upgrade_component_config.sh
  type: upgrade

- path: common/backup_file_for_inherit_security.sh
  desc: common/backup_file_for_inherit_security.sh
  type: prepare

- path: common/deal_sec_harden_after_upgrade.sh
  desc: Fix os harden bugs.
  type: upgrade
  parameters:
    {% if envs.node_list %}
    - node_list: '{{ envs.node_list}}'
    {% else %}
    - node_list: ''
    {% endif %}
    {% if envs.cpaas_to_unified_lcm %}
    - cpaas_to_unified_lcm: '{{ envs.cpaas_to_unified_lcm}}'
    {% else %}
    - cpaas_to_unified_lcm: false
    {% endif %}

- path: inetagent/update_kubeall_inetrules_prepare.sh
  desc: Fix intrules deployment
  type: prepare

- path: common/backup_ansible.sh
  desc: backup old ansible version
  type: prepare
  
- path: adrm/tag_for_biren_exporter.sh
  desc: tag for biren exporter
  type: prepare

- path: common/rollback_ansible.sh
  desc: rollback ansible
  type: rollback
  parameters:
    {% if envs.node_list %}
    - node_list: '{{ envs.node_list}}'
    {% else %}
    - node_list: ''
    {% endif %}

- path: swr/delete_swr_svc.sh
  desc: delete swr-proxy service
  type: upgrade

- path: k8s/remove_runtime_dependencies.sh
  desc: remove container runtime dependencies
  type: prepare

- path: plcm/delete_bak_nodes_info.sh
  desc: delete backup node info dir
  type: upgrade

- path: xlb/xlb_clear_check_pod.sh
  desc: remove xlbcheckpod.sh and rc.local
  type: prepare
