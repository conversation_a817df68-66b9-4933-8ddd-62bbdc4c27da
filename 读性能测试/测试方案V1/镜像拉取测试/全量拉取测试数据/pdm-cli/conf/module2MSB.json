{"module": [{"serviceName": "authen", "sourceName": "utm,utm,", "pkg_name": "utm", "com_name": "utm_server", "version": "v1", "url": "/authen/v1", "roles": ["paas_controller"], "protocol": "HTTP", "visualRange": "1"}, {"serviceName": "author", "sourceName": "utm,utm,", "pkg_name": "utm", "com_name": "utm_server", "version": "v1", "url": "/author/v1", "roles": ["paas_controller"], "protocol": "HTTP", "visualRange": "1"}, {"serviceName": "blockstorage", "sourceName": ",blockstorage,", "pkg_name": "blockstorage", "com_name": "blockstorage", "version": "v1", "url": "/v2", "protocol": "HTTP", "visualRange": "1", "roles": ["paas_controller"]}, {"serviceName": "br-engine", "sourceName": ",,", "pkg_name": "br", "com_name": "br", "version": "v1", "url": "/br-engine/v1", "protocol": "HTTP", "visualRange": "1", "roles": ["paas_controller"]}, {"serviceName": "cas", "sourceName": "utm,utm,", "pkg_name": "utm", "com_name": "utm_server", "version": "v1", "url": "/author/v1", "roles": ["paas_controller"], "protocol": "HTTP", "visualRange": "1"}, {"serviceName": "cassandra", "sourceName": ",cassandra,", "pkg_name": "cassandra", "com_name": "cassandra_cql", "version": "", "protocol": "TCP", "visualRange": "1", "roles": ["cassandra"]}, {"serviceName": "clusterworker", "sourceName": ",cf-pcluster,cf-pcluster-service", "pkg_name": "cf-pcluster", "com_name": "cf_pcluster_api", "version": "", "url": "/clusterworker", "roles": ["paas_controller"], "protocol": "HTTP", "visualRange": "1"}, {"serviceName": "csmworker", "sourceName": "cf-csm,cf-csm,", "pkg_name": "cf-csm", "com_name": "cf_csm", "version": "v1", "url": "/csmworker/v1", "roles": ["paas_controller"], "protocol": "HTTP", "visualRange": "1"}, {"serviceName": "csm-worker", "sourceName": "cf-csm,cf-csm,", "pkg_name": "cf-csm", "com_name": "cf_csm", "version": "v2", "url": "/csmworker/v2", "roles": ["paas_controller"], "protocol": "HTTP", "visualRange": "1"}, {"serviceName": "config", "sourceName": "utm,utm,", "pkg_name": "utm", "com_name": "utm_server", "version": "v1", "url": "/author/v1", "roles": ["paas_controller"], "protocol": "HTTP", "visualRange": "1"}, {"serviceName": "cnrm", "sourceName": ",cnrm-manager,cnrm-manager-service", "pkg_name": "cnrm-manager", "com_name": "cnrm_manager", "version": "v1", "url": "/cnrm/v1", "protocol": "HTTP", "visualRange": "1", "roles": ["paas_controller"]}, {"serviceName": "deployworker", "sourceName": ",cf-pdeploy,cf-pdeploy-service", "pkg_name": "cf-pdeploy", "com_name": "cf_pdeploy_api", "version": "", "url": "/deployworker", "roles": ["paas_controller"], "protocol": "HTTP", "visualRange": "1"}, {"serviceName": "psm", "sourceName": "cf-pdeploy-service,cf-pdeploy,", "pkg_name": "cf-pdeploy", "com_name": "cf_pdeploy_api", "version": "", "url": "/psm", "roles": ["paas_controller"], "protocol": "HTTP", "visualRange": "1"}, {"serviceName": "ens", "sourceName": "eps,eps,", "pkg_name": "eps", "com_name": "eps", "version": "v1", "url": "/ens/v1", "protocol": "HTTP", "visualRange": "1", "roles": ["paas_controller"]}, {"serviceName": "eps", "sourceName": "eps,eps,", "pkg_name": "eps", "com_name": "eps", "version": "", "url": "/eps", "protocol": "HTTP", "visualRange": "1", "roles": ["paas_controller"]}, {"serviceName": "eventmgt", "sourceName": "eventmgt,eventmgt,", "pkg_name": "eventmgt", "com_name": "eventmgt", "version": "v1", "url": "/eventmgt/v1", "protocol": "HTTP", "visualRange": "1", "roles": ["paas_controller"]}, {"serviceName": "harvestor", "sourceName": "harvestor,harvestor,", "pkg_name": "eventmgt", "com_name": "eventmgt", "version": "v1", "url": "/harvestor/v1", "protocol": "HTTP", "visualRange": "1", "roles": ["paas_controller"]}, {"serviceName": "dbtools", "sourceName": ",op-ubs-dbtools,", "pkg_name": "op-ubs-dbtools", "com_name": "op_ubs_dbtools", "version": "v1", "url": "/dbtools/v1", "protocol": "HTTP", "visualRange": "1", "roles": ["paas_controller"]}, {"serviceName": "monitor", "sourceName": ",,", "pkg_name": "monitor", "com_name": "monitor", "version": "v1", "url": "/monitor/v1", "protocol": "HTTP", "visualRange": "1", "roles": ["paas_controller"]}, {"serviceName": "myportal", "sourceName": "myportal,myportal,", "pkg_name": "myportal", "com_name": "myportal", "version": "", "url": "/myportal", "roles": ["paas_controller"], "protocol": "HTTP", "visualRange": "0"}, {"serviceName": "nodeworker", "sourceName": ",cf-pnode,cf-pnode-service", "pkg_name": "cf-pnode", "com_name": "cf_pnode_api", "version": "", "url": "/nodeworker", "roles": ["paas_controller"], "protocol": "HTTP", "visualRange": "1"}, {"serviceName": "nw", "sourceName": ",knitter-manager,knitter-manager", "pkg_name": "nwmaster", "com_name": "nwmaster", "version": "v1", "url": "/nw", "path": "/nw", "protocol": "HTTP", "visualRange": "1", "roles": ["paas_controller"]}, {"serviceName": "nwapi", "sourceName": ",knitter-manager,knitter-manager", "pkg_name": "nwmaster", "com_name": "nwmaster", "version": "v1", "url": "/api", "path": "/nwapi", "protocol": "HTTP", "visualRange": "1", "roles": ["paas_controller"]}, {"serviceName": "opapi", "sourceName": "utm,utm,", "pkg_name": "utm", "com_name": "utm", "version": "", "url": "/api", "roles": ["paas_controller"], "protocol": "HTTP", "visualRange": "0"}, {"serviceName": "exapi", "sourceName": "utm,utm,", "pkg_name": "utm", "com_name": "utm", "version": "", "url": "/exapi", "roles": ["paas_controller"], "protocol": "HTTP", "visualRange": "0"}, {"serviceName": "<PERSON><PERSON><PERSON>", "sourceName": "opslet,opslet,", "pkg_name": "<PERSON><PERSON><PERSON>", "com_name": "<PERSON><PERSON><PERSON>", "version": "v1", "url": "/opslet/v1", "protocol": "HTTP", "visualRange": "1", "roles": ["usednodes"]}, {"serviceName": "pdman", "sourceName": ",cf-pdman,cf-pdman-service", "pkg_name": "cf-pdman", "com_name": "cf_pdman_api", "version": "v1", "url": "/pdman/v1", "roles": ["paas_controller"], "protocol": "HTTP", "visualRange": "1"}, {"serviceName": "portal", "sourceName": "iportal,iportal,", "pkg_name": "iportal", "com_name": "iportal", "version": "", "url": "/portal", "roles": ["paas_controller"], "protocol": "HTTP", "visualRange": "0"}, {"serviceName": "<PERSON><PERSON><PERSON>", "sourceName": "iportaladmin,iportaladmin,", "pkg_name": "iportaladmin", "com_name": "iportaladmin", "version": "", "url": "/portaladmin", "roles": ["paas_controller"], "protocol": "HTTP", "visualRange": "0"}, {"serviceName": "pmc", "sourceName": ",pmc,", "pkg_name": "pmc", "com_name": "pmc", "version": "v1", "url": "/pmc/v1", "protocol": "HTTP", "visualRange": "1", "roles": ["paas_controller"]}, {"serviceName": "pronoea", "sourceName": ",pronoea,", "pkg_name": "pronoea", "com_name": "pronoea", "version": "v1", "url": "/pronoea/v1", "protocol": "HTTP", "visualRange": "1", "roles": ["paas_controller"]}, {"serviceName": "pm-mgt", "sourceName": "opsapiserver_service,opsapiserver,", "pkg_name": "opsapiserver", "com_name": "opsapiserver_metric", "version": "v1", "url": "/pm_mgt/v1", "path": "/pm_mgt/v1", "protocol": "HTTP", "visualRange": "1", "roles": ["paas_controller"]}, {"serviceName": "posd", "sourceName": ",posd,", "pkg_name": "posd", "com_name": "posd", "version": "v1", "url": "/v1", "roles": ["paas_controller"], "protocol": "HTTP", "visualRange": "1", "path": "/v1"}, {"serviceName": "project", "sourceName": "utm,utm,", "pkg_name": "utm", "com_name": "utm_server", "version": "v1", "url": "/project/v1", "roles": ["paas_controller"], "protocol": "HTTP", "visualRange": "1"}, {"serviceName": "sqm", "sourceName": ",sqm,", "pkg_name": "sqm", "com_name": "sqm", "version": "v1", "url": "/sqm/v1", "protocol": "HTTP", "visualRange": "1", "roles": ["paas_controller"]}, {"serviceName": "storage", "sourceName": ",storage,", "pkg_name": "storage", "com_name": "storage", "version": "v1", "url": "/storage/v1", "protocol": "HTTP", "visualRange": "1", "roles": ["paas_controller"], "labels": ["prometheus:service"]}, {"serviceName": "storage-agent", "sourceName": ",,", "pkg_name": "storage_agent", "com_name": "storage_agent", "version": "v1", "url": "/storageagent/v1", "path": "/storage_agent/v1", "protocol": "HTTP", "visualRange": "1", "roles": ["glusterfs_server"]}, {"serviceName": "swr", "sourceName": "zartsrv,zartsrv,zartsrv", "pkg_name": "zart", "com_name": "swr", "version": "", "path": "/swr/v1", "url": "/swr/v1", "protocol": "HTTP", "visualRange": "1", "roles": ["soft-repo"]}, {"serviceName": "toposerver", "sourceName": ",,", "pkg_name": "toposervice", "com_name": "toposervice", "version": "v1", "url": "/toposerver/v1", "protocol": "HTTP", "visualRange": "1", "roles": ["paas_controller"]}, {"serviceName": "topoplat", "sourceName": ",,", "pkg_name": "toposervice", "com_name": "toposervice", "version": "v1", "url": "/topoplat/v1", "protocol": "HTTP", "visualRange": "1", "roles": ["paas_controller"]}, {"serviceName": "user", "sourceName": "utm,utm,", "pkg_name": "utm", "com_name": "utm_server", "version": "v1", "url": "/user/v1", "roles": ["paas_controller"], "protocol": "HTTP", "visualRange": "1"}, {"serviceName": "manager-cert", "sourceName": "manager-cert,manager-cert,", "pkg_name": "manager-cert", "com_name": "manager_cert", "version": "v1", "url": "/manager-cert/v1", "roles": ["paas_controller"], "protocol": "HTTP", "visualRange": "1"}, {"serviceName": "vnm", "sourceName": ",,", "pkg_name": "vnm", "com_name": "vnm", "version": "v2.0", "url": "/v2.0", "protocol": "HTTP", "visualRange": "1", "roles": ["paas_controller"]}, {"serviceName": "vnpm-deploy", "sourceName": "cf-vnpm-service,cf-vnpm,", "pkg_name": "cf-vnpm", "com_name": "cf_vnpm", "version": "v1", "url": "/vnpm_deploy/v1", "path": "/vnpm_deploy/v1", "protocol": "HTTP", "visualRange": "1", "roles": ["paas_controller"]}, {"serviceName": "asm", "sourceName": "cf-vnpm-service,cf-vnpm,", "pkg_name": "cf-vnpm", "com_name": "cf_vnpm", "version": "v2", "url": "/asm/v2", "protocol": "HTTP", "visualRange": "1", "roles": ["paas_controller"]}, {"serviceName": "winery", "sourceName": "modeldesign,modeldesign,", "pkg_name": "zenap_modeldesign", "com_name": "zenap_modeldesign", "version": "", "url": "/winery", "protocol": "HTTP", "visualRange": "1", "roles": ["paas_controller"]}, {"serviceName": "winery-topologymodeler", "sourceName": "modeldesign,modeldesign,", "pkg_name": "zenap_modeldesign", "com_name": "zenap_modeldesign", "version": "", "url": "/winery-topologymodeler", "protocol": "HTTP", "visualRange": "0", "roles": ["paas_controller"]}, {"serviceName": "modeldesign", "sourceName": "modeldesign", "pkg_name": "zenap_modeldesign", "com_name": "zenap_modeldesign", "version": "v1", "url": "/api/modeldesign/v1", "path": "/modeldesign/v1", "protocol": "REST", "visualRange": "1", "roles": ["paas_controller"]}, {"serviceName": "designer", "sourceName": "modeldesign", "pkg_name": "zenap_modeldesign", "com_name": "zenap_modeldesign", "version": "v1", "url": "/designer", "path": "/designer", "protocol": "HTTP", "visualRange": "0", "roles": ["paas_controller"]}, {"serviceName": "nbm", "sourceName": "cf-nbm-service,cf-nbm,", "pkg_name": "cf-nbm", "com_name": "cf_nbm", "version": "v1", "url": "/nbm/v1", "roles": ["paas_controller"], "protocol": "HTTP", "visualRange": "1"}, {"serviceName": "utm", "sourceName": "utm,utm,", "pkg_name": "utm", "com_name": "utm_server", "version": "v1", "url": "/utm/v1", "roles": ["paas_controller"], "protocol": "HTTP", "visualRange": "1"}, {"serviceName": "paasinitiator", "sourceName": ",cf-pdman,cf-pdman-service", "pkg_name": "cf-pdman", "com_name": "cf_pdman_api", "version": "v1", "url": "/paasinitiator/v1", "roles": ["paas_controller"], "protocol": "HTTP", "visualRange": "1"}, {"serviceName": "metric", "sourceName": "opsapiserver_service,opsapiserver,", "pkg_name": "opsapiserver", "com_name": "opsapiserver_metric", "version": "", "url": "/metric", "protocol": "HTTP", "visualRange": "1", "roles": ["paas_controller"]}, {"serviceName": "prometheus", "sourceName": ",,", "pkg_name": "prometheus", "com_name": "prometheus", "version": "", "protocol": "TCP", "visualRange": "1", "roles": ["monitor-pm"]}, {"serviceName": "inetmanager", "sourceName": "inetmanager,inetmanager", "pkg_name": "inetmanager", "com_name": "inetmanager", "version": "v1", "url": "/inetmanager/v1", "roles": ["paas_controller"], "protocol": "HTTP", "visualRange": "1"}, {"serviceName": "msb-apigateway-exporter", "sourceName": ",,", "pkg_name": "zenap_msb_apigateway", "com_name": "zenap_msb_apigateway", "version": "v1", "url": "/admin/microservices/v1", "protocol": "TCP", "visualRange": "1", "roles": ["paas_controller"]}, {"serviceName": "msb-router-exporter", "sourceName": ",,", "pkg_name": "zenap_msb_router", "com_name": "zenap_msb_router_mgt", "version": "v1", "url": "/admin/microservices/v1", "protocol": "TCP", "visualRange": "1", "roles": ["paas_controller"]}, {"serviceName": "nwmonitor", "sourceName": ",knitter-monitor,knitter-monitor,", "pkg_name": "nwmonitor", "com_name": "nwmonitor", "version": "v1", "url": "/nwmonitor/v1", "protocol": "HTTP", "visualRange": "1", "roles": ["master"]}, {"serviceName": "nwd", "sourceName": ",,", "pkg_name": "nwd", "com_name": "nwd", "version": "v1", "url": "/nwd/v1", "protocol": "HTTP", "visualRange": "1", "roles": ["paas_controller"]}, {"serviceName": "dexmesh-pilot", "sourceName": "", "pkg_name": "op-dexmesh-pilot", "com_name": "dexmesh_pilot", "version": "", "url": "/", "protocol": "HTTP", "visualRange": "1", "roles": ["paas_controller"], "path": "/pilot/v1"}, {"serviceName": "dexmesh-canary-server", "sourceName": "", "pkg_name": "op-dexmesh-canary-server", "com_name": "dexmesh_canary_server", "version": "", "labels": ["prometheus:service"], "url": "/", "protocol": "HTTP", "visualRange": "1", "roles": ["paas_controller"], "path": "/dexmesh-canary-server/v1"}, {"serviceName": "dexmesh-config-server", "sourceName": "", "pkg_name": "op-dexmesh-config-server", "com_name": "dexmesh_config_server", "version": "", "url": "/", "protocol": "HTTP", "visualRange": "1", "roles": ["paas_controller"], "path": "/dexmesh-config-server/v1"}, {"serviceName": "dexmesh-resource", "sourceName": "", "pkg_name": "op-dexmesh-resource", "com_name": "dexmesh_resource", "version": "v1", "url": "/", "protocol": "HTTP", "visualRange": "1", "roles": ["paas_controller"], "path": "/dexmesh-resource/v1"}, {"serviceName": "dexmesh-sidecar-injector", "sourceName": "", "pkg_name": "op-dexmesh-sidecar-injector", "com_name": "dexmesh_sidecar_injector", "version": "v1", "url": "/inject", "protocol": "HTTP", "visualRange": "1", "roles": ["paas_controller"], "path": "/inject", "enable_ssl": true}, {"serviceName": "dexmesh-message-routing-poll", "sourceName": "", "pkg_name": "op-dexmesh-config-server", "com_name": "dexmesh_message_routing_poll", "version": "", "url": "/", "protocol": "HTTP", "visualRange": "1", "roles": ["paas_controller"], "path": "/dexmesh-message-routing-poll/v1"}, {"serviceName": "logstash", "sourceName": ",,", "pkg_name": "logstash", "com_name": "logstash", "version": "", "protocol": "TCP", "visualRange": "1", "roles": ["elk"]}, {"serviceName": "pconf", "sourceName": "pconf", "pkg_name": "op-confcenter-pconf", "com_name": "pconf", "version": "v1", "url": "/pconf/v1", "roles": ["paas_controller"], "protocol": "HTTP", "visualRange": "1"}, {"serviceName": "psecret", "sourceName": "pconf", "pkg_name": "op-confcenter-pconf", "com_name": "pconf", "version": "v1", "url": "/psecret/v1", "roles": ["paas_controller"], "protocol": "HTTP", "visualRange": "1"}, {"serviceName": "dexmesh-message-routing", "sourceName": "", "pkg_name": "op-dexmesh-config-server", "com_name": "dexmesh_message_routing_rest", "version": "", "url": "/", "protocol": "HTTP", "visualRange": "1", "roles": ["paas_controller"], "path": "/dexmesh-message-routing/v1"}, {"serviceName": "dexmesh-message-routing-grpc", "sourceName": ",,", "pkg_name": "op-dexmesh-config-server", "com_name": "dexmesh_message_routing_grpc", "version": "", "protocol": "TCP", "visualRange": "1", "roles": ["paas_controller"]}, {"serviceName": "inetagent", "sourceName": ",,", "pkg_name": "inetagent", "com_name": "inetagent", "version": "v1", "url": "/inetagent/v1", "protocol": "HTTP", "visualRange": "1", "roles": ["usednodes"]}, {"serviceName": "nfsagent", "sourceName": "nfs_agent,", "pkg_name": "nfs_agent", "com_name": "nfs_server_metric", "version": "v1", "url": "/", "protocol": "HTTP", "visualRange": "1", "roles": ["nfs_server"], "vips": ["nfs_vip"]}, {"serviceName": "msb-sdclient-exporter", "sourceName": ",,", "pkg_name": "zenap_msb_sdclient", "com_name": "zenap_msb_sdclient", "version": "v1", "url": "sdclient/client/request/metrics", "protocol": "TCP", "visualRange": "1", "roles": ["paas_controller"]}, {"serviceName": "skydive-analyzer", "sourceName": "", "pkg_name": "op-nw-netinsight-analyzer", "com_name": "netinsight_analyzer_offset", "version": "", "url": "", "roles": ["paas_controller"], "protocol": "HTTP", "visualRange": "0", "enable_ssl": true, "vips": ["router_vip", "router_vip_v6"], "publish_port": "8082|null|null", "path": "/ROOT_PATH"}, {"serviceName": "userkpi", "sourceName": "userkpi,userkpi,", "pkg_name": "userkpi", "com_name": "userkpi", "version": "v1", "url": "/userkpi/v1", "roles": ["minion"], "protocol": "HTTP", "visualRange": "1"}]}