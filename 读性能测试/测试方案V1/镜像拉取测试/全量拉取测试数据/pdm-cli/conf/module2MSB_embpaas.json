{"module": [{"serviceName": "authen", "sourceName": "utm,utm,", "pkg_name": "utm", "com_name": "utm_server", "version": "v1", "url": "/authen/v1", "roles": ["paas_controller"], "protocol": "HTTP", "visualRange": "0"}, {"serviceName": "author", "sourceName": "utm,utm,", "pkg_name": "utm", "com_name": "utm_server", "version": "v1", "url": "/author/v1", "roles": ["paas_controller"], "protocol": "HTTP", "visualRange": "0"}, {"serviceName": "blockstorage", "sourceName": ",blockstorage,", "pkg_name": "blockstorage", "com_name": "blockstorage", "version": "v1", "url": "/v2", "protocol": "HTTP", "visualRange": "0", "roles": ["paas_controller"]}, {"serviceName": "br-engine", "sourceName": ",,", "pkg_name": "br", "com_name": "br", "version": "v1", "url": "/br-engine/v1", "protocol": "HTTP", "visualRange": "0", "roles": ["paas_controller"]}, {"serviceName": "cas", "sourceName": "utm,utm,", "pkg_name": "utm", "com_name": "utm_server", "version": "v1", "url": "/author/v1", "roles": ["paas_controller"], "protocol": "HTTP", "visualRange": "0"}, {"serviceName": "cassandra", "sourceName": ",cassandra,", "pkg_name": "cassandra", "com_name": "cassandra_cql", "version": "", "protocol": "TCP", "visualRange": "0", "roles": ["cassandra"]}, {"serviceName": "clusterworker", "sourceName": ",cf-pcluster,cf-pcluster-service", "pkg_name": "cf-pcluster", "com_name": "cf_pcluster_api", "version": "", "url": "/clusterworker", "roles": ["paas_controller"], "protocol": "HTTP", "visualRange": "0"}, {"serviceName": "csmworker", "sourceName": "cf-csm,cf-csm,", "pkg_name": "cf-csm", "com_name": "cf_csm", "version": "v1", "url": "/csmworker/v1", "roles": ["paas_controller"], "protocol": "HTTP", "visualRange": "0"}, {"serviceName": "collectormgt", "sourceName": ",,", "pkg_name": "collectormgt", "com_name": "collectormgt", "version": "v1", "url": "/collectormgt/v1", "protocol": "HTTP", "visualRange": "0", "roles": ["paas_controller"]}, {"serviceName": "config", "sourceName": "utm,utm,", "pkg_name": "utm", "com_name": "utm_server", "version": "v1", "url": "/author/v1", "roles": ["paas_controller"], "protocol": "HTTP", "visualRange": "0"}, {"serviceName": "cnrm", "sourceName": ",cnrm-manager,cnrm-manager-service", "pkg_name": "cnrm-manager", "com_name": "cnrm_manager", "version": "v1", "url": "/cnrm/v1", "protocol": "HTTP", "visualRange": "0", "roles": ["paas_controller"]}, {"serviceName": "deployworker", "sourceName": ",cf-pdeploy,cf-pdeploy-service", "pkg_name": "cf-pdeploy", "com_name": "cf_pdeploy", "version": "v1", "url": "/deployworker/v1", "roles": ["paas_controller"], "protocol": "HTTP", "visualRange": "0"}, {"serviceName": "psm", "sourceName": "cf-pdeploy-service,cf-pdeploy,", "pkg_name": "cf-pdeploy", "com_name": "cf_pdeploy", "version": "v1", "url": "/psm/v1", "roles": ["paas_controller"], "protocol": "HTTP", "visualRange": "0"}, {"serviceName": "ens", "sourceName": "eps,eps,", "pkg_name": "eps", "com_name": "eps", "version": "v1", "url": "/ens/v1", "protocol": "HTTP", "visualRange": "0", "roles": ["paas_controller"]}, {"serviceName": "eps", "sourceName": "eps,eps,", "pkg_name": "eps", "com_name": "eps", "version": "", "url": "/eps", "protocol": "HTTP", "visualRange": "0", "roles": ["paas_controller"]}, {"serviceName": "eventmgt", "sourceName": "opsapiserver_service,opsapiserver,", "pkg_name": "opsapiserver", "com_name": "opsapiserver_event", "version": "v1", "url": "/eventmgt/v1", "protocol": "HTTP", "visualRange": "0", "roles": ["paas_controller"]}, {"serviceName": "fm-mgt", "sourceName": "fm_mgt,fm_mgt,", "pkg_name": "fm_mgt", "com_name": "fm_mgt", "version": "v1", "url": "/fm_mgt/v1", "path": "/fm_mgt/v1", "protocol": "HTTP", "visualRange": "0", "roles": ["paas_controller"]}, {"serviceName": "harvestor-fm", "sourceName": "fm_mgt,fm_mgt,", "pkg_name": "fm_mgt", "com_name": "fm_mgt", "version": "v1", "url": "/harvestor/v1/tenants/admin/events", "protocol": "HTTP", "visualRange": "0", "roles": ["paas_controller"], "path": "/harvestor/v1/tenants/admin/events"}, {"serviceName": "harvestor", "sourceName": "harvestor,harvestor,", "pkg_name": "eventmgt", "com_name": "eventmgt", "version": "v1", "url": "/harvestor/v1", "protocol": "HTTP", "visualRange": "0", "roles": ["paas_controller"]}, {"serviceName": "dbtools", "sourceName": ",op-ubs-dbtools,", "pkg_name": "op-ubs-dbtools", "com_name": "op_ubs_dbtools", "version": "v1", "url": "/dbtools/v1", "protocol": "HTTP", "visualRange": "0", "roles": ["paas_controller"]}, {"serviceName": "monitor", "sourceName": ",,", "pkg_name": "monitor", "com_name": "monitor", "version": "v1", "url": "/monitor/v1", "protocol": "HTTP", "visualRange": "0", "roles": ["paas_controller"]}, {"serviceName": "myportal", "sourceName": "myportal,myportal,", "pkg_name": "myportal", "com_name": "myportal", "version": "", "url": "/myportal", "roles": ["paas_controller"], "protocol": "HTTP", "visualRange": "0"}, {"serviceName": "nodeworker", "sourceName": ",cf-pnode,cf-pnode-service", "pkg_name": "cf-pnode", "com_name": "cf_pnode_api", "version": "v1", "url": "/nodeworker/v1", "roles": ["paas_controller"], "protocol": "HTTP", "visualRange": "0"}, {"serviceName": "nw", "sourceName": ",knitter-manager,knitter-manager", "pkg_name": "nwmaster", "com_name": "nwmaster", "version": "v1", "url": "/nw", "path": "/nw", "protocol": "HTTP", "visualRange": "0", "roles": ["paas_controller"]}, {"serviceName": "nwapi", "sourceName": ",knitter-manager,knitter-manager", "pkg_name": "nwmaster", "com_name": "nwmaster", "version": "v1", "url": "/api", "path": "/nwapi", "protocol": "HTTP", "visualRange": "0", "roles": ["paas_controller"]}, {"serviceName": "opapi", "sourceName": "zenap_msb_router,zenap_msb_router,", "pkg_name": "zenap_msb_router", "com_name": "zenap_msb_router", "version": "", "url": "/", "roles": ["paas_controller"], "protocol": "HTTP", "visualRange": "0"}, {"serviceName": "openpalette-microservices", "sourceName": "zenap_msb_sdclient,zenap_msb_sdclient,", "pkg_name": "zenap_msb_sdclient", "com_name": "zenap_msb_sdclient", "version": "v1", "url": "/api/microservices/v1", "roles": ["paas_controller"], "protocol": "HTTP", "visualRange": "0"}, {"serviceName": "<PERSON><PERSON><PERSON>", "sourceName": "opslet,opslet,", "pkg_name": "<PERSON><PERSON><PERSON>", "com_name": "<PERSON><PERSON><PERSON>", "version": "v1", "url": "/opslet/v1", "protocol": "HTTP", "visualRange": "0", "roles": ["usednodes"]}, {"serviceName": "pdman", "sourceName": ",cf-pdman,cf-pdman-service", "pkg_name": "cf-pdman", "com_name": "cf_pdman", "version": "v1", "url": "/pdman/v1", "roles": ["paas_controller"], "protocol": "HTTP", "visualRange": "0"}, {"serviceName": "portal", "sourceName": "iportal,iportal,", "pkg_name": "iportal", "com_name": "iportal", "version": "", "url": "/portal", "roles": ["paas_controller"], "protocol": "HTTP", "visualRange": "0"}, {"serviceName": "<PERSON><PERSON><PERSON>", "sourceName": "iportaladmin,iportaladmin,", "pkg_name": "iportaladmin", "com_name": "iportaladmin", "version": "", "url": "/portaladmin", "roles": ["paas_controller"], "protocol": "HTTP", "visualRange": "0"}, {"serviceName": "pmc", "sourceName": ",pmc,", "pkg_name": "pmc", "com_name": "pmc", "version": "v1", "url": "/pmc/v1", "protocol": "HTTP", "visualRange": "0", "roles": ["paas_controller"]}, {"serviceName": "pm-agent", "sourceName": "pm_agent,pm_agent,", "pkg_name": "pm_agent", "com_name": "pm_agent_heapster", "version": "", "protocol": "TCP", "visualRange": "0", "roles": ["master,os_master"]}, {"serviceName": "pm-mgt", "sourceName": "opsapiserver_service,opsapiserver,", "pkg_name": "opsapiserver", "com_name": "opsapiserver_metric", "version": "v1", "url": "/pm_mgt/v1", "path": "/pm_mgt/v1", "protocol": "HTTP", "visualRange": "0", "roles": ["paas_controller"]}, {"serviceName": "posd", "sourceName": ",posd,", "pkg_name": "posd", "com_name": "posd", "version": "v1", "url": "/v1", "roles": ["paas_controller"], "protocol": "HTTP", "visualRange": "0", "path": "/v1"}, {"serviceName": "project", "sourceName": "utm,utm,", "pkg_name": "utm", "com_name": "utm_server", "version": "v1", "url": "/project/v1", "roles": ["paas_controller"], "protocol": "HTTP", "visualRange": "0"}, {"serviceName": "sqm", "sourceName": ",sqm,", "pkg_name": "sqm", "com_name": "sqm", "version": "v1", "url": "/sqm/v1", "protocol": "HTTP", "visualRange": "0", "roles": ["paas_controller"]}, {"serviceName": "storage", "sourceName": ",storage,", "pkg_name": "storage", "com_name": "storage", "version": "v1", "url": "/storage/v1", "protocol": "HTTP", "visualRange": "0", "roles": ["paas_controller"]}, {"serviceName": "storage-agent", "sourceName": ",,", "pkg_name": "storage_agent", "com_name": "storage_agent", "version": "v1", "url": "/storageagent/v1", "path": "/storage_agent/v1", "protocol": "HTTP", "visualRange": "0", "roles": ["glusterfs_server"]}, {"serviceName": "swr", "sourceName": "zartsrv,zartsrv,zartsrv", "pkg_name": "zart", "com_name": "swr", "version": "", "path": "/swr/v1", "url": "/swr/v1", "protocol": "HTTP", "visualRange": "0", "roles": ["soft-repo"]}, {"serviceName": "user", "sourceName": "utm,utm,", "pkg_name": "utm", "com_name": "utm_server", "version": "v1", "url": "/user/v1", "roles": ["paas_controller"], "protocol": "HTTP", "visualRange": "0"}, {"serviceName": "vnm", "sourceName": ",,", "pkg_name": "vnm", "com_name": "vnm", "version": "v2.0", "url": "/v2.0", "protocol": "HTTP", "visualRange": "0", "roles": ["paas_controller"]}, {"serviceName": "vnpm-deploy", "sourceName": "cf-vnpm-service,cf-vnpm,", "pkg_name": "cf-vnpm", "com_name": "cf_vnpm", "version": "v1", "url": "/vnpm_deploy/v1", "path": "/vnpm_deploy/v1", "protocol": "HTTP", "visualRange": "0", "roles": ["paas_controller"]}, {"serviceName": "winery", "sourceName": "modeldesign,modeldesign,", "pkg_name": "zenap_modeldesign", "com_name": "zenap_modeldesign", "version": "", "url": "/winery", "protocol": "HTTP", "visualRange": "0", "roles": ["paas_controller"]}, {"serviceName": "winery-topologymodeler", "sourceName": "modeldesign,modeldesign,", "pkg_name": "zenap_modeldesign", "com_name": "zenap_modeldesign", "version": "", "url": "/winery-topologymodeler", "protocol": "HTTP", "visualRange": "0", "roles": ["paas_controller"]}, {"serviceName": "nbm", "sourceName": "cf-nbm-service,cf-nbm,", "pkg_name": "cf-nbm", "com_name": "cf_nbm", "version": "v1", "url": "/nbm/v1", "roles": ["paas_controller"], "protocol": "HTTP", "visualRange": "0"}, {"serviceName": "utm", "sourceName": "utm,utm,", "pkg_name": "utm", "com_name": "utm_server", "version": "v1", "url": "/utm/v1", "roles": ["paas_controller"], "protocol": "HTTP", "visualRange": "0"}, {"serviceName": "paasinitiator", "sourceName": ",cf-pdman,cf-pdman-service", "pkg_name": "cf-pdman", "com_name": "cf_pdman", "version": "v1", "url": "/paasinitiator/v1", "roles": ["paas_controller"], "protocol": "HTTP", "visualRange": "0"}, {"serviceName": "metric", "sourceName": "opsapiserver_service,opsapiserver,", "pkg_name": "opsapiserver", "com_name": "opsapiserver_metric", "version": "", "url": "/metric", "protocol": "HTTP", "visualRange": "0", "roles": ["paas_controller"]}, {"serviceName": "prometheus", "sourceName": ",,", "pkg_name": "prometheus", "com_name": "prometheus", "version": "", "protocol": "TCP", "visualRange": "0", "roles": ["monitor-pm"]}, {"serviceName": "inetmanager", "sourceName": "inetmanager,inetmanager", "pkg_name": "inetmanager", "com_name": "inetmanager", "version": "v1", "url": "/inetmanager/v1", "roles": ["paas_controller"], "protocol": "HTTP", "visualRange": "0"}, {"serviceName": "log-mgt", "sourceName": "logapiserver_service,logapiserver,", "pkg_name": "logapiserver", "com_name": "logapiserver_log", "version": "v1", "url": "/log_mgt/v1", "path": "/log_mgt/v1", "protocol": "HTTP", "visualRange": "0", "roles": ["paas_controller"]}, {"serviceName": "paa<PERSON><PERSON><PERSON>", "sourceName": "paasinitiator", "pkg_name": "paasinitiator", "com_name": "paasinitiator", "version": "v1", "url": "/paasinitemb/v1", "roles": ["paas_controller"], "protocol": "HTTP", "visualRange": "0"}, {"serviceName": "nwmonitor", "sourceName": ",knitter-monitor,knitter-monitor,", "pkg_name": "nwmonitor", "com_name": "nwmonitor", "version": "v1", "url": "/nwmonitor/v1", "protocol": "HTTP", "visualRange": "0", "roles": ["master"]}, {"serviceName": "userkpi", "sourceName": "userkpi,userkpi,", "pkg_name": "userkpi", "com_name": "userkpi", "version": "v1", "url": "/userkpi/v1", "roles": ["minion"], "protocol": "HTTP", "visualRange": "0"}]}