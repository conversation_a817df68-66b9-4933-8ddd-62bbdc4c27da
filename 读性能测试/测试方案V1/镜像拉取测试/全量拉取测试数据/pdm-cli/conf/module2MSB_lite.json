{"module": [{"serviceName": "dbtools", "sourceName": ",op-ubs-dbtools,", "pkg_name": "op-ubs-dbtools", "com_name": "op_ubs_dbtools", "version": "v1", "url": "/dbtools/v1", "protocol": "HTTP", "visualRange": "1", "roles": ["paas_controller"]}, {"serviceName": "msb-sdclient-exporter", "sourceName": ",,", "pkg_name": "zenap_msb_sdclient", "com_name": "zenap_msb_sdclient", "version": "v1", "url": "sdclient/client/request/metrics", "protocol": "TCP", "visualRange": "1", "roles": ["paas_controller"]}, {"serviceName": "msb-apigateway-exporter", "sourceName": ",,", "pkg_name": "zenap_msb_apigateway", "com_name": "zenap_msb_apigateway", "version": "v1", "url": "/admin/microservices/v1", "protocol": "TCP", "visualRange": "1", "roles": ["paas_controller"]}, {"serviceName": "msb-router-exporter", "sourceName": ",,", "pkg_name": "zenap_msb_router", "com_name": "zenap_msb_router_mgt", "version": "v1", "url": "/admin/microservices/v1", "protocol": "TCP", "visualRange": "1", "roles": ["paas_controller"]}, {"serviceName": "clusterworker", "sourceName": "op-plcm-carlina", "pkg_name": "op-plcm-carlina", "com_name": "carlina_https", "version": "", "url": "/clusterworker", "roles": ["paas_controller"], "protocol": "HTTP", "enable_ssl": true, "visualRange": "1"}, {"serviceName": "nodeworker", "sourceName": "op-plcm-carlina", "pkg_name": "op-plcm-carlina", "com_name": "carlina_https", "version": "", "url": "/nodeworker", "roles": ["paas_controller"], "protocol": "HTTP", "enable_ssl": true, "visualRange": "1"}, {"serviceName": "psm", "sourceName": "op-plcm-carlina", "pkg_name": "op-plcm-carlina", "com_name": "carlina_https", "version": "", "url": "/psm", "roles": ["paas_controller"], "protocol": "HTTP", "enable_ssl": true, "visualRange": "1"}, {"serviceName": "deployworker", "sourceName": "op-plcm-carlina", "pkg_name": "op-plcm-carlina", "com_name": "carlina_https", "version": "", "url": "/deployworker", "roles": ["paas_controller"], "protocol": "HTTP", "enable_ssl": true, "visualRange": "1"}, {"serviceName": "pdman", "sourceName": "op-plcm-carlina", "pkg_name": "op-plcm-carlina", "com_name": "carlina_https", "version": "v1", "url": "/pdman/v1", "roles": ["paas_controller"], "protocol": "HTTP", "enable_ssl": true, "visualRange": "1"}, {"serviceName": "inetmanager", "sourceName": "inetmanager,inetmanager", "pkg_name": "inetmanager", "com_name": "inetmanager", "version": "v1", "url": "/inetmanager/v1", "roles": ["paas_controller"], "protocol": "HTTP", "visualRange": "1"}, {"serviceName": "lbmanager", "sourceName": "inetmanager,inetmanager", "pkg_name": "inetmanager", "com_name": "inetmanager", "version": "v1", "url": "/lbmanager/v1", "roles": ["paas_controller"], "protocol": "HTTP", "visualRange": "1"}]}