server {
    listen {{ paas_public_vip }}:{{ netinsight_analyzer_listen_port }} http2 ssl;
    include include/AuthConf/external/oneWay_auth.conf;
    add_header X-Frame-Options DENY;
    add_header Strict-Transport-Security "max-age=63072000; includeSubDomains" always;
    server_tokens off;
    proxy_http_version 1.1;
    proxy_hide_header X-Frame-Options;
    proxy_set_header Host $http_host;
    proxy_set_header Gateway-Host $http_host;
    proxy_set_header X-Real-Ip $remote_addr;
    proxy_set_header X-Forwarded-For $remote_addr;
    proxy_intercept_errors off;

    location / {
        proxy_pass https://{{ paas_router_vip }}:{{ netinsight_analyzer_listen_port }};
    }

    location /netInsight/ws {
        proxy_pass https://{{ paas_router_vip }}:{{ netinsight_analyzer_listen_port }}/netInsight/ws;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }
}
