{% if tecsclient_vip and tecsclient_vip != paas_public_vip %}
server {
  listen {{paas_public_vip}}:2600 http2 ssl;
  listen {{tecsclient_vip}}:2600 http2 ssl;
  server_tokens off;
  proxy_hide_header X-Frame-Options;
  proxy_set_header X-Forwarded-For $remote_addr;
  proxy_set_header Host $http_host;
  proxy_set_header Gateway-Host $http_host;
  add_header X-Frame-Options DENY;
  add_header Strict-Transport-Security "max-age=63072000; includeSubDomains" always;
  include include/AuthConf/external/default_auth.conf;

  location / {
    proxy_pass   https://{{paas_router_vip}}:{{zenap_msb_router_https_port}};
    proxy_http_version 1.1;
    chunked_transfer_encoding off;
    proxy_request_buffering off;
    proxy_connect_timeout 600;
    proxy_send_timeout 600;
    proxy_read_timeout 600;
    client_body_buffer_size 5m;
    proxy_buffering on;
    proxy_buffer_size 2m;
    proxy_buffers 8 1m;
    proxy_busy_buffers_size 2m;
    proxy_max_temp_file_size 0;
    client_max_body_size 0;
      proxy_intercept_errors off ;
  }
}
{% else %}
server {
  listen {{paas_public_vip}}:2600 http2 ssl;
  server_tokens off;
  proxy_hide_header X-Frame-Options;
  proxy_set_header X-Forwarded-For $remote_addr;
  proxy_set_header Host $http_host;
  proxy_set_header Gateway-Host $http_host;
  add_header X-Frame-Options DENY;
  add_header Strict-Transport-Security "max-age=63072000; includeSubDomains" always;
  include include/AuthConf/external/default_auth.conf;

  location / {
    proxy_pass   https://{{paas_router_vip}}:{{zenap_msb_router_https_port}};
    proxy_http_version 1.1;
    chunked_transfer_encoding off;
    proxy_request_buffering off;
    proxy_connect_timeout 600;
    proxy_send_timeout 600;
    proxy_read_timeout 600;
    client_body_buffer_size 5m;
    proxy_buffering on;
    proxy_buffer_size 2m;
    proxy_buffers 8 1m;
    proxy_busy_buffers_size 2m;
    proxy_max_temp_file_size 0;
    client_max_body_size 0;
      proxy_intercept_errors off ;
  }
}
{% endif %}
