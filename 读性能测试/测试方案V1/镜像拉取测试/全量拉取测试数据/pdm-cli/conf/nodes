{"nodepools": [{"cpu": 8, "image": "iaas-cgsl1.1-v5.22", "disk": 80, "nodes": [{"netinfo": {"net_api": {"ip": "************"}}, "hostname": "", "name": "paas-controller-1", "id": ""}, {"netinfo": {"net_api": {"ip": "************"}}, "hostname": "", "name": "paas-controller-2", "id": ""}, {"netinfo": {"net_api": {"ip": "************"}}, "hostname": "", "name": "paas-controller-3", "id": ""}], "memory": 8}, {"cpu": 4, "image": "iaas-cgsl1.1-v5.22", "disk": 40, "nodes": [{"netinfo": {"net_api": {"ip": "************"}}, "name": "jiang-preset-node-1", "id": "", "userdata": {}}, {"netinfo": {"net_api": {"ip": "************"}}, "name": "jiang-preset-node-2", "id": "", "userdata": {}}], "memory": 4}], "vips": {"net_api": [{"isused": "", "ip": "************", "port_name": "router_vip", "bindfloatip": "yes", "floatingip": "************"}, {"ip": "************", "port_name": "", "bindfloatip": "no", "isused": "", "floatingip": ""}, {"ip": "************", "port_name": "", "bindfloatip": "no", "isused": "", "floatingip": ""}, {"ip": "************", "port_name": "", "bindfloatip": "no", "isused": "", "floatingip": ""}, {"ip": "************", "port_name": "", "bindfloatip": "no", "isused": "", "floatingip": ""}, {"ip": "************", "port_name": "", "bindfloatip": "no", "isused": "", "floatingip": ""}, {"ip": "************", "port_name": "", "bindfloatip": "no", "isused": "", "floatingip": ""}, {"ip": "************", "port_name": "", "bindfloatip": "no", "isused": "", "floatingip": ""}, {"ip": "************", "port_name": "", "bindfloatip": "no", "isused": "", "floatingip": ""}]}, "cidr": {"net_api": "***********/16"}}