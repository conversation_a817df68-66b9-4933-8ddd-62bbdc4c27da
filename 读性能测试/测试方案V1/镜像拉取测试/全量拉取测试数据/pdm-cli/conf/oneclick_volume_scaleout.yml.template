---
nodes:
  - name: paas-controller-1
    storage_templates: 'G-CS-P,Cloud-userdata'
  - name: paas-controller-2
    storage_templates: 'G-CS-P,Cloud-userdata'
  - name: paas-controller-3
    storage_templates: 'G-CS-P,Cloud-userdata'
  - name: minion-1
    storage_templates: 'Cloud-for-minion'
storage_templates:
  - backend_type: storage_for_commonservice
    capacity: '60'
    mountpoint: /paasdata/op-comsrv
    name: G-CS-P
  - backend_type: storage_for_userdefined
    capacity: '50'
    mountpoint: /userdata_for_ume
    name: Cloud-userdata
  - backend_type: storage_for_userdefined
    capacity: '20'
    mountpoint: /tmp/user-defined-dir
    name: Cloud-for-minion
