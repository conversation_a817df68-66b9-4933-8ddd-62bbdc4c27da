[paas]
version=v7.24.30.06.f20
registry_address=***********:33666
product_version=v7.24.30.06.f20
plat_external_ips=

[pkg_ver]
ftp_server=************
username=zxpaas
password=
path=/v1.17/v1.17.30/v1.17.30.01

[controller_ha]
controller_ha_count=1

[image_info]
user=ubuntu
#Please do not modify it without authorization
auto_upgrade_with_paas=on

[zoneinfo]
timezone=Asia/Shanghai

[router]
paas_router_ip=
paas_router_ip_v6=
paas_router_subdomain=

[app_scenario]
application_scenario=
fix_ip=

[dns]
paas_dns_servers=

[ntp]
ntp_server=
ntp_server_key=

[provider]
provider_outband_ip=
hardware_network_type = outband

[network]
nw_mode=
tunnel_network=
netapi_default_ip_version=v4
preset_network_device=false
vxlan_tunnel_csum=false

[br_config]
restore_switch=off
br_restore=on
storagetype=SSH
user=
password=
ip=
port=22
fileDir=

[oplog]
language=CN,EN

[cinder_switch]
k8s_cinder = off

[cipher_switch]
cs_cipher = off

[uniview]
#可选: outside_paas/inside_paas/no_uniview
#默认：outside_paas
location=outside_paas

[database]
db_scheme=postgresql
pgpwdneedencryption=true

[secret]
secret_key=

[http]
http_disable=true

[vmwatchdog]
#watchdog state  1:on, 2:off
state=0
#heartbeat period 1<heartbeat<2046, preferably within 400
heartbeat=6
#feedperiod, 0<feedperiod<heartbeat
feedperiod=1
#feedlog state, 1:log on, 2:log off. It is recommended to close the log when used normally.
feedlog=0

[nodeports]
nodeports_range=30000-32767
auto_allocated_nodeports_range=30601-30798,30803-30889,30895-30999,31901-31996,32077-32665
plat_ports_in_nodeports_range=

[port_range]
plat_com_range=53-53, 69-69, 80-80, 111-112, 162-163, 443-443, 500-500, 665-1021, 1025-1042, 1044-3010, 3013-4499, 5000-28000, 29952-29952, 31999-31999, 35357-35357, 35500-35501, 35503-35503, 35505-35515, 35520-35522, 35531-35533, 35550-35550, 35553-35556, 35571-35571, 35593-35593, 35650-35650, 35802-35804, 35807-35807, 35900-35903, 36000-36000, 36100-36100, 36150-36150, 44300-44301, 45456-45456, 49152-49651
common_services_range=4500-4608, 4610-4999, 29951-29951, 29953-29953
ftp_data_range=29900-29950
public_services_range=28001-29800
config_services_range=

[reserved_ports]
zenap_msb_sdclient_coredns_port=53
rpcbind_port=111
k8s_ha_ka_port=112
provider_uaf_snmp1_port=162
provider_fmsnmp_port=163
nw_vpn_ike_port=500
rpc_mount_port=665-1021
sync_port=1870
provider_fm1_port=1949
provider_fm2_port=1950
provider_pmagent_port=2000
provider_pmagentuaf_port=2001
provider_pm_port=2016
provider_hwm_port=2043
provider_pick_port=2045
nfsserver_port=2049
rpc_statd_port=2050
provider_kafka1_port=2181
pcs_port=2224
vnf_lcm_server_port=2328
vnf_lcm_server_oneauth_port=2329
vnf_lcm_server_twoauth_port=2330
docker_port=2375
swarm_port=2377
etcd_client_port=2379
etcd_peer_port=2380
zenap_cos_https_port=2443
zenap_cos_admin_https_port=2444
zenap_cos_port=2480
zenap_cos_admin_port=2481
cosmos_http_port=2500
cosmos_db_port=2501
cosmos_cluster_port=2502
cosmos_internal_port=2503
cosmos_db_extra_port=2504
cosmos_cluster_extra_port=2505
cosmos_proxy_port=2507
nw_cilium_agent_liveness_probe_port=2508
carlina_https_port=2509
registry_authserver_port=2510
fm_mgt_snmp_port=2511
nw_alita_agent_port=2515
nw_alita_node_port=2516
nw_cilium_agent_gops_port=2518
nw_cilium_agent_health_check_port=2519
keystone_public_port=2520
nw_cilium_operator_gops_port=2521
nw_cilium_operator_liveness_probe_port=2522
swr_plat_registry_port=2524
swr_plat_registry_https_port=2525
k8s_internal_port=2530-2535
k8s_external_port=2601-2605
keystone_msb_port=2701
mbr_fixed_internal_data_https_port=3013
dsm_fixed_publish_data_port=3014
nvidia_docker_port=3476
k8s_cd_port=4194
modb_range_port=4321-4332
rabbitmq_erl_epmd_port=4369
provider_iam_port=5001
slb_telnetserver_port=5002
keystone_twoway_port=5005
corosync_qdevice_port=5403
corosync_port=5405
pg_corosync_port=5407
nw_calico_typha_service_port=5473
swr_port=6000
clair_port=6060
clair_health_port=6061
underpan_port=6091
kube_master_api_port=6443
ovs_listen_port=6637
dvs_listen1_port=6640
dvs_listen2_port=6643
swr_registry_port=2512
swr_registry_https_port=2513
nwnode_vxlan_port=6789
apha_port=7000
sbfd_listen_port=7784-7785
tecs_itools_port=8001
uniview_web1_port=8029
uniview_web2_port=8030
uniview_web3_port=8032
grafana_publish_port=8060
apiserver_ha_insecure_port=8080
kube_master_insecure_port=8101
netinsight_analyzer_listen_port=8082
netinsight_agent_listen_port=8083
netinsight_proxy_listen_port=8084
netinsight_syncthing_listen_port=8091
rca_snmp_port=8162
netinsight_analyzer_offset_port=8182
provider_hwmstorageclient_port=8223
inetmanager_port=1871
nw_cilium_vxlan_port=8472
nw_flannel_port=8473
slb_restserver_port=8585
infranw_tunnel_port=8789
provider_kafka_port=9092
provider_kafka_ssl_port=9093
nw_calico_typha_health_port=9098
nw_calico_node_health_port=9099
provider_vim_port=9131
provider_pim_port=9141
provider_cim_port=9151
provider_opslet_port=10092
dexmesh_pilot_secure_grpc_port=10102
dexmesh_pilot_debug_port=10114
master_egresscfg_port=9443
zenap_msb_sdclient_port=10081
k8s_klt_healthz_port=10248
k8s_kbp_metric_port=10249
k8s_klt_port=10250
k8s_sche_port=10251
k8s_contm_port=10252
k8s_read_only_port=10255
k8s_kbp_healthz_port=10256
k8s_contm_secure_port=10257
op_inetmanager_ccm_port=10258
k8s_sche_secure_port=10259
k8s_klt_streaming_port=10260
dexmesh_ingress_sidecar_debug_port=10100
dexmesh_ingress_sidecar_port=10101
dexmesh_pilot_port=10103
dexmesh_ingress_sidecar_inbound_port=10106
dexmesh_ingress_sidecar_status_port=10120
dexmesh_ingress_sidecar_health_port=10121
dexmesh_message_routing_grpc_port=10150
dexmesh_message_routing_poll_port=10151
dexmesh_ingress_sidecar_stat_port=10190
provider_iui_port=12080
netinsight_etcd_listen_port=12379
netinsight_etcd_peer_listen_port=12380
clair_db_port=15432
sre_prometheus_range_port=19090-19102
nfsserver_mountd_port=20048
aifio_server_port=21800
aifio_client_port=21801
provider_uaf_snmp2_port=20162
gluster_daemon_port=24007
gluster_management_port=24008
gluster_events_port=24009
provider_ftp_port=29952
provider_harvestor_port=31999
keystone_admin_port=35357
provider_envconf_port=35500
provider_vresource_port=35501
provider_syncthing_port=35503
provider_topo_port=35505
provider_35506_port=35506
provider_35507_port=35507
provider_snmp_v3_port=35508
provider_fmc1_port=35509
provider_fmc2_port=35510
provider_fmc3_port=35511
provider_dsmm_port=35512
provider_sftpapp_port=35513
provider_topoplat_port=35515
provider_fmssnmpv2_port=35520
provider_innervimops_port=35521
provider_innerpimops_port=35522
provider_innervim_port=35531
provider_inner_pim_port=35532
provider_inner_cim_port=35533
provider_hwmanapi_port=35550
provider_uaf_http_port=35553
provider_uaf_https_port=35554
provider_uaf162_port=35556
provider_hwmapi_port=35571
provider_kafkassl_2_port=35593
provider_cephalarm_port=35650
provider_pronoea_port=35802
provider_pmc_port=35803
provider_sqm_port=35804
provider_opsapiserver_port=35807
provider_hwmsyslog_port=35900
provider_logindexer_port=35901
provider_logmanager_port=35902
provider_logunderpan_port=35903
provider_nbi_port=36000
provider_inner_port=36100
provider_monitorex_port=36150
provider_director_port=44300
provider_directortwa_port=44301
provider_inner_api_port=45456
gluster_bricks_port=49152-49651

[flexible_reserved_ports]
zenap_msb_router_port=80
zenap_msb_router_https_port=443
zenap_msb_router_https_mutual_auth_port=1443
ceph_osd_port=10500-10700

[open_or_close_ports]

[msb]
extra_listen_ips =
msb_router_om_net_iapi_ip =

[cos]
cos_max_body_size = 3072m

[oki]
oki_verify_algorithm = null

[defaults]
listen_on_all_interfaces = yes
deploy_paas_mode = k8s_enhance
is_k8s_api_opened = true
k8s_container_runtime = docker
stop_app_tenants =

[optional_features]
dexmesh = false
kata = false
multi_k8s_clusters = false
dr_gr = false
cosg = true
cluster_used_model = false

[cgsl_rootprotect]
enabled = no

[pvm]
enabled = yes
pvm_mode = recover

[qga]
enabled = no

[storage]
ceph_csi_driver = off
csi_block_driver =
csi_local_driver =

[ansible]
ansible_strategy_parallel = true
timeout_seconds = 1800

[tcf]
is_tcf=true
tcf_scenario=General
tcf_scale_id=2
public_vip=
force_upgrading=false
is_new_fusionized_tcf=false
is_tcf_lite=false
net_iapi_ipstack_default=

[fsck]
fsck_s = no

[swr]
need_registry_authserver = false

[container]
disable_containers_coredump = false

[cmcc]
cmcc_container_cloud = false
cie_nodes_bear_type =

[sshd]
sshd_listen_ips =
sshd_listen_vips =

[check_config]
check_after_deploy_parallel_num=10
check_before_upgrade_parallel_num=1
