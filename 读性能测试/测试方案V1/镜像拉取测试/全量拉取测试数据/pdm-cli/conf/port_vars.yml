---
# from wiki:  2018.11.29 19:53

- iportal_port:
  isopen: False
  net: mgt
  proto: TCP
  ref:
- iportaladmin_port:
  isopen: False
  net: mgt
  proto: TCP
  ref:
- myportal_port:
  isopen: False
  net: mgt
  proto: TCP
  ref:
- utm_port:
  isopen: False
  net: mgt
  proto: TCP
  ref:
- utm_server_port:
  isopen: False
  net: mgt
  proto: TCP
  ref:
- manager_cert_port:
  isopen: False
  net: mgt
  proto: TCP
  ref:
- cf_vnpm_port:
  isopen: False
  net: mgt
  proto: TCP
  ref:
- vnpm_port:
  isopen: False
  net: mgt
  proto: TCP
  ref: cf_vnpm_port
- asm_helm_port:
  isopen: False
  net: mgt
  proto: TCP
  ref:
- app_gateway_port:
  isopen: False
  net: mgt
  proto: TCP
  ref:
- cf_api_port:
  isopen: False
  net: mgt
  proto: TCP
  ref:
- cf_pdman_port:
  isopen: False
  net: mgt
  proto: TCP
  ref: cf_api_port
- cf_pdeploy_port:
  isopen: False
  net: mgt
  proto: TCP
  ref: cf_api_port
- cf_pcluster_port:
  isopen: False
  net: mgt
  proto: TCP
  ref: cf_api_port
- cf_pnode_port:
  isopen: False
  net: mgt
  proto: TCP
  ref: cf_api_port
- cf_broker_port:
  isopen: False
  net: mgt
  proto: TCP
  ref: cf_api_port
- vp_port:
  isopen: False
  net: mgt
  proto: TCP
  ref: cf_api_port
- ubu_mysql_port:
  isopen: False
  net: mgt
  proto: TCP
  ref:
- postgresql_port:
  isopen: False
  net: mgt
  proto: TCP
  ref:
- op_ubs_dbtools_port:
  isopen: False
  net: mgt
  proto: TCP
  ref:
- ubu_rabbit_port:
  isopen: False
  net: api
  proto: TCP
  ref:
- ubu_rabbit_ssl_port:
  isopen: False
  net: api
  proto: TCP
  ref:
- ubu_rabbit_erl_epmd_port:
  isopen: False
  net: api
  proto: TCP
  ref:
- os_pkg_repo_port:
  isopen: False
  net: mgt/admin
  proto: TCP
  ref:
- paasinitiator_port:
  isopen: False
  net: mgt
  proto: TCP
  ref:
- swr_port:
  isopen: False
  net: mgt
  proto: TCP
  ref:
- swr_registry_port:
  isopen: False
  net: mgt
  proto: TCP
  ref:
- swr_registry_https_port:
  isopen: False
  net: mgt
  proto: TCP
  ref:
- swr_plat_registry_port:
  isopen: False
  net: api
  proto: TCP
  ref:
- swr_plat_registry_https_port:
  isopen: False
  net: api
  proto: TCP
  ref:
- clair_port:
  isopen: False
  net: mgt
  proto: TCP
  ref:
- clair_health_port:
  isopen: False
  net: mgt
  proto: TCP
  ref:
- clair_db_port:
  isopen: False
  net: mgt
  proto: TCP
  ref:
- syncthing_web_port:
  isopen: False
  net: mgt
  proto: TCP
  ref:
- syncthing_port:
  isopen: False
  net: mgt
  proto: TCP
  ref:
- registry_proxy_port:
  isopen: False
  net: mgt
  proto: TCP
  ref:
- registry_authserver_proxy_port:
  isopen: False
  net: mgt
  proto: TCP
  ref:
- registry_authserver_port:
  isopen: True
  net: api
  proto: TCP
  ref:
- dragonfly_scheduler1_port:
  isopen: False
  net: api
  proto: TCP
  ref:
- dragonfly_scheduler2_port:
  isopen: False
  net: api
  proto: TCP
  ref:
- dragonfly_scheduler3_port:
  isopen: False
  net: api
  proto: TCP
  ref:
- swr_leader_election_port:
  isopen: False
  net: api
  proto: TCP
  ref:
- opsapiserver_metric_port:
  isopen: False
  net: mgt
  proto: TCP
  ref:
- opsapiserver_event_port:
  isopen: False
  net: mgt
  proto: TCP
  ref: opsapiserver_metric_port
- opsapiserver_log_port:
  isopen: False
  net: mgt
  proto: TCP
  ref: opsapiserver_metric_port
- opslet_port:
  isopen: False
  net: mgt
  proto: TCP
  ref:
- monitor_port:
  isopen: False
  net: mgt
  proto: TCP
  ref:
- monitor_inner_port:
  isopen: False
  net: mgt
  proto: TCP
  ref:
- heartbeat_port:
  isopen: False
  net: mgt
  proto: TCP
  ref:
- heartbeat_udp_port:
  isopen: False
  net: mgt
  proto: UDP
  ref:
- fm_mgt_port:
  isopen: False
  net: mgt
  proto: TCP
  ref:
- fm_mgt_snmp_port:
  isopen: True
  net: mgt
  proto: UDP
  ref:
- keybox_port:
  isopen: False
  net: mgt
  proto: TCP
  ref:
- pm_agent_heapster_port:
  isopen: False
  net: mgt
  proto: TCP
  ref:
- pmc_port:
  isopen: False
  net: mgt
  proto: TCP
  ref:
- pmc_metric_port:
  isopen: False
  net: mgt
  proto: TCP
  ref:
- sqm_port:
  isopen: False
  net: mgt
  proto: TCP
  ref:
- pm_mgt_port:
  isopen: False
  net: mgt
  proto: TCP
  ref:
- prometheus_port:
  isopen: False
  net: mgt
  proto: TCP
  ref:
- vm_port:
  isopen: False
  net: mgt
  proto: TCP
  ref:
- pronoeaagent_port:
  isopen: False
  net: mgt
  proto: TCP
  ref:
- pronoea_port:
  isopen: False
  net: mgt
  proto: TCP
  ref:
- nodeexporter_port:
  isopen: False
  net: mgt
  proto: TCP
  ref:
- toposerver_port:
  isopen: False
  net: mgt
  proto: TCP
  ref:
- toposerver_cache_port:
  isopen: False
  net: mgt
  proto: TCP
  ref:
- toposerver_magent_port:
  isopen: False
  net: mgt
  proto: TCP
  ref:
- grafana_port:
  isopen: False
  net: mgt
  proto: TCP
  ref:
- grafana_publish_port:
  isopen: False
  net: mgt
  proto: TCP
  ref:
- pmmanage_port:
  isopen: False
  net: mgt
  proto: TCP
  ref:
- alertmanager_port:
  isopen: False
  net: mgt
  proto: TCP
  ref:
- alertmanager_cluster_port:
  isopen: False
  net: mgt
  proto: TCP
  ref:
- harvestor_port:
  isopen: False
  net: mgt
  proto: TCP
  ref:
- goindexer_port:
  isopen: False
  net: mgt
  proto: TCP
  ref:
- nats_port:
  isopen: False
  net: mgt
  proto: TCP
  ref:
- nats_prof_port:
  isopen: False
  net: mgt
  proto: TCP
  ref:
- nats_route_port:
  isopen: False
  net: mgt
  proto: TCP
  ref:
- nats_exporter_port:
  isopen: False
  net: mgt
  proto: TCP
  ref:
- nats_monitor_port:
  isopen: False
  net: mgt
  proto: TCP
  ref:
- eventmgt_port:
  isopen: False
  net: mgt
  proto: TCP
  ref:
- event_agent_port:
  isopen: False
  net: mgt
  proto: TCP
  ref:
- eps_port:
  isopen: False
  net: mgt
  proto: TCP
  ref:
- userkpi_port:
  isopen: False
  net: mgt
  proto: TCP
  ref:
- nwmaster_port:
  isopen: False
  net: mgt
  proto: TCP
  ref:
- etcd_port:
  isopen: False
  net: mgt
  proto: TCP
  ref:
- etcd_mate_port:
  isopen: False
  net: mgt
  proto: TCP
  ref:
- etcd_admtool_port:
  isopen: False
  net: mgt
  proto: TCP
  ref:
- nwmonitor_port:
  isopen: False
  net: mgt
  proto: TCP
  ref:
- nwnode_port:
  isopen: False
  net: mgt
  proto: TCP
  ref:
- cnrm_manager_port:
  isopen: False
  net: mgt
  proto: TCP
  ref:
- nwnode_vxlan_port:
  isopen: True
  net: api/admin/sto
  proto: UDP
  ref:
- vnm_port:
  isopen: False
  net: mgt/api
  proto: TCP
  ref:
- inetmanager_port:
  isopen: True
  net: api/mgt/admin
  proto: TCP
  ref:
- infranw_tunnel_port:
  isopen: False
  net: admin
  proto: UDP
  ref:
- inetagent_port:
  isopen: False
  net: mgt
  proto: TCP
  ref:
- inetproxy_port:
  isopen: False
  net: mgt
  proto: TCP
  ref:
- cradle_master_port:
  isopen: False
  net: mgt
  proto: TCP
  ref:
- storage_port:
  isopen: False
  net: mgt
  proto: TCP
  ref:
- storage_apache_port:
  isopen: False
  net: mgt
  proto: TCP
  ref:
- storage_agent_port:
  isopen: False
  net: mgt
  proto: TCP
  ref:
- storage_apache_https_port:
  isopen: False
  net: mgt
  proto: TCP
  ref:
- cephfs_server_port:
  isopen: False
  net: mgt/sto
  proto: TCP
  ref:
- cephfs_server_api_port:
  isopen: False
  net: mgt/sto
  proto: TCP
  ref:
- ceph_osd_port:
  isopen: False
  net: mgt/sto
  proto: TCP
  ref:
- fs_agent_port:
  isopen: False
  net: mgt
  proto: TCP
  ref:
- rbd_agent_port:
  isopen: False
  net: mgt
  proto: TCP
  ref:
- blockstorage_port:
  isopen: False
  net: mgt
  proto: TCP
  ref:
- nfs_agent_port:
  isopen: False
  net: mgt/sto
  proto: TCP/UDP
  ref:
- rpcbind_port:
  isopen: False
  net: mgt/sto
  proto: TCP/UDP
  ref:
- nfsserver_port:
  isopen: False
  net: mgt/sto
  proto: TCP/UDP
  ref:
- rpc_statd_port:
  isopen: False
  net: mgt/sto
  proto: TCP/UDP
  ref:
- nfsserver_mountd_port:
  isopen: False
  net: mgt/api/sto
  proto: TCP/UDP
  ref:
- rpc_mount_port:
  isopen: True
  net: mgt/sto
  proto: TCP/UDP
  ref:
- gluster_daemon_port:
  isopen: False
  net: mgt/sto
  proto: TCP/UDP
  ref:
- gluster_management_port:
  isopen: False
  net: mgt/sto
  proto: TCP/UDP
  ref:
- gluster_bricks_port:
  isopen: False
  net: mgt/sto
  proto: TCP/UDP
  ref:
- zenap_modeldesign_port:
  isopen: False
  net: mgt
  proto: TCP
  ref:
- op_applicationmanager_applcm_port:
  isopen: False
  net: api
  proto: TCP
  ref:
- zenap_msb_sdclient_port:
  isopen: False
  net: api
  proto: TCP
  ref:
- zenap_msb_sdclient_discover_port:
  isopen: False
  net: api
  proto: TCP
  ref:
- zenap_msb_router_port:
  isopen: True
  net: api
  proto: TCP
  ref:
- zenap_msb_router_https_port:
  isopen: True
  net: api
  proto: TCP
  ref:
- zenap_msb_router_https_mutual_auth_port:
  isopen: True
  net: api
  proto: TCP
  ref:
- zenap_msb_router_om_port:
  isopen: True
  net: api
  proto: TCP
  ref: zenap_msb_router_port
- zenap_msb_router_om_https_port:
  isopen: True
  net: api
  proto: TCP
  ref: zenap_msb_router_https_port
- zenap_msb_router_om_https_mutual_auth_port:
  isopen: True
  net: api
  proto: TCP
  ref: zenap_msb_router_https_mutual_auth_port
- zenap_msb_router_apiroute_port:
  isopen: False
  net: api
  proto: TCP
  ref:
- zenap_msb_router_redis_port:
  isopen: False
  net: api
  proto: TCP
  ref:
- zenap_msb_router_openresty_security_port:
  isopen: False
  net: api
  proto: TCP
  ref:
- zenap_msb_router_iag_security_port:
  isopen: False
  net: api
  proto: TCP
  ref:
- zenap_msb_apigateway_openresty_security_port:
  isopen: False
  net: api
  proto: TCP
  ref:
- zenap_msb_apigateway_apiroute_port:
  isopen: False
  net: mgt
  proto: TCP
  ref:
- zenap_msb_apigateway_redis_port:
  isopen: False
  net: mgt
  proto: TCP
  ref:
- zenap_msb_apigateway_port:
  isopen: False
  net: mgt/api
  proto: TCP
  ref:
- zenap_msb_apigateway_https_port:
  isopen: False
  net: mgt
  proto: TCP
  ref:
- zenap_msb_consul_server_rpc_port:
  isopen: False
  net: mgt
  proto: TCP
  ref:
- zenap_msb_consul_server_lan_port:
  isopen: False
  net: mgt
  proto: TCP/UDP
  ref:
- zenap_msb_consul_server_wan_port:
  isopen: False
  net: mgt
  proto: TCP/UDP
  ref:
- zenap_msb_consul_server_http_port:
  isopen: False
  net: mgt
  proto: TCP
  ref:
- zenap_msb_consul_server_dns_port:
  isopen: False
  net: api
  proto: TCP/UDP
  ref:
- zenap_msb_sdclient_coredns_port:
  isopen: False
  net: api
  proto: TCP/UDP
  ref:
- zenap_msb_router_mgt_port:
  isopen: False
  net: api
  proto: TCP
  ref:
- zenap_msb_router_mgt_https_port:
  isopen: False
  net: api
  proto: TCP
  ref:
- zenap_msb_sdclient_provider_port:
  isopen: False
  net: mgt
  proto: TCP
  ref:
- zenap_cos_postgresql_port:
  isopen: False
  net: mgt
  proto: TCP
  ref:
- zenap_cos_port:
  isopen: True
  net: api
  proto: TCP
  ref:
- zenap_cos_https_port:
  isopen: True
  net: api
  proto: TCP
  ref:
- zenap_cos_admin_port:
  isopen: False
  net: api
  proto: TCP
  ref:
- zenap_cos_admin_https_port:
  isopen: False
  net: api
  proto: TCP
  ref:
- zenap_cos_inner_port:
  isopen: False
  net: api
  proto: TCP
  ref:
- zenap_cos_inner_https_port:
  isopen: False
  net: api
  proto: TCP
  ref:
- zenap_kms_port:
  isopen: False
  net: mgt
  proto: TCP
  ref:
- cf_csm_port:
  isopen: False
  net: mgt/api
  proto: TCP
  ref:
- csm_port:
  isopen: False
  net: mgt/api
  proto: TCP
  ref: cf_csm_port
- cf_nbm_port:
  isopen: False
  net: mgt/api
  proto: TCP
  ref:
- br_port:
  isopen: False
  net: mgt
  proto: TCP
  ref:
- br_mysql_agent_port:
  isopen: False
  net: mgt
  proto: TCP
  ref:
- mie_port:
  isopen: False
  net: mgt
  proto: TCP
  ref:
- apiserver_ha_insecure_port:
  isopen: False
  net: mgt
  proto: TCP
  ref:
- etcd_client_port:
  isopen: False
  net: mgt
  proto: TCP
  ref:
- etcd_peer_port:
  isopen: False
  net: mgt
  proto: TCP
  ref:
- k8s_kbp_healthz_port:
  isopen: False
  net: mgt
  proto: TCP
  ref:
- k8s_klt_port:
  isopen: False
  net: mgt
  proto: TCP
  ref:
- k8s_klt_healthz_port:
  isopen: False
  net: mgt
  proto: TCP
  ref:
- k8s_read_only_port:
  isopen: False
  net: mgt
  proto: TCP
  ref:
- k8s_cd_port:
  isopen: False
  net: mgt
  proto: TCP
  ref:
- k8s_kbp_metric_port:
  isopen: False
  net: mgt
  proto: TCP
  ref:
- kube_master_insecure_port:
  isopen: False
  net: mgt
  proto: TCP
  ref:
- kube_master_api_port:
  isopen: False
  net: mgt
  proto: TCP
  ref:
- k8s_ha_ka_port:
  isopen: False
  net: mgt
  proto: TCP
  ref:
- master_cluster_port:
  isopen: False
  net: mgt
  proto: TCP
  ref:
- master_egresscfg_port:
  isopen: False
  net: mgt
  proto: TCP
  ref:
- k8s_sche_port:
  isopen: False
  net: mgt
  proto: TCP
  ref:
- k8s_contm_port:
  isopen: False
  net: mgt
  proto: TCP
  ref:
- k8s_external_port:
  isopen: True
  net: mgt/api
  proto: TCP
  ref:
- k8s_internal_port:
  isopen: False
  net: mgt/api
  proto: TCP
  ref:
- k8sconfig_port:
  isopen: False
  net: api
  proto: TCP
  ref:
- docker_port:
  isopen: False
  net: mgt
  proto: TCP
  ref:
- swarm_port:
  isopen: False
  net: mgt
  proto: TCP
  ref:
- rca_snmp_port:
  isopen: False
  net: mgt/api
  proto: UDP
  ref:
- rca_fia_port:
  isopen: False
  net: mgt
  proto: TCP
  ref:
- openpalette_service_port:
  isopen: False
  net: mgt/api
  proto: TCP
  ref: zenap_msb_apigateway_port
- openpalette_service_query_port:
  isopen: False
  net: api
  proto: TCP
  ref: zenap_msb_sdclient_port
- logportal_port:
  isopen: False
  net: mgt
  proto: TCP
  ref:
- logsearch_port:
  isopen: False
  net: mgt
  proto: TCP
  ref:
- logalert_port:
  isopen: False
  net: mgt
  proto: TCP
  ref:
- logalert_queue_port:
  isopen: False
  net: mgt
  proto: TCP
  ref:
- logapiserver_metric_port:
  isopen: False
  net: mgt
  proto: TCP
  ref:
- logapiserver_event_port:
  isopen: False
  net: mgt
  proto: TCP
  ref: logapiserver_metric_port
- logapiserver_log_port:
  isopen: False
  net: mgt
  proto: TCP
  ref: logapiserver_metric_port
- logpr_server_port:
  isopen: False
  net: mgt
  proto: TCP
  ref:
- collectormgt_port:
  isopen: False
  net: mgt
  proto: TCP
  ref:
- zindexer_port:
  isopen: False
  net: mgt
  proto: TCP
  ref:
- elasticsearch_port:
  isopen: False
  net: mgt
  proto: TCP
  ref:
- elasticsearch_inner_port:
  isopen: False
  net: mgt
  proto: TCP
  ref:
- kafka_broker_port:
  isopen: False
  net: mgt
  proto: TCP
  ref:
- kafka_zookeeper_port:
  isopen: False
  net: mgt
  proto: TCP
  ref:
- kafka_zookeeper_ms_port:
  isopen: False
  net: mgt
  proto: TCP
  ref:
- kafka_zookeeper_leader_port:
  isopen: False
  net: mgt
  proto: TCP
  ref:
- underpan_port:
  isopen: False
  net: mgt
  proto: TCP
  ref:
- log_agent_port:
  isopen: False
  net: mgt
  proto: TCP
  ref:
- logserver_port:
  isopen: False
  net: mgt
  proto: TCP
  ref:
- automation_mistral_port:
  isopen: False
  net: mgt
  proto: TCP
  ref:
- automation_server_port:
  isopen: False
  net: mgt
  proto: TCP
  ref:
- automation_portal_port:
  isopen: False
  net: mgt
  proto: TCP
  ref:
- automation_proxy_port:
  isopen: False
  net: mgt
  proto: TCP
  ref:
- automation_salt_master_port:
  isopen: False
  net: mgt
  proto: TCP
  ref:
- automation_minion_port:
  isopen: False
  net: mgt
  proto: TCP
  ref:
- automation_ftp_port:
  isopen: False
  net: mgt
  proto: TCP
  ref:
- modb_range_port:
  isopen: False
  net: api
  proto: TCP
  ref:
- uniview_web1_port:
  isopen: False
  net: ipmi
  proto: TCP
  ref:
- uniview_web2_port:
  isopen: False
  net: ipmi
  proto: TCP
  ref:
- uniview_web3_port:
  isopen: False
  net: ipmi
  proto: UDP
  ref:
- sre_prometheus_range_port:
  isopen: False
  net: mgt
  proto: TCP
  ref:
- toposervice_port:
  isopen: False
  net: mgt
  proto: TCP
  ref:
- nwd_port:
  isopen: False
  net: mgt
  proto: TCP
  ref:
- dgraphzero_port:
  isopen: False
  net: mgt
  proto: TCP
  ref:
- dgraphzero_grpc_port:
  isopen: False
  net: mgt
  proto: TCP
  ref:
- dgraphalpha_port:
  isopen: False
  net: mgt
  proto: TCP
  ref:
- dgraphalpha_grpc_port:
  isopen: False
  net: mgt
  proto: TCP
  ref:
- dgraphalpha_grpc_int_port:
  isopen: False
  net: mgt
  proto: TCP
  ref:
- k8sopapiserver_port:
  isopen: False
  net: mgt
  proto: TCP
  ref:
- emblog_agent_port:
  isopen: False
  net: mgt
  proto: TCP
  ref:
- dexmesh_pilot_port:
  isopen: False
  net: api/mgt
  proto: TCP
  ref:
- dexmesh_ingress_sidecar_port:
  isopen: False
  net: mgt
  proto: TCP
  ref:
- dexmesh_ingress_sidecar_debug_port:
  isopen: False
  net: mgt
  proto: TCP
  ref:
- dexmesh_message_routing_poll_port:
  isopen: False
  net: mgt
  proto: TCP
  ref:
- dexmesh_message_routing_grpc_port:
  isopen: False
  net: mgt
  proto: TCP
  ref:
- dexmesh_message_routing_rest_port:
  isopen: False
  net: mgt
  proto: TCP
  ref:
- dexmesh_config_server_port:
  isopen: False
  net: mgt
  proto: TCP
  ref:
- dexmesh_canary_server_port:
  isopen: False
  net: mgt
  proto: TCP
  ref:
- dexmesh_resource_port:
  isopen: False
  net: mgt
  proto: TCP
  ref:
- dexmesh_sidecar_injector_port:
  isopen: False
  net: api/mgt
  proto: TCP
  ref:
- dexmesh_pilot_secure_grpc_port:
  isopen: False
  net: mgt
  proto: TCP
  ref:
- dexmesh_pilot_debug_port:
  isopen: False
  net: mgt
  proto: TCP
  ref:
- dexmesh_ingress_sidecar_inbound_port:
  isopen: False
  net: mgt
  proto: TCP
  ref:
- dexmesh_ingress_sidecar_stat_port:
  isopen: False
  net: mgt
  proto: TCP
  ref:
- dexmesh_ingress_sidecar_status_port:
  isopen: False
  net: mgt
  proto: TCP
  ref:
- dexmesh_ingress_sidecar_health_port:
  isopen: False
  net: mgt
  proto: TCP
  ref:
- provider_kafka_ssl_port:
  isopen: True
  net: mgt
  proto: TCP
  ref:
- corosync_port:
  isopen: False
  net: mgt
  proto: UDP
  ref:
- filebeat_port:
  isopen: False
  net: mgt
  proto: TCP
  ref:
- logstash_port:
  isopen: False
  net: mgt
  proto: TCP
  ref:
- logstash_tcp_port:
  isopen: False
  net: mgt
  proto: TCP
  ref:
- logstash_beat_port:
  isopen: False
  net: mgt
  proto: TCP
  ref:
- ndr_port:
  isopen: False
  net: api
  proto: TCP
  ref:
- ndr_data_port:
  isopen: False
  net: api
  proto: TCP
  ref:
- ndr_internal_http_port:
  isopen: False
  net: api
  proto: TCP
  ref:
- ndr_internal_https_port:
  isopen: False
  net: api
  proto: TCP
  ref:
- ndr_publish_data_port:
  isopen: False
  net: api
  proto: TCP
  ref:
- ndr_publish_internal_http_port:
  isopen: False
  net: api
  proto: TCP
  ref:
- ndr_publish_internal_https_port:
  isopen: False
  net: api
  proto: TCP
  ref:
- ceph_csi_http_metrics_port:
  isopen: False
  net: api
  proto: TCP
  ref:
- ceph_csi_grpc_metrics_port:
  isopen: False
  net: api
  proto: TCP
  ref:
- ceph_csi_deploy_http_metrics1_port:
  isopen: False
  net: api
  proto: TCP
  ref:
- ceph_csi_ds_http_metrics1_port:
  isopen: False
  net: api
  proto: TCP
  ref:
- ceph_csi_grpc_metrics1_port:
  isopen: False
  net: api
  proto: TCP
  ref:
- ceph_csi_deploy_http_metrics2_port:
  isopen: False
  net: api
  proto: TCP
  ref:
- ceph_csi_ds_http_metrics2_port:
  isopen: False
  net: api
  proto: TCP
  ref:
- ceph_csi_grpc_metrics2_port:
  isopen: False
  net: api
  proto: TCP
  ref:
- ceph_csi_deploy_http_metrics3_port:
  isopen: False
  net: api
  proto: TCP
  ref:
- ceph_csi_ds_http_metrics3_port:
  isopen: False
  net: api
  proto: TCP
  ref:
- ceph_csi_grpc_metrics3_port:
  isopen: False
  net: api
  proto: TCP
  ref:
- pconf_port:
  isopen: False
  net: mgt
  proto: TCP
  ref:
- pconf_https_port:
  isopen: False
  net: mgt
  proto: TCP
  ref:
- pconf_pub_https_port:
  isopen: False
  net: mgt
  proto: TCP
  ref:
- adrm_apiserver_port:
  isopen: False
  net: mgt
  proto: TCP
  ref:
- adrm_dcgm_port:
  isopen: False
  net: api
  proto: TCP
  ref:
- slb_restsvr_port:
  isopen: False
  net: api/mgt
  proto: TCP
  ref:
- slb_telnet_port:
  isopen: False
  net: api/mgt
  proto: TCP
  ref:
- slb_restserver_port:
  isopen: False
  net: api/mgt
  proto: TCP
  ref:
- slb_telnetserver_port:
  isopen: False
  net: api/mgt
  proto: TCP
  ref:
- slb_agent_port:
  isopen: False
  net: api/mgt
  proto: TCP
  ref:
- slb_vpp_port:
  isopen: False
  net: api/mgt
  proto: TCP
  ref:
- tecs_itools_port:
  isopen: True
  net: api
  proto: TCP
  ref:
- mbr_port:
  isopen: False
  net: api
  proto: TCP
  ref:
- mbr_data_port:
  isopen: False
  net: api
  proto: TCP
  ref:
- mbr_internal_data_http_port:
  isopen: False
  net: api
  proto: TCP
  ref:
- mbr_internal_data_https_port:
  isopen: False
  net: api
  proto: TCP
  ref:
- dsm_port:
  isopen: False
  net: api
  proto: TCP
  ref:
- dsm_data_port:
  isopen: False
  net: api
  proto: TCP
  ref:
- dsm_publish_data_port:
  isopen: False
  net: api
  proto: TCP
  ref:
- dsm_internal_data_http_port:
  isopen: False
  net: api
  proto: TCP
  ref:
- dsm_internal_data_https_port:
  isopen: False
  net: api
  proto: TCP
  ref:
- op_dr_webhook_port:
  isopen: False
  net: api
  proto: TCP
  ref:
- nfs_server_metric_port:
  isopen: False
  net: api
  proto: TCP
  ref:
- sp_nfs_agent_port:
  isopen: False
  net: api
  proto: TCP
  ref:
- containerd_port:
  isopen: False
  net: api
  proto: TCP
  ref:
- nvidia_docker_port:
  isopen: False
  net: mgt
  proto: TCP
  ref:
- cosmos_http_port:
  isopen: False
  net: api
  proto: TCP
  ref:
- cosmos_db_port:
  isopen: False
  net: api
  proto: TCP
  ref:
- cosmos_cluster_port:
  isopen: False
  net: api
  proto: TCP
  ref:
- cosmos_internal_port:
  isopen: False
  net: api
  proto: TCP
  ref:
- cosmos_db_extra_port:
  isopen: False
  net: api
  proto: TCP
  ref:
- cosmos_cluster_extra_port:
  isopen: False
  net: api
  proto: TCP
  ref:
- cosmos_proxy_port:
  isopen: False
  net: api
  proto: TCP
  ref:
- carlina_https_port:
  isopen: False
  net: api
  proto: TCP
  ref:
- security_gateway_port:
  isopen: False
  net: api
  proto: TCP
  ref:
- security_project_port:
  isopen: False
  net: api
  proto: TCP
  ref:
- security_k8shook_port:
  isopen: False
  net: api
  proto: TCP
  ref:
- security_serviceaccount_port:
  isopen: False
  net: api
  proto: TCP
  ref:
- security_tcfutm_port:
  isopen: False
  net: api
  proto: TCP
  ref:
- portal_tcfportal_port:
  isopen: False
  net: api
  proto: TCP
  ref:
- vnf_lcm_client_port:
  isopen: False
  net: api
  proto: TCP
  ref:
- vnf_lcm_server_port:
  isopen: True
  net: api
  proto: TCP
  ref:
- vnf_lcm_server_oneauth_port:
  isopen: True
  net: api
  proto: TCP
  ref:
- vnf_lcm_server_twoauth_port:
  isopen: True
  net: api
  proto: TCP
  ref:
- nw_flannel_port:
  isopen: True
  net: api/admin/sto
  proto: UDP
  ref:
- op_node_agent_port:
  isopen: False
  net: api
  proto: TCP
  ref:
- liteappm_port:
  isopen: False
  net: api
  proto: TCP
  ref:
- liteappm_redis_port:
  isopen: False
  net: api
  proto: TCP
  ref:
- keystone_public_port:
  isopen: True
  net: api
  proto: TCP
  ref:
- keystone_admin_port:
  isopen: True
  net: api
  proto: TCP
  ref:
- keystone_twoway_port:
  isopen: True
  net: api
  proto: TCP
  ref:
- cf_pcluster_api_port:
  isopen: False
  net: api
  proto: TCP
  ref:
- cf_pnode_api_port:
  isopen: False
  net: api
  proto: TCP
  ref:
- cf_pdeploy_api_port:
  isopen: False
  net: api
  proto: TCP
  ref:
- cf_pdman_api_port:
  isopen: False
  net: api
  proto: TCP
  ref:
- opslet_tcp_port:
  isopen: False
  net: api
  proto: TCP
  ref:
- mbr_fixed_internal_data_https_port:
  isopen: True
  net: api
  proto: TCP
  ref:
- dsm_fixed_publish_data_port:
  isopen: True
  net: api
  proto: TCP
  ref:
- ndr_dsm_port:
  isopen: False
  net: api
  proto: TCP
  ref:
- ovs_listen_port:
  isopen: True
  net: api
  proto: TCP
  ref:
- dvs_listen1_port:
  isopen: True
  net: api
  proto: TCP
  ref:
- dvs_listen2_port:
  isopen: True
  net: api
  proto: TCP
  ref:
- op_asm_operator_port:
  isopen: False
  net: api
  proto: TCP
  ref:
- op_eps_metrics_server_port:
  isopen: False
  net: api
  proto: TCP
  ref:
- zenap_msb_sdclient_coredns_health_port:
  isopen: False
  net: api
  proto: TCP
  ref:
- zenap_msb_router_iag_health_port:
  isopen: False
  net: api
  proto: TCP
  ref:
- provider_pg_port:
  isopen: False
  net: api
  proto: TCP
  ref:
- provider_kafka_port:
  isopen: True
  net: api
  proto: TCP
  ref:
- provider_uaf_snmp2_port:
  isopen: False
  net: api
  proto: UDP
  ref:
- provider_snmp_v3_port:
  isopen: True
  net: api
  proto: UDP
  ref:
- provider_uaf_http_port:
  isopen: False
  net: api
  proto: TCP
  ref:
- provider_uaf_https_port:
  isopen: True
  net: api
  proto: TCP
  ref:
- provider_inner_api_port:
  isopen: False
  net: api
  proto: TCP
  ref:
- op_eps_vpa_admission_controller_port:
  isopen: False
  net: api
  proto: TCP
  ref:
- op_eps_vpa_recommender_port:
  isopen: False
  net: api
  proto: TCP
  ref:
- op_eps_vpa_updater_port:
  isopen: False
  net: api
  proto: TCP
  ref:
- daisy_pg_port:
  isopen: False
  net: api
  proto: TCP
  ref:
- nw_alita_agent_port:
  isopen: False
  net: mgt
  proto: TCP
  ref:
- nw_alita_portal_port:
  isopen: False
  net: mgt
  proto: TCP
  ref:
- nw_calico_typha_service_port:
  isopen: False
  net: api
  proto: TCP
  ref:
- nw_calico_typha_health_port:
  isopen: False
  net: api/mgt
  proto: TCP
  ref:
- nw_calico_node_health_port:
  isopen: False
  net: api/mgt
  proto: TCP
  ref:
- nwmaster_https_port:
  isopen: False
  net: mgt
  proto: TCP
  ref:
- mgmt_alarm_https_port:
  isopen: False
  net: mgmt
  proto: TCP
  ref:
- provider_35506_port:
  isopen: False
  net: api
  proto: TCP
  ref:
- provider_35507_port:
  isopen: True
  net: api
  proto: TCP
  ref:
- provider_vim_port:
  isopen: True
  net: api
  proto: TCP
  ref:
- provider_pim_port:
  isopen: True
  net: api
  proto: TCP
  ref:
- provider_cim_port:
  isopen: True
  net: api
  proto: TCP
  ref:
- provider_inner_pim_port:
  isopen: False
  net: api
  proto: TCP
  ref:
- provider_inner_cim_port:
  isopen: False
  net: api
  proto: TCP
  ref:
- provider_uaf_snmp1_port:
  isopen: True
  net: api
  proto: UDP
  ref:
- node_volume_port:
  isopen: False
  net: api
  proto: TCP
  ref:
- pcs_port:
  isopen: False
  net: api
  proto: TCP
  ref:
- corosync_qdevice_port:
  isopen: False
  net: api
  proto: TCP
  ref:
- security_psecret_port:
  isopen: False
  net: api
  proto: TCP
  ref:
- security_psecret_https_port:
  isopen: False
  net: api
  proto: TCP
  ref:
- security_psecret_pub_https_port:
  isopen: False
  net: api
  proto: TCP
  ref:
- pg_corosync_port:
  isopen: False
  net: api
  proto: UDP
  ref:
- gluster_events_port:
  isopen: False
  net: mgt/sto
  proto: TCP/UDP
  ref:
- apha_port:
  isopen: False
  net: api
  proto: TCP/UDP
  ref:
- k8s_contm_secure_port:
  isopen: False
  net: api
  proto: TCP
  ref:
- k8s_klt_streaming_port:
  isopen: False
  net: api
  proto: TCP
  ref:
- k8s_sche_secure_port:
  isopen: False
  net: api
  proto: TCP
  ref:
- sync_port:
  isopen: False
  net: api
  proto: TCP
  ref:
- netinsight_analyzer_listen_port:
  isopen: True
  net: api
  proto: TCP
  ref:
- netinsight_agent_listen_port:
  isopen: False
  net: api
  proto: TCP
  ref:
- netinsight_proxy_listen_port:
  isopen: False
  net: api
  proto: TCP
  ref:
- netinsight_etcd_listen_port:
  isopen: False
  net: api
  proto: TCP
  ref:
- netinsight_etcd_peer_listen_port:
  isopen: False
  net: api
  proto: TCP
  ref:
- security_certcenter_port:
  isopen: False
  net: api
  proto: TCP
  ref:
- op_inetmanager_ccm_port:
  isopen: False
  net: api
  proto: TCP
  ref:
- op_inetmanager_xlbagent_port:
  isopen: False
  net: api
  proto: TCP
  ref:
- provider_fmsnmp_port:
  isopen: False
  net: api
  proto: UDP
  ref:
- provider_fm1_port:
  isopen: False
  net: api
  proto: TCP
  ref:
- provider_fm2_port:
  isopen: False
  net: api
  proto: TCP
  ref:
- provider_pmagent_port:
  isopen: False
  net: api
  proto: TCP
  ref:
- provider_pmagentuaf_port:
  isopen: False
  net: api
  proto: TCP
  ref:
- provider_pm_port:
  isopen: False
  net: api
  proto: TCP
  ref:
- provider_hwm_port:
  isopen: False
  net: api
  proto: TCP
  ref:
- provider_pick_port:
  isopen: False
  net: api
  proto: TCP
  ref:
- provider_kafka1_port:
  isopen: False
  net: api
  proto: TCP
  ref:
- provider_iam_port:
  isopen: False
  net: api
  proto: TCP
  ref:
- provider_hwmstorageclient_port:
  isopen: False
  net: api
  proto: TCP
  ref:
- provider_opslet_port:
  isopen: False
  net: api
  proto: TCP
  ref:
- provider_iui_port:
  isopen: False
  net: api
  proto: TCP
  ref:
- provider_harvestor_port:
  isopen: False
  net: api
  proto: TCP
  ref:
- provider_envconf_port:
  isopen: False
  net: api
  proto: TCP
  ref:
- provider_vresource_port:
  isopen: False
  net: api
  proto: TCP
  ref:
- provider_topo_port:
  isopen: False
  net: api
  proto: TCP
  ref:
- provider_fmc1_port:
  isopen: False
  net: api
  proto: TCP
  ref:
- provider_fmc2_port:
  isopen: False
  net: api
  proto: TCP
  ref:
- provider_fmc3_port:
  isopen: False
  net: api
  proto: TCP
  ref:
- provider_dsmm_port:
  isopen: False
  net: api
  proto: TCP
  ref:
- provider_innervimops_port:
  isopen: False
  net: api
  proto: TCP
  ref:
- provider_innerpimops_port:
  isopen: False
  net: api
  proto: TCP
  ref:
- provider_innervim_port:
  isopen: False
  net: api
  proto: TCP
  ref:
- provider_hwmanapi_port:
  isopen: False
  net: api
  proto: TCP
  ref:
- provider_hwmapi_port:
  isopen: False
  net: api
  proto: TCP
  ref:
- provider_kafkassl_2_port:
  isopen: True
  net: mgt
  proto: TCP
  ref:
- provider_cephalarm_port:
  isopen: False
  net: api
  proto: UDP
  ref:
- provider_pronoea_port:
  isopen: False
  net: api
  proto: TCP
  ref:
- provider_pmc_port:
  isopen: False
  net: api
  proto: TCP
  ref:
- provider_sqm_port:
  isopen: False
  net: api
  proto: TCP
  ref:
- provider_opsapiserver_port:
  isopen: False
  net: api
  proto: TCP
  ref:
- provider_hwmsyslog_port:
  isopen: True
  net: api
  proto: TCP/UDP
  ref:
- provider_logindexer_port:
  isopen: False
  net: api
  proto: TCP
  ref:
- provider_logmanager_port:
  isopen: False
  net: api
  proto: TCP
  ref:
- provider_logunderpan_port:
  isopen: False
  net: api
  proto: TCP
  ref:
- provider_nbi_port:
  isopen: False
  net: api
  proto: TCP
  ref:
- provider_inner_port:
  isopen: False
  net: api
  proto: TCP
  ref:
- provider_monitorex_port:
  isopen: False
  net: api
  proto: TCP
  ref:
- provider_syncthing_port:
  isopen: False
  net: mgt
  proto: TCP
  ref:
- provider_director_port:
  isopen: True
  net: api
  proto: TCP
  ref:
- snmp_trap_port:
  isopen: True
  net: api
  proto: UDP
  ref:
- nw_cilium_vxlan_port:
  isopen: True
  net: api/admin/sto
  proto: UDP
  ref:
- nw_vpn_ike_port:
  isopen: True
  net: api/admin/sto
  proto: UDP
  ref:
- logshipper_port:
  isopen: False
  net: mgt
  proto: TCP
  ref:
- netinsight_analyzer_offset_port:
  isopen: False
  net: api
  proto: TCP
  ref:
- op_nw_operator_port:
  isopen: False
  net: api/mgt
  proto: TCP
  ref:
- nw_alita_node_port:
  isopen: False
  net: mgt
  proto: TCP
  ref:
- keystone_msb_port:
  isopen: False
  net: api/mgt
  proto: TCP
  ref:
- nw_cilium_agent_liveness_probe_port:
  isopen: False
  net: api/mgt
  proto: TCP
  ref:
- nw_cilium_agent_gops_port:
  isopen: False
  net: api/mgt
  proto: TCP
  ref:
- nw_cilium_agent_health_check_port:
  isopen: False
  net: api/mgt
  proto: TCP
  ref:
- nw_cilium_operator_gops_port:
  isopen: False
  net: api/mgt
  proto: TCP
  ref:
- nw_cilium_operator_liveness_probe_port:
  isopen: False
  net: api/mgt
  proto: TCP
  ref:
- node_volume_healthz_port:
  isopen: False
  net: api
  proto: TCP
  ref:
- netinsight_syncthing_listen_port:
  isopen: False
  net: api
  proto: TCP
  ref:
- provider_ftp_port:
  isopen: True
  net: api
  proto: TCP
  ref:
- nw_cilium_operator_https_webhook_port:
  isopen: False
  net: api/mgt
  proto: TCP
  ref:
- containers_kubeall_operator_controller_port:
  isopen: False
  net: api/mgt
  proto: TCP
  ref:
- containers_kubeall_operator_daemon_port:
  isopen: False
  net: api/mgt
  proto: TCP
  ref:
- postgresql_ha_port:
  isopen: False
  net: api/mgt
  proto: TCP
  ref:
- kubeall_ms_elector_port:
  isopen: False
  net: api/mgt
  proto: TCP
  ref:
- zenap_msb_sdclient_webhook_port:
  isopen: False
  net: api
  proto: TCP
  ref:
- op_volmgr_api_port:
  isopen: False
  net: api
  proto: TCP
  ref:
- csi_disk_port:
  isopen: False
  net: api
  proto: TCP
  ref:
- opdisk_port:
  isopen: False
  net: api
  proto: TCP
  ref:
- localstorage_node_port:
  isopen: False
  net: api
  proto: TCP
  ref:
- provider_directortwa_port:
  isopen: True
  net: api
  proto: TCP
  ref:
- provider_sftpapp_port:
  isopen: False
  net: api
  proto: TCP
  ref:
- provider_topoplat_port:
  isopen: False
  net: api
  proto: TCP
  ref:
- provider_fmssnmpv2_port:
  isopen: False
  net: api
  proto: UDP
  ref:
- provider_uaf162_port:
  isopen: False
  net: api
  proto: UDP
  ref:
- sbfd_listen_port:
  isopen: True
  net: api
  proto: UDP
  ref:
- nw_cilium_operator_pprof_port:
  isopen: False
  net: api/mgt
  proto: TCP
  ref:
- nw_cilium_agent_pprof_port:
  isopen: False
  net: api/mgt
  proto: TCP
  ref:
- wks_controller_srv_port:
  isopen: False
  net: api
  proto: TCP
  ref:
- infranwcm_port:
  isopen: False
  net: api
  proto: TCP
  ref:
- infranwagent_port:
  isopen: False
  net: api
  proto: TCP
  ref:
- pim_webserver_port:
  isopen: False
  net: api
  proto: TCP
  ref:
- provider_pg_ha_port:
  isopen: False
  net: api
  proto: TCP
  ref:
- op_ubs_daisy_pg_ha_port:
  isopen: False
  net: api
  proto: TCP
  ref:
- provider_session_lua_port:
  isopen: False
  net: api
  proto: TCP
  ref:
- nw_cilium_operator_http_port:
  isopen: False
  net: api
  proto: TCP
  ref:
- nw_cilium_agent_http_port:
  isopen: False
  net: api
  proto: TCP
  ref:
- nw_knitter_vpn_vxlan_port:
  isopen: False
  net: api/admin/sto
  proto: UDP
  ref:
- apts_wsm_http_port:
  isopen: false
  net: api
  proto: TCP
  ref:
- aifio_server_port:
  isopen: false
  net: api
  proto: TCP
  ref:
- aifio_client_port:
  isopen: false
  net: api
  proto: TCP
  ref:
- nw_flannel_http_port:
  isopen: False
  net: api
  proto: TCP
  ref:
- nw_knitter_policy_http_port:
  isopen: False
  net: api
  proto: TCP
  ref:
- processd_process_ctrl_port:
  isopen: False
  net: api
  proto: TCP
  ref:
- kubeall_assembler_cim_port:
  isopen: False
  net: api
  proto: TCP
  ref:
- kubeall_assembler_daemon_port:
  isopen: False
  net: api
  proto: TCP
  ref:
- op_ops_monitor_opslet_process_ctrl_port:
  isopen: False
  net: api
  proto: TCP
  ref:
- os_pkg_repo_process_ctrl_port:
  isopen: False
  net: api
  proto: TCP
  ref:
- openjdk_process_ctrl_port:
  isopen: False
  net: api
  proto: TCP
  ref:
- kubeall_mesh_process_ctrl_port:
  isopen: False
  net: api
  proto: TCP
  ref:
- op_ops_monitor_pvm_process_ctrl_port:
  isopen: False
  net: api
  proto: TCP
  ref:
- infra_node_disk_agent_process_ctrl_port:
  isopen: False
  net: api
  proto: TCP
  ref:
- infra_node_agent_process_ctrl_port:
  isopen: False
  net: api
  proto: TCP
  ref:
- op_ubs_kubeall_ms_elector_process_ctrl_port:
  isopen: False
  net: api
  proto: TCP
  ref:
- docker_process_ctrl_port:
  isopen: False
  net: api
  proto: TCP
  ref:
- k8s_process_ctrl_port:
  isopen: False
  net: api
  proto: TCP
  ref:
- op_node_kubeagent_process_ctrl_port:
  isopen: False
  net: api
  proto: TCP
  ref:
- op_containers_containerd_process_ctrl_port:
  isopen: False
  net: api
  proto: TCP
  ref:
- op_ops_monitor_heartbeat_process_ctrl_port:
  isopen: False
  net: api
  proto: TCP
  ref:
- underpan_process_ctrl_port:
  isopen: False
  net: api
  proto: TCP
  ref:
- dpdk_deps_process_ctrl_port:
  isopen: False
  net: api
  proto: TCP
  ref:
- nw_knitter_vpn_http_port:
  isopen: False
  net: api
  proto: TCP
  ref:
- nfs_csi_metric_port:
  isopen: False
  net: api
  proto: TCP
  ref:
- aiopclusteragent_port:
  isopen: False
  net: api
  proto: TCP
  ref:
- aiopnodeagent_port:
  isopen: False
  net: api
  proto: TCP
  ref:
- portal_aiportal_port:
  isopen: False
  net: api
  proto: TCP
  ref:
