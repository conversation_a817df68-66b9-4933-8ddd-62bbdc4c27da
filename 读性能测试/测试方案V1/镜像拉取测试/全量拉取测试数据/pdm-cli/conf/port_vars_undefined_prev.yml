---
# ports not in port_vars.yml but used in the old version (such as R6),
# inherit from the old version when upgrading.
# from wiki: 2018.5.21 9:28

- apiserver_ha_insecure_port: 8080
- app_gateway_port: 5666
- ceph_osd_port: 10500-10700
- cf_api_port: 5000
- cnrm_manager_port: 6008
- etcd_mate_port: 20004
- kafka_zookeeper_leader_port: 3888
- kafka_zookeeper_ms_port: 2888
- logalert_port: 8483
- logalert_queue_port: 8484
- logportal_port: 8324
- logsearch_port: 8481
- nats_exporter_port: 24245
- nats_monitor_port: 24222
- nats_port: 24242
- nats_prof_port: 24243
- nats_route_port: 24244
- nwmonitor_port: 6009
- nwnode_port: 6006
- op_opsinsight_cadvisor_port: 16801
- op_opsinsight_mysql_exporter_port: 16802
- op_opsinsight_node_exporter_port: 16800
- op_opsinsight_prom_adapter_port: 16902
- op_opsinsight_prom_server_port: 16900
- os_pkg_repo_port: 6387
- rbd_agent_port: 6779
- tcfs_aerospike_fabric_port: 3001
- tcfs_aerospike_info_port: 3003
- tcfs_aerospike_mesh_port: 3002
- tcfs_redis_a_port: 5200
- tcfs_redis_b_port: 5201
- tcfs_redis_c_port: 5202
- tcfs_redis_m_port: 5205
- tcfs_redis_p_port: 5203
- monitor_inner_port: 19850
- zenap_msb_consul_server_http_port: 8500
- zenap_msb_consul_server_lan_port: 8301
- zenap_msb_consul_server_rpc_port: 8300
- zenap_msb_consul_server_wan_port: 8302
- zenap_msb_router_iag_health_port: 8067
- zenap_msb_sdclient_coredns_health_port: 8077
