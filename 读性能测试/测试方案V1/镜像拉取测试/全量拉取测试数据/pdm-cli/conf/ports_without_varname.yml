- id: 22
  isopen: true
  net: mgt/admin/iapi
  port: '22'
  port_type: null
  proto: TCP
  ref: null
- id: 25
  isopen: false
  net: null
  port: '25'
  port_type: null
  proto: null
  ref: null
- id: 67
  isopen: false
  net: 'null'
  port: '67'
  port_type: null
  proto: UDP
  ref: null
- id: 68
  isopen: false
  net: all
  port: '68'
  port_type: null
  proto: UDP
  ref: null
- id: 69
  isopen: false
  net: all
  port: '69'
  port_type: null
  proto: UDP
  ref: null
- id: 123
  isopen: false
  net: 'null'
  port: '123'
  port_type: null
  proto: UDP
  ref: null
- id: 514
  isopen: true
  net: all
  port: '514'
  port_type: null
  proto: TCP/UDP
  ref: null
- id: 546
  isopen: false
  net: 'null'
  port: '546'
  port_type: null
  proto: UDP
  ref: null
- id: 547
  isopen: false
  net: all
  port: '547'
  port_type: null
  proto: UDP
  ref: null
- id: 587
  isopen: false
  net: null
  port: '587'
  port_type: null
  proto: null
  ref: null
- id: 1025
  isopen: false
  net: all
  port: 1025-1300
  port_type: null
  proto: TCP/UDP
  ref: null
- id: 1161
  isopen: true
  net: api/overlay_netapi/iapi
  port: '1161'
  port_type: null
  proto: UDP
  ref: null
- id: 1480
  isopen: true
  net: api/iapi
  port: '1480'
  port_type: null
  proto: TCP
  ref: null
- id: 1850
  isopen: false
  net: 'null'
  port: '1850'
  port_type: null
  proto: TCP
  ref: null
- id: 1851
  isopen: false
  net: 'null'
  port: '1851'
  port_type: null
  proto: TCP
  ref: null
- id: 1869
  isopen: false
  net: 'null'
  port: '1869'
  port_type: null
  proto: TCP
  ref: null
- id: 1871
  isopen: false
  net: net_iapi
  port: '1871'
  port_type: null
  proto: T
  ref: null
- id: 2022
  isopen: true
  net: 主机网络
  port: '2022'
  port_type: null
  proto: TCP
  ref: null
- id: 2121
  isopen: true
  net: all
  port: '2121'
  port_type: null
  proto: TCP/UDP
  ref: null
- id: 2514
  isopen: false
  net: all
  port: '2514'
  port_type: null
  proto: TCP/UDP
  ref: null
- id: 2523
  isopen: true
  net: net_iapi
  port: '2523'
  port_type: null
  proto: TCP
  ref: null
- id: 2540
  isopen: false
  net: api
  port: '2540'
  port_type: null
  proto: TCP/UDP
  ref: null
- id: 2601
  isopen: false
  net: api
  port: '2601'
  port_type: null
  proto: TCP
  ref: null
- id: 2602
  isopen: false
  net: api
  port: '2602'
  port_type: null
  proto: TCP
  ref: null
- id: 4500
  isopen: true
  net: all
  port: '4500'
  port_type: null
  proto: UDP
  ref: null
- id: 5100
  isopen: false
  net: management_net
  port: '5100'
  port_type: null
  proto: TCP
  ref: null
- id: 5408
  isopen: false
  net: mgt/api
  port: '5408'
  port_type: null
  proto: UDP
  ref: null
- id: 5409
  isopen: false
  net: mgt/api
  port: '5409'
  port_type: null
  proto: UDP
  ref: null
- id: 5555
  isopen: true
  net: 'null'
  port: '5555'
  port_type: null
  proto: TCP
  ref: null
- id: 5671
  isopen: false
  net: management_net
  port: '5671'
  port_type: null
  proto: TCP
  ref: null
- id: 5672
  isopen: false
  net: management_net
  port: '5672'
  port_type: null
  proto: TCP
  ref: null
- id: 5673
  isopen: false
  net: management_net
  port: '5673'
  port_type: null
  proto: TCP
  ref: null
- id: 5674
  isopen: false
  net: management_net
  port: '5674'
  port_type: null
  proto: TCP
  ref: null
- id: 5771
  isopen: false
  net: management_net
  port: '5771'
  port_type: null
  proto: TCP
  ref: null
- id: 5772
  isopen: false
  net: management_net
  port: '5772'
  port_type: null
  proto: TCP
  ref: null
- id: 6379
  isopen: false
  net: localhost
  port: '6379'
  port_type: null
  proto: TCP
  ref: null
- id: 6666
  isopen: false
  net: mgt
  port: '6666'
  port_type: null
  proto: TCP
  ref: null
- id: 7519
  isopen: true
  net: 'null'
  port: '7519'
  port_type: null
  proto: TCP
  ref: null
- id: 8067
  isopen: false
  net: 'null'
  port: '8067'
  port_type: null
  proto: TCP
  ref: null
- id: 8077
  isopen: false
  net: 'null'
  port: '8077'
  port_type: null
  proto: TCP
  ref: null
- id: 8445
  isopen: false
  net: 'null'
  port: '8445'
  port_type: null
  proto: TCP
  ref: null
- id: 8849
  isopen: true
  net: all
  port: '8849'
  port_type: null
  proto: TCP/UDP
  ref: null
- id: 9090
  isopen: false
  net: 'null'
  port: '9090'
  port_type: null
  proto: TCP
  ref: null
- id: 9190
  isopen: false
  net: management_net
  port: '9190'
  port_type: null
  proto: TCP
  ref: null
- id: 9991
  isopen: false
  net: null
  port: '9991'
  port_type: null
  proto: null
  ref: null
- id: 10052
  isopen: true
  net: 主机网络
  port: '10052'
  port_type: null
  proto: TCP
  ref: null
- id: 15050
  isopen: false
  net: all
  port: '15050'
  port_type: null
  proto: TCP
  ref: null
- id: 15672
  isopen: false
  net: 'null'
  port: '15672'
  port_type: null
  proto: TCP
  ref: null
- id: 16162
  isopen: false
  net: 'null'
  port: '16162'
  port_type: null
  proto: UDP
  ref: null
- id: 18080
  isopen: true
  net: all
  port: '18080'
  port_type: null
  proto: TCP
  ref: null
- id: 18443
  isopen: true
  net: all
  port: '18443'
  port_type: null
  proto: TCP
  ref: null
- id: 18990
  isopen: false
  net: all
  port: '18990'
  port_type: null
  proto: TCP
  ref: null
- id: 18998
  isopen: false
  net: all
  port: '18998'
  port_type: null
  proto: TCP
  ref: null
- id: 19000
  isopen: false
  net: all
  port: 19000-19010
  port_type: null
  proto: TCP
  ref: null
- id: 19191
  isopen: false
  net: api
  port: '19191'
  port_type: null
  proto: TCP
  ref: null
- id: 19393
  isopen: true
  net: all
  port: '19393'
  port_type: null
  proto: TCP
  ref: null
- id: 19443
  isopen: true
  net: all
  port: '19443'
  port_type: null
  proto: TCP
  ref: null
- id: 19898
  isopen: false
  net: net_api
  port: '19898'
  port_type: null
  proto: TCP
  ref: null
- id: 20000
  isopen: false
  net: api
  port: 20000-20005
  port_type: null
  proto: UDP
  ref: null
- id: 20710
  isopen: false
  net: null
  port: '20710'
  port_type: null
  proto: null
  ref: null
- id: 20710
  isopen: true
  net: 'null'
  port: '20710'
  port_type: null
  proto: TCP
  ref: null
- id: 21038
  isopen: false
  net: api
  port: '21038'
  port_type: null
  proto: TCP
  ref: null
- id: 23456
  isopen: false
  net: 主机网络
  port: '23456'
  port_type: null
  proto: TCP
  ref: null
- id: 23456
  isopen: true
  net: 主机网络
  port: '23456'
  port_type: null
  proto: TCP
  ref: null
- id: 25000
  isopen: false
  net: management_net
  port: '25000'
  port_type: null
  proto: TCP
  ref: null
- id: 25500
  isopen: false
  net: all
  port: '25500'
  port_type: null
  proto: UDP
  ref: null
- id: 25672
  isopen: false
  net: management_net
  port: '25672'
  port_type: null
  proto: TCP
  ref: null
- id: 25772
  isopen: false
  net: management_net
  port: '25772'
  port_type: null
  proto: TCP
  ref: null
- id: 29292
  isopen: true
  net: all
  port: '29292'
  port_type: null
  proto: TCP
  ref: null
- id: 29443
  isopen: true
  net: all
  port: '29443'
  port_type: null
  proto: TCP
  ref: null
- id: 29989
  isopen: false
  net: management_net
  port: '29989'
  port_type: null
  proto: TCP
  ref: null
- id: 29991
  isopen: false
  net: api
  port: '29991'
  port_type: null
  proto: TCP
  ref: null
- id: 30601
  isopen: false
  net: api
  port: 30601-30798
  port_type: k8s_nodeport
  proto: ALL
  ref: null
- id: 30803
  isopen: false
  net: api
  port: 30803-30889
  port_type: nodeport
  proto: ALL
  ref: null
- id: 30895
  isopen: false
  net: api
  port: 30895-30999
  port_type: nodeport
  proto: ALL
  ref: null
- id: 31901
  isopen: false
  net: api
  port: 31901-31996
  port_type: nodeport
  proto: ALL
  ref: null
- id: 32015
  isopen: false
  net: all
  port: '32015'
  port_type: nodeport
  proto: TCP
  ref: null
- id: 32062
  isopen: false
  net: all
  port: '32062'
  port_type: nodeport
  proto: UDP
  ref: null
- id: 32063
  isopen: false
  net: all
  port: '32063'
  port_type: nodeport
  proto: UDP
  ref: null
- id: 32064
  isopen: false
  net: all
  port: '32064'
  port_type: nodeport
  proto: TCP
  ref: null
- id: 35505
  isopen: false
  net: management_net
  port: '35505'
  port_type: null
  proto: UDP
  ref: null
- id: 35506
  isopen: false
  net: api
  port: '35506'
  port_type: null
  proto: TCP
  ref: null
- id: 35524
  isopen: false
  net: api
  port: '35524'
  port_type: null
  proto: TCP
  ref: null
- id: 35525
  isopen: false
  net: api
  port: '35525'
  port_type: null
  proto: TCP
  ref: null
- id: 35526
  isopen: false
  net: api
  port: '35526'
  port_type: null
  proto: TCP
  ref: null
- id: 35541
  isopen: false
  net: management_net
  port: '35541'
  port_type: null
  proto: TCP
  ref: null
- id: 40443
  isopen: true
  net: all
  port: '40443'
  port_type: null
  proto: UDP/TCP
  ref: null
- id: 45550
  isopen: true
  net: all
  port: '45550'
  port_type: null
  proto: UDP/TCP
  ref: null
- id: 53246
  isopen: false
  net: 'null'
  port: '53246'
  port_type: null
  proto: TCP
  ref: null
- id: 54321
  isopen: true
  net: all
  port: '54321'
  port_type: null
  proto: UDP/TCP
  ref: null
- id: 55357
  isopen: true
  net: 'null'
  port: '55357'
  port_type: null
  proto: TCP
  ref: null
- id: 18234
  isopen: false
  net: api
  port: '18234'
  port_type: null
  proto: UDP/TCP
  ref: null