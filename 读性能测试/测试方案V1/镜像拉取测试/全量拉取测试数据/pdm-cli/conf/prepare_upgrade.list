{"oki-cli": {"components": [{"name": "oki-cli", "model": "bin", "role": "paas_controller"}]}, "oki-gui": {"components": [{"name": "oes_dexcloud_msk_oki-bp", "model": "bp", "tag": "microservice", "role": "paas_controller"}, {"name": "oes_dexcloud_msk_oki-executor-bp", "model": "bp", "tag": "microservice", "role": "paas_controller"}, {"name": "oes_dexcloud_msk_oki-ui-bp", "model": "bp", "tag": "microservice", "role": "paas_controller"}], "scenario": {"tcf_scenario": "UME-standard"}, "timeout": 1500, "upgrade_starting_version": "v7.22.10.10", "retry": {"maxattempts": 3, "interval": 2}}}