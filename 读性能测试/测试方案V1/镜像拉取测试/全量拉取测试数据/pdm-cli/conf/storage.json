{"kind": "storage", "version": "v1", "enable_cluster": "nfs", "clusters": [{"name": "default1", "type": "nfs", "cluster_config": {"disk_type": "cinder", "share_disk": true, "volume_type": null, "share_disk_config": {"capacity": "500G", "cinder": {"cloud_disk_id": "", "judge_disk_id": ""}}}}, {"name": "default1", "type": "gluster", "cluster_config": {"mountDir": ""}}, {"name": "default1", "type": "ceph", "cluster_config": {}}]}