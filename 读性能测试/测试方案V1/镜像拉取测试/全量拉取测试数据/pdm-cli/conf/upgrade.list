[{"name": "op-containers-containerd", "vars": {}, "dep": []}, {"name": "docker", "vars": {}, "dep": []}, {"name": "blockstorage", "vars": {}, "dep": []}, {"name": "op-node-volume", "vars": {}, "dep": []}, {"name": "op-node-config-controller-bp", "vars": {}, "dep": []}, {"name": "op-node-kubeagent", "vars": {}, "dep": []}, {"name": "br", "vars": {}, "dep": []}, {"name": "cassandra", "vars": {}, "dep": []}, {"name": "op-ubs-python3lib", "vars": {}, "dep": []}, {"name": "cf-common", "vars": {}, "dep": []}, {"name": "cf-csm", "vars": {}, "dep": []}, {"name": "cf-nbm", "vars": {}, "dep": []}, {"name": "cf-pcluster", "vars": {}, "dep": []}, {"name": "cf-pdeploy", "vars": {}, "dep": []}, {"name": "cf-pdman", "vars": {}, "dep": []}, {"name": "cf-pnode", "vars": {}, "dep": []}, {"name": "cf-vnpm", "vars": {}, "dep": []}, {"name": "cf-vnpm-api", "vars": {}, "dep": []}, {"name": "cf-zartcli", "vars": {}, "dep": []}, {"name": "cnrm", "vars": {}, "dep": []}, {"name": "cnrm-manager", "vars": {}, "dep": []}, {"name": "dpdk_deps", "vars": {}, "dep": []}, {"name": "eps", "vars": {}, "dep": []}, {"name": "op-ops-fm-mgt-bp", "vars": {}, "dep": []}, {"name": "glusterfs_plugin", "vars": {}, "dep": []}, {"name": "glusterfs_server", "vars": {}, "dep": []}, {"name": "heartbeat", "vars": {}, "dep": []}, {"name": "inetagent", "vars": {}, "dep": []}, {"name": "inetproxy", "vars": {}, "dep": []}, {"name": "inetdeploy", "vars": {}, "dep": []}, {"name": "inetmanager", "vars": {}, "dep": []}, {"name": "in<PERSON><PERSON><PERSON>", "vars": {}, "dep": []}, {"name": "iportal", "vars": {}, "dep": []}, {"name": "iportaladmin", "vars": {}, "dep": []}, {"name": "k8s", "vars": {}, "dep": []}, {"name": "openjdk", "vars": {}, "dep": []}, {"name": "op-ops-log-logapiserver-bp", "vars": {}, "dep": []}, {"name": "mie", "vars": {}, "dep": []}, {"name": "monitor", "vars": {}, "dep": []}, {"name": "myportal", "vars": {}, "dep": []}, {"name": "nwmaster", "vars": {}, "dep": []}, {"name": "nwmonitor", "vars": {}, "dep": []}, {"name": "nwnode", "vars": {}, "dep": []}, {"name": "opsapiserver", "vars": {}, "dep": []}, {"name": "eventmgt", "vars": {}, "dep": []}, {"name": "<PERSON><PERSON><PERSON>", "vars": {}, "dep": []}, {"name": "pacemaker_cluster", "vars": {}, "dep": []}, {"name": "pmc", "vars": {}, "dep": []}, {"name": "prometheus", "vars": {}, "dep": []}, {"name": "pronoea", "vars": {}, "dep": []}, {"name": "posd", "vars": {}, "dep": []}, {"name": "registry", "vars": {}, "dep": []}, {"name": "sqm", "vars": {}, "dep": []}, {"name": "storage", "vars": {}, "dep": []}, {"name": "storage_agent", "vars": {}, "dep": []}, {"name": "nfs_agent", "vars": {}, "dep": []}, {"name": "ubu_mysql", "vars": {}, "dep": []}, {"name": "postgresql", "vars": {}, "dep": []}, {"name": "ubu_rabbit", "vars": {}, "dep": []}, {"name": "underpan", "vars": {}, "dep": []}, {"name": "utm", "vars": {}, "dep": []}, {"name": "manager-cert", "vars": {}, "dep": []}, {"name": "vnm", "vars": {}, "dep": []}, {"name": "zart", "vars": {}, "dep": []}, {"name": "op-asd-swr", "vars": {}, "dep": []}, {"name": "zenap_modeldesign", "vars": {}, "dep": []}, {"name": "zenap_msb_apigateway", "vars": {}, "dep": []}, {"name": "zenap_msb_consul_server", "vars": {}, "dep": []}, {"name": "zenap_msb_sdclient", "vars": {}, "dep": []}, {"name": "zenap_msb_router", "vars": {}, "dep": []}, {"name": "op-ops-tools", "vars": {}, "dep": []}, {"name": "op-ubs-dbtools", "vars": {}, "dep": []}, {"name": "os_pkg_repo", "vars": {}, "dep": []}, {"name": "userkpi", "vars": {}, "dep": []}, {"name": "toposervice", "vars": {}, "dep": []}, {"name": "nwd", "vars": {}, "dep": []}, {"name": "dgraphzero", "vars": {}, "dep": []}, {"name": "d<PERSON><PERSON><PERSON>", "vars": {}, "dep": []}, {"name": "op-dexmesh-canary-server", "vars": {}, "dep": []}, {"name": "op-dexmesh-config-server", "vars": {}, "dep": []}, {"name": "op-dexmesh-pilot", "vars": {}, "dep": []}, {"name": "op-dexmesh-resource", "vars": {}, "dep": []}, {"name": "op-dexmesh-sidecar-injector", "vars": {}, "dep": []}, {"name": "op-msb-router-sidecar", "vars": {}, "dep": []}, {"name": "op-msb-ingresscontroller-deployer", "vars": {}, "dep": []}, {"name": "logstash", "vars": {}, "dep": []}, {"name": "op-storage-ceph_csi_driver", "vars": {}, "dep": []}, {"name": "op-confcenter-pconf", "vars": {}, "dep": []}, {"name": "op-confcenter-confdata", "vars": {}, "dep": []}, {"name": "op-adrm-deploy", "vars": {}, "dep": []}, {"name": "op-gpu-scripts", "vars": {}, "dep": []}, {"name": "op-asm-webhook-bp", "vars": {}, "dep": []}, {"name": "op-nw-multus", "vars": {}, "dep": []}, {"name": "op-nw-flannel", "vars": {}, "dep": []}, {"name": "op-nw-calico", "vars": {}, "dep": []}, {"name": "op-nw-kubecni", "vars": {}, "dep": []}, {"name": "op-nw-cilium", "vars": {}, "dep": []}, {"name": "op-nw-policy", "vars": {}, "dep": []}, {"name": "op-nw-operator", "vars": {}, "dep": []}, {"name": "op-asm-helm-bp", "vars": {}, "dep": []}, {"name": "k8sopapiserver-bp", "vars": {}, "dep": []}, {"name": "zenap_cos-bp", "vars": {}, "dep": []}, {"name": "zenap_kms-bp", "vars": {}, "dep": []}, {"name": "zenap-kms", "vars": {}, "dep": []}, {"name": "op-node-agent", "vars": {}, "dep": []}, {"name": "op-security-gateway-bp", "vars": {}, "dep": []}, {"name": "op-portal-tcfportal-bp", "vars": {}, "dep": []}, {"name": "op-applicationmanager-applcm-bp", "vars": {}, "dep": []}, {"name": "op-portal-regiui-bp", "vars": {}, "dep": []}, {"name": "op-security-k8shook-bp", "vars": {}, "dep": []}, {"name": "op-security-project-bp", "vars": {}, "dep": []}, {"name": "vnf-lcm-bp", "vars": {}, "dep": []}, {"name": "op-asd-liteappm-bp", "vars": {}, "dep": []}, {"name": "op-psd-keystone-bp", "vars": {}, "dep": []}, {"name": "op-ops-metric-pmagt-bp", "vars": {}, "dep": []}, {"name": "op-eps-metrics-server-bp", "vars": {}, "dep": []}, {"name": "op-eps-prometheus-adapter-bp", "vars": {}, "dep": []}, {"name": "op-eps-vpa-updater-bp", "vars": {}, "dep": []}, {"name": "op-eps-vpa-recommender-bp", "vars": {}, "dep": []}, {"name": "op-eps-vpa-admission-controller-bp", "vars": {}, "dep": []}, {"name": "op-asm-operator", "vars": {}, "dep": []}, {"name": "op-asm-operator-bp", "vars": {}, "dep": []}, {"name": "op-asm-kubevela-bp", "vars": {}, "dep": []}, {"name": "op-asm-asm", "vars": {}, "dep": []}, {"name": "op-asm-asm-bp", "vars": {}, "dep": []}, {"name": "op-psd-provider-agent-bp", "vars": {}, "dep": []}, {"name": "op-psd-provider-bp", "vars": {}, "dep": []}, {"name": "op-psd-keystone-proxy", "vars": {}, "dep": []}, {"name": "op-storage-sp_nfs_agent", "vars": {}, "dep": []}, {"name": "op-containers-eviction-bp", "vars": {}, "dep": []}, {"name": "op-containers-coredns-bp", "vars": {}, "dep": []}, {"name": "op-containers-coredns-tenant-bp", "vars": {}, "dep": []}, {"name": "op-containers-dns-operator-bp", "vars": {}, "dep": []}, {"name": "op-containers-capi-kdt-bootstrap", "vars": {}, "dep": []}, {"name": "op-containers-capi-kdt-controlplane", "vars": {}, "dep": []}, {"name": "op-containers-capi-kubeadm-bootstrap", "vars": {}, "dep": []}, {"name": "op-containers-capi-kubeadm-bootstrap", "vars": {}, "dep": []}, {"name": "op-security-tcfutm-bp", "vars": {}, "dep": []}, {"name": "op-storage-csi_disk", "vars": {}, "dep": []}, {"name": "op-asm-knative-autoscaler-bp", "vars": {}, "dep": []}, {"name": "op-asm-knative-activator-bp", "vars": {}, "dep": []}, {"name": "op-asm-knative-controller-bp", "vars": {}, "dep": []}, {"name": "op-asm-knative-webhook-bp", "vars": {}, "dep": []}, {"name": "op-asm-knative-local-gateway-bp", "vars": {}, "dep": []}, {"name": "op-psd-provider-cpaas", "vars": {}, "dep": []}, {"name": "oes_dexcloud_msk_oki-bp", "vars": {}, "dep": []}, {"name": "oes_dexcloud_msk_oki-executor-bp", "vars": {}, "dep": []}, {"name": "oes_dexcloud_msk_oki-ui-bp", "vars": {}, "dep": []}, {"name": "op-containers-k8sconfig-webhook-bp", "vars": {}, "dep": []}, {"name": "op-containers-k8sconfig-bp", "vars": {}, "dep": []}, {"name": "op-containers-k8sextender-bp", "vars": {}, "dep": []}, {"name": "op-containers-kruise-controller-bp", "vars": {}, "dep": []}, {"name": "op-containers-kruise-daemon-bp", "vars": {}, "dep": []}, {"name": "op-containers-multiversionset-bp", "vars": {}, "dep": []}, {"name": "op-ops-event-eventagt-bp", "vars": {}, "dep": []}, {"name": "op-storage-csi_opdisk", "vars": {}, "dep": []}, {"name": "provider-pg", "vars": {}, "dep": []}, {"name": "op-cnrm-hook-bp", "vars": {}, "dep": []}, {"name": "op-ubs-daisy-pg", "vars": {}, "dep": []}, {"name": "op-node-capo", "vars": {}, "dep": []}, {"name": "op-node-capz", "vars": {}, "dep": []}, {"name": "op-nw-alita", "vars": {}, "dep": []}, {"name": "op-nw-moose", "vars": {}, "dep": []}, {"name": "op-nw-alitaportal-bp", "vars": {}, "dep": []}, {"name": "op-storage-csi_local_storage", "vars": {}, "dep": []}, {"name": "op-dr-mbr-bp", "vars": {}, "dep": []}, {"name": "op-dr-dsm-bp", "vars": {}, "dep": []}, {"name": "op-dr-webhook-bp", "vars": {}, "dep": []}, {"name": "op-cluster-api-controller", "vars": {}, "dep": []}, {"name": "op-cluster-clusterctl", "vars": {}, "dep": []}, {"name": "op-swr-trivy-bp", "vars": {}, "dep": []}, {"name": "op-containers-coredns", "vars": {}, "dep": []}, {"name": "op-inet-manager-ccm", "vars": {}, "dep": []}, {"name": "op-inet-manager-xlbmanager", "vars": {}, "dep": []}, {"name": "op-inet-manager-xlbagent", "vars": {}, "dep": []}, {"name": "op-inet-manager-nprm", "vars": {}, "dep": []}, {"name": "op-security-gatekeeper-bp", "vars": {}, "dep": []}, {"name": "op-security-sealedsecret-bp", "vars": {}, "dep": []}, {"name": "op-security-certcenter-bp", "vars": {}, "dep": []}, {"name": "op-ops-log-logshipper-bp", "vars": {}, "dep": []}, {"name": "oes_dexcloud_msg_apim-bp", "vars": {}, "dep": []}, {"name": "oes_dexcloud_msg_apim-ui-bp", "vars": {}, "dep": []}, {"name": "op-asd-cosg-cache-bp", "vars": {}, "dep": []}, {"name": "op-br-br", "vars": {}, "dep": []}, {"name": "op-br-br-bp", "vars": {}, "dep": []}, {"name": "op-br-config-bp", "vars": {}, "dep": []}, {"name": "op-nw-netinsight-analyzer", "vars": {}, "dep": []}, {"name": "op-nw-netinsight-agent", "vars": {}, "dep": []}, {"name": "op-asd-dragonfly", "vars": {}, "dep": []}, {"name": "op-storage-csi_nfs", "vars": {}, "dep": []}, {"name": "op-asd-sm-bp", "vars": {}, "dep": []}, {"name": "op-sysperf-perfgenius-platform", "vars": {}, "dep": []}]