base_dir: /etc/pdm/workspace/upgrade_scripts


scripts:
- path: common/backup_file_for_inherit_security.sh
  desc: common/backup_file_for_inherit_security.sh
  type: prepare

- path: rollback_cnrm_data/rollback_cnrm_data.sh
  desc: rollback cnrm data
  type: normal_rollback

- path: swr/swr_upgrade_post.sh
  desc: Delete soft-repo old data.
  type: post

- path: remove_cosmos_cron/remove_cron.sh
  desc: remove cosmos cron in /var/spool/cron/root
  type: prepare

- path: vnm/add_routes_v6.sh
  desc: Add ipv6 routers when necessary.
  type: prepare
  parameters:
    - netapi_v6_cidr: {{ com_vars.netapi_v6_cidr }}
    - netmgt_v6_cidr: {{ com_vars.netmgt_v6_cidr }}

- path: monitor/nw_reboot_clean.sh
  desc: Clean nw reboot.sh file.
  type: prepare

- path: common/fix_os_harden_bugs.sh
  desc: Fix os harden bugs before upgrade.
  type: prepare

- path: common/fix_nodeworker_bugs.sh
  desc: Fix nodeworker bugs before upgrade.
  type: prepare

- path: common/fix_volume_for_rollback.sh
  desc: Fix volume data for rollback after upgrade(only for cpaas to tcf).
  type: upgrade
  parameters:
    - cpaas_to_unified_lcm: {{ com_vars.cpaas_to_unified_lcm }}

- path: common/deny_vip_for_chrony.sh
  desc: Add deny vip line for chrony after upgrade.
  type: upgrade

- path: common/restart_chrony_when_vip_chage.sh
  desc: Restart chrony when vip changed.
  type: upgrade

- path: common/fix_clear_network.sh
  desc: Delete network cleanup script in rc.local.
  type: prepare

- path: common/port_range_modify.sh
  desc: Modify port_range before upgrade.
  type: prepare

- path: clean-docker-locks/clean_docker_locks.sh
  desc: Clean docker locks.
  type: prepare

- path: dbtools/dbtools_dir_pre_treatment.sh
  desc: Change dbtools data directory owner.
  type: prepare

- path: postgresql/pg_pre_treatment.sh
  desc: postgresql prepare treatment.
  type: prepare

- path: blockstorage/depend_pg_pre_treatment.sh
  desc: Change blockstorage ocf dependence.
  type: prepare

- path: nfs/clear_nfs_resource.sh
  desc: Clear nfs ocf resource when not nfs_server nodes.
  type: prepare

- path: k8s/modify_etcd_monitor.sh
  desc: Turn off etcd monitor on master nodes.
  type: prepare

- path: k8s/kruise_check_and_update.sh
  desc: Update kruise if necessary before other uds components.
  type: prepare

- path: k8s/mvs_check_and_update.sh
  desc: Update multiversionset if necessary before other mvs components.
  type: prepare

- path: set_docker_selinux_context/set_docker_selinux_context.sh
  desc: Set docker selinux context on all nodes.
  type: prepare

- path: pacemaker/run_pcmk_ansible_module_align_sh.sh
  desc: Align pacemaker ansible module according to ansible version.
  type: rollback


- path: common/clear_distributed_ver_list.sh
  desc: clear parallel distributed version list.
  type: upgrade

- path: os-package/os-package-prepare.sh
  desc: Check whether iso and upgrade package exist under the/paasdata/offline/paas/ , exec os upgrade prepare
  type: prepare

- path: inetagent/update_net_addr.sh
  desc: Network configuration reversal for fault node recovery in ume scenario
  type: upgrade

- path: storage/repair_volume_without_ClusterUUID.sh
  desc: repair volumes without ClusterUUID
  type: prepare

- path: ../../deploy_playbooks/iso-file-prepare.sh
  desc: From iso file distribute qcow2.
  type: prepare

- path: op-containers-containerd/sync_config_toml.sh
  desc: Sync containerd config.toml before paas upgraded
  type: prepare

- path: storage/stopAppMountedGlusterVolume.sh
  desc: stop glusterfs service before upgrade
  type: preprocess

- path: storage/startAppMountedGlusterVolume.sh
  desc: start glusterfs service after upgrade
  type: upgrade

- path: common/remove_audit_bind_rules/fix_audit_bind_bugs.sh
  desc: Fix audit bind bugs before upgrade.
  type: prepare

- path: storage/gluster_volume_heal.sh
  desc: do gluster volume heal
  type: prepare

- path: inetmanager/expand_overlay.sh
  desc: add vxlan in type_drivers.
  type: prepare

- path: elk/upgrade.sh
  desc: patch kafka services.
  type: prepare

- path: deal_parameter/pre_deal_parameter.sh
  desc: delete  'net.ipv4.tcp_tw_recycle=1' in /etc/sysctl.conf.
  type: prepare

- path: fm-mgt/clean_fm_mgt_snmp_service.sh
  desc: clean msb residual fm-mgt-snmp service.
  type: upgrade

- path: ../../deploy_playbooks/swr_art_transform/chart_transform.sh
  desc: transform chart art
  type: upgrade

- path: common/fix_provider_bugs.sh
  desc: Fix provider bugs
  type: prepare

- path: common/fix_sshd_AcceptEnv.sh
  desc: delete AcceptEnv from sshd_config
  type: prepare

- path: storage/modify_dynamic_block_config.sh
  desc: modify dynamic block config and opdisk driver switch(only for cpaas to tcf).
  type: prepare
  parameters:
    - cpaas_to_unified_lcm: {{ com_vars.cpaas_to_unified_lcm }}

- path: common/delete_log.sh
  desc: Delete expired log path when upgrade.
  type: upgrade

- path: common/fix_os_bug.sh
  desc: fix os bug before upgrade.
  type: prepare

- path: inetagent/add_iapi_addr_ssh_listen.sh
  desc: fix can not get net iapi ip bug before upgrade.
  type: prepare

- path: inetagent/prepare_for_the_upgrade_of_infra_nw.sh
  desc: prepare for the upgrade of infra nw.
  type: prepare

- path: storage/delete_lib_soft_links.sh
  desc: delete soft links in /usr/lib if exist
  type: prepare

- path: ftpv3/delete_ftpv3_xlb_service.sh
  desc: delete ftpv3 xlb service.
  type: prepare

- path: rollback_bp_component_sql_data/rollback_bp_component_sql_data.sh
  desc: rollback bp component sql data
  type: normal_rollback

- path: clean_confcenter_confdata/clean_confdata.sh
  desc: clean confcenter confdata when upgrade.
  type: upgrade

- path: common/delete_posd_image.sh
  desc: Delete posd(cpaas component) docker image.
  type: upgrade

- path: common/add_sshd_match_all.sh
  desc: Add Match all for Match user/group.
  type: prepare

- path: inetagent/update_listen_ip.sh
  desc: update listen ip.
  type: prepare

- path: provider-fm/delete_default_key.sh
  desc: delete default key.
  type: prepare

- path: inetagent/close_inetrules_syn_flood.sh
  desc: close inetrules syn flood.
  type: prepare

- path: op-ubs-python3lib/delete_old_data.sh
  desc: Delete python3lib old version data after upgrade.
  type: upgrade
