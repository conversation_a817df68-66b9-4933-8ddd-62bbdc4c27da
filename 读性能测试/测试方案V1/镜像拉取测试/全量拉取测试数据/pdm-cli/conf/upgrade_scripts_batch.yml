base_dir: /etc/pdm/workspace/upgrade_scripts


scripts:
- path: common/set_chrony_mode_fast.sh
  desc: Set chrony mode to fast for old version.
  type: upgrade
  parameters:
    {% if envs.node_list %}
    - node_list: '{{ envs.node_list}}'
    {% else %}
    - node_list: ''
    {% endif %}

- path: common/deal_sec_harden_after_upgrade.sh
  desc: Fix os harden bugs.
  type: upgrade
  parameters:
    {% if envs.node_list %}
    - node_list: '{{ envs.node_list}}'
    {% else %}
    - node_list: ''
    {% endif %}
    {% if envs.cpaas_to_unified_lcm %}
    - cpaas_to_unified_lcm: '{{ envs.cpaas_to_unified_lcm}}'
    {% else %}
    - cpaas_to_unified_lcm: false
    {% endif %}

- path: op-containers-containerd/containerd_config.sh
  desc: Update containerd config.toml after component upgraded
  type: upgrade
  parameters:
    {% if envs.node_list %}
    - node_list: '{{ envs.node_list}}'
    {% else %}
    - node_list: ''
    {% endif %}
    {% if envs.cpaas_to_unified_lcm %}
    - cpaas_to_unified_lcm: '{{ envs.cpaas_to_unified_lcm}}'
    {% else %}
    - cpaas_to_unified_lcm: false
    {% endif %}
