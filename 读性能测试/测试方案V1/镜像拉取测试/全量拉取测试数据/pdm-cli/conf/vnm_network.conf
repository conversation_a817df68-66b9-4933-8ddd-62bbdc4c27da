[neutron]
bridge_mappings = physnet0:br-phy0,physnet1:br-phy1,physnet2:br-phy2
type_drivers = vlan,flat,vxlan
tenant_network_types = vlan
flat_networks = physnet0
network_vlan_ranges = physnet0:1000:1010,physnet1:1011:1020,physnet2:1021:1030
external_network_bridge = br-iapi
l3agent_type=neutron
vni_ranges = 1:16777215

[slb]
separate_flag=
lb7_fwd_type=
slb_memory=
lvs_inetrules=off
workmode=service
slb_inst_num=multiple
exclusivecpus=1
routes=[]
bfdsessions=[]
natrules=[]
external_prenetworks=[]
external_networks=[]
external_networks_gateways=[]
inner_networks=[{"name":"net_api"}]
lvs_cpunum=4
maxconn=30w
lvs_external_networks=
redundancys=
persistentconns=
private_lvs=false
tcp_timeout=
udp_timeout=
icmp_timeout=
http_timeout=
ka_adv_interval=
ka_adv_timeout_times=
node_type=
gwg_routes=
apiroute_cpu_limits=
apiroute_memory_limits=
apiroute_l7_max_conns=
apiroute_l4_max_conns=
apiroute_l7_limit_conn=
apiroute_l4_limit_conn=
multi_gw=[]

[paasconf]
serviceips=[]

[google]
gcloud_flag=false
slbvm_list=[]
aliases=[]

[infra_network]
infranw_mode=underlay
tunnel_network=net_admin
tunnel_type=vxlan
tunnel_localport=8789
infra_networks=[{"name":"net_api", "subnets":[{"name":"subnet_api","cidr":"***********/24","gateway_ip":"***********"}]}]
pre_networks=[{"name":"net_admin", "cidr":"*********/24"},{"name":"net_ext", "cidr":"*********/24","gateway_ip":"*********"}]
lldp_mode = disable_lldp
