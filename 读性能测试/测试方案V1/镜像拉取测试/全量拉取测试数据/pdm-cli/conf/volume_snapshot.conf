[DEFAULT]
# ssc_block_storage & ssc_local_storage are modified according to actual config, separated with ','
# ssc_block_storage specify volume_snapshot_class used for dynamic blocks
# ssc_local_storage specify volume_snapshot_class used for local storage
ssc_block_storage =
ssc_local_storage =

[example_block_volume_snapshot_class1]
used_node_type = vm
reclaim_policy = Delete
is_default_class = true

[example_block_volume_snapshot_class2]
used_node_type = bm
reclaim_policy = Retain
is_default_class = false

[example_local_volume_snapshot_class3]
reclaim_policy = Delete
is_default_class = false
