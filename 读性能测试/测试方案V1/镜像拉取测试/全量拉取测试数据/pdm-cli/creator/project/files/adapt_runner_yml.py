#!/usr/bin/env python

import os
import getopt
import sys
import yaml


def _adapt_vars_files(data):
    if 'vars_files' not in data or data['vars_files'] is None:
        data['vars_files'] = ['com_vars.yml', 'port_vars.yml']
    else:
        if ('/root/common/com_vars.yml' not in data['vars_files'] and
                'com_vars.yml' not in data['vars_files']):
            data['vars_files'].append('com_vars.yml')
        if ('/root/common/port_vars.yml' not in data['vars_files'] and
                'port_vars.yml' not in data['vars_files']):
            data['vars_files'].append('port_vars.yml')
    return


def _adapt_runner_yml(pkg_name, yml_path):
    with open(yml_path) as f:
        dataMap = yaml.safe_load(f)
    for data in dataMap:
        if 'hosts' not in data or data['hosts'] is None:
            data['hosts'] = 'nodes'
        data['remote_user'] = 'ubuntu'
        if pkg_name != 'cf-common':
            _adapt_vars_files(data)
    with open(yml_path, "w") as f:
        yaml.dump(dataMap, stream=f, default_flow_style=False)
    return True


def _generate_run_yml(pkg_name, script_dir, action):
    action_map = {"deploy": ["deploy.yml"],
                  "upgrade": ["backup.yml", "upgrade.yml"],
                  "rollback": ["rollback.yml"]}
    include_playbook = []
    alternative_scripts = ["upgrade.yml", "rollback.yml"]
    for script in action_map[action]:
        script_path = os.path.join(script_dir, script)
        if not os.path.exists(script_path):
            if script not in alternative_scripts:
                continue
            script = "deploy.yml"
            script_path = os.path.join(script_dir, script)
        # deploy.yml default exists
        _adapt_runner_yml(pkg_name, script_path)
        include_playbook.append({"import_playbook": script})

    run_yml = os.path.join(script_dir, "run.yml")
    with open(run_yml, "w") as f:
        yaml.safe_dump(include_playbook,
                       stream=f,
                       allow_unicode=True,
                       default_flow_style=False)
    return True


def main(argv=None):
    if argv is None:
        argv = sys.argv
    _, args = getopt.getopt(argv[1:], "h", ["help"])

    pkg_name = args[0]
    script_dir = args[1]
    action = args[2]
    _generate_run_yml(pkg_name, script_dir, action)


if __name__ == '__main__':
    main()
