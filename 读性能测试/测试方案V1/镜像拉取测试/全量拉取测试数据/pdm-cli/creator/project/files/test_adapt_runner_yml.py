import mock
import pytest

import adapt_runner_yml


INPUT_LIST = [[{"become": True,
                "become_method": "sudo",
                "tasks": [{"include": "task.yml"}],
                "vars_files": ["vars.yml",
                               "com_global_vars.yml"]}],
              [{"become": True,
                "become_method": "sudo",
                "tasks": [{"include": "task.yml"}]}]]
EXPECT_LIST = [[{"become": True,
                 "become_method": "sudo",
                 "hosts": "nodes",
                 "remote_user": "ubuntu",
                 "tasks": [{"include": "task.yml"}],
                 "vars_files": ["vars.yml",
                                "com_global_vars.yml",
                                "com_vars.yml",
                                "port_vars.yml"]}],
               [{"become": True,
                 "become_method": "sudo",
                 "hosts": "nodes",
                 "remote_user": "ubuntu",
                 "tasks": [{"include": "task.yml"}],
                 "vars_files": ["com_vars.yml",
                                "port_vars.yml"]}]]


def _get_test_case_list():
    case_list = []
    for idx, ip in enumerate(INPUT_LIST):
        case = (ip, EXPECT_LIST[idx])
        case_list.append(case)
    return case_list


@pytest.mark.parametrize("input_json,expect",
                         _get_test_case_list())
def test_adapt_runner_yml(mocker, input_json, expect):
    mocker.patch("builtins.open")
    mock_load = mocker.patch("yaml.safe_load")
    mock_dump = mocker.patch("yaml.dump")
    mock_load.return_value = input_json
    res = adapt_runner_yml._adapt_runner_yml("vnm", "")
    mock_dump.assert_called_once_with(expect,
                                      stream=mock.ANY,
                                      default_flow_style=False)
    assert(res is True)


@pytest.mark.parametrize("action,expect",
                         [("deploy",
                           [{"import_playbook": "deploy.yml"}]),
                          ("upgrade",
                           [{"import_playbook": "backup.yml"},
                            {"import_playbook": "upgrade.yml"}]),
                          ("rollback",
                           [{"import_playbook": "rollback.yml"}])])
def test__generate_run_yml(mocker, action, expect):
    mock_dump = mocker.patch("yaml.safe_dump")
    mocker.patch("builtins.open")
    mocker.patch("os.path.exists")
    mocker.patch("adapt_runner_yml._adapt_runner_yml")
    res = adapt_runner_yml._generate_run_yml("vnm", "", action)
    mock_dump.assert_called_once_with(expect,
                                      stream=mock.ANY,
                                      allow_unicode=True,
                                      default_flow_style=False)
    assert(res is True)


@pytest.mark.parametrize("action,ope_return,expect",
                         [("upgrade", [False, False],
                           [{"import_playbook": "deploy.yml"}]),
                          ("upgrade", [False, True],
                           [{"import_playbook": "upgrade.yml"}]),
                          ("upgrade", [True, False],
                           [{"import_playbook": "backup.yml"},
                            {"import_playbook": "deploy.yml"}]),
                          ("rollback", [False],
                           [{"import_playbook": "deploy.yml"}])])
def test__generate_run_yml_no_exists(mocker, action, ope_return, expect):
    mocker.patch("builtins.open")
    mock_dump = mocker.patch("yaml.safe_dump")
    mock_ope = mocker.patch("os.path.exists")
    mocker.patch("adapt_runner_yml._adapt_runner_yml")
    mock_ope.side_effect = ope_return
    res = adapt_runner_yml._generate_run_yml("vnm", "", action)
    mock_dump.assert_called_once_with(expect,
                                      stream=mock.ANY,
                                      allow_unicode=True,
                                      default_flow_style=False)
    assert(res is True)
