- name: "check {{download_pkg_path}}/{{pkg_info_path}} exist"
  stat:
    path: "{{download_pkg_path}}/{{pkg_info_path}}"
  register: pkg_exist

- include: deploy_action.yml
  when: action == "deploy"

- include: other_action.yml
  when: action != "deploy"

- include: local_common.yml

# from here do sth for other processes
- name: "{{pkg.name}} - generate role ansible hosts"
  copy:
    src: "/etc/pdm/hosts_{{comp_role}}"
    dest: "/etc/pdm/"

- name: "{{pkg.name}} sync to /paasdata/op-data/pdm-cli/pdm/pkg_for_nodeworker for nodeworker"
  synchronize:
    src: "{{download_pkg_path}}/{{pkg_path}}/"
    dest: "/paasdata/op-data/pdm-cli/pdm/pkg_for_nodeworker/{{pkg_path}}"
  when:
    - pkg.name in ["inetagent", "docker", "op-containers-containerd", "op-node-agent"]
