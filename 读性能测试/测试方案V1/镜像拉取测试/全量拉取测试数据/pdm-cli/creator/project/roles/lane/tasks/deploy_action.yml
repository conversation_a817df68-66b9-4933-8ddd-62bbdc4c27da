- block:
  - name: "{{pkg.name}} - download package"
    shell: ./zartcli -o=download -m={{pkg.model}} -i={{pkg.reponame}} -n={{pkg.name}} -v={{pkg.version}} -p={{download_pkg_path}}
    args:
      chdir: "{{ zartcli_path }}"
    when: not pkg_exist.stat.exists

  - name: "{{pkg.name}} - pull and tag package image"
    docker_image:
      name: "{{remote_registry_address}}/{{pkg.reponame}}/{{pkg.name}}"
      repository: "{{local_registry_address}}/{{pkg.reponame}}/{{pkg.name}}"
      tag: "{{pkg.version}}"
      state: present
      source: pull
      timeout: 300
    when:
      - pkg.model == "com"
      - comp_role in ["paas_init","paas_controller"]
  when: not offline

- block:
  - name: "{{pkg.name}} - copy package"
    synchronize:
      src: "/home/<USER>/pkg/{{pkg.model}}__@__{{pkg.reponame}}__@__{{pkg.name}}__@__{{pkg.version}}__@__no/"
      dest: "{{download_pkg_path}}/{{pkg_path}}"
    when: not pkg_exist.stat.exists

  - name: "{{pkg.name}} - load and tag package image"
    docker_image:
      name: "{{remote_registry_address}}/{{pkg.reponame}}/{{pkg.name}}"
      repository: "{{local_registry_address}}/{{pkg.reponame}}/{{pkg.name}}"
      tag: "{{pkg.version}}"
      load_path: "{{download_pkg_path}}/{{pkg_path}}/{{pkg.name}}.tar"
      state: present
      source: load
      timeout: 300
    when:
      - pkg.model == "com"
      - comp_role in ["paas_init","paas_controller"]
  when: offline

- name: "{{pkg.name}} - rm package image tag"
  docker_image:
    name: "{{remote_registry_address}}/{{pkg.reponame}}/{{pkg.name}}"
    tag: "{{pkg.version}}"
    state: absent
    source: local
    timeout: 300
  when:
    - pkg.model == "com"
    - comp_role in ["paas_init","paas_controller"]
