- block:
  # use shell in order to optimize time
  - name: "{{pkg.name}} - create runner env and inventory directory"
    shell: mkdir -p {{runner_dir}}/env && mkdir -p {{runner_dir}}/inventory

  - name: "{{pkg.name}} - generate env settings"
    copy:
      src: files/settings
      dest: "{{runner_dir}}/env/"

  # - name: "{{pkg.name}} - delete inventory"
  #   file:
  #     state: absent
  #     dest: "{{runner_dir}}/inventory/hosts"

  # - name: "{{pkg.name}} - create inventory"
  #   file:
  #     state: link
  #     src: "/etc/pdm/hosts_{{comp_role}}"
  #     dest: "{{runner_dir}}/inventory/hosts"

  - name: "{{pkg.name}} - make symlink to package"
    file:
      state: link
      src: "{{download_pkg_path}}/{{pkg_path}}"
      dest: "{{runner_dir}}/project"

  - name: "{{pkg.name}} - modify entry yml"
    shell: |
      if command -v pythonlatest &>/dev/null; then
        pythonlatest files/adapt_runner_yml.py "{{pkg.name}}" "{{download_pkg_path}}/{{pkg_path}}" "{{action}}"
      else
        python files/adapt_runner_yml.py "{{pkg.name}}" "{{download_pkg_path}}/{{pkg_path}}" "{{action}}"
      fi
    args:
      chdir: "{{ playbook_dir }}"

  # use shell in order to optimize time
  - name: "{{pkg.name}} - copy com_vars.yml and port_vars.yml"
    copy: src="/root/common/{{item}}" dest="{{runner_dir}}/project/{{item}}" mode=0640
    with_items:
      - com_vars.yml
      - port_vars.yml
    when: pkg.name != "cf-common"
  delegate_to: localhost
  run_once: true
