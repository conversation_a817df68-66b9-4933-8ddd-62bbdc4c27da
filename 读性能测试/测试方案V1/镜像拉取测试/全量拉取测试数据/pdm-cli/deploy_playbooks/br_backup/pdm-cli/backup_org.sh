#!/bin/bash -e

# Started by AICoder, pid:89ed13de1ddec8a149c10888a136db3ff952773f

# Usage
# 1. Obtain the json file required for exporting the template:
# curl -X GET -k https://assembler-apiserver.admin.svc:2509/kubeall/v1/config/template -o tmp.json
# 2. Prepare a template file with the same branch and scenario as the current environment
# 3. Run: pdm-cli reverse_build_template --json_path tmp.json --template "$xlsm_name"

log_file=/var/log/pdm-cli.log

if [ ! -f "$log_file" ]; then
    touch "$log_file"
    chmod 640 "$log_file"
fi

logger_info() {
    local script_name=$(basename "${BASH_SOURCE[1]}" .sh)
    local line_no=${BASH_LINENO[0]}
    LOG_DATE=$(date "+%Y-%m-%d %H:%M:%S,%3N")
    echo "$LOG_DATE - INFO - $script_name - $line_no - $@" >>  $log_file
}
logger_error() {
    local script_name=$(basename "${BASH_SOURCE[1]}" .sh)
    local line_no=${BASH_LINENO[0]}
    LOG_DATE=$(date "+%Y-%m-%d %H:%M:%S,%N")
    echo "$LOG_DATE - ERROR - $script_name - $line_no - $@" >>  $log_file
}

logger_info "plcm backup process begin ..."
file_path="/root/common/com_vars.yml"

if [ ! -f "$file_path" ]; then
    logger_error "the file com_vars.yml not exists, skip pdm-cli backup process"
    exit 0
fi

if ! grep -q -w "is_kubeall" "$file_path"; then
    logger_error "is_kubeall not exists, skip pdm backup process"
    exit 0
fi

if grep -w "is_kubeall" "$file_path" | grep -iq "false"; then
    logger_error "is_kubeall is false, skip pdm backup process"
    exit 0
fi

if [ -z "$1" ]; then
  logger_error "no parameter. Please provide the backup xlsm file saving path as a parameter."
  exit 1
fi
backup_path=$1
if [ ! -d "$backup_path" ]; then
  logger_error "$backup_path is not a valid path, start creating it"
  mkdir -p "$backup_path"
else
  logger_info "backup_path: "$backup_path" exists"
fi
json_name="openpalette.json"
json_url="https://assembler-apiserver.admin.svc:2509/kubeall/v1/config/template"
xlsm_name="OpenPalette_template.xlsm"
xlsm_path="/etc/pdm/deploy_playbooks/br_backup/pdm-cli"
cd  "$backup_path"
if [ -f "$json_name" ]; then
  logger_info "$json_name should not exist, deleting it"
  rm -rf "$json_name"
else
  logger_info "$json_name empty yet"
fi
max_retries=12
retry_secs=5
timeout_seconds=10
retries=0

while [ $retries -lt $max_retries ]; do
  response=$(curl -X GET -k -m $timeout_seconds -sSL -f -w "%{http_code}" "$json_url" -o tmp.json)
  # curl -X GET -k -m 10 -sSL -f -w "%{http_code}"
  #  "https://assembler-apiserver.admin.svc:2509/kubeall/v1/config/template" -o tmp.json
  http_code=$response
  status_code=$?
  logger_info "kubeall-assembler template request http_code: $http_code"
  logger_info "kubeall-assembler template request status_code: $status_code"

  if [ $status_code -eq 0 ] && [ "$http_code" -eq 200 ]; then
    jq '.data' tmp.json > "$json_name"
    rm -rf tmp.json
    if grep -q "page not found" "$json_name"; then
      retries=$((retries + 1))
      sleep $retry_secs
      logger_error "HTTP request page not found. Retrying $retries time(s)."
    else
      logger_info "$json_name downloaded successfully"
      break
    fi
  else
    rm -rf tmp.json
    if [ "$http_code" = "000" ] || [ "$http_code" -eq 500 ]; then
      retries=$((retries + 1))
      sleep $retry_secs
      logger_error "HTTP status code $http_code. Retrying $retries time(s)."
    else
      logger_error "HTTP status code $http_code. Exiting with error."
      break
    fi
  fi
done

if [ $retries -eq $max_retries ]; then
  logger_error "$json_name downloaded failed"
  exit 1
fi
if [ -f "$json_name" ]; then
  logger_info "$json_name exists"
else
  logger_error "$json_name downloaded failed"
fi

if [ ! -f "$xlsm_name" ]; then
  logger_info "xlsm file empty, copy the initial file to the current directory now"
  cp "$xlsm_path"/"$xlsm_name"  .
else
  logger_info "xlsm file exists"
fi

logger_info "begin reverse_build_template process ..."
pdm-cli  reverse_build_template --json_path "$json_name" --template "$xlsm_name" >>$log_file 2>&1
if [ $? -ne 0 ]; then
   rm -rf $json_name
   exit 1
fi

rm -rf $json_name
logger_info "pdm backup process ended successfully ..."
exit 0

# Ended by AICoder, pid:89ed13de1ddec8a149c10888a136db3ff952773f