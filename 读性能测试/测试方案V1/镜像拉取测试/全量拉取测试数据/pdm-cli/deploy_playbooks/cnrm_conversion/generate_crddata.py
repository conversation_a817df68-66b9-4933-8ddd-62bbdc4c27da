# -*- coding: UTF-8 -*-
import os
import json
import logging
import traceback
import sys
sys.path.append(os.path.dirname(os.path.dirname(__file__)))
# sys.path.append("/etc/pdm/deploy_playbooks/")
import conversion  # noqa: E402


class CRDdata(object):
    def __init__(self):
        self.config_logging()
        self.openpalette_file = '/etc/pdm/conf/openpalette.json'
        self.openpalette_json = {}  # /etc/pdm/conf/openpalette.json
        self.nodes = []
        self.templates = {}
        # key是  computing-template name，如computing-default，
        # 值是NodeEnhanceConfigs CR
        self.enhanceTempmapping = {}
        self.enCpuCRs = []
        self.enHugepageCRs = []
        self.nodeEnConfigCRs = []

    # def hasComputingEnhanceLabel(self, nodeLabels):
    #     for label in nodeLabels.split(','):
    #         for kv in label.split(':'):
    #             if len(kv) < 2:
    #                 continue
    #             if "cnrm.kubernetes.io/enhancecfg" == kv[0]:
    #                 return True, kv[1]
    #     return False, ""

    def generate_cnrm_crd_data(self):
        try:
            for node in self.nodes:
                labels = node["labels"]
                logging.info('node name %s has labels %s ' %
                             (node['name'], labels))
                # bok, labelVal = self.hasComputingEnhanceLabel(labels)
                # if not bok:
                #     logging.info('no cpu enhance label in node %s' %
                #                  node['name'])
                #     continue

                enhanceTmp = node["computing_enhancement_templates"]
                enhanceCR_name = enhanceTmp.lower()
                # if labelVal != enhanceCR_name:
                #     logging.error('labelVal is %s,enhanceCR_name is %s'
                #      ' not matched' % (labelVal, enhanceCR_name))
                #     continue
                if len(enhanceCR_name) == 0:
                    continue

                if enhanceCR_name in list(self.enhanceTempmapping.keys()):
                    logging.info('enhanceCR_name %s already generate ' %
                                 (enhanceCR_name))
                    continue

                enCpu_crName = enhanceCR_name + "-cpu"
                enHp_crName = enhanceCR_name + "-hp"

                configure = self.templates.get(enhanceTmp, '')
                enCpu_cr = {
                    "apiVersion": "cnrm.zte.com.cn/v1",
                    "kind": "EnhanceCpu",
                    "metadata": {
                        "name": enCpu_crName
                    },
                    "spec": {
                        "app_exclusive_count":
                            configure["app_exclusive_count"],
                        "user_reserved_cpu":
                            int(configure["user_reserved_cpu"] * 1000),
                        "cpu_pools": configure["cpu_allocation"]
                    }
                }

                hp_2m_count = int(configure["hugepage_2m_total_size"] / 2)
                hp_1g_count = int(configure["hugepage_1g_total_size"])
                user_rsvd_mem = int(configure["user_reserved_mem"] * 1024)
                hp_by_NumaId = configure["hugepage_byNumaId"]
                enHp_cr = {
                    "apiVersion": "cnrm.zte.com.cn/v1",
                    "kind": "EnhanceHugePage",
                    "metadata": {
                        "name": enHp_crName
                    },
                    "spec": {
                        "user_reserved_mem": user_rsvd_mem,
                        "hugepage_2m_count_PerNuma": hp_2m_count,
                        "hugepage_1g_count_PerNuma": hp_1g_count,
                        "hugepage_byNumaId": hp_by_NumaId,
                    }
                }

                nodeEnCfg_cr = {
                    "apiVersion": "cnrm.zte.com.cn/v1",
                    "kind": "NodeEnhanceConfig",
                    "metadata": {
                        "name": enhanceCR_name
                    },
                    "spec": {
                        "cpuTemplate": enCpu_crName,
                        "hugePageTemplate": enHp_crName
                    }
                }

                self.enhanceTempmapping[enhanceCR_name] = nodeEnCfg_cr
                self.enCpuCRs.append(enCpu_cr)
                self.enHugepageCRs.append(enHp_cr)
                self.nodeEnConfigCRs.append(nodeEnCfg_cr)

        except Exception as e:
            logging.error('error: %s' % e.args[0])
            logging.error(traceback.format_exc())

    def write_crddata_file(self):
        crd_datas = {"enhanceCpus": self.enCpuCRs,
                     "enhanceHugePages": self.enHugepageCRs,
                     "nodeEnhanceConfigs": self.nodeEnConfigCRs}
        with open("/etc/pdm/conf/cnrm_crddata.json", 'w') as f:
            f.write(json.dumps(crd_datas))

    def read_openpalette_file(self):
        filePath = self.openpalette_file
        self.openpalette_json = self.read_json_file(filePath)

    def get_sheets(self):
        self.nodes = self.openpalette_json.get('nodes')
        computCfg = conversion.ComputeConfigure(self.openpalette_json)
        self.templates = dict(computCfg)

    @staticmethod
    def config_logging(file_name='/var/log/generate_cnrm_crd_data.log'):
        logging.basicConfig(level=logging.DEBUG,
                            format='%(asctime)s %(levelname)s %(funcName)s'
                                   ' line[%(lineno)d]: %(message)s',
                            datefmt='%Y-%m-%d %H:%M:%S',
                            filename=file_name,
                            filemode='a')
        os.chmod(file_name, 0o640)
        logging.info('==== start generate cnrm crd data====')

    @staticmethod
    def read_json_file(file_path):
        try:
            with open(file_path) as fp:
                return json.load(fp)
        except Exception:
            logging.error('read_json_file %s err' % file_path)
            return {}

    def is_ran_mec_min_scene(self):
        try:
            scene = self.openpalette_json.get('global')[0]['tcf_scenario']
            if scene.upper() == 'RAN-MEC-MINI'.upper():
                return True
            return False
        except Exception as e:
            logging.error('get tcf scene failed, %s' % e.args[0])
            return False

    def start(self):
        try:
            if os.path.exists(self.openpalette_file) is False:
                logging.Info('%s file not exist!' % self.openpalette_file)
                return
            # 读openpalette.json到内存
            self.read_openpalette_file()

            if not self.is_ran_mec_min_scene():
                # only RAN-MEC-MINI
                logging.error('scene is not RAN-MEC-MINI, skip')
                return

            # 读取节点配置，computing templates to memory
            self.get_sheets()
            self.generate_cnrm_crd_data()
            self.write_crddata_file()
        except Exception as e:
            logging.error('error: %s' % e.args[0])
            logging.error(traceback.format_exc())


if __name__ == '__main__':
    CRDdata().start()
