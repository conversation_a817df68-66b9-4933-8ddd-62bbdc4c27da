import logging
import json
import os
import subprocess
import time
import uuid

import httplib2
import yaml
from six.moves import http_client

logging.basicConfig(filename="/var/log/pdm-cli.log",
                    format="%(asctime)s-%(name)s-%(levelname)s-%(message)s",
                    level=logging.INFO)
LOG = logging.getLogger(__name__)

SUCCESS = [http_client.OK, http_client.CREATED,
           http_client.ACCEPTED, http_client.NO_CONTENT]
COM_VARS = "/root/common/com_vars.yml"
PORT_VARS = "/root/common/port_vars.yml"


def get_yaml_val(comvars, element):
    with open(comvars) as f:
        dataMap = yaml.safe_load(f)
    element_value = ""
    for var in dataMap:
        if element in var:
            element_value = var[element]
            break
    return element_value


def _request(url, method, header=None, data=None,
             session_id=None, timeout=60):
    LOG.debug("Request: %s, %s, %s, %s" % (method, url, str(header), str(data)))
    try:
        if session_id is not None and data is not None:
            data['session_id'] = session_id
        http = httplib2.Http(timeout=timeout,
                             disable_ssl_certificate_validation=True)
        if data is None:
            response, content = http.request(url,
                                             method,
                                             headers=header)
        else:
            response, content = http.request(url,
                                             method,
                                             body=json.dumps(data),
                                             headers=header)
        if '' != content and content is not None:
            content = json.loads(content.decode())
        result = True if response.status in SUCCESS else False
        return result, content
    except Exception as e:
        print("Http request failed.(%s)", str(e))
        return False, None


def request(url, method, header=None, data=None,
            need_retry=True, with_session_id=False, timeout=None):
    result = False
    content = None

    try_count = 1
    if need_retry:
        try_count = 5

    session_id = None
    if with_session_id:
        session_id = str(uuid.uuid4())

    for _ in range(try_count):
        result, content = _request(url, method, header,
                                   data, session_id, timeout)
        if result:
            break
        time.sleep(5)
    return result, content


def get_timezone_by_cmd():
    try:
        command = "sudo timedatectl | grep zone | awk -F ': ' "\
                "'{print $2}' | awk '{print $1}'"
        timezone = subprocess.check_output(command, shell=True,
                                           universal_newlines=True).strip()
        LOG.debug("timezone info: %s" % timezone)
        return timezone
    except Exception as e:
        LOG.error("Get timezon info failed! %s" % e)
        return ""


# Started by AICoder

def get_conf(file_path):
    if not os.path.exists(file_path):
        return {}

    with open(file_path, 'r') as conf_file:
        try:
            return json.load(conf_file)
        except Exception as e:
            raise ValueError("Load %s error! \n%s" % (file_path, e))


def write_conf_to_file(data, file_path):
    if not data:
        return False
    with open(file_path, 'w') as conf_file:
        json.dump(data, conf_file, indent=4)
        return True


def setup_logging(log_path):
    try:
        log_dir = os.path.dirname(log_path)
        if not os.path.exists(log_dir):
            os.makedirs(log_dir, 0o750)

        logger = get_or_create_logger(log_path)
        configure_logger(logger, log_path)

        if not os.path.exists(log_path):
            with open(log_path, 'a') as _:
                pass
        os.chmod(log_path, 0o640)
        return logger
    except Exception as e:
        LOG.debug("Set up logging for %s failed, using default config instead."
                  % log_path)
        LOG.debug(e)
        return LOG


def get_or_create_logger(log_path):
    for handler in logging.root.handlers[:]:
        logging.root.removeHandler(handler)
    return logging.getLogger(log_path)


def configure_logger(logger, log_path):
    logger.setLevel(logging.DEBUG)

    if not any(handler for handler in logger.handlers if
               getattr(handler, 'baseFilename', None) == log_path):
        file_handler = logging.FileHandler(log_path)
        file_handler.setLevel(logging.DEBUG)
        file_handler.setFormatter(logging.Formatter(
            "%(asctime)s-%(name)s - line:%(lineno)d - %(levelname)s - %(message)s"))
        logger.addHandler(file_handler)

# Ended by AICoder


# Started by AICoder, pid:0be4d84aa78d41be88d3287fdfb9bc48
def update_yaml_file(file_path, data):
    """
    Updates the specified YAML file with the given key-value pairs
    without overwriting other content.

    :param file_path: The path to the YAML file to be updated.
    :param data: A dictionary containing the key-value pairs to be updated.
    :return: A tuple containing a boolean indicating success or failure,
             and a dictionary containing error information if any.
    """
    try:
        with open(file_path, "r") as file:
            yaml_data = yaml.safe_load(file)
        if yaml_data is None:
            yaml_data = {}
        if not isinstance(yaml_data, dict):
            raise ValueError("YAML content is not a dictionary as expected.")
        yaml_data.update(data)
        with open(file_path, "w") as file:
            yaml.safe_dump(yaml_data, file, default_flow_style=False)
        return True, {}
    except Exception as e:
        return False, {"message": str(e)}
# Ended by AICoder, pid:0be4d84aa78d41be88d3287fdfb9bc48
