# Started by AICoder, pid:z1319bcdf51a7d1149020b857197c55541220dee

import os
import sys
from openpyxl.utils import get_column_letter

"""
This module provides some functions to process Excel files using openpyxl library.
Considering openpyxl is a third-party library, it is not included in the node by default.
Besides, non-Ascii characters in this file may cause encoding issues.
So do not import this module directly in the code.
"""

sys.path.append("/etc/pdm")
from deploy_playbooks.common import common


LOG_DIR = "/paasdata/op-log/pdm-cli/non_kubeall_template_export"
LOG_FILE = os.path.join(LOG_DIR, "template_export.log")
LOG = common.setup_logging(LOG_FILE)


class ExcelOperation:
    def __init__(self):
        pass

    # 获取中英文映射表
    @staticmethod
    def get_en_ui_map(workbook):
        LOG.debug("get Chinese to English map begin")
        sheet_name = ExcelOperation.get_translation_sheet_name(workbook)
        current_sheet = workbook[sheet_name]
        rows = current_sheet.max_row
        en_ui_map = {}
        for i in range(1, rows + 1):
            en_key = current_sheet.cell(row=i, column=2).value
            ui_value = current_sheet.cell(row=i, column=1).value
            if en_key and ui_value:
                if "Summary" in workbook.sheetnames and ("PaaS"+ui_value) in workbook.sheetnames:
                    en_ui_map[en_key] = "PaaS" + ui_value
                else:
                    en_ui_map[en_key] = ui_value
        return en_ui_map

    # 查找中英文对照的 sheet 名称
    @staticmethod
    def get_translation_sheet_name(workbook):
        if "中英文对照" in workbook.sheetnames:
            return "中英文对照"
        elif "UIToEnglish" in workbook.sheetnames:
            return "UIToEnglish"
        else:
            raise ValueError(
                "Neither '中英文对照' nor 'UIToEnglish' sheet found in the workbook."
            )

    # 查找对应的中文 sheet 名称
    @staticmethod
    def find_ui_name_by_en_name(d, target_key):
        if target_key in d:
            value = d[target_key]
            LOG.debug(f"find ui name for {target_key}: '{value}'")
            return value
        LOG.debug(f"{target_key} not found in the map")
        return None

    # 设置 sheet 页的状态（隐藏或显示）
    @staticmethod
    def set_sheet_state(workbook, sheet_names: list, state: str):
        try:
            en_ui_map = ExcelOperation.get_en_ui_map(workbook)
            for sheet_name in sheet_names:
                ExcelOperation._set_single_sheet_state(
                    workbook, en_ui_map, sheet_name, state
                )
            return True
        except Exception as e:
            LOG.error("set_sheet_state for sheet %s failed: %s" % (sheet_names, str(e)))
            return False

    # 单个 sheet 页的状态设置
    @staticmethod
    def _set_single_sheet_state(workbook, en_ui_map, en_name, state):
        try:
            ui_name = ExcelOperation.find_ui_name_by_en_name(en_ui_map, en_name)
            if ui_name not in workbook.sheetnames:
                raise Exception(f"sheet {ui_name} not exist in workbook")
            sheet = workbook[ui_name]
            sheet.sheet_state = state
            LOG.debug(f"Set sheet {ui_name} state to {state}")
        except KeyError:
            LOG.warning(f"sheet {ui_name} not exist in workbook")

    # 显示或隐藏 sheet 页
    @staticmethod
    def toggle_sheets_visibility(workbook, sheets: list, visible: bool = True):
        try:
            visibility_state = "visible" if visible else "hidden"
            LOG.debug(f"Toggle sheets {sheets} visibility to {visibility_state} begin.")
            LOG.debug(f"Sheet list: {workbook.sheetnames}")

            if not ExcelOperation.set_sheet_state(workbook, sheets, visibility_state):
                raise Exception("set_sheet_state failed")

            LOG.debug(
                f"Toggle sheets {sheets} visibility to {visibility_state} success."
            )
            return True
        except Exception as e:
            LOG.warning(f"Unknown error occurred: {e}")
            return False

    # 显示或隐藏列
    @staticmethod
    def toggle_columns_visibility(
        workbook, sheet_name: str, names: list, visible: bool = True
    ):
        try:
            LOG.debug(f"Toggle columns {names} visibility to {visible} begin.")
            en_ui_map = ExcelOperation.get_en_ui_map(workbook)
            ui_sheet_name = ExcelOperation.find_ui_name_by_en_name(
                en_ui_map, sheet_name
            )
            for name in names:
                ui_column_name = ExcelOperation.find_ui_name_by_en_name(en_ui_map, name)
                if not ExcelOperation._toggle_column(
                    workbook, ui_sheet_name, ui_column_name, visible
                ):
                    raise Exception(f"toggle_column for {name} failed")
            LOG.debug(f"Toggle columns {names} visibility to {visible} success.")
            return True
        except Exception as e:
            LOG.warning(f"Unknown error occurred: {e}")
            return False

# Started by AICoder, pid:30ba0z19d39282d141a00b5df0081437a832b0ba
    @staticmethod
    def is_column_exist(workbook, sheet_en_name: str, column_en_name: str) -> bool:
        try:
            en_ui_map = ExcelOperation.get_en_ui_map(workbook)

            ui_sheet_name = ExcelOperation.find_ui_name_by_en_name(
                en_ui_map, sheet_en_name
            )
            ui_column_name = ExcelOperation.find_ui_name_by_en_name(
                en_ui_map, column_en_name
            )

            if not ui_sheet_name or not ui_column_name:
                LOG.warning(
                    f"Sheet '{sheet_en_name}' or column '{column_en_name}' not found in UI map."
                )
                return False

            sheet = workbook[ui_sheet_name]
            first_row_values = [cell.value for cell in sheet[1]]

            return ui_column_name in first_row_values

        except KeyError:
            LOG.warning(f"Sheet '{ui_sheet_name}' not found in the workbook.")
            return False

        except Exception as e:
            LOG.warning(
                f"An error occurred while checking column {column_en_name} existence: {e}"
            )
            return False
# Ended by AICoder, pid:30ba0z19d39282d141a00b5df0081437a832b0ba

    # 处理单列的隐藏或显示
    @staticmethod
    def _toggle_column(workbook, sheet_name: str, ui_name: str, visible: bool):
        try:
            sheet = workbook[sheet_name]
            column_letter = None
            for cell in sheet[1]:
                if cell.value == ui_name:
                    column_letter = get_column_letter(cell.column)
                    break

            if not column_letter:
                LOG.error(f"Error: Column header '{ui_name}' not found.")
                return False

            # 根据 visible 设置列是否隐藏
            is_hidden = not visible
            if sheet.column_dimensions[column_letter].hidden != is_hidden:
                sheet.column_dimensions[column_letter].hidden = is_hidden
                LOG.debug(
                    f"Column {column_letter} in sheet '{sheet_name}' is now {'hidden' if is_hidden else 'visible'}."
                )
            else:
                LOG.debug(
                    f"Column {column_letter} in sheet '{sheet_name}' is already {'hidden' if is_hidden else 'visible'}."
                )
            return True
        except Exception as e:
            LOG.error(f"An unknown error occurred while toggling column: {e}")
            return False

# Ended by AICoder, pid:z1319bcdf51a7d1149020b857197c55541220dee
