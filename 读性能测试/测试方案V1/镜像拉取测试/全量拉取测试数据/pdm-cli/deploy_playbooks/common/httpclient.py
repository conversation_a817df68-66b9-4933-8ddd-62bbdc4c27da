import httplib2
import logging
import time
import json

from six.moves import http_client

logging.basicConfig(filename="/var/log/pdm-cli.log",
                    format="%(asctime)s-%(name)s-%(levelname)s-%(message)s",
                    level=logging.INFO)
LOG = logging.getLogger(__name__)

SUCCESS_CODE = [http_client.OK,
                http_client.CREATED,
                http_client.ACCEPTED,
                http_client.NO_CONTENT]


def set_headers(headers):
    if headers is None:
        headers = {}
    headers['User-Agent'] = 'pdm-cli'
    return headers


def json_loads(json_str):
    try:
        return json.loads(json_str)
    except Exception:
        return json_str


def _request(url, method, header=None, data=None, timeout=None):
    LOG.debug("Request %s %s %s %s" % (method, url, str(header), str(data)))
    try:
        time_out = 60 if timeout is None else timeout
        http_cli = httplib2.Http(timeout=time_out,
                                 disable_ssl_certificate_validation=True)
        header = set_headers(header)
        data = None if data is None else json.dumps(data)
        resp, cnt = http_cli.request(url, method, body=data, headers=header)
        if not resp:
            return False, None
        cnt = json_loads(cnt.decode()) if cnt else None
        LOG.debug("Req %s %s. resp staus is %s" % (method, url, resp.status))
        result = True if resp.status in SUCCESS_CODE else False
        return result, cnt
    except Exception as e:
        LOG.debug("send http request failed.(%s)" % str(e))
        return False, None


def send_http_request(url, method, header=None, data=None, timeout=None,
                      retry_times=5):
    result = False
    content = None
    for _ in range(retry_times):
        result, content = _request(url, method, header, data, timeout)
        if result:
            break
        time.sleep(5)
    return result, content
