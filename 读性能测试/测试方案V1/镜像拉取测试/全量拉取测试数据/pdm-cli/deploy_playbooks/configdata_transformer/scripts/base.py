import logging

from abc import ABCMeta, abstractmethod


DEBUG_LOG_FILE = "/var/log/pdm-cli.log"


class Transformer(metaclass=ABCMeta):
    def __init__(self):
        self.config_logging()

    def config_logging(self):
        try:
            format = ('%(asctime)s %(levelname)s %(filename)s'
                      '%(funcName)s line[%(lineno)d]: %(message)s')
            logging.basicConfig(level=logging.INFO,
                                format=format,
                                datefmt='%Y-%m-%d %H:%M:%S',
                                filename=DEBUG_LOG_FILE,
                                filemode='a'
                                )
            return True
        except Exception:
            return False

    @abstractmethod
    def do_transform(self, scenario):
        pass
