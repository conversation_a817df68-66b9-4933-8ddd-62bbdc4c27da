import os
import json
import logging
import traceback


class Utils(object):
    @staticmethod
    def read_json(filename):
        try:
            if os.path.exists(filename):
                with open(filename) as fp:
                    return json.load(fp)
            return {}
        except Exception:
            logging.error(traceback.format_exc())
            return {}


class cnrm_cfg_transform(object):
    def __init__(self):
        try:
            self.inject_file = '/etc/pdm/conf/cnrm.json'
            self.application_name = 'cnrm'

            self.cnrm_data = {}
            self.cpu = {}

            self.init_data()
        except Exception:
            logging.error(traceback.format_exc())

    def init_data(self):
        try:
            self.cnrm_data = Utils.read_json(self.inject_file)
            # print(self.cnrm_data)
            if 'cpu' in self.cnrm_data:
                self.cpu = self.cnrm_data.get('cpu', {})
                # print(self.cpu)
            logging.info('cpu: %s' % str(self.cpu))
        except Exception:
            logging.error(traceback.format_exc())

    def do_transform(self, scenario=None):
        try:
            logging.info('cnrm data init')
            vethirqswitch = \
                self.cpu.get('veth_softirq_affinity_switch', True)
            nodeirqswitch = \
                self.cpu.get('nodeport_softirq_affinity_switch', True)
            syssrvaffinityswitch = \
                self.cpu.get('sysservice_cpu_affinity_switch', False)
            phyportaffinityswitch = \
                self.cpu.get('physical_netport_irq_affinity_switch', False)
            data = {
                "application": "cnrm",
                "items": {
                    "taking_effect_mode":
                        self.cpu.get('taking_effect_mode', ''),
                    "veth_softirq_affinity_switch":
                        'true' if vethirqswitch is True else 'false',
                    "nodeport_softirq_affinity_switch":
                        'true' if nodeirqswitch is True else 'false',
                    "sysservice_cpu_affinity_switch":
                        'true' if syssrvaffinityswitch is True else 'false',
                    "physical_netport_irq_affinity_switch":
                        'true' if phyportaffinityswitch is True else 'false',
                }
            }

            return data
        except Exception:
            logging.error(traceback.format_exc())
            return {}


if __name__ == '__main__':
    print(cnrm_cfg_transform().do_transform())
