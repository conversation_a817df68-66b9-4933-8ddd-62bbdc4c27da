import logging
import sys
import traceback
if sys.version > '3':
    import configparser as ConfigParser
else:
    import ConfigParser


class global_cfg_transform(object):
    def fill_option_data(self, config, data, section, option, default_value):
        logging.info("fill option %s data" % option)
        try:
            if config.has_option(section, option):
                data["items"][option] = config.get(section, option)
                return data
        except Exception:
            logging.error(traceback.format_exc())
        data["items"][option] = default_value
        return data

    def generate_paas_conf_data(self, data):
        paas_conf_filepath = "/etc/pdm/conf/paas.conf"
        config = ConfigParser.ConfigParser(allow_no_value=True)
        config.read(paas_conf_filepath)
        data = self.fill_option_data(config, data, "cmcc",
                                     "cmcc_container_cloud", "false")
        data = self.fill_option_data(config, data, "optional_features",
                                     "multi_k8s_clusters", "false")
        return data

    def do_transform(self, scenario=None):
        logging.info("global data init")
        data = {"application": "Global",
                "items": {}}
        data = self.generate_paas_conf_data(data)
        return data


if __name__ == '__main__':
    print(global_cfg_transform().do_transform())
