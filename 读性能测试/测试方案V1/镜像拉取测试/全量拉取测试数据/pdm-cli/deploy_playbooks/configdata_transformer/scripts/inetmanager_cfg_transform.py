# -*- coding:utf-8 -*-
import os
import json
import copy
import logging
import traceback

from IPy import IP


class Check(object):
    @staticmethod
    def is_vlan_legal(vlan_str):
        try:
            if 0 < int(vlan_str) < 4095:
                # 合法vlan范围： 1-4094
                return True
            return False
        except Exception:
            return False

    @staticmethod
    def is_ip_legal(ip_str, version=None):
        try:
            if '/' in ip_str:
                return False
            IP(ip_str)
            if version is None:
                return True
            else:
                return IP(ip_str).version() == version
        except Exception:
            return False

    @staticmethod
    def is_cidr_legal(cidr_str, version=None):
        try:
            if '/' not in cidr_str:
                return False
            IP(cidr_str)
            if version is None:
                return True
            else:
                return IP(cidr_str).version() == version
        except Exception:
            return False

    @staticmethod
    def is_metric_legal(metric_str):
        try:
            if metric_str == '':
                return True
            # 范围是32位无符号整数？ 与操作系统有关吗？
            if 0 < int(metric_str) < 4294967295:
                return True
            return False
        except Exception:
            return False


class Utils(object):
    @staticmethod
    def read_json(filename):
        try:
            if os.path.exists(filename):
                with open(filename) as fp:
                    return json.load(fp)
            return {}
        except Exception:
            logging.error(traceback.format_exc())
            return {}

    @staticmethod
    def format_ip(ip_str):
        try:
            return str(IP(ip_str))
        except Exception:
            return ip_str

    @staticmethod
    def add_bracket_ip_v6(ip_addr):
        if '/' not in ip_addr:
            if not ip_addr.startswith('['):
                return '[' + str(ip_addr) + ']'
        ip, mask = ip_addr.split('/')
        if not ip.startswith('['):
            return '[' + str(ip) + ']' + '/' + str(mask)
        return ip_addr


class inetmanager_cfg_transform(object):
    def __init__(self):
        try:
            self.inject_file = '/etc/paas/config.d/openpalette.json'
            self.application_name = 'inetmanager'

            self.openpalette_data = {}
            self.networks = []

            self.__static_routes_v4 = ''
            self.__static_routes_v6 = ''

            self.init_data()
        except Exception:
            logging.error(traceback.format_exc())

    def init_data(self):
        try:
            self.openpalette_data = Utils.read_json(self.inject_file)
            if 'networks' in self.openpalette_data:
                self.networks = self.openpalette_data.get('networks', [])
            logging.info('networks: %s' % str(self.networks))
        except Exception:
            logging.error(traceback.format_exc())

    def do_transform(self, scenario=None):
        try:
            logging.info('inetmanager data init')
            net_dict = {
                'net_ex': 'net_iapi',
                'net_traffic': 'net_traffic'
            }
            self.init_static_routes(net_name=net_dict['net_ex'])
            # 1. vip 只支持配一个，不能配一个范围。
            # 2.
            data = {
                "application": "inetmanager",
                "items": {
                    "net_ex_vip_v4":
                        self.get_vip_v4(net_name=net_dict['net_ex']),
                    "net_ex_cidr_v4":
                        self.get_cidr_v4(net_name=net_dict['net_ex']),
                    "net_ex_gw_v4":
                        self.get_gw_v4(net_name=net_dict['net_ex']),
                    "net_ex_vip_v6":
                        self.get_vip_v6(net_name=net_dict['net_ex']),
                    "net_ex_cidr_v6":
                        self.get_cidr_v6(net_name=net_dict['net_ex']),
                    "net_ex_gw_v6":
                        self.get_gw_v6(net_name=net_dict['net_ex']),
                    "net_ex_vlan_id":
                        self.get_net_vlan_id(net_name=net_dict['net_ex']),
                    "static_routes_v4":
                        self.get_static_routes(ip_ver=4),
                    "static_routes_v6":
                        self.get_static_routes(ip_ver=6),
                    "net_traffic_vip_v4":
                        self.get_vip_v4(net_name=net_dict['net_traffic']),
                    "net_traffic_cidr_v4":
                        self.get_cidr_v4(net_name=net_dict['net_traffic']),
                    "net_traffic_gw_v4":
                        self.get_gw_v4(net_name=net_dict['net_traffic']),
                    "net_traffic_vip_v6":
                        self.get_vip_v6(net_name=net_dict['net_traffic']),
                    "net_traffic_cidr_v6":
                        self.get_cidr_v6(net_name=net_dict['net_traffic']),
                    "net_traffic_gw_v6":
                        self.get_gw_v6(net_name=net_dict['net_traffic']),
                    "net_traffic_vlan_id":
                        self.get_net_vlan_id(net_name=net_dict['net_traffic']),
                    "net_traffic_port": self.get_net_traffic_port()
                }
            }
            return data
        except Exception:
            logging.error(traceback.format_exc())
            return {}

    def format_static_route(self):
        try:
            route_v6_str_list = []
            for route_v6 in self.__static_routes_v6.split(','):
                route = self.parser_route_v6_str_to_format(route_v6)
                if route is None:
                    continue
                dest = route.get('destination', '')
                next_hop = route.get('next_hop', '')
                metric = route.get('metric', '')
                route = {
                    'destination': Utils.format_ip(dest),
                    'next_hop': Utils.format_ip(next_hop),
                    'metric': metric
                }
                route_str = self.parser_route_v6_format_to_string(route)
                route_v6_str_list.append(route_str)
            self.__static_routes_v6 = ','.join(route_v6_str_list)

            route_v4_str_list = []
            for route_v4 in self.__static_routes_v4.split(','):
                route = self.parser_route_v4_str_to_format(route_v4)
                if route is None:
                    continue
                dest = route.get('destination', '')
                next_hop = route.get('next_hop', '')
                metric = route.get('metric', '')
                route = {
                    'destination': Utils.format_ip(dest),
                    'next_hop': Utils.format_ip(next_hop),
                    'metric': metric
                }
                route_str = self.parser_route_v4_format_to_string(route)
                route_v4_str_list.append(route_str)
            self.__static_routes_v4 = ','.join(route_v4_str_list)
        except Exception:
            logging.error(traceback.format_exc())

    @staticmethod
    def parser_route_v4_format_to_string(route):
        try:
            destination = route['destination']
            next_hop = route['next_hop']
            metric = route['metric']
            return destination + ':' + next_hop + ':' + metric
        except Exception:
            logging.error(traceback.format_exc())
            return ''

    @staticmethod
    def parser_route_v4_str_to_format(string):
        try:
            # ********/24:**********:
            if string.strip() == '':
                return None
            destination = string.split(':')[0]
            next_hop = string.split(':')[1]
            if len(string.split(':')) != 3:
                metric = ''
            else:
                metric = string.split(':')[2]
            if not Check.is_ip_legal(next_hop, 4):
                return None
            if not Check.is_cidr_legal(destination, 4):
                return None
            if not Check.is_metric_legal(metric):
                metric = ''
            data = {
                'destination': destination,
                'next_hop': next_hop,
                'metric': metric
            }
            return data
        except Exception:
            logging.error(traceback.format_exc())
            return None

    @staticmethod
    def parser_route_v6_format_to_string(route):
        try:
            destination = route['destination']
            next_hop = route['next_hop']
            metric = route['metric']
            dst = Utils.add_bracket_ip_v6(destination)
            hop = Utils.add_bracket_ip_v6(next_hop)
            return dst + ':' + hop + ':' + metric
        except Exception:
            logging.error(traceback.format_exc())
            return ''

    @staticmethod
    def parser_route_v6_str_to_format(string):
        try:
            # [1118::]/64:[1118::1111]:1
            # [1118::]/64:[1118::1111]:
            # [1118::]/64:[1118::1111]:
            if string.strip() == '':
                return None
            last_ip_start = string.rfind('[')
            last_ip_end = string.rfind(']')
            next_hop = string[last_ip_start + 1: last_ip_end]
            split_index = string.find('/')
            mask = string[split_index + 1: last_ip_start - 1]
            ip = string[1: split_index - 1]
            destination = ip + '/' + mask
            metric = string[string.rfind(']:') + 2:].strip()
            if not Check.is_cidr_legal(destination, 6):
                return None
            if not Check.is_ip_legal(ip, 6):
                return None
            if not Check.is_metric_legal(metric):
                metric = ''
            data = {
                'destination': destination,
                'next_hop': next_hop,
                'metric': metric
            }
            return data
        except Exception:
            logging.error(traceback.format_exc())
            return None

    @staticmethod
    def format_network(network):
        try:
            # 格式化openpalette.json 里的 network
            if network is None:
                return None
            if network == {}:
                return None
            new_network = copy.deepcopy(network)
            subnet_names = new_network.get('subnet_names', [])
            if not isinstance(subnet_names, list):
                new_network['subnet_names'] = \
                    [new_network.get('subnet_names', '')]
                new_network['ip_versions'] = \
                    [new_network.get('ip_versions', '')]
                new_network['cidrs'] = \
                    [new_network.get('cidrs', '')]
                new_network['vips'] = \
                    [new_network.get('vips', '')]
                new_network['gateways'] = \
                    [new_network.get('gateways', '')]
                new_network['allocation_pools'] = \
                    [new_network.get('allocation_pools', '')]
                new_network['is_default_gateways'] = \
                    [new_network.get('is_default_gateways', '')]
                new_network['static_routes'] = \
                    [new_network.get('static_routes', '')]
            return new_network
        except Exception:
            logging.error(traceback.format_exc())
            return network

    def init_static_routes(self, net_name):
        try:
            self.__static_routes_v4 = ''
            self.__static_routes_v6 = ''
            for network in self.networks:
                network = self.format_network(network)
                if network.get('name', '') == net_name:
                    ip_versions = network.get('ip_versions')
                    static_routes = network.get('static_routes', '')
                    for index, static_route in enumerate(static_routes):
                        if str(ip_versions[index]).upper() == 'IPV4':
                            self.__static_routes_v4 = static_route
                        if str(ip_versions[index]).upper() == 'IPV6':
                            self.__static_routes_v6 = static_route
            self.format_static_route()
        except Exception:
            logging.error(traceback.format_exc())

    def get_static_routes(self, ip_ver):
        try:
            if ip_ver == 4:
                return self.__static_routes_v4
            elif ip_ver == 6:
                return self.__static_routes_v6
        except Exception:
            logging.error(traceback.format_exc())
            return ''

    def get_net_vlan_id(self, net_name):
        try:
            vlan_ids = ''
            for network in self.networks:
                if network.get('name', '') == net_name:
                    vlan_ids = network.get('segment_id', '')
            logging.info('vlan_ids : %s ' % str(vlan_ids))
            if isinstance(vlan_ids, list):
                for vlan_id in vlan_ids:
                    if Check.is_vlan_legal(vlan_id):
                        return vlan_id
            elif Check.is_vlan_legal(vlan_ids):
                return vlan_ids
            return ''
        except Exception:
            logging.error(traceback.format_exc())
            return ''

    def get_net_traffic_port(self):
        return ''

    def get_vip_v4(self, net_name):
        try:
            vips = ''
            for network in self.networks:
                if network.get('name', '') == net_name:
                    vips = network.get('vips', '')
            logging.info('vips : %s ' % str(vips))
            if isinstance(vips, list):
                for vip in vips:
                    if Check.is_ip_legal(vip, 4):
                        return vip
            elif Check.is_ip_legal(vips, 4):
                return vips
            return ''
        except Exception:
            logging.error(traceback.format_exc())
            return ''

    def get_cidr_v4(self, net_name):
        try:
            cidrs = ''
            for network in self.networks:
                if network.get('name', '') == net_name:
                    cidrs = network.get('cidrs', '')
            logging.info('cidrs : %s' % str(cidrs))
            if isinstance(cidrs, list):
                for cidr in cidrs:
                    if Check.is_cidr_legal(cidr, 4):
                        return cidr
            elif Check.is_cidr_legal(cidrs, 4):
                return cidrs
            return ''
        except Exception:
            logging.error(traceback.format_exc())
            return ''

    def get_gw_v4(self, net_name):
        try:
            gws = ''
            for network in self.networks:
                if network.get('name', '') == net_name:
                    gws = network.get('gateways', '')
            logging.info('gateways : %s' % str(gws))
            if isinstance(gws, list):
                for gw in gws:
                    if Check.is_ip_legal(gw, 4):
                        return gw
            elif Check.is_ip_legal(gws, 4):
                return gws
            return ''
        except Exception:
            logging.error(traceback.format_exc())
            return ''

    def get_vip_v6(self, net_name):
        try:
            vips = ''
            for network in self.networks:
                if network.get('name', '') == net_name:
                    vips = network.get('vips', '')
            logging.info('vips6 : %s ' % str(vips))
            if isinstance(vips, list):
                for vip in vips:
                    if Check.is_ip_legal(vip, 6):
                        return vip
            elif Check.is_ip_legal(vips, 6):
                return vips
            return ''
        except Exception:
            logging.error(traceback.format_exc())
            return ''

    def get_cidr_v6(self, net_name):
        try:
            cidrs = ''
            for network in self.networks:
                if network.get('name', '') == net_name:
                    cidrs = network.get('cidrs', '')
            logging.info('cidrs6 : %s' % str(cidrs))
            if isinstance(cidrs, list):
                for cidr in cidrs:
                    if Check.is_cidr_legal(cidr, 6):
                        return cidr
            elif Check.is_cidr_legal(cidrs, 6):
                return cidrs
            return ''
        except Exception:
            logging.error(traceback.format_exc())
            return ''

    def get_gw_v6(self, net_name):
        try:
            gws = ''
            for network in self.networks:
                if network.get('name', '') == net_name:
                    gws = network.get('gateways', '')
            logging.info('gateways6 : %s' % str(gws))
            if isinstance(gws, list):
                for gw in gws:
                    if Check.is_ip_legal(gw, 6):
                        return gw
            elif Check.is_ip_legal(gws, 6):
                return gws
            return ''
        except Exception:
            logging.error(traceback.format_exc())
            return ''


if __name__ == '__main__':
    print(json.dumps(inetmanager_cfg_transform().do_transform(), indent=4))
