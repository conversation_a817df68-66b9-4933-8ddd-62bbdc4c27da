class ntp_server_cfg_transform(object):
    def do_transform(self, scenario=None):
        init_data = {
            "application": "status_Global",
            "subtrees": [{
                "treeKey": "status_ntp_server",
                "treeValue": "status_ntp_server_value",
                "items": {
                    "config_timeout_limit": "180"
                }
            }]
        }
        return init_data


if __name__ == '__main__':
    print(ntp_server_cfg_transform().do_transform())
