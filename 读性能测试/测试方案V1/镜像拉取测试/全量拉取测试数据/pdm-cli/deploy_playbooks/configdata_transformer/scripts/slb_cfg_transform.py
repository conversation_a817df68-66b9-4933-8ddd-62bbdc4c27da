# -*- coding:utf-8 -*-
# author：lisen qiu
# crete date：2020-10-15
# update date：2020-10-28
# version:v1.1
# python script name:slb_cfg_transform.py
# update record:
# 1)confirm the interface,add class Transformer.
# 2)construct objects
# 3)change all item type to string
# 4)add log info after return None
# Requirement description：
# provide init config python script，read vnm_network.conf,...
# extract serviceips value, return agreed init.json format data.
import logging
import json
import sys
if sys.version > '3':
    import configparser as ConfigParser
    python_2 = False
    python_3 = True
else:
    import ConfigParser
    python_2 = True
    python_3 = False
# global path var:vnm_conf_filepath
vnm_conf_filepath = '/etc/pdm/conf/vnm_network.conf'
LOG = logging.getLogger(__name__)
# def slb_cfg_transform class


class slb_cfg_transform(object):
    def do_transform(self, scenario):
        try:
            transform_value = self.initjson_get_dict()
            if transform_value == '':
                LOG.info('info,Read None of vnm_network.conf file')
                return None
            else:
                return transform_value
        except Exception:
            LOG.error('Error!Call initjson_get_dict function failed')
            raise Exception('Error!Call initjson_get_dict fucnion failed')

    def read_vnm_network_paasconf_success(self):
        vnmread_success = True
        vnm_exist = True
        serviceips_exist = True
        # 1.create configparser object
        cf = ConfigParser.ConfigParser(allow_no_value=True)
        # 2.read the conf file
        try:
            # path vnm_conf_filepath
            cf_readconf = cf.read(vnm_conf_filepath)
            if cf_readconf == []:
                vnm_exist = False
                LOG.error('Error!The conf is not exist in this path')
                return False, []
            else:
                # 4.read paasconf's value
                try:
                    paasconf_serviceipsvalue = cf.get("paasconf", 'serviceips')
                except Exception:
                    serviceips_exist = False
                    LOG.error('Error!without paasconf or serviceips')
                    return False, []
        except Exception:
            vnmread_success = False
            LOG.error('Error!Reading conf is failed,wrong of format')
            return False, []
        SuccessFlag = vnmread_success and vnm_exist and serviceips_exist
        return SuccessFlag, paasconf_serviceipsvalue

    def read_vnm_network_paasconf_serviceips_v(self):
        RFsuccess, serviceips_v = self.read_vnm_network_paasconf_success()
        # 5.read serviceips value
        if RFsuccess is True:
            # if it is without any problem in reading file
            try:
                serviceips_v = json.loads(serviceips_v)
                serviceips_value_len = len(serviceips_v)
                if serviceips_value_len == 0:
                    LOG.error('error!The serviceips value is null')
                    return []
                else:
                    return serviceips_v
            except Exception:
                LOG.error('Error!load json error,josn format is wrong')
                return []
        else:
            return []

    def serviceips_rolesdeal(self, paasconf_serviceips_value):
        paasconf_serviceips_value_format_error = False
        if 'roles' in paasconf_serviceips_value:
            roles_eum = [[], ["sys"], ["app"], ["sys", "app"], ["app", "sys"]]
            if paasconf_serviceips_value['roles'] in roles_eum:
                pass
            else:
                paasconf_serviceips_value_format_error = True
            if paasconf_serviceips_value_format_error is True:
                LOG.error('Error!roles enum values is out of range')
        return paasconf_serviceips_value_format_error

    def serviceips_entrydeal(self, paasconf_serviceips_value):
        paasconf_serviceips_value_format_error = False
        if 'entry' in paasconf_serviceips_value:
            entry_enums = ["", "slb", "slbdnat"]
            if paasconf_serviceips_value['entry'] in entry_enums:
                pass
            else:
                paasconf_serviceips_value_format_error = True
            if paasconf_serviceips_value_format_error is True:
                LOG.error('Error!entry enum values is out of range')
        return paasconf_serviceips_value_format_error

    def serviceips_field_processM(self, paasconf_serviceips_value):
        paasconf_serviceips_format_error = False
        if 'external_ip_address' not in paasconf_serviceips_value:
            LOG.error('Error!external_ip_address field is not exist')
            paasconf_serviceips_format_error = True
        elif paasconf_serviceips_value['external_ip_address'] == "":
            LOG.error('Error!external_ip_address must be configured')
            paasconf_serviceips_format_error = True
        else:
            roles_format = self.serviceips_rolesdeal(paasconf_serviceips_value)
            entry_format = self.serviceips_entrydeal(paasconf_serviceips_value)
            paasconf_serviceips_format_error = (roles_format or entry_format)
        return paasconf_serviceips_format_error

    def serviceips_preprocess_value(self):
        # extrat the paasconf serviceips value filed,return with list format
        serviceipsv = self.read_vnm_network_paasconf_serviceips_v()
        serviceips_valuelen = len(serviceipsv)
        format_error = False
        if serviceips_valuelen == 0:
            return []
        else:
            for i in range(serviceips_valuelen):
                # call serviceips_field_processM()
                format_error = self.serviceips_field_processM(serviceipsv[i])
                if format_error is True:
                    break
            if format_error is True:
                return []
            else:
                return serviceipsv

    def secondlyr_mltdict(self, SipV, itemsNum):
        subtrees_second_dict = {}
        subtrees_second_dict['treeKey'] = "serviceips"
        subtrees_second_dict['treeValue'] = str(itemsNum)
        items_dict = {}
        # items_dict init
        items_dict['external_ip_address'] = ""
        items_dict['floating_ip_address'] = ""
        items_dict['fixed_ip_address'] = ""
        items_dict['label'] = ""  # string
        items_dict['entry'] = ""
        items_dict['roles'] = ""  # string
        items_dict['scope'] = ""
        # SipV is serviceips value
        if 'external_ip_address' in SipV:
            items_dict['external_ip_address'] = SipV['external_ip_address']
        if 'floating_ip_address' in SipV:
            items_dict['floating_ip_address'] = SipV['floating_ip_address']
        if 'fixed_ip_address' in SipV:
            items_dict['fixed_ip_address'] = SipV['fixed_ip_address']
        if 'label' in SipV:
            items_dict['label'] = ",".join(SipV['label'])  # string type
        if 'entry' in SipV:
            items_dict['entry'] = SipV['entry']
        if 'roles' in SipV:
            items_dict['roles'] = ",".join(SipV['roles'])  # string type
        if 'scope' in SipV:
            items_dict['scope'] = SipV['scope']
        subtrees_second_dict['items'] = items_dict
        return subtrees_second_dict

    def initjson_dict_preprocess(self):
        serviceips_value = self.serviceips_preprocess_value()
        dict_number = len(serviceips_value)
        # def first layer dict
        dict_json = {}
        dict_json['application'] = "slb"
        dict_json['subtrees'] = []
        # def second layer dict
        subtrees_first_dict = {}
        subtrees_first_dict['treeKey'] = "slb_networks"
        subtrees_first_dict['treeValue'] = "slb_networks"
        subtrees_first_dict['subtrees'] = []
        subtrees_second_dict_list = []
        for i in range(dict_number):
            subtrees_snd_dict = self.secondlyr_mltdict(serviceips_value[i], i)
            subtrees_second_dict_list.append(subtrees_snd_dict)
        # merge
        subtrees_first_dict['subtrees'] = subtrees_second_dict_list
        dict_json['subtrees'] = [subtrees_first_dict]
        return dict_json

    def initjson_get_dict(self):
        serviceips_value = self.serviceips_preprocess_value()
        serviceips_value_len = len(serviceips_value)
        if serviceips_value_len == 0:
            return ''
        else:
            # call the initjson_dict_preprocess()
            dict_json = self.initjson_dict_preprocess()
            return dict_json
