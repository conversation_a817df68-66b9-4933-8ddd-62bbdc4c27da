class timezone_cfg_transform(object):
    def do_transform(self, scenario=None):
        init_data = {
            "application": "status_Global",
            "subtrees": [{
                "treeKey": "status_timezone",
                "treeValue": "status_timezone_value",
                "items": {
                    "config_timeout_limit": "180"
                }
            }]
        }
        return init_data


if __name__ == '__main__':
    print(timezone_cfg_transform().do_transform())
