import logging
import sys
import os
import traceback
import json
import pkgutil


DEBUG_LOG_FILE = "/var/log/pdm-cli.log"
logging.basicConfig(level=logging.INFO,
                    format='%(asctime)s %(levelname)s %(filename)s '
                    '%(funcName)s line[%(lineno)d]: %(message)s',
                    datefmt='%Y-%m-%d %H:%M:%S',
                    filename=DEBUG_LOG_FILE,
                    filemode='a'
                    )
LOG = logging.getLogger()
OP_CONFDATA_DIR = "/etc/pdm/conf/op-confcenter-confdata"
DIR_MODE = 0o750
BASE_DIR = os.path.abspath(os.path.dirname(os.path.dirname(__file__)))


def generate_init_file(path, init_data):
    with open(path, 'w', ) as f:
        json.dump(init_data, f)
    os.chmod(path, 0o640)


def transform(scenario, module_path='scripts.'):
    try:
        path = BASE_DIR + "/configdata_transformer/scripts"
        modules = pkgutil.iter_modules(path=[path])
        init_data = {"data": []}
        if not os.path.exists(OP_CONFDATA_DIR):
            os.makedirs(OP_CONFDATA_DIR, mode=DIR_MODE)
        for _, mode_name, _ in modules:
            if "base" == mode_name:
                continue
            loaded_mode = __import__(
                module_path + mode_name, fromlist=[mode_name])
            class_name = mode_name
            loaded_class = getattr(loaded_mode, class_name)
            instance = loaded_class()
            transform_data = instance.do_transform(scenario)
            if transform_data:
                init_data['data'].append(transform_data)

        init_path = OP_CONFDATA_DIR + "/init_data.json"
        generate_init_file(init_path, init_data)
    except Exception:
        LOG.error("transform error: %s" % traceback.format_exc())
        sys.exit(1)


if __name__ == "__main__":
    args = sys.argv
    scenario = args[1]
    transform(scenario)
