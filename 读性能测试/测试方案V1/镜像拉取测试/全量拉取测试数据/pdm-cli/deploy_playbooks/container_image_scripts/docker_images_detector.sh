#!/bin/bash

LOG_DIR="/paasdata/op-log/docker"
LOG_FILENAME="$LOG_DIR/docker_scripts.log"
selfScriptName=$(basename "$0")

log_info() {
    LOG_DATE=$(date "+%Y-%m-%dT%H:%M:%S+08:00")

    if [ ! -d "$LOG_DIR" ]; then
        mkdir -p $LOG_DIR
        chmod 750 $LOG_DIR
    fi

    if [ ! -f "$LOG_FILENAME" ]; then
        touch $LOG_FILENAME
        chmod 640 $LOG_FILENAME
    fi

    mode=$(stat -c %a $LOG_FILENAME)
    if [ "$mode" != "640" ]; then
        chmod 640 $LOG_FILENAME
    fi

    echo "time=\"$LOG_DATE\" level=[$1] scriptFile=\"$selfScriptName\" msg=\"$2\"" |tee -a "$LOG_FILENAME"
}

# Wait for docker status to be ok
wait_docker_status_ok(){
    for a in $(seq 1 1 12)
    do
        docker_service_status=$(systemctl status docker.service|grep "active (running)")
        if [ -n "$docker_service_status" ]; then
            break
        fi
        sleep 5
        if [ "$a" -eq 12 ]; then
            log_info error "Docker daemon is not in active status"
            exit 1
        fi
    done
}

# Use docker-image-check-tool check image
image_check_tool_image(){
    docker_images_list=$(timeout 120 docker images -q)
    if [ -n "$docker_images_list" ]; then
        for i in $docker_images_list
        do
            output=$(timeout 60 docker-image-check-tool --logdir /paasdata/op-log/docker check image "$i" -q 2>&1)
            ret4=$?
            if [ "$ret4" -ne 0 ]; then
		(( check_images_timeout++ ))
                log_info error "Failed to execute the docker-image-check-tool: $output"
                output=""
            fi
            if [ "$output" != "" ]; then
                timeout 60 docker rmi -F "$i" >/dev/null 2>&1
                ret5=$?
                if [ "$ret5" -ne 0 ]; then
                    (( rm_images_failed++ ))
                    log_info error "docker-image-check-tool check image $i abnormal,but delete failed"
                else
                    (( rm_images_succ++ ))
                    log_info info "docker-image-check-tool check image $i abnormal,delete successfully"
                fi
            fi
        done

        if [ "$rm_images_failed" -ne 0 ]; then
            log_info error "Docker image abnormal and try to delete image,delete is failed"
            exit 1
        elif [ "$check_images_timeout" -ne 0 ]; then
            log_info error "docker-image-check-tool check image timeout,please check the IO"
            exit 1
        elif [ "$rm_images_succ" -ne 0 ]; then
            log_info info "Docker image abnormal and try to delete image,delete is ok"
            exit 0
        fi
    fi
}
# Check if all images are ok
check_docker_images(){
    docker-image-check-tool >/dev/null 2>&1
    ret=$?
    if [ "$ret" -ne 0 ]; then
        log_info error "docker-image-check-tool is not installed or abnormal"
        exit 1
    else
        image_check_tool_image
    fi
}

# main
rm_images_failed=0
rm_images_succ=0
check_images_timeout=0

log_info info "Begin to check container images......"
wait_docker_status_ok
check_docker_images

