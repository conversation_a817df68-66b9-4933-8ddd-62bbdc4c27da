#!/bin/bash

LOG_DIR="/paasdata/op-log/docker"
LOG_FILENAME="$LOG_DIR/docker_scripts.log"
selfScriptName=$(basename "$0")

log_info() {
    LOG_DATE=$(date "+%Y-%m-%dT%H:%M:%S+08:00")

    if [ ! -d "$LOG_DIR" ]; then
        mkdir -p $LOG_DIR
        chmod 750 $LOG_DIR
    fi

    if [ ! -f "$LOG_FILENAME" ]; then
        touch $LOG_FILENAME
        chmod 640 $LOG_FILENAME
    fi

    mode=$(stat -c %a $LOG_FILENAME)
    if [ "$mode" != "640" ]; then
        chmod 640 $LOG_FILENAME
    fi

    echo "time=\"$LOG_DATE\" level=[$1] scriptFile=\"$selfScriptName\" msg=\"$2\"" |tee -a "$LOG_FILENAME"
}

# Wait for docker status to be ok
wait_docker_status_ok(){
    for a in $(seq 1 1 12)
    do
        docker_service_status=$(systemctl status docker.service|grep "active (running)")
        if [ -n "$docker_service_status" ]; then
            break
        fi
        sleep 5
        if [ "$a" -eq 12 ]; then
            log_info error "Docker daemon is not in active status"
            exit 1
        fi
    done
}

# Use docker image prune to remove dangling images
docker_image_prune(){
    timeout 300 docker image prune -f >> "$LOG_FILENAME" 2>&1
    ret=$?
    if [ "$ret" -ne 0 ]; then
        log_info error "docker image prune -f exec failed"
        exit 1
    else
        log_info info "docker image prune -f exec successfully"
    fi
}

# main
log_info info "Begin to remove dangling images......"
wait_docker_status_ok
docker_image_prune

