#### note: modify nginx.conf must modify the playbooks/nginx/nginx.conf.j2
#### 2021/11/15 modified for not listen on all interfaces by 00120897

user  pict;
worker_processes  1;
daemon off;

#error_log  logs/error.log;
#error_log  logs/error.log  notice;
#error_log  logs/error.log  info;

#pid        logs/nginx_cosmos.pid;
worker_rlimit_nofile  65530;

events {
    worker_connections  5000;
}

{% set iplist = [] %}

http {
    include       mime.types;
    default_type  application/octet-stream;

    log_format  main  '$remote_addr - $remote_user [$time_local] "$request" '
                      '$status $body_bytes_sent "$http_referer" '
                      '"$http_user_agent" "$http_x_forwarded_for"';


    access_log  /var/log/cosmos.log  main;
    error_log  logs/error.log error;

    sendfile        on;
    #tcp_nopush     on;

    keepalive_timeout  65;
    client_body_timeout 60;
    send_timeout 60;
    server_tokens off;
    client_header_timeout 60;
    client_header_buffer_size  64k;
    client_max_body_size   200m;

    limit_conn_zone $binary_remote_addr zone=perip:1m;
    limit_conn perip 50;


  server {
        listen       127.0.0.1:2500 ssl;
        listen       [::1]:2500 ssl;
        listen       {{ apigateway_vip| ipwrap }}:2500 ssl;

        {% set _ = iplist.append(apigateway_vip) %}
{% if api_network_ip_v4 is defined %}
        {% if api_network_ip_v4 not in iplist %}
            {% set _ = iplist.append(api_network_ip_v4) %}
            listen       {{api_network_ip_v4}}:2500 ssl;
        {% endif %}
{% endif %}
{% if api_network_ip_v6 is defined %}
        {% if api_network_ip_v6 not in iplist %}
            {% set _ = iplist.append(api_network_ip_v6) %}
            listen       [{{api_network_ip_v6}}]:2500 ssl;
        {% endif %}
{% endif %}
{% if net_iapi_paas_vip_v4 is defined %}
        {% if net_iapi_paas_vip_v4 not in iplist %}
            {% set _ = iplist.append(net_iapi_paas_vip_v4) %}
            listen       {{net_iapi_paas_vip_v4}}:2500 ssl;
        {% endif %}
{% endif %}
{% if net_iapi_paas_vip_v6 is defined %}
        {% if net_iapi_paas_vip_v6 not in iplist %}
            {% set _ = iplist.append(net_iapi_paas_vip_v6) %}
            listen       [{{net_iapi_paas_vip_v6}}]:2500 ssl;
        {% endif %}
{% endif %}
{% if iaas_public_vip is defined %}
        {% if iaas_public_vip not in iplist %}
            {% set _ = iplist.append(iaas_public_vip) %}
            listen       {{ iaas_public_vip| ipwrap }}:2500 ssl;
        {% endif %}
{% endif %}
{% if iaas_tecsclient_vip is defined %}
        {% if iaas_tecsclient_vip not in iplist %}
            {% set _ = iplist.append(iaas_tecsclient_vip) %}
            listen       {{ iaas_tecsclient_vip| ipwrap }}:2500 ssl;
        {% endif %}
{% endif %}
{% if plat_external_ips is defined %}
        {% set plat_ips = plat_external_ips.split(',') %}
        {% for plat_ip in plat_ips %}
            {% if plat_ip not in iplist %}
                {% set _ = iplist.append(plat_ip) %}
                listen       {{ plat_ip| ipwrap }}:2500 ssl;
            {% endif %}
        {% endfor %}
{% endif %}

        add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
        add_header X-Frame-Options SAMEORIGIN;
        ssl_certificate      /opt/cosmos/cosmos/restframe/plugins/playbooks/cert.pem;
        ssl_certificate_key  /opt/cosmos/cosmos/restframe/plugins/playbooks/key.pem;
        ssl_session_cache    shared:SSL:1m;
        ssl_session_timeout  5m;
        ssl_protocols        TLSV1.2 TLSV1.3;

        ssl_ciphers  EECDH+AESGCM:EDH+AESGCM;
        ssl_prefer_server_ciphers  on;

        if ($request_method !~ ^(GET|HEAD|POST|PUT|DELETE)$ ) {
          return 444;
        }

    location / {
        proxy_pass https://127.0.0.1:2503;
        add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
        proxy_ssl_protocols TLSv1.1 TLSv1.2;
    }

    error_page 400 401 403 404 408 413 500 502 504 @jump_to_error;
    location @jump_to_error {
      default_type text/plain;
      return 404 'Sorry, the page you are looking for is currently unavailable. Please try again later.';
    }

  }
}
