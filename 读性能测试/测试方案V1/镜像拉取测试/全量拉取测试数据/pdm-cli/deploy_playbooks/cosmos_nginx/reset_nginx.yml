- hosts: "{{ host | default('nodes') }}"
  remote_user: ubuntu
  become: True
  become_method: sudo
  gather_facts: no
  any_errors_fatal: true
  tasks:
  - block:
    - name: generate nginx config
      template:
        owner: root
        group: root
        mode: 0600
        src: /opt/cosmos/cosmos/restframe/plugins/playbooks/nginx/nginx.conf.j2
        dest: /opt/cosmos/nginx/conf/nginx.conf
    - name: service nginx_cosmos restart
      shell: service nginx_cosmos restart