import subprocess
import sys
import os
import yaml
import traceback
import logging
import time


COM_VARS_FILE = "/root/common/com_vars.yml"
NAMESPACE_LIST = ["default", "kube-system", "kube-node-lease",
                  "kube-public", "admin", "opcs", ""]


log_dir = "/paasdata/op-log/pdm-cli/delete_cluster/"
if not os.path.exists(log_dir):
    os.makedirs(log_dir, 0o750)
log_file = "%s/check_before_delete.log" % log_dir
if not os.path.exists(log_file):
    os.mknod(log_file)
    os.chmod(log_file, 0o640)
logging.basicConfig(filename=log_file,
                    format="%(asctime)s  -%(name)s - %(levelname)s - %(message)s",
                    level=logging.DEBUG)
LOG = logging.getLogger(__name__)


def subprocess_execute(cmd):
    start_time = time.time()
    result = subprocess.Popen(cmd,
                              shell=True,
                              universal_newlines=True,
                              stdout=subprocess.PIPE,
                              stderr=subprocess.STDOUT)
    stdout = str(result.communicate()[0])
    end_time = time.time()
    duration = end_time - start_time
    LOG.debug("exec cmd: %s, result: %s, %s; Spend %s seconds." %
              (cmd, result.returncode, stdout, duration))
    if result.returncode != 0:
        return False, stdout
    return True, stdout


def get_cmds(config_file):
    base_dir = "/etc/pdm/deploy_playbooks/delete_cluster/"
    register_file_path = base_dir + config_file
    cmds = []
    with open(register_file_path, "r") as f:
        data_info = yaml.safe_load(f)
        if not data_info:
            return cmds
        for data in data_info:
            cmd_info = {}
            script_path = base_dir + data["path"]
            cmd = ("if command -v pythonlatest &>/dev/null; "
                   "then python_cmd=pythonlatest; "
                   "else python_cmd=python; fi; "
                   "$python_cmd {0}").format(script_path)
            cmd_info['cmd'] = cmd
            cmd_info["desc"] = data["desc"]
            cmds.append(cmd_info)
    return cmds


def execute_scripts(paras):
    configure_file = "check_before_delete.yml"
    cmds = get_cmds(configure_file)
    LOG.debug("all cmds: %s." % cmds)
    result_list = []
    for cmd in cmds:
        ex_cmd = cmd.get('cmd', '') + " " + paras
        result, output = subprocess_execute(ex_cmd)
        if not result:
            result_list.append(output)
    return result_list


def get_from_comvars(varname, filepath=COM_VARS_FILE):
    if not os.path.exists(filepath):
        err_info = "%s not found!" % filepath
        LOG.error(err_info)
        raise Exception(err_info)
    try:
        with open(filepath, 'r') as f:
            data_map = yaml.safe_load(f)
    except Exception as e:
        err_info = "Load from %s get exception: %s" % (filepath, e)
        LOG.error(err_info)
        raise Exception(err_info)
    for data in data_map:
        if varname in data:
            return data[varname]
    err_info = "%s not found in %s" % (varname, filepath)
    LOG.warning(err_info)
    raise IndexError(err_info)


def get_tenants_from_kubectl():
    cmd = "kubectl get namespaces -o=jsonpath='{.items[*].metadata.name}'"
    result, output = subprocess_execute(cmd)
    tenants = []
    if result:
        namespaces = output.strip().split(" ")
        for name in namespaces:
            if name not in NAMESPACE_LIST:
                tenants.append(name)
    if tenants:
        return ",".join(tenants)
    else:
        return '""'


def main(paras):
    try:
        result_list = execute_scripts(paras)
        if result_list:
            LOG.error(
                "Deleting a cluster is not allowed, because there are resources: %s"
                % result_list)
            LOG.error("Check before delete cluster failed!")
            return 1, "\n".join(result_list)
        LOG.info("Check before delete cluster success! Now can delete the cluster.")
        return 0, "success"
    except Exception:
        LOG.error("Check before delete cluster failed! err is: \n %s" %
                  traceback.format_exc())
        return 1, traceback.format_exc()


if __name__ == "__main__":
    LOG.info("Begin check before delete cluster.")
    cluster_used_type = get_from_comvars("cluster_used_type")
    cluster_used_model = get_from_comvars("cluster_used_model")
    if not cluster_used_model:
        cluster_used_model = "default"
    tenants = get_tenants_from_kubectl()
    paras = "--cluster_used_type {} --cluster_used_model {} --tenants {}".format(
        cluster_used_type, cluster_used_model, tenants)
    LOG.info("Get cluster paras success, paras: %s" % paras)
    code, message = main(paras)
    sys.stdout.write(message)
    sys.stdout.flush()
    sys.exit(code)
