import subprocess
import sys
import traceback

from config import format_cmd_list

def subprocess_execute(cmd):

    result = subprocess.Popen(cmd,
                            shell=True,
                            stdout=subprocess.PIPE,
                            stderr=subprocess.STDOUT)
    stdout = str(result.communicate()[0])
    if result.returncode != 0:
        return False, stdout

    return True, stdout

def get_cmds(config_file):
    return format_cmd_list(config_file)

def execute_scripts():
    configure_file = "clean_up_resources.yml"
    cmds = get_cmds(configure_file)
    for cmd in cmds:
        result, output = subprocess_execute(cmd.get('cmd', ''))
        if not result:
            return result, output
    return True, ""

def main():
    try:
        result, ouput = execute_scripts()
        if not result:
            sys.stderr.write(ouput)
            sys.stderr.flush()
            sys.exit(1)
        sys.stdout.write('success')
        sys.stdout.flush()
        sys.exit(0)

    except Exception:
        sys.stderr.write(traceback.format_exc())
        sys.stderr.flush()
        sys.exit(1)

if __name__ == "__main__":
    main()
