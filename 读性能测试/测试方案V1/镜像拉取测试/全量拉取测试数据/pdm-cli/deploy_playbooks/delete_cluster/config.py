import yaml


def format_cmd_list(configure_file):
    base_dir = "/etc/pdm/deploy_playbooks/delete_cluster/"
    register_file_path = base_dir + configure_file
    cmds = []
    with open(register_file_path, "r") as f:
        data_info = yaml.safe_load(f)
        if not data_info :
            return cmds
        for data in data_info:
            cmd_info = {}
            script_path = base_dir + data["path"]
            cmd = ("if command -v pythonlatest &>/dev/null; "
                   "then python_cmd=pythonlatest; "
                   "else python_cmd=python; fi; "
                   "$python_cmd {0}").format(script_path)
            cmd_info['cmd'] = cmd
            cmds.append(cmd_info)
    return cmds
