# -*- coding: UTF-8 -*-
import httplib2
import json
import os
import yaml
from six.moves import http_client

from common import log

LOG_FILE = "/paasdata/op-log/pdm-cli/" \
           "delete_cluster/helm_check_before_delete.log"
LOG = log.get_logger(__name__, LOG_FILE)

COM_VARS_FILE = "/root/common/com_vars.yml"
PORT_VARS_FILE = "/root/common/port_vars.yml"
SUCCESS = [http_client.OK, http_client.CREATED,
           http_client.ACCEPTED, http_client.NO_CONTENT]


def get_from_file(filepath, varname):
    if os.path.exists(filepath):
        f = open(filepath)
        dataMap = yaml.safe_load(f)
        f.close()
        for data in dataMap:
            if varname in data:
                return data[varname]
        LOG.debug("get_from_file(%s) no find  var(%s) !" %
                  (filepath, varname))
        return None
    else:
        LOG.debug("get_from_file no file(%s) !" % filepath)
        return None

def get_from_comvars(varname, filepath=None):

    portvar = "/root/common/port_vars.yml"
    comvar = "/root/common/com_vars.yml"
    if '_port' in varname:
        res = get_from_file(portvar, varname)
        if not res:
            res = get_from_file(comvar, varname)
    else:
        res = get_from_file(comvar, varname)
    return res

def json_loads(str_need_load):
    try:
        return json.loads(str_need_load)
    except Exception:
        return str_need_load


def set_user_agent_to_headers(headers):
    if headers is None:
        headers = {}
    headers['User-Agent'] = 'oneclick_scaleout-pdmcli'
    return headers


def _request(url, method, header=None, data=None, timeout=None):
    try:
        if timeout is None:
            time_out = 60
        else:
            time_out = timeout
        http = httplib2.Http(timeout=time_out,
                             disable_ssl_certificate_validation=True)
        header = set_user_agent_to_headers(header)
        LOG.info("http request: url=%s\ndata=%s" % (url, data))
        if data is None:
            response, content = http.request(url,
                                             method,
                                             headers=header)
        else:
            response, content = http.request(url,
                                             method,
                                             body=json.dumps(data),
                                             headers=header)
        LOG.info("http response=%s,content=%s" % (response, content))
        if not response:
            return False, None
        if content:
            content = json_loads(content.decode())
        result = True if response.status in SUCCESS else False
        return result, content
    except Exception as ex:
        LOG.error("Http request except=%s" % ex)
        return False, str(ex)


def http_request(url, method, header={'Content-Type': 'application/json'},
                 data=None, timeout=None,
                 retry_count=5):

    for _ in range(retry_count):
        result, content = _request(url, method, header, data, timeout)
        if result:
            break
    return result, content
