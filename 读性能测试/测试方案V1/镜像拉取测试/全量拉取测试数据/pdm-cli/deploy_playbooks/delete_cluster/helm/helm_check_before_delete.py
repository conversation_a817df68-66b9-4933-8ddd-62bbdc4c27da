import sys
import traceback
from common.utils import get_from_comvars, http_request


class Helm(object):
    @classmethod
    def get_url(cls):
        ip = get_from_comvars('openpalette_service_ip')
        port = get_from_comvars('openpalette_service_port')
        url = "http://%s:%s/helm/v3/releases" % (ip, port)
        return url

    @staticmethod
    def check():
        result, content = http_request(
            Helm.get_url(), "GET", retry_count=1, timeout=10)
        if not result:
            return False, str(content)
        if content.get("releases", []):
            msg = Helm.construct_output(content.get("releases", []))
            return False, msg

        return True, ""

    @classmethod
    def construct_output(cls, releases):
        release_map = {}
        for release in releases:
            ns = release.get("namespace")
            if not release_map.get(ns):
                release_map[ns] = release.get("name")
            else:
                if release_map[ns].endswith("... "):
                    continue
                if len(release_map[ns].split(",")) == 10:
                    release_map[ns] = release_map[ns] + ", ... "
                    continue
                release_map[ns] = release_map[ns] + ", " + release.get("name")
        msg = "helm resources exist:"
        re = ""
        for ns, rel in release_map.items():
            re = re + msg + " tenant %s: %s;\n" % (ns, rel)
        return re


if __name__ == "__main__":
    try:
        result, ouput = Helm.check()
        if not result:
            sys.stderr.write(ouput)
            sys.stderr.flush()
            sys.exit(1)
        sys.stdout.write('success')
        sys.stdout.flush()
        sys.exit(0)

    except Exception:
        sys.stderr.write(traceback.format_exc())
        sys.stderr.flush()
        sys.exit(1)