#!/usr/bin/python
# -*- coding: utf-8 -*-
# @Time    : 23-09-25
# <AUTHOR> k8s
# @Site    :
# @File    : k8s_check_before_delete.py
# description:
# ----------------------------------------------------------------------
import argparse
import sys
import socket
import signal
import requests
import os
import yaml


# ExitCode:
# 0  指定租户下无指定列表类型的资源
# 1  指定租户下存在指定列表类型的资源
# 2  脚本执行超时退出
# 3  用户中断程序执行
# 4  集群不正常


def timeout_handler(sig, frame):
    raise socket.timeout("Script execution timed out")


def get_resource_count(api_url, resource_type, tenant):
    try:
        endpoint = api_url +\
                   "/namespaces/" + tenant +\
                   "/" + resource_type+"?limit=11"
        # 发起 API 请求
        response = requests.get(
            endpoint,
            cert=("/etc/kubernetes/certs/kubecfg.crt",
                  "/etc/kubernetes/certs/kubecfg.key"),
            verify="/etc/kubernetes/certs/ca.crt",
        )

        # 解析 API 响应
        if response.status_code == 200:
            data = response.json()
            resource_count = len(data["items"])
            return resource_count, data
        else:
            print(endpoint)
            print("get {0} list failed，HTTP err-code：{1}".format(
                resource_type,
                response.status_code))
            exit(4)
    except Exception as e:
        print("get API failed:", e)
        exit(4)


def get_resource_count_continue(api_url, resource_type, tenant, token):
    try:
        endpoint = api_url + "/namespaces/" +\
                   tenant + "/" + resource_type +\
                   "?continue=" +\
                   token+"&limit=11"
        # print(endpoint)
        # 发起 API 请求
        response = requests.get(
            endpoint,
            cert=("/etc/kubernetes/certs/kubecfg.crt",
                  "/etc/kubernetes/certs/kubecfg.key"),
            verify="/etc/kubernetes/certs/ca.crt",
        )

        # 解析 API 响应
        if response.status_code == 200:
            data = response.json()
            resource_count = len(data["items"])
            return resource_count, data
        else:
            print(endpoint)
            print("get {0} list failed，HTTP err-code：{1}".format(
                resource_type,
                response.status_code))
            exit(4)
    except Exception as e:
        print("get API failed:", e)
        exit(4)


def calculate_v1(resource_types, api_url, tenant):
    resource_exist = False
    for resource_type in resource_types:
        count, data = get_resource_count(api_url, resource_type, tenant)
        if count and count > 0:
            resource_identifiers = []  # 创建一个列表来保存符合条件的资源标识符
            flag = 0
            resultRetur = data["items"]
            for item in resultRetur:
                flag = flag + 1
                labels = item.get("metadata", {}).get("labels", {})
                resource_name = item.get("metadata", {}).get("name")
                if "tcf.io/ignore-resource-check" in labels and\
                        labels["tcf.io/ignore-resource-check"] == "true":
                    continue
                if resource_type == "secrets" and \
                        item["type"] == "kubernetes.io/service-" \
                                        "account-token":
                    continue
                if resource_type == "configmaps" and \
                        resource_name == "kube-root-ca.crt":
                    continue
                identifier = item.get("metadata", {}).get("name")
                if identifier:
                    if len(resource_identifiers) == 10:
                        resource_identifiers.append("... ")
                        break
                    else:
                        resource_identifiers.append(identifier)
                        token = data.get("metadata", {}).get("continue")
                        if flag == len(resultRetur) and token:
                            countContinue, dataContinue = \
                                get_resource_count_continue(
                                    api_url,
                                    resource_type,
                                    tenant, token)
                            resultRetur.extend(dataContinue["items"])
                            data = dataContinue
            if resource_identifiers:
                resource_exist = True
                identifiers_str = ", ".join(resource_identifiers)
                sys.stdout.write("{0} resources exist: "
                                 "tenant {1}: {2};\n"
                                 "".format(resource_type,
                                           tenant,
                                           identifiers_str))
    if not resource_exist:
        return 0
    else:
        return 1


def get_k8s_url(kubeconfig):
    if os.path.exists(kubeconfig):
        with open(kubeconfig, 'r') as f:
            dataMap = yaml.safe_load(f)
        clusters = dataMap['clusters']
        for cluster in clusters:
            url = cluster['cluster']['server']
            return url
    else:
        print("[FAILED] get clusters info error: %s not exist" % kubeconfig)
        exit(4)


def main():

    parser = argparse.ArgumentParser(
        description="Check resources "
                    "in a Kubernetes cluster")
    parser.add_argument("--cluster_used_type",
                        help="Cluster type",
                        required=False)
    parser.add_argument("--cluster_used_model",
                        help="Cluster shared mode",
                        required=False)
    parser.add_argument("--tenants",
                        help="List of tenants",
                        nargs='?')

    args = parser.parse_args()
    if args.tenants is None:
        print("No tenants specified")
        sys.exit(0)
    else:
        tenants = args.tenants.split(",")
        if len(tenants) == 1 and tenants[0] == "":
            print("No tenants specified")
            sys.exit(0)

    resource_types_v1 = [
        "configmaps",
        "secrets",
        "pods",
        "podtemplates",
        "replicationcontrollers",
        "services",
    ]
    resource_types_v1_apps = [
        "replicasets",
        "deployments",
        "statefulsets",
    ]
    resource_types_v1_networking = [
        "ingresses",
        "networkpolicies",
    ]
    resource_types_v1_autoscaling = [
        "horizontalpodautoscalers",
    ]
    resource_types_v1_batch = [
         "jobs",
         "cronjobs",
    ]

    kubeconfigpath = '/etc/kubernetes/kubectl.kubeconfig'
    k8s_url_base = get_k8s_url(kubeconfigpath)
    api_url_v1 = k8s_url_base + "/api/v1"
    api_url_v1_apps = k8s_url_base + "/apis/apps/v1"
    api_url_v1_networking = k8s_url_base + "/apis/networking.k8s.io/v1"
    api_url_v1_autoscaling = k8s_url_base + "/apis/autoscaling/v1"
    api_url_v1_batch = k8s_url_base + "/apis/batch/v1"

    results = []
    for tenant in tenants:
        if tenant == "":
            continue
        # print("Checking resources for tenant: {0}".format(tenant))
        results.append(calculate_v1(resource_types_v1, api_url_v1, tenant))
        results.append(calculate_v1(
            resource_types_v1_apps,
            api_url_v1_apps,
            tenant))
        results.append(calculate_v1(
            resource_types_v1_networking,
            api_url_v1_networking,
            tenant))
        results.append(calculate_v1(
            resource_types_v1_autoscaling,
            api_url_v1_autoscaling,
            tenant))
        results.append(calculate_v1(
            resource_types_v1_batch,
            api_url_v1_batch,
            tenant))
    # Set the exit code based on results
    if any(result == 1 for result in results):
        sys.exit(1)
    else:
        print("No resources found")
        sys.exit(0)


if __name__ == "__main__":
    try:
        # 设置超时时间（以秒为单位）
        timeout_seconds = 120

        # 创建一个线程，在超时后触发timeout_function
        signal.signal(signal.SIGALRM, timeout_handler)
        signal.alarm(timeout_seconds)
        main()
    except KeyboardInterrupt:
        sys.exit(3)
    except socket.timeout:
        print("Script execution timed out "
              "after {} seconds".format(timeout_seconds))
        exit(2)