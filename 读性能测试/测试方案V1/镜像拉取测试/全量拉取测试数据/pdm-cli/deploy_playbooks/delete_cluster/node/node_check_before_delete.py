import os
import sys
import requests
import yaml
if sys.version_info.major == 3:
    import urllib3
    urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)


COM_VARS_FILE = "/root/common/com_vars.yml"
PORT_VARS_FILE = "/root/common/port_vars.yml"


def request_cluster(url, params, verify=False):
    try:
        response = requests.get(url, params=params, verify=verify)
        if response.status_code >= 200 and response.status_code < 300:
            return response.json()
        else:
            return False
    except Exception:
        return False


def get_from_comvars(varname, filepath=COM_VARS_FILE):
    if not os.path.exists(filepath):
        err_info = "%s not found!" % filepath
        raise Exception(err_info)
    try:
        with open(filepath, 'r') as f:
            data_map = yaml.safe_load(f)
    except Exception as e:
        err_info = "Load from %s get exception: %s" % (filepath, e)
        raise Exception(err_info)
    for data in data_map:
        if varname in data:
            return data[varname]
    return None


def get_openpalette_service_ip_port():
    op_service_ip = get_from_comvars('openpalette_service_ip')
    op_service_port = \
        get_from_comvars('openpalette_service_port', PORT_VARS_FILE)
    return op_service_ip, op_service_port


def get_nodes_from_cluster(nodes):
    node_list = []
    for node in nodes:
        node_list.append(node.get("name_in_k8s", ""))
        if len(node_list) == 10:
            node_list.append("... ")
            break
    return node_list


def query_private_node_not_in_kubeall():
    ip, port = get_openpalette_service_ip_port()
    if ":" in ip:
        ip = "[%s]" % ip
    url = "http://%s:%s/clusterworker/v1.1/tenants/admin/nodes" % (ip, port)
    params = {
        "node_type": "private"
    }
    result = request_cluster(url, params)
    if result:
        return True, get_nodes_from_cluster(result.get("nodes", []))
    else:
        return False, "node check failed!"


def query_private_node_in_kubeall(tenants):
    port = get_from_comvars('carlina_https_port', PORT_VARS_FILE)
    url = ("https://assembler-apiserver.admin.svc:%s/clusterworker/v1.1/tenants/admin/"
           "nodes" % (port))
    params = None
    result = request_cluster(url, params)
    if result:
        private_nodes = [n for n in result.get("nodes", []) if n.get(
            "node_labels", {}).get("namespace") in tenants]
        return True, get_nodes_from_cluster(private_nodes)
    else:
        return False, "node check failed!"


def query_private_node(tenants):
    is_kubeall = get_from_comvars("is_kubeall")
    if is_kubeall:
        return query_private_node_in_kubeall(tenants)
    else:
        return query_private_node_not_in_kubeall()


def main(cluster_used_type, cluster_used_model, tenants):
    if cluster_used_type == "public" and cluster_used_model in ["model1", "model2"]:
        result, node_list = query_private_node(tenants)
        if not result:
            return 1, node_list
        if node_list:
            resources = "node resources exist: %s;" % ", ".join(node_list)
            return 1, resources
        return 0, 'success'
    else:
        message = 'cluster_used_type: %s, cluster_used_model: %s, no need check!' % (
            cluster_used_type, cluster_used_model)
        return 0, message


if __name__ == "__main__":
    args = sys.argv[1:]
    params = {}
    while args:
        if args[0] == "--cluster_used_type":
            params["cluster_used_type"] = args[1]
            args = args[2:]
        elif args[0] == "--cluster_used_model":
            params["cluster_used_model"] = args[1]
            args = args[2:]
        elif args[0] == "--tenants":
            params["tenants"] = args[1].split(",")
            args = args[2:]
        else:
            print("Invalid argument:", args[0])
            sys.exit(1)
    cluster_used_type = params.get("cluster_used_type")
    cluster_used_model = params.get("cluster_used_model")
    tenants = params.get("tenants", [])
    code, message = main(cluster_used_type, cluster_used_model, tenants)
    sys.stdout.write(message)
    sys.stdout.flush()
    sys.exit(code)
