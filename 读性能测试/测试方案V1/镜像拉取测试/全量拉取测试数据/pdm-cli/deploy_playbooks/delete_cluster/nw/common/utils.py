import sys
import time
import json
import six
from six.moves import configparser as Config<PERSON>arser
if six.PY2:
    import commands as subprocess
else:
    import subprocess

ABNORMALEXITCODE = 1
NORMALEXITCODE = 0

def checkStatus(operationName, status, output=""):
    if status == NORMALEXITCODE:
        return status, output
    exitCode = ABNORMALEXIT<PERSON>DE

    if "healthCheck" in operationName and "[FAILED]" not in output:
        output = output.replace('\n', ' => ').replace('\r', ' => ')

    # outputStr = "[FAILED] {operationName}, exec result: {output}".format(operationName=operationName, output=output)
    print(output)
    sys.exit(exitCode)

def execCmd(cmd, intervalTime, tryNum, operationName):
    status, output = ABNORMALEXITCODE, ""
    for _ in range(tryNum):
        status, output = subprocess.getstatusoutput(cmd)
        if status != NORMALEXITCODE:
            time.sleep(intervalTime)
            continue
        return status, output
    return checkStatus(operationName=operationName,
                       status=status, output=output)

def get_componet_host(componet_name):
    operationName = "get {componet} host".format(componet=componet_name)
    intervalTime = 3
    tryNum = 5
    get_componet_info_cmd = "pdm-cli controllergroupinfo get {componet}".format(componet=componet_name)
    _, componet_info = execCmd(get_componet_info_cmd, intervalTime, tryNum, operationName)
    try:
        # componet_info_format: {"controllergroupinfo": [{"ips": ["111:111:1::15", "111:111:1::13"]}]}
        controllergroupinfo = json.loads(componet_info).get("controllergroupinfo", [])
        if not controllergroupinfo:
            print("[PASS] {componet} does not exist, need not to check".format(componet=componet_name))
            sys.exit(NORMALEXITCODE)
        componet_host = controllergroupinfo[0].get("ips", [])[0]
        return componet_host
    except Exception as jsondecode_err:
        outputStr = "[FAILED] componet info: {componet_info}, get {componet} host failed, err: {output}"\
                .format(componet_info=componet_info, componet=componet_name, output=jsondecode_err)
        print(outputStr)
        sys.exit(ABNORMALEXITCODE)

def ssh_to_componet_host_exec_cmd(componet_name, cmd_name):
    user = "ubuntu"
    componet_host = get_componet_host(componet_name)
    ssh_cmd = "ssh -q -n \
            -o ConnectTimeout=3 -o StrictHostKeyChecking=no \
            -o UserKnownHostsFile=/dev/null \
            -o PasswordAuthentication=no \
            {}@{}".format(user, componet_host)
    sys.argv.append("--is_ssh=true")
    to_componet_exe_cmd = " ".join(sys.argv)
    ssh_to_componet_exe_cmd = "{} sudo python {}".format(ssh_cmd, to_componet_exe_cmd)
    operationName = "ssh to {componet} host exec {cmd}".format(componet=componet_name, cmd=cmd_name)
    intervalTime = 0
    tryNum = 1
    _, output = execCmd(ssh_to_componet_exe_cmd, intervalTime, tryNum, operationName)
    print(output)

def is_new_fusionized_tcf(paas_conf="/etc/pdm/conf/paas.conf"):
    config = ConfigParser.ConfigParser()
    try:
        file_obj = config.read(paas_conf)
    except Exception as e:
        print("read paas.conf failed: " + str(e))
        return False

    if file_obj:
        try:
            tcf_scenario = config.get("tcf", "tcf_scenario")
            is_new_fus_tcf =  config.get("tcf", "is_new_fusionized_TCF")
            if is_new_fus_tcf == "true" and tcf_scenario == "fusionized-TCF":
                return True
        except Exception as e:
            print(e)
            return False
    else:
        print("paas.conf not exist")
        return False
