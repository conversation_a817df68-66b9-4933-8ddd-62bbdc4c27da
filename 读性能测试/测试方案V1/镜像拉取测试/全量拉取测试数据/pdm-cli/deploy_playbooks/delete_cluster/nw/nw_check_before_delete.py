import json
import os
import sys
import logging
import six
if six.PY2:
    import commands as subprocess
else:
    import subprocess

pdm_common_path = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
sys.path.append(pdm_common_path)
from common.utils import ssh_to_componet_host_exec_cmd
from common.utils import is_new_fusionized_tcf

def SetLogging():
    filePath = "/paasdata/op-log/pdm-cli/delete_cluster"
    fileName = "nw_check_before_delete.log"
    logfile = """%s/%s""" % (filePath, fileName)
    logFileMode = 0o640
    filePathMode = 0o750

    if os.path.exists(filePath) is False:
        os.makedirs(filePath, filePathMode)

    if os.path.exists(logfile):
        os.chmod(logfile, logFileMode)
    else:
        os.mknod(logfile, logFileMode)

    logging.basicConfig(
        level=logging.DEBUG,
        filename=logfile,
        filemode="a",
        format="%(asctime)s-%(filename)s[line:%(lineno)d]-%(process)d"
               "-%(levelname)s:%(message)s"
    )

def Is_whitelist_nsnetwork(nwList):
    whitelist_namespaces = {"admin", "default", "kube-node-lease", "kube-public", "kube-system", "opcs"}
    filtered_nwList = [item for item in nwList if item["metadata"]["namespace"] not in whitelist_namespaces]

    return filtered_nwList

def QueryNsNetwork():
    timeOutCode = 124
    message = None
    returnCode = 0
    queryNwCmd = """kubectl get nsnetwork -A -o json"""
    status, output = subprocess.getstatusoutput(queryNwCmd)
    if status == timeOutCode:
        message = "nw component exec kubectl cmd timeout"
        returnCode = 2
        logging.error("returnCode: %d; message: %s, outputErr: %s",
                      returnCode, message, output)
        return returnCode, message
    if status != 0:
        message = "nw component exec kubectl cmd error"
        returnCode = 3
        logging.error("returnCode: %d; message: %s, outputErr: %s",
                      returnCode, message, output)
        return returnCode, message
    try:
        nwJson = json.loads(output)
        nwList = nwJson["items"]

        nwList = Is_whitelist_nsnetwork(nwList)

        if not nwList:
            message = "nw component query, no nws in cluster"
            returnCode = 0
            logging.info("returnCode: %d; message: %s",
                         returnCode, message)
            return returnCode, message

        existNw = {}

        for nw in nwList:
            namespace = nw["metadata"]["namespace"]
            name = nw["metadata"]["name"]
            if namespace == "default" and name == "default":
                continue
            if namespace not in existNw:
                existNw[namespace] = []
            existNw[namespace].append(name)

        message = "NsNetwork resources exist: "
        for namespace, names in existNw.items():
            message += f"tenant {namespace}: "
            if len(names) <= 10:
                message += ", ".join(names) + ";"
            else:
                message += ", ".join(names[:10]) + ", ... ;"

        returnCode = 1
        logging.error("returnCode: %d; message: %s", returnCode, message)
        return returnCode, message
    except Exception as e:
        message = "nw component exec kubectl cmd error"
        returnCode = 3
        logging.error("returnCode: %d; message: %s, output: %s",
                      returnCode, message, output)
        logging.error(e)
        return returnCode, message

if __name__ == "__main__":
    if is_new_fusionized_tcf() and sys.argv[-1] != "--is_ssh=true":
        ssh_to_componet_host_exec_cmd(componet_name="k8s", cmd_name="nw_check_before_delete")
    else:
        SetLogging()
        code, message = QueryNsNetwork()
        output = """%s""" % message
        sys.stdout.write(output)
        sys.stdout.flush()
        sys.exit(code)
