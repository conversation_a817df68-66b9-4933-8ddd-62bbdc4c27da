# Started by AICoder, pid:fc591pa389u7a731403c095d106d3866a141dd57
import subprocess
import sys
import time

def execute_helm_uninstall(retry_limit=3, retry_interval=5):
    """
    执行 helm 卸载命令，带有异常处理和重试机制
    :param retry_limit: 最大重试次数
    :param retry_interval: 每次重试的等待时间（秒）
    :return: 成功返回 True，发生“not found” 时返回 True，其他错误返回 False
    """
    cmd = "helm uninstall -n admin op-containers-kubeall-operator"

    for attempt in range(1, retry_limit + 1):
        try:
            print(f"Attempt {attempt}: Executing command: {cmd}")
            # 执行命令并捕获标准输出和错误输出
            process = subprocess.Popen(cmd, shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
            stdout, stderr = process.communicate()

            stdout_str = stdout.decode('utf-8')
            stderr_str = stderr.decode('utf-8')

            # 检查命令执行结果
            if process.returncode == 0:
                print("Command executed successfully. Output:")
                print(stdout_str)
                return True

            # 如果 stderr 中包含 "not found"，表示组件不存在，打印信息并正常退出
            if "not found" in stderr_str.lower():
                print(f"Component not found. Full error: {stderr_str}")
                return False  # 正常退出，不影响后续流程

            # 其他错误情况，抛出异常以便进入重试逻辑
            raise subprocess.CalledProcessError(process.returncode, cmd, output=stdout_str, stderr=stderr_str)

        except subprocess.CalledProcessError as e:
            # 处理执行失败的情况
            print(f"Error during helm uninstall: {e.stderr}")
            # 检查是否达到了重试次数限制
            if attempt == retry_limit:
                print(f"Failed to uninstall kubeall-operator after {retry_limit} attempts.")
                return False  # 最终重试失败，返回 False
            else:
                print(f"Retrying in {retry_interval} seconds...")
                time.sleep(retry_interval)

    return False  # 如果所有重试均失败，返回 False

# 示例使用
if __name__ == '__main__':
    success = execute_helm_uninstall()
    if success:
        print("Helm uninstall script completed successfully.")
    else:
        print("Helm uninstall script failed after retries, but continuing the process.")

    # 确保主脚本退出码为 0，不影响后续流程
    sys.exit(0)

# Ended by AICoder, pid:fc591pa389u7a731403c095d106d3866a141dd57
