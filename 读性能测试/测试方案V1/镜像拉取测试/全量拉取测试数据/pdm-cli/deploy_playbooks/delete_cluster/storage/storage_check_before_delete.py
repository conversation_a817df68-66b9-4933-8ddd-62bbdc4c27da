#!/usr/bin/python
# -*- coding: utf-8 -*-
import os
import sys
import socket
import signal
import argparse
import requests
import yaml


# ExitCode:
# 0  无资源残留
# 1  有资源残留
# 2  脚本执行超时退出
# 3  用户中断程序执行
# 4  集群不正常
# 5  未知错误

IS_DEFAULT_SC = "storageclass.kubernetes.io/is-default-class"
CREATED_BY = "createdBy"
KUBE_CONFIG_PATH = "/etc/kubernetes/kubectl.kubeconfig"


def timeout_handler(sig, frame):
    raise socket.timeout("Script execution timeout")


def get_k8s_url(kubeconfig):
    if os.path.exists(kubeconfig):
        with open(kubeconfig, 'r') as f:
            dataMap = yaml.safe_load(f)
        clusters = dataMap['clusters']
        for cluster in clusters:
            url = cluster['cluster']['server']
            return url
    else:
        print("Not found config: {}".format(kubeconfig))
        exit(4)


def get_resource(api_url, resource_type, tenant=""):
    try:
        endpoint = api_url + "/" + resource_type + "?limit=11"
        if tenant:
            endpoint = api_url + "/namespaces/" + tenant + "/" + \
                resource_type + "?limit=11"
        response = requests.get(
            endpoint,
            cert=("/etc/kubernetes/certs/kubecfg.crt",
                  "/etc/kubernetes/certs/kubecfg.key"),
            verify="/etc/kubernetes/certs/ca.crt",
        )
        if response.status_code == 200:
            return response.json()["items"]
        if response.status_code == 404:
            print("Get {0} list: {1}".format(resource_type, response.text))
            return []
        else:
            print(endpoint)
            print("Get {0} list failed, HTTP err-code：{1}".format(
                resource_type,
                response.status_code))
            exit(4)
    except Exception as e:
        print(e)
        exit(4)


def _print(resource_type, identifiers, tenant):
    identifiers_str = ", ".join(identifiers)
    tenant_str = "tenant {}: ".format(tenant) if tenant else ""
    output_line = "{0} resources exist: {1}{2} ;\n".format(
        resource_type, tenant_str, identifiers_str)
    sys.stdout.write(output_line)


def do_check(resource_type, api_url, tenant=""):
    identifiers = []
    items = get_resource(api_url, resource_type, tenant=tenant)
    for item in items:
        if resource_type == "storageclasses":
            provisioner = item.get("provisioner", "")
            annotations = item.get("metadata", {}).get("annotations", {})
            if provisioner not in ["cinder.csi.openstack.org", "opdisk.csi.openpalette.org"]:
                continue
            if annotations.get(CREATED_BY, "") != "storageManager":
                continue
            if annotations.get(IS_DEFAULT_SC, "").lower() == "true":
                continue
        if resource_type == "persistentvolumeclaims":
            identifier = item.get("metadata", {}).get("name")
            if identifier == "swr-pvc":
                continue
        if resource_type == "persistentvolumes":
            identifier = item.get("spec", {}).get("claimRef", {}).get("name")
            if identifier == "swr-pvc":
                continue
        identifier = item.get("metadata", {}).get("name")
        if identifier:
            if len(identifiers) < 10:
                identifiers.append(identifier)
            else:
                identifiers.append("...")
                break
    if identifiers:
        _print(resource_type, identifiers, tenant)
    return len(identifiers) > 0


def check_storage_resource(tenants):
    k8s_url_base = get_k8s_url(KUBE_CONFIG_PATH)
    api_url_v1 = k8s_url_base + "/api/v1"
    api_url_v1_storage = k8s_url_base + "/apis/storage.k8s.io/v1"
    api_url_v1_snapshot = k8s_url_base + "/apis/snapshot.storage.k8s.io/v1"
    resource_list = [
        {
            "resourceType": "persistentvolumes",
            "apiUrl": api_url_v1,
            "scope": "cluster",
        },
        {
            "resourceType": "persistentvolumeclaims",
            "apiUrl": api_url_v1,
            "scope": "namespace",
        },
        {
            "resourceType": "storageclasses",
            "apiUrl": api_url_v1_storage,
            "scope": "cluster",
        },
        {
            "resourceType": "volumesnapshots",
            "apiUrl": api_url_v1_snapshot,
            "scope": "namespace",
        },
    ]

    results = []
    for resource in resource_list:
        if resource["scope"] == "namespace":
            for tenant in tenants:
                if tenant:
                    results.append(do_check(resource["resourceType"],
                                            resource["apiUrl"],
                                            tenant=tenant))
        else:
            results.append(do_check(resource["resourceType"],
                                    resource["apiUrl"]))

    if any(result is True for result in results):
        sys.exit(1)
    else:
        print("No storage resources left")
        sys.exit(0)


if __name__ == "__main__":
    parser = argparse.ArgumentParser(
        description="Check if storage resources left")
    parser.add_argument("--cluster_used_type",
                        help="Cluster type",
                        required=False)
    parser.add_argument("--cluster_used_model",
                        help="Cluster shared mode",
                        required=False)
    parser.add_argument("--tenants",
                        help="List of tenants",
                        nargs='?')

    args = parser.parse_args()
    if args.tenants is None:
        print("No tenant specified")
        sys.exit(0)
    else:
        tenants = args.tenants.split(",")
        if len(tenants) == 1 and tenants[0] == "":
            print("No tenant specified")
            sys.exit(0)
    try:
        timeout_seconds = 120
        signal.signal(signal.SIGALRM, timeout_handler)
        signal.alarm(timeout_seconds)
        check_storage_resource(tenants)
    except KeyboardInterrupt:
        sys.exit(3)
    except socket.timeout:
        print("Timeout {}s waiting for check to complete".format(timeout_seconds))
        exit(2)
    else:
        print("Unkown error occured")
        sys.exit(5)
    finally:
        signal.alarm(0)
