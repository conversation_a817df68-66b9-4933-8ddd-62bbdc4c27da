#!/bin/bash

# ExitCode:
# 0  无资源残留
# 1  有资源残留
# 2  脚本执行超时退出
# 3  用户中断程序执行
# 4  集群不正常
# 5  未知错误

# Started by AICoder, pid:3a6b7h2c39n17a3145940b7d2023075c81d6758b

if ! (kubectl get pvc -n admin | grep -q "swr-pvc"); then
    echo "not found swr-pvc, exiting"
    exit 0
fi

if kubectl exec -it -n admin $(kubectl get pod -n admin | grep op-asd-swr-cim | awk '{print $1}') -c zart-swr -- rm -rf /home/<USER>/datas/docker; then
    echo "/home/<USER>/datas/docker deletion successful."
else
    echo "Failed to delete /home/<USER>/datas/docker"
    exit 1
fi

if kubectl exec -it -n admin $(kubectl get pod -n admin | grep op-asd-swr-cim | awk '{print $1}') -c zart-swr -- rm -rf /home/<USER>/datas/swr; then
    echo "/home/<USER>/datas/swr deletion successful."
else
    echo "Failed to delete /home/<USER>/datas/swr"
    exit 1
fi
echo "clean swr pvc data success"

helm uninstall -n admin op-asd-swr

# 设置最大执行次数为60次
max_attempts=60
interval=5    # 每次检查间隔时间，单位：秒
wait_time=5   # 找到 PV 后等待时间，单位：秒
found_count=0 # 记录连续找到 PV 的次数

for (( attempt=1; attempt<=$max_attempts; attempt++ )); do
    if kubectl get pv | grep -q "swr-pvc"; then
        echo "Found matching PV. Waiting for $wait_time seconds."
        sleep $wait_time
        found_count=$((found_count + 1))
    else
        echo "PV not found. Exiting."
        exit 0
    fi

    # 等待一段时间再重新检查
    sleep $interval
done

echo "Maximum attempts reached. No matching PV found."

exit 1

# Ended by AICoder, pid:3a6b7h2c39n17a3145940b7d2023075c81d6758b