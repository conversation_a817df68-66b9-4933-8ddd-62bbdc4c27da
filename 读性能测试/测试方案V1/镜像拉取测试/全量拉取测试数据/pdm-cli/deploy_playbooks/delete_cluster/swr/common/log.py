import os
import logging


def get_logger(log_name='log', log_file="/var/log/pdm-cli.log"):
    log_dir = os.path.dirname(log_file)
    if not os.path.exists(log_dir):
        os.makedirs(log_dir, mode=0o750)
    if not os.path.exists(log_file):
        os.mknod(log_file, 0o640)
    return Logger(log_name, log_file).logger


class Logger:
    def __init__(self, log_name, log_file):
        self.logger = logging.getLogger(log_name)
        self.logger.setLevel(logging.INFO)

        fh = logging.FileHandler(log_file, "a")
        formatter = logging.Formatter('%(asctime)s %(levelname)s %(filename)s'\
                                       '%(funcName)s line[%(lineno)d]: %(message)s')
        fh.setFormatter(formatter)

        sh = logging.StreamHandler()
        sh.setLevel(logging.ERROR)

        self.logger.addHandler(fh)
        self.logger.addHandler(sh)
