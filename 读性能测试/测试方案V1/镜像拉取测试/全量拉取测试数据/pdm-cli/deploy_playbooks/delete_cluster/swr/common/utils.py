# -*- coding: UTF-8 -*-
import httplib2
import json
import os
import yaml

from common import log

LOG_FILE = "/paasdata/op-log/pdm-cli/" \
           "delete_cluster/swr_check_before_delete.log"
LOG = log.get_logger(__name__, LOG_FILE)

COM_VARS_FILE = "/root/common/com_vars.yml"
PORT_VARS_FILE = "/root/common/port_vars.yml"
SUCCESS = 2
MAX_LENGTH = 10


def get_from_file(filepath, varname):
    if os.path.exists(filepath):
        f = open(filepath)
        dataMap = yaml.safe_load(f)
        f.close()
        for data in dataMap:
            if varname in data:
                return data[varname]
        LOG.debug("get_from_file(%s) no find  var(%s) !" %
                  (filepath, varname))
        return None
    else:
        LOG.debug("get_from_file no file(%s) !" % filepath)
        return None


def get_from_comvars(varname, filepath=None):
    portvar = "/root/common/port_vars.yml"
    comvar = "/root/common/com_vars.yml"
    if '_port' in varname:
        res = get_from_file(portvar, varname)
        if not res:
            res = get_from_file(comvar, varname)
    else:
        res = get_from_file(comvar, varname)
    return res


def json_loads(str_need_load):
    try:
        return json.loads(str_need_load)
    except Exception:
        return str_need_load


def _request(url, method, header=None, data=None, timeout=None):
    try:
        if timeout is None:
            time_out = 60
        else:
            time_out = timeout
        http = httplib2.Http(timeout=time_out,
                             disable_ssl_certificate_validation=True)
        LOG.info("http request: url=%s\ndata=%s" % (url, data))
        if data is None:
            response, content = http.request(url,
                                             method,
                                             headers=header)
        else:
            response, content = http.request(url,
                                             method,
                                             body=json.dumps(data),
                                             headers=header)
        LOG.info("http response=%s,content=%s" % (response, content))
        if not response:
            return False, "get response fail"
        if response.status / 100 != SUCCESS:
            return False, content.decode()
        if content:
            content = json_loads(content.decode())
        return True, content
    except Exception as ex:
        LOG.error("Http request except=%s" % ex)
        return False, str(ex)


def http_request(url, method, header={'Content-Type': 'application/json'},
                 data=None, timeout=None,
                 retry_count=5):

    for _ in range(retry_count):
        result, content = _request(url, method, header, data, timeout)
        if result:
            break
    return result, content


def get_cmcc_images(imageList):
    if len(imageList) == 0:
        return ""
    end, i, isListOverMax, msgList = 0, 0, False, []
    if len(imageList) <= MAX_LENGTH:
        end = len(imageList)
    else:
        isListOverMax = True
        end = MAX_LENGTH
    while i < end:
        imageId = imageList[i]["imageId"]
        msgList.append(imageId)
        i += 1
    msg = ', '.join(msgList)
    if isListOverMax:
        msg += ", ... "
    return "image resources exist: " + msg


def get_cmcc_charts(chartList):
    if len(chartList) == 0:
        return ""
    end, i, isListOverMax, msgList = 0, 0, False, []
    if len(chartList) <= MAX_LENGTH:
        end = len(chartList)
    else:
        isListOverMax = True
        end = MAX_LENGTH
    while i < end:
        chartId = chartList[i]["chartId"]
        msgList.append(chartId)
        i += 1
    msg = ', '.join(msgList)
    if isListOverMax:
        msg += ", ... "
    return "chart resources exist: " + msg
