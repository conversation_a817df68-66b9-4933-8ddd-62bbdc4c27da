import sys
import traceback
import subprocess
from common.utils import get_from_comvars, http_request,\
      get_cmcc_images, get_cmcc_charts


class Swr(object):
    @classmethod
    def get_image_url(cls):
        ip = get_from_comvars('openpalette_service_ip')
        port = get_from_comvars('openpalette_service_port')
        url = "http://%s:%s/swr/v1/tenants/admin/repository/v1/images" % (ip, port)
        return url

    @classmethod
    def get_chart_url(cls):
        ip = get_from_comvars('openpalette_service_ip')
        port = get_from_comvars('openpalette_service_port')
        url = "http://%s:%s/swr/v1/tenants/admin/helm/v3/charts" % (ip, port)
        return url

    @staticmethod
    def check():
        result, contentImage = http_request(
            Swr.get_image_url(), "GET", retry_count=3, timeout=10)
        if not result:
            return False, str(contentImage)
        result, contentChart = http_request(
            Swr.get_chart_url(), "GET", retry_count=3, timeout=10)
        if not result:
            return False, str(contentChart)
        if len(contentImage["images"]) > 0 or len(contentChart["charts"]) > 0:
            images = get_cmcc_images(contentImage["images"])
            charts = get_cmcc_charts(contentChart["charts"])
            if images != "" and charts != "":
                cmcc_arts = images + ";\n" + charts + ";"
            else:
                cmcc_arts = images + charts + ";"
            return False, cmcc_arts
        return True, ""

    @staticmethod
    def pod_exists():
        try:
            output = subprocess.check_output(
                ["kubectl", "get", "pods", "-n", "admin"], universal_newlines=True
            )
            return "op-asd-swr-cim" in output
        except subprocess.CalledProcessError:
            return False



if __name__ == "__main__":
    try:
        if not Swr.pod_exists():
            sys.stdout.write('Pod op-asd-swr-cim not found. Skipping check.\n')
            sys.stdout.flush()
            sys.exit(0)
        sys.stdout.write('Pod op-asd-swr-cim exist. Start checking cmcc image & chart.\n')
        result, ouput = Swr.check()
        if not result:
            sys.stderr.write(ouput)
            sys.stderr.flush()
            sys.exit(1)
        sys.stdout.write('success.\n')
        sys.stdout.flush()
        sys.exit(0)

    except Exception:
        sys.stderr.write(traceback.format_exc())
        sys.stderr.flush()
        sys.exit(1)
