import subprocess
import os
import sys

# Started by AICoder, pid:rb3dfe0a23d25fd149f60a0630f6a434fae44b23

def execute_clean_swr_datas_script():
    try:
        # 获取当前文件所在路径
        current_dir = os.path.dirname(os.path.abspath(__file__))

        # 构造执行命令，拼接当前路径下的 clean_swr_datas.sh
        cmd = f"sh {os.path.join(current_dir, 'clean_swr_datas.sh')}"
        print("Start to execute clean_swr_datas.sh")
        # 执行命令并获取结果
        process = subprocess.Popen(cmd, shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        stdout, stderr = process.communicate()

        # 检查命令执行结果
        if process.returncode != 0:
            print(f"Failed to execute script: {stderr.decode('utf-8')}")
            sys.exit(1)  # 返回非零状态
        else:
            print("Script output:")
            print(stdout.decode('utf-8'))

    except subprocess.CalledProcessError as e:
        # 处理执行失败的情况
        print(f"Failed to execute script: {e}")
        print("Error output:")
        print(e.stderr)
        sys.exit(1)  # 返回非零状态

# 示例使用
execute_clean_swr_datas_script()

# Ended by AICoder, pid:rb3dfe0a23d25fd149f60a0630f6a434fae44b23