#!/bin/bash

print_log() {
    LOG_DATE=$(date "+%Y-%m-%d %H:%M:%S")
    LOG_FILENAME="/var/log/drain_pod-$(date "+%Y-%m-%d").log"
    { echo -e  "${LOG_DATE}" "[$$]" "$@"; } >> "${LOG_FILENAME}"
    chmod 640 $LOG_FILENAME
}

set_node_status() {
    local attrd_name=$1
    local status=$2
    crm_attribute -n $attrd_name -v $status -l forever> /dev/null 2>&1
    local rc=$?
    case $rc in
        0) print_log "$attrd_name = $status" ;;
        *) print_log "Could not set $attrd_name = $status: rc=$rc";;
    esac
    return $rc
}

get_node_status() {
    local attrd_name=$1
    local status=`crm_attribute -n $attrd_name -G -l forever -q 2>/dev/null`
    local rc=$?
    case $rc in
        0)
            print_log "get $attrd_name=$status"
            ;;
        *)
            print_log "Could not get $attrd_name=: rc=$rc"
            status=""
            ;;
    esac
    echo "$status"
}

delete_node_status() {
    local attrd_name=$1
    local status=`crm_attribute -n $attrd_name -D -l forever -q 2>/dev/null`
    local rc=$?
    case $rc in
        0)
            print_log "delete $attrd_name=$status"
            ;;
        *)
            print_log "Could not delete $attrd_name=: rc=$rc"
            status=""
            ;;
    esac
    echo "$status"
}

stop_pod() {
    local url=$(cat /etc/podNotifyUrl)
    local status=`get_node_status "block_flag"`
    if [[ "$status" == "ok" ]]; then
        return 0
    fi

    local result_code="000"
    # shellcheck disable=SC2076
    if [[ $url =~ "[" ]]; then
        result_code=`curl -s -g -o /dev/null -w %{http_code} $url -H "Content-Type:application/json" -X POST -d '{"unschedulable": "true"}'`
        print_log "stop pod rc=$result_code to close node"
        result_code=`curl -s -g -o /dev/null -w %{http_code} $url -H "Content-Type:application/json" -X POST -d '{"drain": "true"}'`
        print_log "stop pod rc=$result_code to move node"
    else
        result_code=`curl -s -i -o /dev/null -w %{http_code} $url -H "Content-Type:application/json" -X POST -d '{"unschedulable": "true"}'`
        print_log "stop pod rc=$result_code to close node"
        result_code=`curl -s -i -o /dev/null -w %{http_code} $url -H "Content-Type:application/json" -X POST -d '{"drain": "true"}'`
        print_log "stop pod rc=$result_code to move node"
    fi

    if [[ "$result_code" != "202"  ]]; then
        set_node_status "block_flag" "fail"
        return 1
    fi
    set_node_status "block_flag" "ok"
    delete_node_status "unblock_flag"
    return 0
}

if ! stop_pod;then
    exit 1
fi
exit 0
