import logging
import sys
import os
import yaml

LOG_FILE = "/var/log/collect_mountpoints.log"

LOG = logging.getLogger(__name__)
file_fmt = '%(asctime)s - %(pathname)s[line:%(lineno)d] - '\
    '%(levelname)s: %(message)s'
file_format_str = logging.Formatter(file_fmt)
file_handler = logging.FileHandler(LOG_FILE, "a")
file_handler.setFormatter(file_format_str)
LOG.addHandler(file_handler)
LOG.setLevel("DEBUG")
os.chmod(LOG_FILE, 0o640)

def process_mountpoint(mountpoint):
    if len(mountpoint) > 1 and mountpoint[-1] == "/":
        mountpoint = mountpoint[:-1]
    return mountpoint


def exec_os_popen(cmd):
    cmd_res = os.popen(cmd).readlines()
    LOG.info("exec os popen result is %s" % cmd_res)
    if cmd_res:
        return cmd_res
    return ""


def get_exists_mountpoints(mountpoint):
    exists_mountpoints = []
    cmd1 = "cat /etc/fstab | grep  ' %s/' | awk '{ print $2 }'" % mountpoint
    cmd1_res = exec_os_popen(cmd1)
    cmd2 = "cat /etc/fstab | grep  ' %s ' | awk '{ print $2 }'" % mountpoint
    cmd2_res = exec_os_popen(cmd2)
    if cmd1_res:
        for line in cmd1_res:
            exists_mountpoints.append(line.strip())
    if cmd2_res:
        for line in cmd2_res:
            if line.strip():
                exists_mountpoints.append(line.strip())
    LOG.info("get exists mountpoints are %s" % exists_mountpoints)
    return exists_mountpoints


def collect_mnts_action(fsck_info):
    mountpoint = process_mountpoint(fsck_info["mountpoint"])
    include_sub_mountpoint = fsck_info["include_sub_mountpoint"]
    exclude_mountpoints = []
    if fsck_info.get("exclude_mountpoints", []):
        for mnt in fsck_info.get("exclude_mountpoints", []):
            exclude_mountpoints.append(process_mountpoint(mnt))
    LOG.info("exclude_mountpoints are %s" % exclude_mountpoints)
    exists_mountpoints = get_exists_mountpoints(mountpoint)
    real_mountpoints = [mountpoint]
    if include_sub_mountpoint == "yes":
        real_mountpoints = \
            list(set(exists_mountpoints) - set(exclude_mountpoints))
    real_prcess_fsck_info = []
    for mnt in real_mountpoints:
        mnt_dict = dict(mountpoint=mnt, fsck=fsck_info["fsck"])
        real_prcess_fsck_info.append(mnt_dict)
    LOG.info("real_prcess_fsck_info are %s" % real_prcess_fsck_info)
    return real_prcess_fsck_info


def collect_mnts(config_file):
    with open(config_file, "r") as f:
        fsck_infos = yaml.safe_load(f)
    real_prcoess_fsck_info = []
    for fsck_info in fsck_infos["fsck"]:
        real_prcoess_fsck = collect_mnts_action(fsck_info)
        real_prcoess_fsck_info = real_prcoess_fsck_info + real_prcoess_fsck
    return real_prcoess_fsck_info


if __name__ == "__main__":
    config_file = sys.argv[1]
    collect_mountpoints = collect_mnts(config_file)
    print(collect_mountpoints)
