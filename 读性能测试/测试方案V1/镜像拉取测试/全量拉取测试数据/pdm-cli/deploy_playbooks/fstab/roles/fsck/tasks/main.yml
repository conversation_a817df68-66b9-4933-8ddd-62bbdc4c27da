- name: copy collect_mountpoints.py to node
  copy: 
    src: collect_mountpoints.py
    dest: /paasdata/op-tmp/collect_mountpoints.py
    mode: 0744

- name: copy config_file to node
  copy: 
    src: "{{config_file}}"
    dest: /paasdata/op-tmp/fsck_config.yml
    mode: 0744

- name: get valid_mountpints
  shell: $(command -v pythonlatest || echo python) /paasdata/op-tmp/collect_mountpoints.py /paasdata/op-tmp/fsck_config.yml
  register: valid_mountpoints

- name: backup /etc/fstab
  shell: cp /etc/fstab /etc/fstab_before_modify_fsck.bak

- include: modify_fsck.yml
  with_items:  "{{valid_mountpoints.stdout}}"
  loop_control:
    loop_var: valid_mountpoint
  when: valid_mountpoint != ""
