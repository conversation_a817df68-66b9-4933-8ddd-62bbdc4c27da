---
- debug:
    msg: "{{valid_mountpoint}}"

- name: get src_mnt_part
  shell: |
    src_mnt_part=`cat /etc/fstab | grep " {{valid_mountpoint.mountpoint}} " | awk '{print $1,$2,$3,$4,$5}'`
    echo $src_mnt_part
  register: src_mnt_part

- name: set volume auto-login when system boot
  lineinfile:
    dest: /etc/fstab
    state: present
    regexp: " {{valid_mountpoint.mountpoint}} "
    line: '{{ src_mnt_part.stdout }} {{ valid_mountpoint.fsck }}'
  when: src_mnt_part.stdout != ""
