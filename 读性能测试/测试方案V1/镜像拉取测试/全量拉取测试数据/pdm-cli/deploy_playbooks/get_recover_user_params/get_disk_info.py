import copy
import logging
import os
import json
import yaml

from deploy_playbooks.get_recover_user_params import nodeworker_api
from deploy_playbooks.get_recover_user_params import get_nfs_disk_info

LOG = logging.getLogger(__name__)

CURRENT_DIR = os.path.dirname(__file__)
USER_PARAMS_FILE = CURRENT_DIR + "/user_params_cfg.json"


def read_json_file(filename):
    fo = open(filename)
    try:
        content = json.load(fo)
    except Exception as e:
        err_str = ("Load %s error! \n%s" % (filename, e))
        raise ValueError(err_str)
    finally:
        fo.close()
    return content


def get_vm_disk_info_value_template():
    LOG.info("get_vm_disk_info_value_template called, "
             "USER_PARAMS_FILE:%s" % USER_PARAMS_FILE)
    user_params_data = read_json_file(USER_PARAMS_FILE)
    for element in user_params_data:
        if element.get("param", {}).get("name", "") == "disk_info":
            if element.get("param", {}).get("value", []):
                template = element.get("param", {}).get("value")[0]
                LOG.debug("get_vm_disk_info_value_template %s" % template)
                return template
    raise Exception("get_vm_disk_info_value_template failed")


def get_nfs_multiattach_disks():
    return get_nfs_disk_info.get_nfs_multiattach_disk()


def get_nfs_multiattach_disk_map():
    nfs_multiattach_disks = get_nfs_multiattach_disks()
    map = {}
    for nfs_multiattach_disk in nfs_multiattach_disks:
        device_by_id = nfs_multiattach_disk["device_by_id"]
        map[device_by_id] = nfs_multiattach_disk
    return map


def get_node_bear_type(node, tcf_scenario, scenariotype):
    bear_type = node.get("bear_type")
    if not bear_type:
        if tcf_scenario == "UME-standard" and scenariotype == "preset_node":
            bear_type = "VM"
    return bear_type


def is_system_disk(disk):
    partition_mount_info = disk["partition_mount_info"]
    mountpoint_list = partition_mount_info.split(",")
    if "/boot" in mountpoint_list:
        return True
    return False


def get_is_kubeall_from_file(name):
    com_vars_file = "/root/common/com_vars.yml"
    if os.path.exists(com_vars_file):
        with open(com_vars_file) as f:
            data = yaml.safe_load(f)
        for data in data:
            if name in data:
                return data[name]
        LOG.info("get_is_kubeall_from_file(%s) no find  var(%s) !"
                 % (com_vars_file, name))
        return None
    else:
        LOG.info("get_is_kubeall_from_file no file(%s) !" % com_vars_file)
        return None


def is_kubeall_scene():
    if get_is_kubeall_from_file("is_kubeall"):
        return True
    return False


def get_vm_disk_info(hostname, tcf_scenario="", scenariotype=""):  # noqa: C901
    try:
        LOG.info("get_vm_disk_info called, hostname:%s" % hostname)
        if is_kubeall_scene():
            LOG.info("get_vm_disk_info it's kubeall scene, \
                     no need to input vm_disk_info")
            return {
                "result": True,
                "message": "",
                "is_needed": False,
                "value": []
            }

        memory = nodeworker_api.Memory(hostname)
        memory.setup()
        disks = memory.disks
        node = memory.node
        bear_type = get_node_bear_type(node, tcf_scenario, scenariotype)
        if bear_type != "VM":
            LOG.info("no need to input vm_disk_info")
            return {
                "result": True,
                "message": "",
                "is_needed": False,
                "value": []
            }
        nfs_multiattach_disk_map = get_nfs_multiattach_disk_map()
        value_template = get_vm_disk_info_value_template()
        values = []
        for disk in disks["disks"]:
            device_by_id = disk["device_by_id"]
            if device_by_id in nfs_multiattach_disk_map:
                continue
            if is_system_disk(disk):
                continue
            one_group = copy.deepcopy(value_template)
            for part in one_group:
                if part.get("name") == "mountpoint":
                    part["value"] = disk["partition_mount_info"]
                if part.get("name") == "disk_type":
                    default_disk_type = "Cloud"
                    part["value"] = default_disk_type
                if part.get("name") == "devname":
                    part["value"] = disk["devname"]
                if part.get("name") == "device_by_id":
                    part["value"] = disk["device_by_id"]
            values.append(one_group)
        LOG.info("need to input vm_disk_info:%s, value:%s" %
                 (bool(values), values))
        return {"result": True,
                "message": "",
                "is_needed": bool(values),
                "value": values}
    except Exception as e:
        LOG.error("get_vm_disk_info failed: %s" % str(e))
        return {"result": False,
                "message": "get_vm_disk_info failed: %s" % str(e),
                "is_needed": True,
                "value": []}


def get_param_info(hostname, tcf_scenario, scenariotype, is_tcf_lite):
    return get_vm_disk_info(hostname, tcf_scenario, scenariotype)
