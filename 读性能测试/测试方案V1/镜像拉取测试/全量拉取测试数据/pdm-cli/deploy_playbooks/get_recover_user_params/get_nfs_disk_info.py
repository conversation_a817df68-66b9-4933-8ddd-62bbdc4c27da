#!/usr/bin/python
import subprocess
import os
import json
import six
import logging

Log = logging.getLogger(__name__)

def run_cmd(cmd):
    p = subprocess.Popen(cmd, shell=True,
                         stdout=subprocess.PIPE,
                         stderr=subprocess.PIPE)
    out, _ = p.communicate()
    rc = p.poll()
    if six.PY3:
        out = out.decode()
    return rc, out


def get_disk_backend_info():
    iscsi_cmd = "iscsiadm -m session 2>/dev/null |awk '{print $3}'"
    _, output = run_cmd(iscsi_cmd)
    sessions = []
    for session in output.split("\n"):
        if "," in session:
            sessions.append(session.split(",")[0])
    return sessions


def get_scsi_ids(lv_path):
    try:
        lvs_cmd = "lvs -o lv_name {} --reportformat=json".format(lv_path)
        _, lvs_output = run_cmd(lvs_cmd)
        '''
        output example:
        {
         "report": [
          {
              "lv": [
                  {"lv_name":"lvtest"}
              ]
          }
         ]
        }
        '''
        lvs_json = json.loads(lvs_output)
        lv_name = lvs_json["report"][0]["lv"][0]["lv_name"]
        if not lv_name:
            Log.error("LV name not found.")
            return []
    except Exception as e:
        Log.error("Error retrieving VG and LV names for LV path %s: %s" % (lv_path, e))
        return []

    try:
        pvs_cmd = "pvs -o pv_name -S lv_name={} --reportformat=json".format(lv_name)
        _, pvs_output = run_cmd(pvs_cmd)
        '''
        output example:
        {
            "report": [
                {
                    "pv": [
                        {"pv_name":"/dev/mapper/mpaths"},
                        {"pv_name":"/dev/mapper/mpatht"}
                    ]
                }
            ]
        }
        '''
        pvs_json = json.loads(pvs_output)
        pv_names = [pv["pv_name"] for pv in pvs_json["report"][0]["pv"]]
    except Exception as e:
        Log.error("Error retrieving PVs for LV %s: %s" % (lv_name, e))
        return []

    scsi_ids = []
    for pv in pv_names:
        try:
            scsi_cmd = "/lib/udev/scsi_id --page=0x83 -g -u -d {}".format(pv)
            _, scsi_output = run_cmd(scsi_cmd)
            scsi_id = scsi_output.strip()
            if scsi_id:
                scsi_ids.append(scsi_id)
        except Exception as e:
            Log.error("Error retrieving SCSI ID for PV %s: %s" % (pv, e))

    return scsi_ids


def get_device_info(dev):
    scsi_ids = get_scsi_ids(dev)
    dev_paths = []
    dev_ids = []
    for dev_path in os.listdir("/dev/disk/by-id"):
        for scsi_id in scsi_ids:
            if scsi_id in dev_path and dev_path.startswith("dm-uuid-mpath"):
                if scsi_id.startswith("3"):
                    dev_paths.append("/dev/disk/by-id/" + dev_path)
                    dev_ids.append(scsi_id[1:])

    return ",".join(dev_paths), ",".join(dev_ids)


def get_nfs_device_info():
    nfs_device = "/etc/remote_fstab"
    data = ""

    if os.path.exists(nfs_device):
        with open(nfs_device, "r") as f:
            data = f.read().replace('\n', '')

    if data.startswith('/dev/disk/by-id/virtio-'):
        dev_id = data.replace("/dev/disk/by-id/virtio-", "")
        return "Local", data, dev_id, "/", "/"

    lvm = []
    if data.startswith("/dev/mapper/"):
        lvm = data.replace("/dev/mapper/", "").split("-")
    elif data.startswith("/dev/"):
        lvm = data.replace("/dev/", "").split("/")

    if len(lvm) == 2:
        vg_name = lvm[0]
        lv_name = lvm[1]
        dev_path, dev_id = get_device_info(data)
        return "Cloud-preset", dev_path, dev_id, vg_name, lv_name

    return "", "", "", "/", "/"


def get_nfs_multiattach_disk():
    nfs_disk = []
    disk_type, dev_path, dev_id, vg_name, lv_name = get_nfs_device_info()
    if dev_path == "":
        return nfs_disk

    lvm_enable = "no"
    if vg_name != "/" and lv_name != "/":
        lvm_enable = "yes"

    disk_backend_info = "N/A"
    backend_info = get_disk_backend_info()
    if len(backend_info) > 0:
        disk_backend_info = ",".join(backend_info)

    disk = {
        "multiattach": "yes",
        "disk_type": disk_type,
        "disk_backend_info": disk_backend_info,
        "backend_type": "N/A",
        "capacity": "",
        "device_name": dev_id,
        "mntopts": "/",
        "mountpoint": "N/A",
        "filesystem_type": "N/A",
        "lv_name": lv_name,
        "partition_size": "100%",
        "vg_name": vg_name,
        "lvm_enable": lvm_enable,
        "pre_create_dirs": "/",
        "description": "NFS share disk",
        "device_by_id": dev_path
    }
    nfs_disk.append(disk)
    return nfs_disk


# if __name__ == '__main__':
#     print(get_nfs_multiattach_disk())
