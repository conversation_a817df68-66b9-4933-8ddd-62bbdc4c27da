import argparse
import json
import os
import sys
import yaml
import logging
import functools

from importlib import import_module

if sys.version > '3':
    import configparser as ConfigParser
else:
    import ConfigParser

USER_PARAMS_CFG_PATH = "/etc/pdm/deploy_playbooks/" \
    "get_recover_user_params/user_params_cfg.json"

LOG_DIR = "/paasdata/op-log/pdm-cli/get_recover_user_params"
LOG_FILE = "/paasdata/op-log/pdm-cli/get_recover_user_params.log"
SERVICE_NAME = 'openpalette_service_ip'
SERVICE_PORT = 'openpalette_service_port'
PAAS_CONF = "/etc/pdm/conf/paas.conf"
PDM_CONF_JSON = "/etc/pdm/conf/conf.json"
MODULE_BASE = "deploy_playbooks.get_recover_user_params."

sys.path.append("/etc/pdm")

if not os.path.exists(LOG_DIR):
    os.makedirs(LOG_DIR, 0o750)
logging.basicConfig(filename=LOG_FILE,
                    format="%(asctime)s-%(name)s-%(levelname)s-%(message)s",
                    level=logging.INFO)

LOG = logging.getLogger(__name__)
CONFIG = {
    "port": "/root/common/port_vars.yml",
    "common": "/root/common/com_vars.yml"
}


def log_file(func):
    if not os.path.exists(LOG_DIR):
        os.makedirs(LOG_DIR, 0o750)

    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        result = func(*args, **kwargs)
        if os.path.exists(LOG_FILE):
            os.chmod(LOG_FILE, 0o640)
        return result
    return wrapper


def get_from_file(filepath, name):
    if os.path.exists(filepath):
        with open(filepath) as f:
            data = yaml.safe_load(f)
        for data in data:
            if name in data:
                return data[name]
        LOG.debug("get_from_file(%s) no find  var(%s) !" %
                  (filepath, name))
        return None
    else:
        LOG.debug("get_from_file no file(%s) !" % filepath)
        return None


def get_from_cfg(name, cfg=CONFIG):
    port_cfg = cfg.get('port')
    common_cfg = cfg.get('common')
    if '_port' in name:
        res = get_from_file(port_cfg, name)
        if not res:
            res = get_from_file(common_cfg, name)
    else:
        res = get_from_file(common_cfg, name)
    return res


def get_params_from_cfg():
    with open(USER_PARAMS_CFG_PATH) as f:
        return json.load(f)


def get_tcf_scenario():
    config = ConfigParser.ConfigParser(allow_no_value=True)
    config.read(PAAS_CONF)
    return config.get("tcf", 'tcf_scenario')


def is_tcf_lite():
    config = ConfigParser.ConfigParser(allow_no_value=True)
    config.read(PAAS_CONF)
    if config.has_option("tcf", "is_tcf_lite"):
        return config.get("tcf", 'is_tcf_lite')
    return False


def get_scenario_type():
    with open(PDM_CONF_JSON) as f:
        data = json.load(f)
        return data["region"]["scenariotype"]


def get_paas_environment_info(hostname):
    info = {}
    info["is_tcf_lite"] = is_tcf_lite()
    info["tcf_scenario"] = get_tcf_scenario()
    info["scenariotype"] = get_scenario_type()
    info["hostname"] = hostname
    return info


class UserParam(object):
    def __init__(self, param, environment_info):
        self.param = param
        self.environment_info = environment_info

    def get_preset_param(self):
        params = self.get_param_from_script()
        if not params.get('result'):
            message = params.get("message")
            raise Exception(message)
        if not params.get("is_needed"):
            return {}
        self.param.get("param").update({"value": params.get("value")})
        return self.param.get("param")

    def get_param_from_script(self):
        module_name = self.param.get("get_param_module")
        hostname = self.environment_info.get("hostname")
        tcf_scenario = self.environment_info.get("tcf_scenario")
        scenariotype = self.environment_info.get("scenariotype")
        is_tcf_lite = self.environment_info.get("is_tcf_lite")
        if not module_name:
            raise Exception("preset user_params.json error")
        module_name = MODULE_BASE + module_name
        module = import_module(module_name)
        params = module.get_param_info(hostname,
                                       tcf_scenario,
                                       scenariotype,
                                       is_tcf_lite)
        return params


@log_file
def get_user_params(hostname):
    params = get_params_from_cfg()
    environment_info = get_paas_environment_info(hostname)
    user_params = []
    for param in params:
        param = UserParam(param, environment_info).get_preset_param()
        if param:
            user_params.append(param)
    return json.dumps({"user_params": user_params})


if __name__ == "__main__":
    try:
        useage = ("python /etc/pdm/deploy_playbooks/"
                  "get_recover_user_params/get_recover_user_params.py"
                  " --hostname <hostname>")
        parser = argparse.ArgumentParser(usage=useage)
        parser.add_argument('--hostname', dest="hostname", required=True)
        args = parser.parse_args()
        hostname = args.hostname
        user_params = get_user_params(hostname)
        sys.stdout.write(user_params)
        sys.stdout.flush()
        sys.exit(0)
    except Exception as e:
        err_msg = "get recover user params failed: %s" % str(e)
        sys.stderr.write(err_msg)
        sys.stderr.flush()
        sys.exit(1)
