import copy
import logging
import os
import json
import traceback

import requests
import yaml
from IPy import IP


LOG = logging.getLogger(__name__)

CURRENT_DIR = os.path.dirname(__file__)
USER_PARAMS_FILE = CURRENT_DIR + "/user_params_cfg.json"
com_var = '/root/common/com_vars.yml'
port_var = '/root/common/port_vars.yml'

def read_json_file(filename):
    try:
        with open(filename, "r") as fo:
            content = json.load(fo)
    except Exception as e:
        err_str = ("open file %s error or json load this file error:\n%s" %
                   (filename, e))
        raise ValueError(err_str)
    return content


def get_node_bear_type(node, tcf_scenario, scenariotype):
    bear_type = node.get("bear_type")
    if not bear_type:
        if tcf_scenario == "UME-standard" and scenariotype == "preset_node":
            bear_type = "VM"
    return bear_type


def get_slb_preset_network_info_template():
    LOG.info("get_slb_preset_network_info_template called, "
             "USER_PARAMS_FILE:%s" % USER_PARAMS_FILE)
    user_params_data = read_json_file(USER_PARAMS_FILE)
    for element in user_params_data:
        if element.get("param", {}).get("name", "") == \
                "slb_preset_network_info":
            if element.get("param", {}).get("value", []):
                template = element.get("param", {}).get("value")[0]
                LOG.debug("get_slb_preset_network_info_template %s" % template)
                return template
    raise Exception("get_slb_preset_network_info_template failed")


def get_need_user_config_mac_values(node, value_template):
    values = []
    ports = node.get("userdata", {}).get("ports", [])
    for port in ports:
        one_group = copy.deepcopy(value_template)
        LOG.info("userdata port info:%s", port)
        if port.get("ip", "") or port.get("ipv6", ""):
            continue
        for part in one_group:
            if part.get("name") == "infra_network":
                part["value"] = port.get("infra_network", "")
            if part.get("name") == "mac":
                part["value"] = port.get("mac", "")
        values.append(one_group)
    LOG.info("get net mac mapping without ip address:%s", values)
    return values


def read_yaml_file(file_path):
    with open(file_path) as fp:
        documents = yaml.safe_load(fp)
    return documents


def get_msb_url():
    try:
        ip = ''
        port = ''
        data_map = read_yaml_file(com_var)
        for data in data_map:
            if 'openpalette_service_ip' in data:
                ip = data.get('openpalette_service_ip', '')
        data_map = read_yaml_file(port_var)
        for data in data_map:
            if 'openpalette_service_port' in data:
                port = data.get('openpalette_service_port', '')
        if IP(ip).version() == 4:
            return 'http://%s:%s' % (ip, port)
        elif IP(ip).version() == 6:
            return 'http://[%s]:%s' % (ip, port)
        return ''
    except Exception:
        LOG.error(traceback.format_exc())
        return ''


def get_node_info(hostname):
    headers = {
        "accept": "application/json",
        "content-type": "application/json"}
    base_url = get_msb_url()
    url = base_url + '/nodeworker/v1/tenants/admin' \
                     '/nodes?hostname=%s' % hostname
    resp = requests.get(url=url, headers=headers,
                        allow_redirects=False, timeout=10.0)
    if resp.status_code != 200:
        LOG.warning('get node info failed, url: %s, code: %s' %
                        (url, resp.status_code))
        return {}
    LOG.info(resp.text)
    data = json.loads(resp.text)
    nodes = data.get('nodes', [])
    if len(nodes) > 0:
        return nodes[0]
    return {}


def get_slb_preset_network_info(hostname, tcf_scenario, scenariotype):
    no_need_res = {
        "result": True,
        "message": "",
        "is_needed": False,
        "value": []
    }
    try:
        LOG.info("get_vm_disk_info called, hostname:%s" % hostname)
        node = get_node_info(hostname)
        LOG.info("hostname:%s", hostname)
        LOG.info("node info:%s", node)
        bear_type = get_node_bear_type(node, tcf_scenario, scenariotype)
        if bear_type != "VM":
            LOG.info("no need to input slb_preset_network_info")
            return no_need_res
        value_template = get_slb_preset_network_info_template()
        values = get_need_user_config_mac_values(node, value_template)
        if not values:
            return no_need_res
        else:
            return {
                "result": True,
                "message": "",
                "is_needed": True,
                "value": values
            }
    except Exception as e:
        LOG.error("get_slb_preset_network_info failed: %s" % str(e))
        return {"result": False,
                "message": "get_slb_preset_network_info failed: %s" % str(e),
                "is_needed": True,
                "value": []}


def get_param_info(hostname, tcf_scenario, scenariotype, is_tcf_lite):
    return get_slb_preset_network_info(hostname, tcf_scenario, scenariotype)
