import json
import logging
import os
import time
import yaml

from deploy_playbooks.common import httpclient as http_cli


LOG = logging.getLogger(__name__)

COM_VARS_FILE = "/root/common/com_vars.yml"
PORT_VARS_FILE = "/root/common/port_vars.yml"


def get_from_comvars(varname, filepath=COM_VARS_FILE):
    if not os.path.exists(filepath):
        err_info = "%s not found!" % filepath
        LOG.error(msg=err_info)
        raise Exception(err_info)
    try:
        with open(filepath, 'r') as f:
            data_map = yaml.safe_load(f)
    except Exception as e:
        err_info = "Load from %s get exception: %s" % (filepath, e)
        LOG.error(msg=err_info)
        raise Exception(err_info)
    for data in data_map:
        if varname in data:
            return data[varname]
    err_info = "%s not found in %s" % (varname, filepath)
    LOG.error(msg=err_info)
    raise Exception(err_info)


class NodeWorkerAPI(object):
    def __init__(self):
        ip = get_from_comvars('openpalette_service_ip', filepath=COM_VARS_FILE)
        port = get_from_comvars('openpalette_service_port',filepath=PORT_VARS_FILE)
        self.op_service_url = "%s:%s" % (ip, port)
        self.nodeworker_base_url = "http://%s/nodeworker/v1/tenants/admin" \
                                   % self.op_service_url
        self.header = {'Content-Type': 'application/json'}

    def get_nodes(self):
        url = self.nodeworker_base_url + "/nodes"
        return http_cli.send_http_request(url, "GET")

    def get_node(self, node_uuid):
        url = self.nodeworker_base_url + "/nodes/" + node_uuid
        return http_cli.send_http_request(url, "GET")

    def get_volume_scaleout_process(self):
        url = self.nodeworker_base_url + "/volume_scaleout"
        return http_cli.send_http_request(url, "GET")

    def node_volume_scaleout(self, data):
        url = self.nodeworker_base_url + "/volume_scaleout"
        return http_cli.send_http_request(url, "POST", header=self.header, data=data)

    def get_node_disks(self, node_uuid):
        url = self.nodeworker_base_url + "/nodes/%s/get_disks" % node_uuid
        retry_times = 60
        success_flag = False
        for i in range(retry_times):

            result, disks = http_cli.send_http_request(url, 'GET', retry_times=1)
            if result and disks.get("disks", []):
                success_flag = True
                break
            time.sleep(5)
        if not success_flag:
            error_info = "get disks from nodeworker failed for node:%s" % node_uuid
            raise Exception(error_info)
        return disks


class JsonFileOperator(object):
    def read_json_file(self, filename):
        fo = open(filename)
        try:
            content = json.load(fo)
        except Exception as e:
            err_str = ("Load %s error! \n%s" % (filename, e))
            raise ValueError(err_str)
        finally:
            fo.close()
        return content

    def write_json_file(self, file_json, file_path):
        json_str = json.dumps(file_json, indent=4)
        with open(file_path, "w+") as f:
            f.write(json_str)


class Memory(object):
    def __init__(self, hostname):
        self.disks = {}
        self.nodes = []
        self.node = {}
        self.hostname = hostname
        self.API = NodeWorkerAPI()
        self.backup_path = "/paasdata/op-data/pdm-cli/" \
                           "reverse_build_recover_template_data/" \
                           "backup_nodeinfo/%s" % hostname
        self.nodes_file = self.backup_path + "/node_all_nodes"
        self.node_file = self.backup_path + "/node_node_%s_detail" % hostname
        self.node_disk_file = \
            self.backup_path + "/node_node_%s_disks_detail" % hostname

    def setup(self):
        try:
            self.get_nodes()
            node = self.get_matched_node()
            self.get_disks(node["uuid"])
        except Exception as e:
            LOG.info("get node info from nodeworker failed,"
                     "node may be deleted: %s" % e)
            if os.path.exists(self.nodes_file) and \
                    os.path.exists(self.node_file) and \
                    os.path.exists(self.node_disk_file):
                self.read_from_file()
            else:
                raise Exception("get node info from nodeworker and"
                                " backup file both failed")

    def read_from_file(self):
        LOG.debug("read_from_file called")
        json_file_operator = JsonFileOperator()
        self.nodes = json_file_operator.read_json_file(self.nodes_file)
        self.node = json_file_operator.read_json_file(self.node_file)
        self.disks = json_file_operator.read_json_file(self.node_disk_file)

    def get_nodes(self):
        result, nodes_from_nodeworker = self.API.get_nodes()
        if not result:
            raise Exception("Get nodes failed")
        self.nodes = nodes_from_nodeworker.get("nodes", [])

    def get_node(self, node_uuid):
        result, node = self.API.get_node(node_uuid)
        if not result:
            raise Exception("Get node %s failed" % node_uuid)
        return node

    def get_matched_node(self):
        for node in self.nodes:
            if node["hostname"] == self.hostname:
                node = self.get_node(node["uuid"])
                self.node = node
                return node
        error_info = "cannot find hostname %s from nodeworker nodes"\
                     % self.hostname
        raise Exception(error_info)

    def get_disks(self, node_uuid):
        self.disks = self.API.get_node_disks(node_uuid)
        LOG.debug("node:%s 's disks: %s" % (node_uuid, self.disks))
