[{"param": {"name": "disk_info", "cn_name": "磁盘信息", "en_name": "disk info", "cn_desc": "磁盘信息", "en_desc": "disk info", "required": true, "readonly": false, "cn_tip": "", "en_tip": "", "verify_re": "", "max_len": 255, "value": [[{"name": "mountpoint", "cn_name": "挂载点", "en_name": "mountpoint", "cn_desc": "挂载点", "en_desc": "mountpoint", "required": false, "cn_tip": "", "en_tip": "", "verify_re": "", "max_len": 1000, "value": "", "type": "inputText", "readonly": true}, {"name": "disk_type", "cn_name": "磁盘类型", "en_name": "disk_type", "cn_desc": "磁盘类型", "en_desc": "disk type", "required": true, "cn_tip": "虚机本地盘设为Local,云盘设为Cloud", "en_tip": "virtual machine's local disk should be set to Local, cloud disk should be set to Cloud", "verify_re": "", "max_len": 255, "value": "", "options": [{"label": "Local", "value": "Local"}, {"label": "Cloud", "value": "Cloud"}], "type": "radio", "readonly": false}, {"name": "devname", "cn_name": "设备名称", "en_name": "device_name", "cn_desc": "设备名称", "en_desc": "device name", "required": true, "cn_tip": "", "en_tip": "", "verify_re": "", "max_len": 255, "value": "", "type": "inputText", "readonly": true}, {"name": "device_by_id", "cn_name": "设备ID", "en_name": "device_by_id", "cn_desc": "设备ID", "en_desc": "device ID", "required": true, "cn_tip": "", "en_tip": "", "verify_re": "", "max_len": 255, "value": "", "type": "inputText", "readonly": true}]]}, "get_param_module": "get_disk_info"}, {"param": {"name": "slb_preset_network_info", "cn_name": "SLB预置网络信息", "en_name": "slb_preset_network_info", "cn_desc": "SLB预置网络信息", "en_desc": "slb_preset_network_info", "cn_tip": "", "en_tip": "", "verify_re": "", "max_len": 255, "required": true, "readonly": false, "value": [[{"name": "infra_network", "cn_name": "网络名称", "en_name": "infra_network", "cn_desc": "网络名称", "en_desc": "infra_network", "required": true, "cn_tip": "", "en_tip": "", "verify_re": "", "max_len": 1000, "value": "", "type": "inputText", "readonly": true}, {"name": "mac", "cn_name": "mac地址", "en_name": "mac", "cn_desc": "mac地址", "en_desc": "mac", "required": true, "cn_tip": "", "en_tip": "", "verify_re": "^\\s*([0-9a-fA-F]{2,2}:){5,5}[0-9a-fA-F]{2,2}\\s*$", "max_len": 1000, "value": "", "type": "inputText", "readonly": false}]]}, "get_param_module": "get_slb_preset_network_info"}]