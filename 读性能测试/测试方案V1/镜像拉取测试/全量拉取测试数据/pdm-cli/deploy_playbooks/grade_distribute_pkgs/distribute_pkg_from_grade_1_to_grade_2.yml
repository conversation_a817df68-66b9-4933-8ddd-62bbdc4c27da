---
- hosts: nodes
  remote_user: "{{ customized_user | default('ubuntu') }}"
  gather_facts: no
  become: yes
  become_method: sudo

  tasks:
    - name: create "{{ distribute_scripts_path }}" working dir
      file:
        path: "{{ distribute_scripts_path }}"
        state: directory

    - name: copy playbooks
      copy:
        src: "{{ item }}"
        dest: "{{ distribute_scripts_path }}/"
      with_items:
        - ansible.cfg
        - execute_tasks_on_grade_1.yml
        - "{{ classed_hosts_file }}"

    - name: run ansible-playbook
      shell:  |
        cd {{ distribute_scripts_path }}
        ansible-playbook -i {{ classed_hosts_file.split('/')[-1] }} execute_tasks_on_grade_1.yml --extra-vars pkg_name={{ pkg_name }} --extra-vars comp_name={{ comp_name }} --extra-vars pdeploy_version_pkg_path={{ pdeploy_version_pkg_path }} --extra-vars pdm_cli_version_pkg_path={{ pdm_cli_version_pkg_path }}
      register: result
      async: 1200
      poll: 10
