---
- hosts: nodes
  remote_user: "{{ customized_user | default('ubuntu') }}"
  gather_facts: no
  become: yes
  become_method: sudo
  serial: 15
  tasks:
    - name: set pkg dest path
      set_fact:
        pkg_dest_path: "{{ pdm_cli_version_pkg_path }}"
        pkg_path: "{{ pdm_cli_version_pkg_path }}/{{ pkg_name }}"
      delegate_to: localhost
      run_once: True
      when:
        - comp_name in ['op-containers-containerd', 'docker', 'inetagent', 'os_pkg_repo']

    - name: set pkg dest path
      set_fact:
        pkg_dest_path: "{{ pdeploy_version_pkg_path }}"
        pkg_path: "{{ pdeploy_version_pkg_path }}/{{ pkg_name }}"
      delegate_to: localhost
      run_once: True
      when:
      - comp_name not in ['op-containers-containerd', 'docker', 'inetagent', 'os_pkg_repo']

    - name: check {{ pkg_path }} on grade_1 nodes exists
      stat:
        path: "{{ pkg_path }}"
        get_checksum: yes
      register: pkg_path_exist
      delegate_to: localhost
      run_once: True

    - name: create {{ pkg_dest_path }} directory
      file:
        path: "{{ pkg_dest_path }}"
        state: directory
        mode: 0750

    - name: distribute {{ pkg_path }} from grade_1 to grade_2
      synchronize:
        src: "{{ pkg_path }}"
        dest: "{{ pkg_dest_path }}"
        checksum: yes
      when:
      - pkg_path_exist.stat.exists
