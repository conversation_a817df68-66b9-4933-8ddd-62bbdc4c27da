# -*- coding: UTF-8 -*-
import argparse
import os
import json
import logging
import sys
import time
import traceback
import re

if sys.version > '3':
    import configparser as ConfigParser
else:
    import ConfigParser

validName = re.compile(r'^[a-z0-9]([-a-z0-9]*[a-z0-9])?(\.[a-z0-9]([-a-z0-9]*[a-z0-9])?)*$')
sync_file_flag = False

def sync_file(file_path, owner='root', group='root', mode='0755',
              hosts='/etc/pdm/hosts', exclude_ip='', limit_ip=''):
    try:
        if exclude_ip and limit_ip:
            logging.debug("Wrong use of sync_file!")
            return False
            
        paas_conf = ConfigParser.ConfigParser()
        paas_conf.read("/etc/pdm/conf/paas.conf")
        controller_ha_count = int(paas_conf.get("controller_ha",
                                                "controller_ha_count"))
        if 1 == controller_ha_count:
            logging.debug("no need sync file when controller num is 1.")
            return True
        exclude_ip_cmd = ''
        if exclude_ip:
            exclude_ip_cmd = '-l nodes,!%s ' % exclude_ip

        limit_ip_cmd = ''
        if limit_ip:
            limit_ip_cmd = "-l %s" % limit_ip

        src = file_path
        dest = file_path
        cmd = ('ansible nodes -i %s %s %s -m copy -a '
               '"src=%s dest=%s owner=%s group=%s mode=%s" -u ubuntu -b '
               '>/dev/null 2>/dev/null'
               % (hosts, exclude_ip_cmd, limit_ip_cmd,
                  src, dest, owner, group, mode))
        logging.debug('Sync file. \n%s', cmd)
        retry_count = 5
        for _ in range(retry_count):
            result = os.system(cmd + '>/dev/null 2>/dev/null')
            if result != 0:
                time.sleep(5)
                logging.debug('Sync file failed and retry')
            else:
                break
        else:
            logging.debug('Sync %s file failed.' % src)
            return False
        logging.debug('Sync file successfully.')
        return True
    except Exception as e:
        logging.error('error: %s' % e.args[0])
        logging.error(traceback.format_exc())

def formatLabel(label):
    try:
        kv = label.split(':')
        if len(kv) != 2:
            return ''
        k = kv[0]
        v = kv[1]
        if len(k) == 0 or len(v) == 0 or k.isspace() or v.isspace():
            return ''
        return k.strip()+": "+v.strip()
    except Exception:
        logging.error('formatLabel err')
        logging.error(traceback.format_exc())
        return ''

class GenerateIngressControllerConf(object):
    def __init__(self,source_file):
        self.config_log()
        self.openpalette_file = source_file
        self.openpalette_data = {}
        self.ingress_controller_deploy_config = []
        self.ingress_controllers = []
        self.init_data()

    @staticmethod
    def config_log():
        try:
            log_format = '%(asctime)s %(levelname)s %(filename)s ' \
                         '[line:%(lineno)d] %(funcName)s %(message)s'
            log_file = '/var/log/generate_ingress_controller_data.log'
            logging.basicConfig(level=logging.DEBUG,
                                filename=log_file,
                                filemode='a',
                                datefmt='%Y-%m-%d %H:%M:%S',
                                format=log_format)
            os.chmod(log_file, 0o640)
            logging.info('======== start ========')
        except Exception:
            logging.error(traceback.format_exc())

    def init_data(self):
        try:
            logging.info(self.openpalette_file)
            self.openpalette_data = self.read_json_file(self.openpalette_file)
            self.ingress_controller_deploy_config = \
                self.openpalette_data.get('ingress_controller_deploy_config', [])
            logging.info(self.ingress_controller_deploy_config)

        except Exception:
            logging.error(traceback.format_exc())

    @staticmethod
    def read_json_file(file_path):
        try:
            if os.path.exists(file_path) is False:
                logging.error('%s file not exist!' % file_path)
                return {}
            with open(file_path) as fp:
                return json.load(fp)
        except Exception:
            logging.error('read_json_file %s err' % file_path)
            logging.error(traceback.format_exc())
            return {}

    def get_name(self,conf):
        try:
            name = conf.get('ingress_controller_name','')
            if len(name) > 53 or not validName.match(name):
                logging.error('must match regex and the length must not be longer than 53')
                return ''
            return name
        except Exception:
            logging.error('get_name err')
            logging.error(traceback.format_exc())
            return ''

    def get_namespace(self,conf):
        try:
            namespace = conf.get('ingress_controller_namespace','')
            if len(namespace) > 63 or not validName.match(namespace):
                logging.error('must match regex and the length must not be longer than 63')
                return ''
            return namespace
        except Exception:
            logging.error('get_namespace err')
            logging.error(traceback.format_exc())
            return ''

    def get_service_annotations(self,conf):
        service_annotations = []
        try:
            annotations = conf.get('ingress_controller_service_annotations','')
            annotation_list = annotations.split(',')
            for annotation in annotation_list:
                temp = formatLabel(annotation)
                if temp != '':
                    service_annotations.append(temp)
            return service_annotations
        except Exception:
            logging.error('get_service_annotations err')
            logging.error(traceback.format_exc())
            return []

    def get_service_type(self,conf):
        types = ['ClusterIP', 'NodePort', 'LoadBalancer']
        service_type = 'LoadBalancer'
        try:
            stype = conf.get('ingress_controller_service_type','')
            if stype in types:
                service_type = stype
            return service_type
        except Exception:
            logging.error('get_service_type err')
            logging.error(traceback.format_exc())
            return service_type

    def get_service_ipstacks(self,conf):
        stacks = ['IPv4','IPv6','IPv4&IPv6','IPv6&IPv4']
        service_ipstacks = ['IPv4','IPv6']
        try:
            ipstacks = conf.get('ingress_controller_service_ipstacks','')
            if ipstacks in stacks:
                service_ipstacks = ipstacks.split('&')
            return service_ipstacks
        except Exception:
            logging.error('get_service_ipstacks err')
            logging.error(traceback.format_exc())
            return service_ipstacks

    def get_service_externaltrafficpolicy(self,conf):
        policys = ["Local","Cluster"]
        service_externaltrafficpolicy = 'Cluster'
        try:
            externaltrafficpolicy = conf.get('ingress_controller_service_externaltrafficpolicy','')
            if externaltrafficpolicy in policys:
                service_externaltrafficpolicy = externaltrafficpolicy
            return service_externaltrafficpolicy
        except Exception:
            logging.error('get_service_externaltrafficpolicy err')
            logging.error(traceback.format_exc())
            return service_externaltrafficpolicy

    def get_pod_replicacount(self,conf):
        pod_replicacount = '1'
        try:
            replicacount = conf.get('ingress_controller_pod_replicacount','')
            if replicacount != '' and replicacount.isdigit():
                pod_replicacount = replicacount
            return pod_replicacount
        except Exception:
            logging.error('get_pod_replicacount err')
            logging.error(traceback.format_exc())
            return pod_replicacount

    def get_pod_request_cpu(self,conf):
        pod_request_cpu = '3'
        try:
            request_cpu = conf.get('ingress_controller_pod_request_cpu','')
            if request_cpu != '':
                pod_request_cpu = request_cpu
            return pod_request_cpu
        except Exception:
            logging.error('get_pod_request_cpu err')
            logging.error(traceback.format_exc())
            return pod_request_cpu

    def get_pod_request_mem(self,conf):
        pod_request_mem = '6Gi'
        try:
            request_mem = conf.get('ingress_controller_pod_request_mem','')
            if request_mem != '':
                pod_request_mem = request_mem
            return pod_request_mem
        except Exception:
            logging.error('get_pod_request_mem err')
            logging.error(traceback.format_exc())
            return pod_request_mem

    def get_pod_limit_cpu(self,conf):
        pod_limit_cpu = '3'
        try:
            limit_cpu = conf.get('ingress_controller_pod_limit_cpu','')
            if limit_cpu != '':
                pod_limit_cpu = limit_cpu
            return pod_limit_cpu
        except Exception:
            logging.error('get_pod_limit_cpu err')
            logging.error(traceback.format_exc())
            return pod_limit_cpu

    def get_pod_limit_mem(self,conf):
        pod_limit_mem = '6Gi'
        try:
            limit_mem = conf.get('ingress_controller_pod_limit_mem','')
            if limit_mem != '':
                pod_limit_mem = limit_mem
            return pod_limit_mem
        except Exception:
            logging.error('get_pod_limit_mem err')
            logging.error(traceback.format_exc())
            return pod_limit_mem

    def get_pod_nodeselector(self,conf):
        pod_nodeselector = []
        try:
            nodeselectors = conf.get('ingress_controller_pod_nodeselector','')
            nodeselector_list = nodeselectors.split(',')
            for nodeselector in nodeselector_list:
                temp = formatLabel(nodeselector)
                if temp != '':
                    pod_nodeselector.append(temp)
            return pod_nodeselector
        except Exception:
            logging.error('get_pod_nodeselector err')
            logging.error(traceback.format_exc())
            return pod_nodeselector

    def generate_ingress_controller_confs(self):
        try:
            for conf in self.ingress_controller_deploy_config:
                controller_name = self.get_name(conf)
                if controller_name == '':
                    continue

                ingress_controller = {
                    'name': self.get_name(conf),
                    'namespace': self.get_namespace(conf),
                    'service_annotations': self.get_service_annotations(conf),
                    'service_type': self.get_service_type(conf),
                    'service_ipstacks': self.get_service_ipstacks(conf),
                    'service_externaltrafficpolicy': self.get_service_externaltrafficpolicy(conf),
                    'pod_replicacount': self.get_pod_replicacount(conf),
                    'pod_request_cpu': self.get_pod_request_cpu(conf),
                    'pod_request_mem': self.get_pod_request_mem(conf),
                    'pod_limit_cpu': self.get_pod_limit_cpu(conf),
                    'pod_limit_mem': self.get_pod_limit_mem(conf),
                    'pod_nodeselector': self.get_pod_nodeselector(conf)
                }
                self.ingress_controllers.append(ingress_controller)
        except Exception as e:
            logging.error('error: %s' % e.args[0])
            logging.error(traceback.format_exc())

    def write_ingress_controller_file(self):
        logging.info(self.ingress_controllers)
        logging.info("write to file /etc/pdm/conf/ingress_controllers.json")
        with open("/etc/pdm/conf/ingress_controllers.json", 'w') as f:
            f.write(json.dumps(self.ingress_controllers))

    def sync_ingress_controller_file(self):
        sync_file("/etc/pdm/conf/ingress_controllers.json")

    def start(self):
        logging.info('====write ingress_controllers.json')
        self.generate_ingress_controller_confs()
        self.write_ingress_controller_file()
        if sync_file_flag:
            self.sync_ingress_controller_file()

if __name__ == '__main__':
    parser = argparse.ArgumentParser()
    parser.add_argument("--new_config",
                        help="new configuration file")
    args, _ = parser.parse_known_args()
    source_file = '/etc/network/openpalette.json'
    sync_file_flag = False
    if args.new_config:
        source_file = args.new_config
        sync_file_flag= True
        
    generator = GenerateIngressControllerConf(source_file)
    generator.start()