#!/bin/bash

# shellcheck disable=SC2002
# shellcheck disable=SC2164
# shellcheck disable=SC2046
# shellcheck disable=SC2086
# shellcheck disable=SC2034

rh7=$(cat /etc/os-release | grep -w "^REDHAT_BUGZILLA_PRODUCT" |grep "Linux 7")
rh8=$(cat /etc/os-release | grep -w "^REDHAT_BUGZILLA_PRODUCT" |grep "Linux 8")
cgsl5=$(cat /etc/os-release | grep -w "^VERSION_ID" |grep "5.0" )
cgsl6=$(cat /etc/os-release | grep -w "^VERSION_ID" |grep "6.0" )
version_id=$(cat /etc/os-release | grep -w "^VERSION_ID" | cut -d "=" -f 2)

if [[ -d /var/tcf_uninstall/config/os_rpm ]];then
    cat << EOF > /var/tcf_uninstall/config/os_rpm/os-pkg-repo_black.list
ansible-collection-ansible-netcommon
ansible-collection-ansible-posix
ansible-collection-ansible-utils
ansible-collection-community-docker
ansible-collection-community-general
ansible-core
EOF
fi

cd $(dirname $0)
if [[ -n "$cgsl5" ]] || [[ -n "$rh7" ]] || [[ "$version_id" == '"7"' ]];then
    rpm -Uvh --force ansible-2.9.27-1.el7.cgslv5.0.5.g5a9b3ed.lite.noarch.rpm python2-jmespath-0.9.0-3.el7.noarch.rpm &> /tmp/force_install_ansible_print.log
else
    if command -v pythonlatest &>/dev/null; then
        rpm -Uvh --force --nodeps 2.15.3/ansible-*.rpm &> /tmp/force_install_ansible_print.log
        rpm -qa | grep "^ansible-2" | xargs -r rpm -e
    else
        rpm -Uvh --force ansible-2.9.27-1.zncgsl6.t0.0.3.g25864f6c.noarch.rpm &> /tmp/force_install_ansible_print.log
    fi
fi
result=$?
cat /tmp/force_install_ansible_print.log | grep -v 'warning:'
rm -f /tmp/force_install_ansible_print.log
if [[ $result != 0 ]]; then
    echo "sync ansible version failed"
    exit 1
fi
