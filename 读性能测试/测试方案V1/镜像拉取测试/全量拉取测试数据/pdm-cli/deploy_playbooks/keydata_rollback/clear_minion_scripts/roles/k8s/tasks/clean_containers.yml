---
- name: get docker containers
  shell: docker ps -a |grep -v iag |grep -v kube |awk 'NR == 1 {next} {print $1}'
  register: containers_result

- name: clean docker containers created by kubernetes
  shell: >-
    docker container inspect {{ item }}|tee /tmp/{{ item }}.kdttmp | grep io.kubernetes.pod.uid > /dev/null &&
    grep '"Id":' /tmp/{{ item }}.kdttmp | awk -F":" '{print $2}'|awk -F'"' '{print $2}'|
    xargs -r docker rm -fv; rm -f /tmp/{{ item }}.kdttmp
  with_items: "{{containers_result.stdout_lines|default([])}}"

