- name: debug message
  debug:
    msg: "{{ inventory_hostname }} is_master=yes"
  when: is_master|string == "yes"

- name: Delete kube cron
  shell: >-
    crontab -r -u kube || true

- name: Delete kubelet.dynamicConfig and kubelet.kubeconfig
  shell: >-
    rm -rf /etc/kubernetes/kubelet.dynamicConfig* /etc/kubernetes/kubelet.kubeconfig /etc/kubernetes/kube_cron

- name: Add --register-node=false to the kubelet config file
  shell: sed -ri '/--register-node=false/!{s/--config/--register-node=false --config/g}' /etc/kubernetes/kubelet
  ignore_errors: true

- name: stop kubelet service
  shell: systemctl stop kubelet
  ignore_errors: true

- name: execute command umount
  shell: mount | grep /paasdata/docker/pods | awk -F ' ' '{print $3}' | xargs -rI {} umount {}

- include: clean_containers.yml
