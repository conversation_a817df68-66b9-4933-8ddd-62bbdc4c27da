- name: debug message
  debug:
    msg: "{{ inventory_hostname }} is_master=no"
  when: is_master|string == "no"

- name: Delete k8s binary and link file
  shell: >-
    rm -rf /paasdata/op-data/k8s-bin /usr/bin/kube*

- name: Delete kube cron
  shell: >-
    crontab -r -u kube || true

- name: Delete files or directories
  file:
    path: "{{ item }}"
    state: absent
  with_items:
    - "/paasdata/op-log/k8s"
    - "/paasdata/op-conf/k8s"
    - "/etc/kubernetes"
    - "/etc/systemd/system/kubelet.service"
    - "/paasdata/paas_upgrade_backup/kdt_backup"

- name: stop kubelet service
  shell: systemctl stop kubelet
  ignore_errors: true

- name: execute command umount
  shell: mount | grep /paasdata/docker/pods | awk -F ' ' '{print $3}' | xargs -rI {} umount {}

- name: remove /paasdata/docker/pods
  file: path=/paasdata/docker/pods state=absent

- name: clean systemd config files and service files
  shell: >-
    rm -rf /etc/systemd/system/system.conf.d/kubernetes-accounting.conf /var/lib/kubelet \
      /etc/systemd/system/kubelet.service || true

- name: delete k8s binary and link file
  shell: >-
    rm -rf /paasdata/op-data/k8s-bin /usr/bin/kube*

- include: clean_containers.yml
