---
- hosts: nodes
  remote_user: ubuntu
  become: yes
  become_method: sudo
  gather_facts: no
  max_fail_percentage: 0
  vars:
    - bak_dir: /paasdata/op-data/paas_upgrade_backup_keydata
  tasks:
    - name: delete paas_upgrade_backup_keydata dir
      file:
        path: /paasdata/op-data/paas_upgrade_backup_keydata
        state: absent
      tags: del_bak

    - name: create paas_upgrade_backup_keydata dir
      file:
        path: "{{ item }}"
        state: directory
        mode: 0750
      with_items:
        - "{{ bak_dir }}"
        - "/paasdata/op-log/pdm-cli"
      tags: bak_prepare

    - name: create paas_upgrade_backup_keydata item dir
      file:
        path: "{{ bak_dir }}/{{ item }}"
        state: directory
        mode: 0750
        recurse: yes
      with_items:
        - node_info/cluster
        - resource_reg/new
        - resource_reg/old
        - postgresql
        - k8s_etcd
        - filedir
        - sysconfig
        - rpm
        - pacemaker
        - os_pkg_repo
        - scaleout_k8s_cfg
        - misc
        - msb
        - image
        - op-plcm-carlina
      tags: bak_prepare

    - name: sync script dir to host
      synchronize:
        src: /etc/pdm/deploy_playbooks/keydata_rollback/keydata_scripts
        dest: /paasdata/op-tmp/
      tags: bak_prepare

    - name: add 0750 mode for /paasdata/op-tmp/keydata_scripts
      file:
        path: /paasdata/op-tmp/keydata_scripts
        state: directory
        mode: 0750
      tags: bak_prepare

    - name: sync all_node_role.list
      synchronize:
        src: "{{ bak_dir }}/node_info/all_node_role.list"
        dest: "{{ bak_dir }}/node_info/all_node_role.list"
      tags: cur_node_role

    - name: generate cur_node_role.list
      shell: |
        if [ -f "/paasdata/op-tmp/keydata_scripts/gen_cur_info_list.sh" ]; then
            sh /paasdata/op-tmp/keydata_scripts/gen_cur_info_list.sh {{ ansible_ssh_host }} "all_node_role.list" "current_node_role.list"
        fi
      tags: cur_node_role

    - name: sync no_instance_compo.list
      synchronize:
        src: "{{ bak_dir }}/node_info/no_instance_compo.list"
        dest: "{{ bak_dir }}/node_info/no_instance_compo.list"
      tags: cur_node_compo

    - name: sync all_nodes_compo_tmp.list
      synchronize:
        src: "{{ bak_dir }}/node_info/all_nodes_compo_tmp.list"
        dest: "{{ bak_dir }}/node_info/all_node_component.list"
      tags: cur_node_compo

    - name: generate current_node_component.list
      shell: |
        if [ -f "/paasdata/op-tmp/keydata_scripts/gen_cur_info_list.sh" ];then
            sh /paasdata/op-tmp/keydata_scripts/gen_cur_info_list.sh {{ ansible_ssh_host }} "all_node_component.list" "current_node_component.list"
        fi
      tags: cur_node_compo

    - name: sync all_node_subdomain.list
      synchronize:
        src: "{{ bak_dir }}/node_info/all_node_subdomain.list"
        dest: "{{ bak_dir }}/node_info/all_node_subdomain.list"
      tags: cur_node_old_subdomain

    - name: generate current_node_subdomain_old.list
      shell: |
        if [ -f "/paasdata/op-tmp/keydata_scripts/gen_cur_info_list.sh" ]; then
            sh /paasdata/op-tmp/keydata_scripts/gen_cur_info_list.sh {{ ansible_ssh_host }} "all_node_subdomain.list" "current_node_subdomain_old.list"
        fi
      tags: cur_node_old_subdomain

    - name: sync all_node_subdomain.list
      synchronize:
        src: "{{ bak_dir }}/node_info/all_node_subdomain.list"
        dest: "{{ bak_dir }}/node_info/all_node_subdomain.list"
      tags: cur_node_new_subdomain

    - name: generate current_node_subdomain_new.list
      shell: |
        if [ -f "/paasdata/op-tmp/keydata_scripts/gen_cur_info_list.sh" ]; then
            sh /paasdata/op-tmp/keydata_scripts/gen_cur_info_list.sh {{ ansible_ssh_host }} "all_node_subdomain.list" "current_node_subdomain_new.list"
        fi
      tags: cur_node_new_subdomain

    - name: sync resource registry file
      synchronize:
        src: "/etc/{{ item.from_pdm }}/deploy_playbooks/keydata_rollback/resource_reg/./"
        dest: "{{ bak_dir }}/resource_reg/{{ item.dest_dir }}"
      with_items:
        - {from_pdm: 'pdm', dest_dir: 'new'}
        - {from_pdm: 'pdm_bak', dest_dir: 'old'}
      tags: res_reg_bak

    - name: backup file and dir
      shell: |
        if [ -f "/paasdata/op-tmp/keydata_scripts/scripts_call.sh" ];then
            sh /paasdata/op-tmp/keydata_scripts/scripts_call.sh "backup_filedir.py" "res_filedir.yml"
        fi
      tags: file_dir_bak

    - name: backup filedir extra
      shell: |
        if [ -f "/paasdata/op-tmp/keydata_scripts/scripts_call.sh" ];then
            sh /paasdata/op-tmp/keydata_scripts/scripts_call.sh "backup_filedir.py" "res_filedir_extra.yml"
        fi
      tags: file_dir_extra_bak

    - name: backup filedir kubeall
      shell: |
        if [ -f "/paasdata/op-tmp/keydata_scripts/scripts_call.sh" ];then
            sh /paasdata/op-tmp/keydata_scripts/scripts_call.sh "backup_filedir.py" "res_filedir_kubeall.yml"
        fi
      tags: file_dir_kubeall_bak

    - name: backup sysconfig
      shell: |
        if [ -f "/paasdata/op-tmp/keydata_scripts/scripts_call.sh" ];then
            sh /paasdata/op-tmp/keydata_scripts/scripts_call.sh "backup_sysconfig.py"
        fi
      tags: sysconfig_bak

    - name: backup component misc
      shell: |
        if [ -f "/paasdata/op-tmp/keydata_scripts/scripts_call.sh" ];then
            sh /paasdata/op-tmp/keydata_scripts/scripts_call.sh "backup_restore_components_misc.py" "backup"
        fi
      tags: compo_misc_bak

    - name: backup scaleout k8s cfg
      shell: |
        if [ -d "/paasdata/op-data/paas_upgrade_backup_keydata/scaleout_k8s_cfg/" ];then
            \cp -f /etc/kubernetes/kubelet /paasdata/op-data/paas_upgrade_backup_keydata/scaleout_k8s_cfg/
        fi
      when: is_master|bool
      tags: backup_scaleout_k8s_cfg

    - name: backup scaleout k8s cfg
      shell: |
        if [ -d "/paasdata/op-data/paas_upgrade_backup_keydata/scaleout_k8s_cfg/" ];then
            \cp -f  /etc/systemd/system/kubelet.service /paasdata/op-data/paas_upgrade_backup_keydata/scaleout_k8s_cfg/
        fi
      when: is_master|bool
      tags: backup_scaleout_k8s_cfg

    - name: backup carlina DB
      shell: |
        if [ -f "/paasdata/op-data/op-plcm-carlina/carlina.db" ];then
            \cp -f /paasdata/op-data/op-plcm-carlina/carlina.db "{{ bak_dir }}"/op-plcm-carlina/carlina.db
        fi
      tags: backup_carlina_db
