---
- hosts: nodes
  remote_user: ubuntu
  become: yes
  become_method: sudo
  gather_facts: no
  max_fail_percentage: 0
  vars:
    - bak_dir: /paasdata/op-data/paas_upgrade_backup_keydata
  tasks:
    - name: stop opslet service
      shell: sh /paasdata/op-tmp/keydata_scripts/scripts_call.sh
               "restore_stop_spc.py" "res_service.yml" "stop" "opslet"
      tags: stop_opslet

    - name: stop etcd service
      shell: sh /paasdata/op-tmp/keydata_scripts/scripts_call.sh
               "restore_stop_spc.py" "res_service.yml" "stop" "etcd"
      tags: stop_etcd

    - name: stop docker service
      shell: sh /paasdata/op-tmp/keydata_scripts/scripts_call.sh
               "restore_stop_spc.py" "res_service.yml" "stop" "docker"
      tags: stop_docker

    - name: stop containerd service
      shell: sh /paasdata/op-tmp/keydata_scripts/scripts_call.sh
        "restore_stop_spc.py" "res_service.yml" "stop" "op-containers-containerd"
      tags: stop_op-containers-containerd

    - name: stop service
      shell: sh /paasdata/op-tmp/keydata_scripts/scripts_call.sh
               "restore_stop_spc.py" "res_service.yml" "stop"
      tags: stop_spc

    - name: stop process
      shell: sh /paasdata/op-tmp/keydata_scripts/scripts_call.sh
               "restore_stop_spc.py" "res_process.yml" "stop"
      tags: stop_spc

    - name: stop container
      shell: sh /paasdata/op-tmp/keydata_scripts/scripts_call.sh
               "restore_stop_spc.py" "res_indep_container.yml" "stop"
      tags: stop_spc

    - name: stop special container
      shell: sh /paasdata/op-tmp/keydata_scripts/scripts_call.sh
               "restore_stop_spc.py" "res_special_container.yml" "stop"
      run_once: true
      tags: stop_special_container

    - name: restore rpm
      shell: sh /paasdata/op-tmp/keydata_scripts/scripts_call.sh
               "restore_clear_scripts.py" "res_rpm.yml"
      tags: res_rpm

    - name: restore filedir
      shell: sh /paasdata/op-tmp/keydata_scripts/scripts_call.sh
               "restore_clear_scripts.py" "res_filedir.yml"
      tags: res_filedir

    - name: restore filedir_extra
      shell: sh /paasdata/op-tmp/keydata_scripts/scripts_call.sh
        "restore_clear_scripts.py" "res_filedir_extra.yml"
      tags: res_filedir_extra

    - name: restore filedir_kubeall
      shell: sh /paasdata/op-tmp/keydata_scripts/scripts_call.sh
        "restore_clear_scripts.py" "res_filedir_kubeall.yml"
      tags: res_filedir_kubeall

    - name: restore filedir
      shell: systemctl daemon-reload
      tags: res_filedir

    - name: restore sysconfig
      shell: sh /paasdata/op-tmp/keydata_scripts/scripts_call.sh
               "restore_clear_scripts.py" "res_sysconfig.yml"
      tags: res_sysconfig

    - name: restore components misc
      shell: sh /paasdata/op-tmp/keydata_scripts/scripts_call.sh
               "backup_restore_components_misc.py" "restore"
      tags: restore_compo_misc

    - name: restore service
      shell: sh /paasdata/op-tmp/keydata_scripts/scripts_call.sh
               "restore_stop_spc.py" "res_service.yml" "restore"
      tags: restore_spc

    - name: restore process
      shell: sh /paasdata/op-tmp/keydata_scripts/scripts_call.sh
               "restore_stop_spc.py" "res_process.yml" "restore"
      tags: restore_spc

    - name: restore container
      shell: sh /paasdata/op-tmp/keydata_scripts/scripts_call.sh
               "restore_stop_spc.py" "res_indep_container.yml" "restore"
      tags: restore_spc

    - name: restore opslet
      shell: sh /paasdata/op-tmp/keydata_scripts/scripts_call.sh
               "restore_stop_spc.py" "res_service.yml" "restore" "opslet"
      tags: restore_opslet

    - name: resotre etcd service
      shell: sh /paasdata/op-tmp/keydata_scripts/scripts_call.sh
               "restore_stop_spc.py" "res_service.yml" "restore" "etcd"
      tags: restore_etcd

    - name: restore docker service
      shell: sh /paasdata/op-tmp/keydata_scripts/scripts_call.sh
               "restore_stop_spc.py" "res_service.yml" "restore" "docker"
      tags: restore_docker

    - name: restore containerd service
      shell: sh /paasdata/op-tmp/keydata_scripts/scripts_call.sh
        "restore_stop_spc.py" "res_service.yml" "restore" "op-containers-containerd"
      tags: restore_op-containers-containerd

    - name: decouple nfs
      shell: if [ -f "/etc/sysconfig/umount_nfs_mp.sh" ]; then sh /etc/sysconfig/umount_nfs_mp.sh ;fi
      tags: decouple_nfs

    - name: restore scaleout k8s cfg
      shell: \cp -f /paasdata/op-data/paas_upgrade_backup_keydata/scaleout_k8s_cfg/kubelet /etc/kubernetes/kubelet
      when: is_master|bool
      tags: restore_scaleout_k8s_cfg

    - name: restore scaleout k8s cfg
      shell: \cp -f /paasdata/op-data/paas_upgrade_backup_keydata/scaleout_k8s_cfg/kubelet.service /etc/systemd/system/kubelet.service
      when: is_master|bool
      tags: restore_scaleout_k8s_cfg

    - name: restore op-plcm-carlina DB
      shell: |
        if [ -f "{{ bak_dir }}/op-plcm-carlina/carlina.db" ];then
            \cp -f "{{ bak_dir }}"/op-plcm-carlina/carlina.db /paasdata/op-data/op-plcm-carlina/carlina.db
        fi
      tags: restore_carlina_db
