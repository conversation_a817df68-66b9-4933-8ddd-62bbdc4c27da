import os
import sys
import yaml


def get_from_file(filepath, varname):
    if os.path.exists(filepath):
        f = open(filepath)
        dataMap = yaml.safe_load(f)
        f.close()
        for data in dataMap:
            if varname in data:
                return data[varname]
        return None
    else:
        return None


def is_new_fusionized_TCF():
    comvar_path = "/root/common/com_vars.yml"
    tcf_scenario = get_from_file(comvar_path, "tcf_scenario")
    is_new_fusionized_TCF = get_from_file(comvar_path, "is_new_fusionized_TCF")
    return True if tcf_scenario == "fusionized-TCF" and \
        is_new_fusionized_TCF else False


def item_in_file(item, path):
    with open(path, "r") as f:
        lines = f.readlines()
        for i in range(0, len(lines)):
            if item == lines[i].strip():
                return True
            continue
        return False


def check_roles_in_file(roles, role_path):
    for role in roles:
        if role == "usednodes":
            return True
        if item_in_file(role, role_path):
            return True
    return False


def check_subdomain_in_file(roles, subdomain):
    bak_dir = "/paasdata/op-data/paas_upgrade_backup_keydata/node_info/"
    subdomain_dir = bak_dir + "current_node_subdomain_old.list"
    if item_in_file(subdomain, subdomain_dir):
        return True
    return False


def node_has_resource(judge_method, roles, compo_owner, res_file, subdomain):
    bak_dir = "/paasdata/op-data/paas_upgrade_backup_keydata/node_info/"
    role_path = bak_dir + "current_node_role.list"
    compo_path = bak_dir + "current_node_component.list"
    no_int_compo_path = bak_dir + "no_instance_compo.list"
    no_int_type = ["res_process.yml", "res_service.yml",
                   "res_indep_container.yml"]
    if res_file in no_int_type and compo_owner != "":
        if item_in_file(compo_owner, no_int_compo_path):
            return False
    if judge_method == "by_component":
        if item_in_file(compo_owner, compo_path):
            return True
        else:
            return False
    elif judge_method == "by_role":
        is_new_fusionized_tcf = is_new_fusionized_TCF()
        if subdomain and is_new_fusionized_tcf:
            for role in roles:
                if role != "paas_controller":
                    continue
                if check_subdomain_in_file(roles, subdomain):
                    return True
        if check_roles_in_file(roles, role_path):
            return True
        return False
    else:
        return False


def main():
    res_file = sys.argv[1]
    res_filedir = ("/paasdata/op-data/paas_upgrade_backup_keydata"
                   "/resource_reg/old/" + res_file)
    with open(res_filedir, "r") as f:
        dataMap = yaml.safe_load(f)
    for data in dataMap:
        judge_method = data.get("judge_method", "")
        roles = data.get("roles", "")
        compo_owner = data.get("compo_owner", "")
        res_name = data.get("res_name", "")
        subdomain = data.get("subdomain", "")
        if not node_has_resource(judge_method, roles,
                                 compo_owner, res_file, subdomain):
            continue
        inclu_dir = " ".join(data.get("inclu_dir", ""))
        exclu_dir = " ".join(data.get("exclu_dir", ""))
        shell_cmd = \
            ('bash /paasdata/op-tmp/keydata_scripts/backup_filedir.sh ' +
             '"' + inclu_dir + '" ' +
             '"' + exclu_dir + '" ' +
             '"' + res_name + '"')
        if "inclu_big_subdir" in data:
            inclu_big_subdir = " ".join(data.get("inclu_big_subdir", ""))
            shell_cmd += ' "' + inclu_big_subdir + '"'
        else:
            shell_cmd += ' ""'
        result = os.system(shell_cmd + ' >/dev/null')
        if result != 0:
            print("backup filedir script failed!")
            return False
    return True


if __name__ == "__main__":
    if not main():
        exit(1)
    exit(0)
