#!/bin/bash

log_dir="/paasdata/op-log/pdm-cli"
big_dir_threshold=3072
acl_exclude_dir=("/paasdata/op-data/op-ubs-python3lib")
filedir_tmp="/paasdata/op-tmp/keydata_scripts/filedir_tmp"

function log_info(){
    local log_date=
    local log_file_name="$log_dir/keydata_rollback.log"
    log_date=$(date "+%Y-%m-%d %H:%M:%S")
    echo "$log_date $1 [$2] - $3" >> $log_file_name
    chmod 640 $log_file_name
}

timeout_func(){
    local l_time=$1
    shift
    local rc=0
    if [ -x "/usr/bin/timeout" ]; then
        TIMEOUT="/usr/bin/timeout $l_time"
    else
        TIMEOUT=""
    fi
    $TIMEOUT "${@}"
    rc=$?
    if [ $rc -eq 124 ]; then
        log_info "INFO" "backup_filedir.sh cmd timeout $l_time: " "${@}"
        echo "cmd timeout $l_time: " "${@}" >&2
        kill -9 $$
    fi
    return $rc
}

function is_empty_dir(){
    local dir=$1
    if [ "$(timeout_func 120 ls -A "$dir")" = "" ]; then
        return 0
    else
        return 1
    fi
}

function is_big_dir(){
    local l_sub_full_dir=$1
    local l_is_big_dir=1
    local l_skip_judge=false
    local sub_du=
    local arr_big_dir=
    for ex_dir in "${exclu_list[@]}"
    do
        if [[ $ex_dir/ =~ $l_sub_full_dir/ ]]; then
            l_skip_judge=true
            break
        fi
    done
    if ! $l_skip_judge; then
        # shellcheck disable=SC2206
        arr_big_dir=($inclu_big_subdir)
        for big_dir in "${arr_big_dir[@]}"
        do
            if [[ $big_dir/ =~ $l_sub_full_dir/ ]]; then
                l_skip_judge=true
                break
            fi
        done
    fi
    if ! $l_skip_judge; then
        sub_du=$(timeout_func 1800 du -m -s "$l_sub_full_dir" | awk '{print $1}')
        if [ -z "$sub_du" ] || [ "$sub_du" -gt "$big_dir_threshold" ]; then
            echo "$l_sub_full_dir" >> "$exclu_big_dir_list"
            l_is_big_dir=0
        fi
    fi
    return "$l_is_big_dir"
}

if [ ! -e $log_dir ]; then
    timeout_func 120 mkdir -m 750 $log_dir
fi

log_info "INFO" "backup_filedir.sh" "inclu_dir:$1, exclu_dir:$2, res_name:$3, inclu_big_subdir:$4"

inclu_dir=$1
exclu_dir=$2
res_name=$3
inclu_big_subdir=$4
base_dir="/paasdata/op-data/paas_upgrade_backup_keydata/filedir"
res_dir="$base_dir/$res_name"
exclu_big_dir_list="$res_dir/exclu_big_dir_list"

if [ ! -e "$res_dir" ]; then
    mkdir -p "$res_dir"
fi

# shellcheck disable=SC2206
exclu_list=($exclu_dir)
# shellcheck disable=SC2206
inclu_list=($inclu_dir)

timeout_func 1200 rm -rf "$exclu_big_dir_list"
for dir in "${inclu_list[@]}"
do
    if [ -d "$dir" ]; then
        if is_empty_dir "$dir"; then
            if ! timeout_func 1200 cp -r -f --parents --preserve=all "$dir" "$res_dir"; then
                log_info "ERROR" "backup_filedir.sh" "$dir copy to $res_dir failed!"
            fi
            continue
        fi
        shopt -s dotglob
        for sub_full_dir in "$dir"/*
        do
            if is_big_dir "$sub_full_dir"; then
                continue
            fi
            can_full_copy=true
            for ex_dir in "${exclu_list[@]}"
            do
                if [[ $ex_dir/ =~ ^$sub_full_dir/ ]]; then
                    can_full_copy=false
                    break
                fi
            done
            if $can_full_copy; then
                if ! timeout_func 3600 cp -r -f --parents --preserve=all "$sub_full_dir" "$res_dir"; then
                    log_info "ERROR" "backup_filedir.sh" "$sub_full_dir copy to $res_dir failed!"
                fi
                continue
            else
                grep_cmd=""
                for exc_dir in "${exclu_list[@]}"
                do
                    grep_cmd="$grep_cmd -e $exc_dir"
                done
                if [ "$grep_cmd" != "" ]; then
                    # shellcheck disable=SC2086
                    timeout_func 1800 find "$sub_full_dir" | grep -w -v $grep_cmd > $filedir_tmp
                else
                    # shellcheck disable=SC2086
                    timeout_func 1800 find "$sub_full_dir" > $filedir_tmp
                fi
                if [ -e "$filedir_tmp" ]; then
                    # shellcheck disable=SC2162
                    while read dir_line
                    do
                        if [ ! -d "$dir_line" ] && [ ! -f "$dir_line" ] && [ ! -L "$dir_line" ]; then
                            log_info "WARNING" "backup_filedir.sh" "$dir_line is not dir or not file or not symbol link , it is skipped!"
                            continue
                        fi
                        if [ -d "$dir_line" ] && ! is_empty_dir "$dir_line"; then
                            continue
                        fi
                        if ! timeout_func 1200 cp -r -f --parents --preserve=all "$dir_line" "$res_dir"; then
                            log_info "ERROR" "backup_filedir.sh" "$dir_line copy to $res_dir failed!"
                        fi
                    done < "$filedir_tmp"
                    timeout_func 1200 rm -rf "$filedir_tmp"
                fi
            fi
        done
    fi
    if [ -f "$dir" ]; then
        if ! timeout_func 1200 cp -r -f --parents --preserve=all "$dir" "$res_dir"; then
            log_info "ERROR" "backup_filedir.sh" "$dir copy to $res_dir failed!"
        fi
    fi
done

grep_acl_cmd=""
for acl_exclu_dir in "${acl_exclude_dir[@]}"
do
    grep_exclu_dir=${acl_exclu_dir#*/}
    grep_acl_cmd="$grep_acl_cmd -e $grep_exclu_dir"
done

timeout_func 120 cd $base_dir
# shellcheck disable=SC2164
cd $base_dir
# shellcheck disable=SC2086
timeout_func 1800 find $res_name/ | grep -w -v $grep_acl_cmd > $filedir_tmp
if [ -e "$filedir_tmp" ]; then
    # shellcheck disable=SC2162
    while read dir
    do
        org_dir=${dir#*/}
        if [ "$org_dir" == "" ] || [ "$org_dir" == "exclu_big_dir_list" ] || \
        [ "$org_dir" == "symbol_link_dir_list" ]; then
            continue
        fi
        spe_acl=$(timeout_func 120 getfacl -p "/$org_dir" | grep ":..*:" | grep -v "^#")
        if [ "$spe_acl" == "" ]; then
            continue
        fi
        acl_args=""
        # shellcheck disable=SC2206
        array=($spe_acl)
        for acl in "${array[@]}"
        do
            acl_args="$acl_args -m $acl"
        done
        log_info "INFO" "backup_filedir.sh" "/$org_dir acl args are $acl_args"
        full_path="$base_dir/$dir"
        log_info "INFO" "backup_filedir.sh" "setfacl $acl_args $full_path"
        # shellcheck disable=SC2086
        timeout_func 120 setfacl $acl_args "$full_path"
    done < "$filedir_tmp"
    timeout_func 1200 rm -rf $filedir_tmp
fi
