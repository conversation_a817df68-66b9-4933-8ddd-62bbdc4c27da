import os
import sys
import yaml
import subprocess
import time

def item_in_file(item, path):
    with open(path, "r") as f:
        lines = f.readlines()
        for i in range(0, len(lines)):
            if item == lines[i].strip():
                return True
            continue
        return False


def check_roles_in_file(roles, role_path):
    for role in roles:
        if role == "usednodes":
            return True
        if item_in_file(role, role_path):
            return True
    return False


def node_has_resource(judge_method, roles, compo_owner, res_file):
    bak_dir = "/paasdata/op-data/paas_upgrade_backup_keydata/node_info/"
    role_path = bak_dir + "current_node_role.list"
    compo_path = bak_dir + "current_node_component.list"
    no_int_compo_path = bak_dir + "no_instance_compo.list"
    no_int_type = ["res_process.yml", "res_service.yml",
                   "res_indep_container.yml"]
    if res_file in no_int_type and compo_owner != "":
        if item_in_file(compo_owner, no_int_compo_path):
            return False
    if judge_method == "by_component":
        if item_in_file(compo_owner, compo_path):
            return True
        else:
            return False
    elif judge_method == "by_role":
        if check_roles_in_file(roles, role_path):
            return True
        return False
    else:
        return False


def get_shell_cmd(cmd, prefix):
    file_name = cmd.split(' ')[0]
    file_path = prefix + "/" + file_name
    if (os.path.exists(file_path) and
            os.path.isfile(file_path)):
        shell_cmd = "bash " + file_path
    else:
        shell_cmd = cmd
    return shell_cmd


def call_cmd(res_filedir, cmd_key):
    res_file = "components_misc.yml"
    prefix = os.path.dirname(res_filedir)
    log_file = "/paasdata/op-log/pdm-cli/keydata_rollback.log"
    with open(res_filedir, "r") as f:
        dataMap = yaml.safe_load(f)
    for data in dataMap:
        judge_method = data.get("judge_method", "")
        roles = data.get("roles", "")
        compo_owner = data.get("compo_owner", "")
        cmd = data.get(cmd_key, "")
        if not node_has_resource(judge_method, roles,
                                 compo_owner, res_file):
            continue
        shell_cmd = get_shell_cmd(cmd, prefix)
        if shell_cmd == "":
            continue
        cmd_1 = ["bash", "-c", shell_cmd]
        cmd_result = subprocess.Popen(cmd_1, stderr=subprocess.PIPE)
        _, error = cmd_result.communicate()
        return_code = cmd_result.returncode
        if return_code != 0:
            date_time = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(int(time.time())))
            write_log = '%s ERROR %s' % (date_time, bytes.decode(error))
            with open(log_file, 'a+') as f:
                f.write(write_log + "\n")
            print('exec cmd %s failed!' % shell_cmd)
            return False
        os.chmod(log_file, 0o640)
    return True


def main():
    cmd_type = sys.argv[1]
    res_file = "components_misc.yml"
    keydata_bak_dir = \
        "/paasdata/op-data/paas_upgrade_backup_keydata/resource_reg"
    old_res = keydata_bak_dir + "/old/" + res_file
    new_res = keydata_bak_dir + "/new/" + res_file
    if cmd_type == "backup":
        if os.path.exists(old_res):
            if not call_cmd(old_res, "backup_cmd"):
                return False
    if cmd_type == "restore":
        if not call_cmd(new_res, "clear_cmd"):
            return False
        if os.path.exists(old_res):
            if not call_cmd(old_res, "restore_cmd"):
                return False
    return True


if __name__ == "__main__":
    if not main():
        exit(1)
    exit(0)
