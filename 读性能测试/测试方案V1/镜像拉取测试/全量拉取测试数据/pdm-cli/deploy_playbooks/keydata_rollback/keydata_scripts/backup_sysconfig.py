import os
import yaml
import subprocess
import time


def item_in_file(item, path):
    with open(path, "r") as f:
        lines = f.readlines()
        for i in range(0, len(lines)):
            if item == lines[i].strip():
                return True
            continue
        return False


def check_roles_in_file(roles, role_path):
    for role in roles:
        if role == "usednodes":
            return True
        if item_in_file(role, role_path):
            return True
    return False


def node_has_resource(judge_method, roles, compo_owner, res_file):
    bak_dir = "/paasdata/op-data/paas_upgrade_backup_keydata/node_info/"
    role_path = bak_dir + "current_node_role.list"
    compo_path = bak_dir + "current_node_component.list"
    no_int_compo_path = bak_dir + "no_instance_compo.list"
    no_int_type = ["res_process.yml", "res_service.yml",
                   "res_indep_container.yml"]
    if res_file in no_int_type and compo_owner != "":
        if item_in_file(compo_owner, no_int_compo_path):
            return False
    if judge_method == "by_component":
        if item_in_file(compo_owner, compo_path):
            return True
        else:
            return False
    elif judge_method == "by_role":
        if check_roles_in_file(roles, role_path):
            return True
        return False
    else:
        return False


def write_stderr_to_log(log_file, cmd):
    cmd_1 = ["bash", "-c", cmd]
    cmd_result = subprocess.Popen(cmd_1, stderr=subprocess.PIPE)
    _, error = cmd_result.communicate()
    return_code = cmd_result.returncode
    if return_code != 0:
        date_time = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(int(time.time())))
        write_log = '%s ERROR %s' % (date_time, bytes.decode(error))
        with open(log_file, 'a+') as f:
            f.write(write_log + "\n")
        return 1
    return 0


def copy_config(config_path, res_name):
    pre_src = os.path.dirname(config_path)
    bkup_dir = "/paasdata/op-data/paas_upgrade_backup_keydata/sysconfig/"
    dest_dir = bkup_dir + res_name + pre_src
    log_file = "/paasdata/op-log/pdm-cli/keydata_rollback.log"
    if not os.path.exists(dest_dir):
        mkdir_cmd = "mkdir -p " + dest_dir
        result = write_stderr_to_log(log_file, mkdir_cmd)
        if result != 0:
            print('mkdir %s failed!', mkdir_cmd)
            return False
    cmd = "cp -r -f --preserve=all " + config_path + " " + dest_dir
    result = write_stderr_to_log(log_file, cmd)
    if result != 0:
        print('copy file failed! \n%s' % cmd)
        return False
    os.chmod(log_file, 0o640)
    return True


def main():
    res_file = "res_sysconfig.yml"
    res_sysconfig = ("/paasdata/op-data/paas_upgrade_backup_keydata"
                     "/resource_reg/old/res_sysconfig.yml")
    with open(res_sysconfig, "r") as f:
        dataMap = yaml.safe_load(f)
    for data in dataMap:
        judge_method = data.get("judge_method", "")
        roles = data.get("roles", "")
        compo_owner = data.get("compo_owner", "")
        config_path = data.get("config_path", "")
        res_name = data.get("res_name", "")
        if not node_has_resource(judge_method, roles,
                                 compo_owner, res_file):
            continue
        if not copy_config(config_path, res_name):
            return False
    return True


if __name__ == "__main__":
    if not main():
        exit(1)
    exit(0)
