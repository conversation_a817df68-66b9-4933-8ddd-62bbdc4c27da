---
- hosts: nodes
  remote_user: ubuntu
  become: True
  become_method: sudo
  gather_facts: no
  tasks:
    - name: check {{daisy_scripts}} exist
      shell: ls {{daisy_scripts}}
      ignore_errors: True
      register: result
    - name: clear daisy resource
      shell: |
          if command -v pythonlatest &> /dev/null; then
            pythonlatest /usr/bin/clean_daisy_for_cpaas.py
          else
            python /usr/bin/clean_daisy_for_cpaas.py
          fi
      when: result is succeeded
