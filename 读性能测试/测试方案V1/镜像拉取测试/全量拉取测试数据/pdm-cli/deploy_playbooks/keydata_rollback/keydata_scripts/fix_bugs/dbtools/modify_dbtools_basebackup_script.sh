#!/bin/bash
# shellcheck disable=SC2002,SC2034,SC2181,SC2086
# ----------------------------------------------------------------------
# name:         modify_dbtools_basebackup_script.sh
# version:      0.1
# createTime:   2020-03-23
# description:  修改dbtools的basebackup脚本
# author:
# wiki:
# ----------------------------------------------------------------------

log_info() {
    LOG_DATE=$(date "+%Y-%m-%d %H:%M:%S")
    LOG_FILENAME=""
    PID_INFO=$$

    echo "$LOG_DATE [$PID_INFO] ${FUNCNAME[*]} - $1 $2"
}

check_env() {
    $TIMEOUT crm_resource -r op-ubs-dbtools -W
    if [ $? -ne 0 ]; then
        log_info info "this environment don't need  modify dbtools basebackup script"
        exit 0
    fi
}

get_local_node_name() {
    local_node=$($TIMEOUT crm_node -n)
    if [ $? -ne 0 ]; then
        log_info error "get local node name failed"
        exit 1
    fi
    if [ -z "$local_node" ]; then
        log_info error "local node name is empty"
        exit 2
    fi

    log_info info "local_node is $local_node"
}

get_dbtools_running_node() {
    dbtools_status=$($TIMEOUT crm_resource -r op-ubs-dbtools -W | grep "is running on")
    if [ $? -ne 0 ]; then
        log_info error "dbtools is not running"
        exit 3
    fi
    dbtools_node=$(echo "$dbtools_status" | awk '{print $6}')
    if [ -z "$dbtools_node" ]; then
        log_info error "dbtools running node name is empty"
        exit 4
    fi

    log_info info "dbtools_node is $dbtools_node"
}

modify_script() {
    script="go/src/op-ubs-dbtools/script/pg_basebackup.sh"
    docker_layer=$($TIMEOUT docker container inspect op-ubs-dbtools | grep MergedDir | awk -F '"' '{print $4}')

    if [ -z "$docker_layer" ]; then
        log_info error "docker_layer is empty"
        exit 5
    fi

    if [ ! -f "${docker_layer}"/"${script}" ]; then
        exit 0
    fi

    cat "${docker_layer}"/"${script}" | grep "checkpoint"
    if [ $? -eq 0 ]; then
        log_info info "no need pretreatment, exit 0"
        exit 0
    fi

    $TIMEOUT sed -i 's/fetch/fetch --checkpoint=fast/g' "${docker_layer}"/"${script}"
}

#
# main starts here
#

log_info info "the script begin"

if [ -x "/usr/bin/timeout" ]; then
    TIMEOUT="/usr/bin/timeout 60"
else
    TIMEOUT=""
fi

check_env
get_local_node_name
get_dbtools_running_node

if [ "$local_node" != "$dbtools_node" ]; then
    log_info info "not dbtools node, exit 0"
    exit 0
fi

modify_script
