#!/bin/bash
# shellcheck disable=SC2002,SC2034,SC2181,SC2086
# ----------------------------------------------------------------------
# name:         restart_dbtools.sh
# version:      0.1
# createTime:   2020-03-23
# description:  重启dbtools
# wiki:
# ----------------------------------------------------------------------

log_info() {
    LOG_DATE=$(date "+%Y-%m-%d %H:%M:%S")
    LOG_FILENAME=""
    PID_INFO=$$

    echo "$LOG_DATE [$PID_INFO] ${FUNCNAME[*]} - $1 $2"
}

check_env() {
    $TIMEOUT crm_resource -r op-ubs-dbtools -W
    if [ $? -ne 0 ]; then
        log_info info "this environment don't need restart"
        exit 0
    fi
}

get_local_node_name() {
    local_node=$($TIMEOUT crm_node -n)
    if [ $? -ne 0 ]; then
        log_info error "get local node name failed"
        exit 1
    fi
    if [ -z "$local_node" ]; then
        log_info error "local node name is empty"
        exit 2
    fi

    log_info info "local_node is $local_node"
}

get_dbtools_running_node() {
    local i=0
    while [ $i -lt 5 ]; do
        dbtools_status=$($TIMEOUT crm_resource -r op-ubs-dbtools -W | grep "is running on")
        if [ $? -eq 0 ]; then
            break
        else
            log_info warning "dbtools is not running"
            sleep 5
            ((i = i + 1))
        fi

        if [ $i -eq 5 ]; then
            log_info error "dbtools is not running"
            exit 3
        fi
    done

    dbtools_node=$(echo "$dbtools_status" | awk '{print $6}')
    if [ -z "$dbtools_node" ]; then
        log_info error "dbtools running node name is empty"
        exit 4
    fi

    log_info info "dbtools_node is $dbtools_node"
}

check_parameter() {
    script="go/src/op-ubs-dbtools/script/pg_basebackup.sh"
    docker_layer=$($TIMEOUT docker container inspect op-ubs-dbtools | grep MergedDir | awk -F '"' '{print $4}')

    if [ -z "$docker_layer" ]; then
        log_info error "docker_layer is empty"
        exit 5
    fi

    if [ ! -f "${docker_layer}"/"${script}" ]; then
        exit 0
    fi

    cat "${docker_layer}"/"${script}" | grep "checkpoint"
    if [ $? -eq 0 ]; then
        log_info info "no need pre-treatment, exit 0"
        exit 0
    else
        log_info info "need pre-treatment"
    fi
}

stop_dbtools() {
    log_info info "try to stop dbtools"
    $TIMEOUT crm_resource -r op-ubs-dbtools -m -p target-role -v Stopped
}

check_dbtools_stop_status() {
    log_info info "check dbtools if stopped"

    local i=0
    while [ $i -lt "${TIMEOUT_DBTOOLS}" ]; do
        $TIMEOUT crm_resource -r op-ubs-dbtools -W | grep "is running on"
        if [ $? -ne 0 ]; then
            log_info info "dbtools stop successfully"
            break
        else
            log_info warning "dbtools is still working: $i"
            sleep 1
            ((i = i + 1))
        fi
    done

    if [ "${i}" -eq "${TIMEOUT_DBTOOLS}" ]; then
        log_info error "stop dbtools timeout"
        start_dbtools
        exit 6
    fi

    log_info info "check dbtools stop status end"
}

start_dbtools() {
    log_info info "try to start dbtools"
    $TIMEOUT crm_resource -r op-ubs-dbtools -m -d target-role
}

check_dbtools_start_status() {
    log_info info "check dbtools if started"

    local i=0
    while [ $i -lt "${TIMEOUT_DBTOOLS}" ]; do
        $TIMEOUT crm_resource -r op-ubs-dbtools -W | grep "is running on"
        if [ $? -eq 0 ]; then
            log_info info "dbtools start successfully"
            break
        else
            log_info warning "dbtools is not ok: $i"
            sleep 1
            ((i = i + 1))
        fi
    done

    if [ "${i}" -eq "${TIMEOUT_DBTOOLS}" ]; then
        log_info error "start dbtools timeout"
        exit 7
    fi

    log_info info "check dbtools start status end"
}

#
# main starts here
#

log_info info "the script begin"

TIMEOUT_DBTOOLS=120

if [ -x "/usr/bin/timeout" ]; then
    TIMEOUT="/usr/bin/timeout 60"
else
    TIMEOUT=""
fi

check_env
get_local_node_name
get_dbtools_running_node

if [ "$local_node" != "$dbtools_node" ]; then
    log_info info "not dbtools node, exit 0"
    exit 0
fi

check_parameter
sleep 5
stop_dbtools
check_dbtools_stop_status
start_dbtools
check_dbtools_start_status
