- remote_user: ubuntu
  become: yes
  become_user: root
  hosts: nodes
  gather_facts: no
  any_errors_fatal: true
  tasks:
  - name: restart dbtools
    script: restart_dbtools.sh
    ignore_errors: no
    run_once: false
    retries: 3
    delay: 2

  - name: modify basebackup script
    script: modify_dbtools_basebackup_script.sh
    ignore_errors: no
    run_once: false
    retries: 3
    delay: 2


  - name: rm pg_xlog softlink
    shell: if [ -f /paasdata/op-data/postgresql/data/pgdata/postgres/ha_recovery.conf ]; then docker exec postgresql bash -c "rm -rf /zxdata/pgdata/postgres/pg_xlog"; fi
    retries: 3
    delay: 2

  - name: rm sedxxx file for basebackup
    shell: if [ -f /paasdata/op-data/postgresql ]; then find /paasdata/op-data/postgresql/data/pgdata/postgres -maxdepth 1 -name "*" -type f -size 0c|grep sed|awk '{print $NF}'|xargs rm -rf; fi
    retries: 3
    delay: 2

  - name: rm core file for basebackup
    shell: rm -f /paasdata/op-data/postgresql/data/pgdata/postgres/core*
    retries: 3
    delay: 2
