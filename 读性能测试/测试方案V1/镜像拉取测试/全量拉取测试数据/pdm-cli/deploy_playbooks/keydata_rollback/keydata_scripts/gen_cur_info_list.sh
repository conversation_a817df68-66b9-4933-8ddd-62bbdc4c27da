#!/bin/bash

cur_node_ip=$1
all_info_file=$2
cur_info_file=$3
info_bak_dir="/paasdata/op-data/paas_upgrade_backup_keydata/node_info"
all_info_bak_file="$info_bak_dir/$all_info_file"
cur_info_bak_file="$info_bak_dir/$cur_info_file"
while read line
do
    node_ip=$(echo "$line" | cut -d '@' -f 1)
    if [ "$node_ip" == "$cur_node_ip" ];then
        rm -rf "$cur_info_bak_file"
        cur_node_info=$(echo "$line" | cut -d '@' -f 2)
        cur_node_info=${cur_node_info//,/ }
        info_arr=($cur_node_info)
        for info in "${info_arr[@]}"
        do
            echo $info >> "$cur_info_bak_file"
        done
    fi
done < "$all_info_bak_file"