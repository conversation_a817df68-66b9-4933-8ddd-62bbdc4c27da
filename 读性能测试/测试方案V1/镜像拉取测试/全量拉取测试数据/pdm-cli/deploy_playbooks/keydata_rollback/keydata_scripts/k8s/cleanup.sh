#!/bin/sh

# Copyright 2020 The Kubernetes Authors.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# should this be defined as ansible j2 template?

# shellcheck disable=SC2039
pods_dirs=(
/paasdata/docker/pods
/var/lib/kubelet/pods
)

tear_down_mounts() {
  for pods_dir in "${pods_dirs[@]}"
  do
    mount | grep "$pods_dir" | awk -F ' ' '{print $3}' | xargs -rI {} umount {}
  done
}


clean_k8s_containers() {
  if grep "container-runtime=remote" /etc/kubernetes/kubelet >/dev/null 2>&1;
  then
    all_container_ids=$(nerdctl -n k8s.io ps -qa)
    for id in $all_container_ids
    do
      if nerdctl -n k8s.io inspect --mode=native "$id" | grep "io.kubernetes.pod.uid" >/dev/null 2>&1;
      then
        nerdctl -n k8s.io rm -fv "$id" >/dev/null 2>&1
      fi
    done
    return
  fi

  all_container_ids=$(docker ps -aq)
  for id in $all_container_ids
  do

    if docker container inspect "$id" |grep 'io.kubernetes.pod.uid' >/dev/null 2>&1;
    then
      docker rm -fv "$id" >/dev/null 2>&1
    fi
  done
}


clean_k8s_containers
tear_down_mounts
