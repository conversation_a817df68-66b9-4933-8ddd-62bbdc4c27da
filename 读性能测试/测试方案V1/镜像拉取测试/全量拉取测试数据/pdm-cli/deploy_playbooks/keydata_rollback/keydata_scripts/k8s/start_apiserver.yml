- name: delete /paasdata/docker/pods and resources mounts and  start apiserver after keydata-rollback
  hosts: all
  gather_facts: false
  remote_user: ubuntu
  become: yes
  tasks:
    - name: Determine whether /paasdata/op-data/paas_upgrade_backup_keydata/node_info/current_node_role.list has master
      shell: grep -w "master" /paasdata/op-data/paas_upgrade_backup_keydata/node_info/current_node_role.list || true
      register: has_master_result
    - name: Determine whether /paasdata/op-data/paas_upgrade_backup_keydata/node_info/current_node_role.list has minion
      shell: grep -w "minion" /paasdata/op-data/paas_upgrade_backup_keydata/node_info/current_node_role.list || true
      register: has_minion_result
    - block:
        - name: copy cleanup script
          template:
            src: "/paasdata/op-tmp/keydata_scripts/k8s/cleanup.sh"
            dest: "~/clean_up.sh"
            mode: 0700
        - name: run clean up
          shell: . ~/clean_up.sh
        - name: clean up self
          shell: rm -f ~/clean_up.sh
        - name: clean /paasdata/docker/pods
          shell: rm -rf /paasdata/docker/pods/* || true
      when: has_master_result.stdout != "" and has_minion_result.stdout == ""

    - name: start apiserver
      shell: if [[ -f /etc/systemd/system/kube-apiserver.service ]];then systemctl start kube-apiserver; fi
      ignore_errors: true
    - name: start apiserver static pod
      shell: if [[ ! -f /etc/kubernetes/manifests/kube-apiserver.yml && -f /etc/kubernetes/kube-apiserver.yml ]];then cp -f /etc/kubernetes/kube-apiserver.yml /etc/kubernetes/manifests; fi
      ignore_errors: true
    - name: start etcd static pod
      shell: if [[ ! -f /etc/kubernetes/manifests/etcd.yml && -f /etc/kubernetes/etcd.yml ]];then cp -f /etc/kubernetes/etcd.yml /etc/kubernetes/manifests; fi
      ignore_errors: true
    - name: restart apiserver proxy controller-manager scheduler
      shell: >-
        if [ -f /etc/kubernetes/manifests/kube-apiserver.yml ];then ps -ef | grep kube-apiserver | grep -v grep | awk '{print $2}' | xargs -r kill -s 9 || true ;fi &&
        if [ -f /etc/kubernetes/manifests/kube-controller-manager.yml ];then ps -ef | grep kube-controller-manager | grep -v grep | awk '{print $2}' | xargs -r kill -s 9 || true ;fi &&
        if [ -f /etc/kubernetes/manifests/kube-scheduler.yml ];then ps -ef | grep kube-scheduler | grep -v grep | awk '{print $2}' | xargs -r kill -s 9 || true ;fi &&
        if [ -f /etc/kubernetes/manifests/kube-proxy.yml ];then ps -ef | grep kube-proxy | grep -v grep | awk '{print $2}' | xargs -r kill -s 9 || true ;fi
      ignore_errors: true
