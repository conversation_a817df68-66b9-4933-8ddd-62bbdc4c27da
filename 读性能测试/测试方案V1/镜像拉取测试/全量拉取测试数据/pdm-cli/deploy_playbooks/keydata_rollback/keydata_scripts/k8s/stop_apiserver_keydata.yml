- name: stop apiserver 
  hosts: all
  gather_facts: false
  remote_user: ubuntu
  become: yes
  tasks:
    - name: stop apiserver 
      shell: if [[ -f /etc/systemd/system/kube-apiserver.service ]];then systemctl stop kube-apiserver; fi
      ignore_errors: true
    - name: check apiserver static pod exist
      stat: path=/etc/kubernetes/manifests/kube-apiserver.yml
      register: is_apiserver_static
    - name: stop apiserver pod
      command: mv /etc/kubernetes/manifests/kube-apiserver.yml /etc/kubernetes
      when: is_apiserver_static.stat.exists
      ignore_errors: true
    - name: check etcd static pod exist
      stat: path=/etc/kubernetes/manifests/etcd.yml
      register: is_etcd_static
    - name: stop etcd pod
      command: mv /etc/kubernetes/manifests/etcd.yml /etc/kubernetes
      when: is_etcd_static.stat.exists
      ignore_errors: true
    - name: restart kubelet
      shell: if [[ -f /etc/systemd/system/kubelet.service ]];then systemctl restart kubelet; fi
      ignore_errors: true
