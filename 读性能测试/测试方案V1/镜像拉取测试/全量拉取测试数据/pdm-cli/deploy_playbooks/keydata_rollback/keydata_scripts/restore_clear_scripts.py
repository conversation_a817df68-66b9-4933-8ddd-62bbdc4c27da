import os
import sys
import yaml
import time

log_file_path = "/paasdata/op-log/pdm-cli/keydata_rollback.log"


def item_in_file(item, path):
    with open(path, "r") as f:
        lines = f.readlines()
        for i in range(0, len(lines)):
            if item == lines[i].strip():
                return True
            continue
        return False


def check_roles_in_file(roles, role_path):
    for role in roles:
        if role == "usednodes":
            return True
        if item_in_file(role, role_path):
            return True
    return False


def judge_by_component_or_role(judge_method, compo_owner, compo_path,
                               roles, role_path):
    if judge_method == "by_component":
        if item_in_file(compo_owner, compo_path):
            return True
        else:
            return False
    elif judge_method == "by_role":
        if check_roles_in_file(roles, role_path):
            return True
        return False
    return False


def node_has_resource(judge_method, roles, compo_owner,
                      res_file, optional_component, cmd_type):
    bak_dir = "/paasdata/op-data/paas_upgrade_backup_keydata/node_info/"
    role_path = bak_dir + "current_node_role.list"
    compo_path = bak_dir + "current_node_component.list"
    no_int_compo_path = bak_dir + "no_instance_compo.list"
    no_int_type = ["res_process.yml", "res_service.yml",
                   "res_indep_container.yml"]
    if optional_component is True and cmd_type == "clear":
        return True
    if res_file in no_int_type and compo_owner != "":
        if item_in_file(compo_owner, no_int_compo_path):
            return False
    return judge_by_component_or_role(judge_method, compo_owner, compo_path,
                                      roles, role_path)


def get_shell_cmd(res_file_name, data, cmd_type, prefix):
    cmd = ""
    res_name = data.get("res_name", "")
    if res_file_name in ["res_filedir.yml", "res_filedir_extra.yml",
                         "res_filedir_kubeall.yml"]:
        inclu_dir = " ".join(data.get("inclu_dir", ""))
        exclu_dir = " ".join(data.get("exclu_dir", ""))
        cmd = ('bash /paasdata/op-tmp/keydata_scripts/restore_filedir.sh ' +
               '"' + inclu_dir + '" ' +
               '"' + exclu_dir + '" ' +
               '"' + res_name + '" ' +
               '"' + cmd_type + '"')
    else:
        if cmd_type == "clear":
            cmd = data.get("clear_cmd", "")
        if cmd_type == "restore":
            cmd = data.get("restore_cmd", "")
    file_name = cmd.split(' ')[0]
    file_path = prefix + "/" + file_name
    if (os.path.exists(file_path) and
            os.path.isfile(file_path)):
        shell_cmd = "bash " + file_path
    else:
        shell_cmd = cmd
    return shell_cmd


def call_cmd(res_file, cmd_type):
    prefix = os.path.dirname(res_file)
    res_file_name = os.path.basename(res_file)
    with open(res_file, "r") as f:
        dataMap = yaml.safe_load(f)
    for data in dataMap:
        judge_method = data.get("judge_method", "")
        roles = data.get("roles", "")
        compo_owner = data.get("compo_owner", "")
        optional_component = data.get("optional_component", False)
        if not node_has_resource(judge_method, roles,
                                 compo_owner, res_file_name,
                                 optional_component, cmd_type):
            continue
        shell_cmd = get_shell_cmd(res_file_name, data, cmd_type, prefix)
        if shell_cmd == "":
            continue
        result = os.system(shell_cmd + " >/dev/null")
        if result != 0:
            print('exec cmd %s failed!' % shell_cmd)
            return False
        write_cmd_to_log(shell_cmd)
        os.chmod(log_file_path, 0o640)
    return True


def write_cmd_to_log(shell_cmd):
    date_time = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(int(time.time())))
    cmd_log = "%s INFO %s execute successfully" % (date_time, shell_cmd)
    if not os.path.exists("/paasdata/op-log/pdm-cli"):
        os.makedirs("/paasdata/op-log/pdm-cli", 0o750)
    with open(log_file_path, "a+") as f:
        f.write(cmd_log + "\n")


def main():
    res_file = sys.argv[1]
    keydata_bak_dir = \
        "/paasdata/op-data/paas_upgrade_backup_keydata/resource_reg"
    old_res = keydata_bak_dir + "/old/" + res_file
    new_res = keydata_bak_dir + "/new/" + res_file
    if not call_cmd(new_res, "clear"):
        return False
    if res_file in ["res_filedir.yml", "res_filedir_extra.yml"]:
        if not call_cmd(old_res, "clear"):
            return False
    if not call_cmd(old_res, "restore"):
        return False
    return True


if __name__ == "__main__":
    if not main():
        exit(1)
    exit(0)
