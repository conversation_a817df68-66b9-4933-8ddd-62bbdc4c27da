#!/bin/bash

log_dir="/paasdata/op-log/pdm-cli"
acl_exclude_dir=("/paasdata/op-data/op-ubs-python3lib")
filedir_tmp="/paasdata/op-tmp/keydata_scripts/filedir_tmp"

function log_info(){
    local log_date=
    local log_file_name="$log_dir/keydata_rollback.log"
    log_date=$(date "+%Y-%m-%d %H:%M:%S")
    echo "$log_date $1 [$2] - $3" >> $log_file_name
    chmod 640 $log_file_name
}

timeout_func(){
    local l_time=$1
    shift
    local rc=0
    if [ -x "/usr/bin/timeout" ]; then
        TIMEOUT="/usr/bin/timeout $l_time"
    else
        TIMEOUT=""
    fi
    $TIMEOUT "${@}"
    rc=$?
    if [ $rc -eq 124 ]; then
        log_info "INFO" "restore_filedir.sh cmd timeout $l_time: " "${@}"
        echo "cmd timeout $l_time: " "${@}" >&2
        kill -9 $$
    fi
    return $rc
}

function is_empty_dir(){
    local dir=$1
    if [ "$(timeout_func 120 ls -A "$dir")" = "" ]; then
        return 0
    else
        return 1
    fi
}

function is_big_dir(){
    local l_sub_full_dir=$1
    local l_is_big_dir=1
    local l_skip_judge=false
    for ex_dir in "${exclu_list[@]}"
    do
        if [[ $ex_dir/ =~ ^$l_sub_full_dir/ ]]; then
            l_skip_judge=true
            break
        fi
    done
    #只有老版本备份时记录的大目录需要保留，新版本的大目录需要删除，不需要判断是否是大目录
    if ! $l_skip_judge; then
        if [ -e "$exclu_big_dir_list" ]; then
            # shellcheck disable=SC2162
            while read big_dir
            do
                if [ "$l_sub_full_dir" == "$big_dir" ]; then
                    l_is_big_dir=0
                fi
            done < "$exclu_big_dir_list"
        fi
    fi
    return "$l_is_big_dir"
}

function get_symbol_link_dir(){
    local l_inclu_dir=$1
    local l_array=
    local l_dir=""
    local loop_dir=""
    local OLD_IFS="$IFS"
    IFS="/"
    # shellcheck disable=SC2206
    l_array=($l_inclu_dir)
    IFS="$OLD_IFS"
    for i in $(seq 0 ${#l_array[@]})
    do
        loop_dir=${l_array[$i]}
        if [ "$loop_dir" == "" ]; then
            continue
        fi
        l_dir="$l_dir/$loop_dir"
        if [ -h "$l_dir" ]; then
            echo "$l_dir" >> "$symbol_link_dir_list"
            break
        fi
    done
}

if [ ! -e $log_dir ]; then
    timeout_func 120 mkdir -m 750 $log_dir
fi

log_info "INFO" "restore_filedir.sh" "inclu_dir:$1, exclu_dir:$2, res_name:$3, cmd_type:$4"

inclu_dir=$1
exclu_dir=$2
res_name=$3
cmd_type=$4

base_dir="/paasdata/op-data/paas_upgrade_backup_keydata/filedir"
res_dir="$base_dir/$res_name"
exclu_big_dir_list="$res_dir/exclu_big_dir_list"
symbol_link_dir_list="$res_dir/symbol_link_dir_list"

# shellcheck disable=SC2206
exclu_list=($exclu_dir)
# shellcheck disable=SC2206
inclu_list=($inclu_dir)

if [ "$cmd_type" == "clear" ]; then
    for dir in "${inclu_list[@]}"
    do
        if [ -d "$dir" ]; then
            if is_empty_dir "$dir"; then
                timeout_func 1200 rm -rf "$dir"
                continue
            fi
            shopt -s dotglob
            for sub_full_dir in "$dir"/*
            do
                if is_big_dir "$sub_full_dir"; then
                    continue
                fi
                can_full_copy=true
                for ex_dir in "${exclu_list[@]}"
                do
                    if [[ $ex_dir/ =~ ^$sub_full_dir/ ]]; then
                        can_full_copy=false
                        break
                    fi
                done
                if $can_full_copy; then
                    timeout_func 1200 rm -rf "$sub_full_dir"
                    continue
                else
                    grep_cmd=""
                    for exc_dir in "${exclu_list[@]}"
                    do
                        grep_cmd="$grep_cmd -e $exc_dir"
                    done

                    if [ "$grep_cmd" != "" ]; then
                        # shellcheck disable=SC2086
                        timeout_func 1800 find "$sub_full_dir" | grep -w -v $grep_cmd > $filedir_tmp
                    else
                        # shellcheck disable=SC2086
                        timeout_func 1800 find "$sub_full_dir" > $filedir_tmp
                    fi
                    if [ -e "$filedir_tmp" ]; then
                        # shellcheck disable=SC2162
                        while read dir_line
                        do
                            if [ ! -d "$dir_line" ] && [ ! -f "$dir_line" ] && [ ! -L "$dir_line" ]; then
                                log_info "INFO" "restore_filedir.sh" "$dir_line is not dir or not file or not symbol link, it is skipped!"
                                continue
                            fi
                            if [ -d "$dir_line" ] && ! is_empty_dir "$dir_line"; then
                                continue
                            fi
                            timeout_func 1200 rm -rf "$dir_line"
                        done < "$filedir_tmp"
                        timeout_func 1200 rm -rf $filedir_tmp
                    fi
                fi
            done
            if is_empty_dir "$dir"; then
                timeout_func 1200 rm -rf "$dir"
            fi
        fi
        if [ -f "$dir" ] || [ -L "$dir" ]; then
            timeout_func 1200 rm -rf "$dir"
        fi
    done
fi

if [ "$cmd_type" == "restore" ]; then
    timeout_func 120 cd "$res_dir"
    # shellcheck disable=SC2164
    cd "$res_dir"
    if is_empty_dir "$res_dir"; then
        exit 0
    fi
    for loop_dir in "${inclu_list[@]}"
    do
        get_symbol_link_dir "$loop_dir"
    done
    for res_subdir in "$res_dir"/*
    do
        res_subdir=${res_subdir##*/}
        if [ "$res_subdir" == "exclu_big_dir_list" ] || \
        [ "$res_subdir" == "symbol_link_dir_list" ]; then
            continue
        fi
        full_copy=true
        if [ -e "$symbol_link_dir_list" ]; then
            # shellcheck disable=SC2162
            while read link_dir
            do
                if [[ $link_dir/ =~ ^/$res_subdir/ ]]; then
                    full_copy=false
                    break
                fi
            done < "$symbol_link_dir_list"
        fi
        if $full_copy; then
            if ! timeout_func 3600 cp -r -f --parents --preserve=all "$res_subdir" /; then
                log_info "ERROR" "restore_filedir.sh" "$res_subdir copy to / failed!"
            fi
        else
            timeout_func 1800 find "$res_subdir/" > $filedir_tmp
            # shellcheck disable=SC2162
            while read dir
            do
                if [ -d "$dir" ] && ! is_empty_dir "$dir"; then
                    continue
                fi
                if ! timeout_func 1200 cp -r -f --parents --preserve=all "$dir" /; then
                    log_info "ERROR" "restore_filedir.sh" "$dir copy to / failed!"
                fi
            done < "$filedir_tmp"
            timeout_func 1200 rm -rf $filedir_tmp
        fi
    done
    grep_acl_cmd=""
    for acl_exclu_dir in "${acl_exclude_dir[@]}"
    do
        grep_exclu_dir=${acl_exclu_dir#*/}
        grep_acl_cmd="$grep_acl_cmd -e $grep_exclu_dir"
    done
    # shellcheck disable=SC2086
    timeout_func 1800 find ./ | grep -w -v $grep_acl_cmd > $filedir_tmp
    if [ -e "$filedir_tmp" ]; then
        # shellcheck disable=SC2162
        while read dir
        do
            org_dir=${dir#*/}
            if [ "$org_dir" == "" ] || [ "$org_dir" == "exclu_big_dir_list" ] || \
            [ "$org_dir" == "symbol_link_dir_list" ]; then
                continue
            fi
            spe_acl=$(timeout_func 120 getfacl "$dir" | grep ":..*:" | grep -v "^#")
            if [ "$spe_acl" == "" ]; then
                continue
            fi
            acl_args=""
            # shellcheck disable=SC2206
            array=($spe_acl)
            for acl in "${array[@]}"
            do
                acl_args="$acl_args -m $acl"
            done
            log_info "INFO" "restore_filedir.sh -- restore" "/$org_dir acl args are $acl_args"
            full_path="/$org_dir"
            log_info "INFO" "restore_filedir.sh -- restore" "setfacl $acl_args $full_path"
            # shellcheck disable=SC2086
            timeout_func 120 setfacl $acl_args "$full_path"
        done < "$filedir_tmp"
        timeout_func 1200 rm -rf $filedir_tmp
    fi
fi
