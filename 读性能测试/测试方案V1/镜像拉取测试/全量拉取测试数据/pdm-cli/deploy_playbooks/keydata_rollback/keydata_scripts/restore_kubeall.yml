---
- hosts: nodes
  become: true
  gather_facts: no
  remote_user: ubuntu
  tasks:
    - name: restore components after rebootstrap
      tags: after_rebootstrap
      block:
        - name: set after rebootstrap parameter
          set_fact:
            TmpKubeReFile: /tmp/kubeall_after_rebootstrap.yaml

        - name: Check componet script file {{ script_path }} is exists
          stat:
            path: "{{ script_path }}"
          register: script_path_exist

        - name: fail is {{ script_path }} not exist
          fail: msg="{{ script_path }} not exist"
          when: not ignore_file_exist

        - name: create tmp task file {{ TmpKubeReFile }}
          copy:
            dest: "{{ TmpKubeReFile }}"
            content: |
              ---
              - name: exec script_path
                shell: >
                  {{ cmd }}
                run_once: true
          delegate_to: localhost
          run_once: true

        - name: exec script_path
          include_tasks: "{{ TmpKubeReFile }}"
          when: script_path_exist.stat.exists == True

        - name: delete tmp file {{ TmpKubeReFile }}
          file:
            dest: "{{ TmpKubeReFile }}"
            state: absent
