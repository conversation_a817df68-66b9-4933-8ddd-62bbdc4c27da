import os
import sys
import yaml
import time
import subprocess

log_file_path = "/paasdata/op-log/pdm-cli/keydata_rollback.log"


def item_in_file(item, path):
    with open(path, "r") as f:
        lines = f.readlines()
        for i in range(0, len(lines)):
            if item == lines[i].strip():
                return True
            continue
        return False


def check_roles_in_file(roles, role_path):
    for role in roles:
        if role == "usednodes":
            return True
        if item_in_file(role, role_path):
            return True
    return False


def node_has_resource(judge_method, roles, compo_owner, res_file):
    bak_dir = "/paasdata/op-data/paas_upgrade_backup_keydata/node_info/"
    role_path = bak_dir + "current_node_role.list"
    compo_path = bak_dir + "current_node_component.list"
    no_int_compo_path = bak_dir + "no_instance_compo.list"
    no_int_type = ["res_process.yml", "res_service.yml",
                   "res_indep_container.yml"]
    if res_file in no_int_type and compo_owner != "":
        if item_in_file(compo_owner, no_int_compo_path):
            return False
    if judge_method == "by_component":
        if item_in_file(compo_owner, compo_path):
            return True
        else:
            return False
    elif judge_method == "by_role":
        if check_roles_in_file(roles, role_path):
            return True
        return False
    else:
        return False


def need_skip_item(stop_item, res_name):
    exclud_item = ["opslet", "etcd", "op-containers-containerd", "docker"]
    if stop_item is not None:
        if res_name != stop_item:
            return True
    elif res_name in exclud_item:
        return True
    return False


def modify_wait_time(data, max_wait_time):
    auto_start_mode = data.get("auto_start_mode", "")
    auto_start_wait = data.get("auto_start_wait", "")
    if auto_start_mode == "cron":
        if max_wait_time < auto_start_wait:
            max_wait_time = auto_start_wait
    return max_wait_time


def get_shell_cmd(spc_cmd, res_file_name, data, prefix):
    cmd = ""
    if spc_cmd == "stop":
        if res_file_name in ["res_indep_container.yml",
                             "res_special_container.yml"]:
            cmd = data.get("clear_cmd", "")
        else:
            cmd = data.get("stop_cmd", "")
    if spc_cmd == "restore":
        cmd = data.get("restore_cmd", "")
    file_name = cmd.split(' ')[0]
    if res_file_name in ["res_special_container.yml"]:
        # scripts in component's data dir
        file_path = file_name
        if not os.path.exists(file_path):
            return ""
    else:
        file_path = prefix + "/" + file_name
    if (os.path.exists(file_path) and
            os.path.isfile(file_path)):
        shell_cmd = "bash " + file_path
    else:
        shell_cmd = cmd
    return shell_cmd


def exec_shell_cmd(spc_cmd, res_file_name, data, prefix):
    log_file = "/paasdata/op-log/pdm-cli/keydata_rollback.log"
    shell_cmd = get_shell_cmd(spc_cmd, res_file_name, data, prefix)
    if shell_cmd == "":
        return True
    result = write_stderr_to_log(log_file, shell_cmd)
    if result != 0:
        print('exec cmd %s failed!' % shell_cmd)
        return False
    write_cmd_to_log(shell_cmd)
    os.chmod(log_file_path, 0o640)
    return True


def write_cmd_to_log(shell_cmd):
    date_time = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(int(time.time())))
    cmd_log = "%s INFO %s execute successfully" % (date_time, shell_cmd)
    if not os.path.exists("/paasdata/op-log/pdm-cli"):
        os.makedirs("/paasdata/op-log/pdm-cli", 0o750)
    with open(log_file_path, "a+") as f:
        f.write(cmd_log + "\n")


def call_cmd(res_file, stop_item, spc_cmd):
    prefix = os.path.dirname(res_file)
    if not os.path.exists(res_file):
        return True
    res_file_name = os.path.basename(res_file)
    with open(res_file, "r") as f:
        dataMap = yaml.safe_load(f)
    for data in dataMap:
        res_name = data.get("res_name", "")
        if need_skip_item(stop_item, res_name):
            continue
        judge_method = data.get("judge_method", "")
        roles = data.get("roles", "")
        compo_owner = data.get("compo_owner", "")
        if not node_has_resource(judge_method, roles,
                                 compo_owner, res_file_name):
            if stop_item is not None:
                break
            else:
                continue
        if not exec_shell_cmd(spc_cmd, res_file_name, data, prefix):
            return False
        if stop_item is not None:
            break
    return True


def call_process_cmd(res_file, spc_cmd):
    max_wait_time = 0
    prefix = os.path.dirname(res_file)
    res_file_name = os.path.basename(res_file)
    with open(res_file, "r") as f:
        dataMap = yaml.safe_load(f)
    for data in dataMap:
        judge_method = data.get("judge_method", "")
        roles = data.get("roles", "")
        compo_owner = data.get("compo_owner", "")
        if not node_has_resource(judge_method, roles,
                                 compo_owner, res_file_name):
            continue
        if spc_cmd == "restore":
            max_wait_time = modify_wait_time(data, max_wait_time)
            continue
        if not exec_shell_cmd(spc_cmd, res_file_name, data, prefix):
            return False
    log_file = "/paasdata/op-log/pdm-cli/keydata_rollback.log"
    if spc_cmd == "restore":
        sleep_cmd = "sleep " + str(max_wait_time)
        slp_result = write_stderr_to_log(log_file, sleep_cmd)
        if slp_result != 0:
            print('exec sleep cmd %s failed!' % sleep_cmd)
            return False
        os.chmod(log_file, 0o640)
    return True


def write_stderr_to_log(log_file, cmd):
    cmd_1 = ["bash", "-c", cmd]
    cmd_result = subprocess.Popen(cmd_1, stderr=subprocess.PIPE)
    _, error = cmd_result.communicate()
    return_code = cmd_result.returncode
    if return_code != 0:
        date_time = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(int(time.time())))
        write_log = '%s ERROR %s' % (date_time, bytes.decode(error))
        with open(log_file, 'a+') as f:
            f.write(write_log + "\n")
        return 1
    return 0


def restore_spc(new_res, old_res, stop_item, spc_cmd):
    res_file_name = os.path.basename(new_res)
    if res_file_name == "res_process.yml":
        if spc_cmd == "stop":
            if not call_process_cmd(new_res, spc_cmd):
                return False
        if not call_process_cmd(old_res, spc_cmd):
            return False
    else:
        if spc_cmd == "stop":
            if not call_cmd(new_res, stop_item, spc_cmd):
                return False
        if not call_cmd(old_res, stop_item, spc_cmd):
            return False
    return True


def main():
    res_file = sys.argv[1]
    stop_item = None
    spc_cmd = None
    if len(sys.argv) >= 3:
        spc_cmd = sys.argv[2]
    if len(sys.argv) >= 4:
        stop_item = sys.argv[3]
    keydata_bak_dir = \
        "/paasdata/op-data/paas_upgrade_backup_keydata/resource_reg"
    old_res = keydata_bak_dir + "/old/" + res_file
    new_res = keydata_bak_dir + "/new/" + res_file
    if not restore_spc(new_res, old_res, stop_item, spc_cmd):
        return False
    return True


if __name__ == "__main__":
    if not main():
        exit(1)
    exit(0)
