#!/bin/bash

ip=$(cat /root/common/com_vars.yml |grep -w openpalette_service_ip |awk '{print $NF}' |tr -d "'}" |head -1)
if [ -z "$ip" ];then
    echo "get ip failed"
    exit 255
fi
port=$(grep -w openpalette_service_port /root/common/port_vars.yml|awk '{print $7}'|tr -d ',')
if [ -z "$port" ]; then
    echo "get port failed"
    exit 255
fi
format_ip=$(echo "${ip}" | awk -F '[' '{print $NF}' | awk -F ']' '{print $1}')
ipv6tag=':'
if [[ "$format_ip" == *"$ipv6tag"* ]]; then
  echo "It's ipv6."
  host=["$format_ip"]:"${port}"
else
  host="$format_ip":"${port}"
fi
echo "host is: $host"

for i in {1..5}
do
   curl --connect-timeout 5 -m 1800 -s http://"${host}"/swr/v1/alignment -X POST | grep success
   result=$?
    if [[ "${result}" -eq 0 ]]; then
        echo ""
        echo "correct swr data success"
        break
    fi

    if [ "$i" -eq 5 ]; then
        echo ""
        echo "correct swr data failed"
        exit 1
    fi

    echo " $i times to try"
    sleep 20s
done