- hosts: nodes
  remote_user: ubuntu
  become: yes
  become_method: sudo
  gather_facts: no
  vars:
    bin_path: "/opt/oki-cli"
    bin_link_path: "/usr/bin/oki-cli"
    v2_backup_path: "/opt/oki-cli_bak"

  tasks:
    - name: oki-cli src_version not exist block
      block:
        - name: rm the /usr/bin/oki-cli directory
          file: name={{bin_link_path}} state=absent

        - name: rm the /opt/oki-cli directory
          file: name={{bin_path}} state=absent
      when: src_version == ""

    - name: oki-cli src_version exist block
      block:
        - name: check if {{v2_backup_path}} existed
          stat: path={{v2_backup_path}}
          register: dir_existed
        - name: copy oki-cli_backup to {{bin_path}}
          copy: src={{v2_backup_path}}/ dest={{bin_path}} remote_src=yes mode=0750
          when: dir_existed.stat.exists
      when:
        - src_version != ""
