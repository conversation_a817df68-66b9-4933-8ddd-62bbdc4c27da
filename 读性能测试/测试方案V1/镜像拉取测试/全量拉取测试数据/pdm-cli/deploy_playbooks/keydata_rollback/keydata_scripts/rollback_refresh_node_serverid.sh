#!/bin/bash

# Started by AICoder, pid:87649k4c18s802414644085bf0392551c9b0b53f
# JSON文件路径
file_path="/paasdata/op-data/pdm-cli/new_serverid_and_hostname.json"

# 检查jq命令
if [[ $(command -v jq-linux64) ]]; then
    jq_cmd=jq-linux64
else
    jq_cmd=jq
fi

# 读取JSON文件并遍历
if [[ -f "$file_path" ]]; then
    load_list=$(cat "$file_path" | $jq_cmd -c '.[]')

    for entry in $load_list; do
        hostname=$(echo "$entry" | $jq_cmd -r '.hostname')
        new_serverid=$(echo "$entry" | $jq_cmd -r '.new_serverid')

        if [[ -z "$hostname" || -z "$new_serverid" ]]; then
            echo "Incomplete data for entry: $entry" >&2
            exit 1
        fi

        # 构建要发送的数据
        data_to_patch=$($jq_cmd -n --arg hostname "$hostname" --arg new_serverid "$new_serverid" '
        {
            "index": [
                {
                    "hostname": $hostname
                }
            ],
            "data": {
                "uuid": $new_serverid
            }
        }')

        echo "Patching node data: $data_to_patch"

        max_retries=12
        retry_secs=5
        retries=0
        timeout_seconds=30

        while [ $retries -lt $max_retries ]; do
            res=$(timeout $timeout_seconds curl -s -o /dev/null -w "%{http_code}" -X PATCH -k https://assembler-apiserver.admin.svc:2509/kubeall/v1/config/cluster/nodes -d "$data_to_patch")

            if [ "$res" -eq 200 ]; then
                echo "Patch operation successful for hostname: $hostname"
                break
            else
                retries=$((retries + 1))
                echo "Patch cluster nodes failed for hostname: $hostname with status code $res. Retrying $retries time(s)."
                sleep $retry_secs
            fi
        done

        if [ $retries -eq $max_retries ]; then
            echo "Patch operation failed for hostname: $hostname after $max_retries attempts" >&2
            exit 1
        fi
    done
    echo "Patch operation completed successfully"
else
# 如果不存在，直接正常退出
    echo "File $file_path not found, no node need to update" >&2
    exit 0
fi
# Ended by AICoder, pid:87649k4c18s802414644085bf0392551c9b0b53f