#!/bin/bash

py_name=$1
py_dir="/paasdata/op-tmp/keydata_scripts"
py_path="$py_dir/$py_name"
if [ $# -ge 2 ];then
    py_arg1=$2
    py_path="$py_path $py_arg1"
fi
if [ $# -ge 3 ];then
    py_arg2=$3
    py_path="$py_path $py_arg2"
fi
if [ $# -ge 4 ];then
    py_arg3=$4
    py_path="$py_path $py_arg3"
fi
# shellcheck disable=SC2086
if command -v pythonlatest &> /dev/null; then
  python_cmd="pythonlatest"
else
  python_cmd="python"
fi
$python_cmd $py_path
exit $?