- name: stop apiserver
  hosts: all
  gather_facts: false
  remote_user: ubuntu
  become: yes
  vars:
    base_processd_url: http://localhost/api/v1/process
    get_all_process_url: "{{ base_processd_url }}/all/all/list"
    sock_file: /paasdata/op-data/processd/kubeall/run/hub/processd/sock/forPod/processd.sock
    comp_name: openjdk
  tasks:
    - name: get all processes
      uri:
        url: "{{ get_all_process_url }}"
        method: GET
        headers:
          Content-Type: application/json
        unix_socket: "{{ sock_file }}"
      register: _all_process_result
      until: _all_process_result.status == 200
      retries: 12
      delay: 5

    - name: debug all processes
      debug:
        msg: "{{ _all_process_result.json }}"

    - name: Find objects with process name {{ comp_name }}
      set_fact:
        comp_objs: "{{ _all_process_result.json | selectattr('name', 'eq', comp_name) | list }}"

    - name: Extract {{ comp_name }} ctrl-ids for filtered list
      set_fact:
        comp_ctrl_ids: "{{ comp_objs | map(attribute='ctrl-id') | list }}"

    - debug:
        msg: "{{ comp_name }} with ctrl-id: {{ comp_ctrl_ids }}"

    - name: Stop {{comp_name}} process
      uri:
        url: "{{ base_processd_url }}/{{ comp_name }}/{{ item }}"
        method: DELETE
        headers:
          Content-Type: application/json
        unix_socket: "{{ sock_file }}"
      loop: "{{ comp_ctrl_ids }}"
      ignore_errors: yes
