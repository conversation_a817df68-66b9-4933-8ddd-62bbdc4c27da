#!/bin/bash

pwd_dir=$(cd "$(dirname "$0")" || exit;pwd)

function log(){
  LOG_DATE=`date "+%Y-%m-%d %H:%M:%S"`
  LOG_FILENAME="/paasdata/op-log/pdm-cli/stop_containers_used_nfs_volumes.log"
  PID=`printf "%05d" $$`
  echo "$LOG_DATE $1 [$PID] - $2" >> $LOG_FILENAME
  access=$(stat -c %a $LOG_FILENAME)
  if [ "$access" != "640" ]; then
    chmod 640 $LOG_FILENAME
  fi
}

#Stop containers use nfs volume
function stop_containers_use_nfs_volume(){
  cat /dev/null >"$pwd_dir"/pod_info
  if [ ! -d "/paasdata/docker/pods" ];then
    log "ERROR" "path /paasdata/docker/pods is not exist"
    return 1
  fi
  timeout 15 ls /paasdata/docker/pods >"$pwd_dir"/pod_info
  rc=$?
  if [ $rc -ne 0 ];then
    log "ERROR" "get pods id failed, with rc $rc"
    return 1
  fi
  get_pod_info=$(timeout 15 cat "$pwd_dir"/pod_info)
  if [ ! -z "$get_pod_info" ]; then
    check_mount_point_dockerd
  fi
  rm -f "$pwd_dir"/pod_info
}

#Check mount point dockerd
function check_mount_point_dockerd(){
  cat /dev/null >"$pwd_dir"/container_list 
  timeout 30 mount > "$pwd_dir"/mount_info
  if [ $? -ne 0 ];then
    log "ERROR" "get mount info failed, with rc $rc"
    return 1
  fi
  timeout 120 docker ps > "$pwd_dir"/containers_info
  if [ $? -ne 0 ];then
    log "ERROR" "get containers info failed, with rc $rc"
    return 1
  fi
  timeout 15 cat "$pwd_dir"/pod_info |while read -r LINE >/dev/null 2>&1
  do
    if timeout 15 cat "$pwd_dir"/mount_info |grep "$LINE" |grep "type\s*nfs" >/dev/null 2>&1; then
       timeout 15 cat "$pwd_dir"/containers_info |grep "$LINE" |grep -v k8s_POD |awk '{print $1}'>> "$pwd_dir"/container_list  
    fi
  done
  get_container_info=$(timeout 15 cat "$pwd_dir"/container_list)
  if [ ! -z "$get_container_info" ]; then
    dockerd_stop_container
  fi
  rm -f "$pwd_dir"/mount_info "$pwd_dir"/containers_info "$pwd_dir"/container_list
}

#Docker stop container
function dockerd_stop_container(){
  rst=$(cat "$pwd_dir"/container_list |xargs timeout 600 docker stop 2>&1)
  rc=$?
  if [ $rc -eq 0 ];then
    log "INFO" "stop containers successfully, result is $rst"
	return 0
  fi
  log "ERROR" "stop containers failed, with rc $rc, result is $rst"
}

#main
if timeout 30 docker version >/dev/null 2>&1; then
   stop_containers_use_nfs_volume
else
   log "INFO" "docker service is not ok, skipping stop contianers"
fi
#Not block keydata rollback, always exit 0 
exit 0
