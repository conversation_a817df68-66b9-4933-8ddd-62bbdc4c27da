#!/bin/bash

logFile="/var/log/cinder_volumeattachment_rollback.log"
rollbackPkg="/etc/pdm/deploy_playbooks/keydata_rollback/resource_reg/fix_bugs/storage/resource_reg_11.4_40.07/volume_rollback.tar.gz"
keydataRollbackPath="/etc/pdm/deploy_playbooks/keydata_rollback"
rollbackCmdPath="${keydataRollbackPath}/volume_rollback"
rollbackCmd="${rollbackCmdPath}/cinder_volumeattachment_rollback"

log() {
  LOG_DATE=$(date "+%Y-%m-%d %H:%M:%S")
  PID=$(printf "%05d" $$)
  echo "${LOG_DATE}" "[$PID]" "${FUNCNAME[@]}" - "$1" >> "$logFile"
  access=$(stat -c %a $logFile)
  if [ "$access" != "640" ]; then
    chmod 640 $logFile
  fi
}

unzipVolumeRollbackCmd(){
  unzip_rst=$(tar xzf ${rollbackPkg} -C ${keydataRollbackPath} 2>&1 || tar xf ${rollbackPkg} -C ${keydataRollbackPath} 2>&1)
  if [[ $? -ne 0 ]];then
    log "unzip volumeRollbackCmd failed , error is ${unzip_rst}"
    return 1
  fi
  return 0
}

exitscript(){
  exit_code=$1
  clearVolumeRollbackCmd
  exit ${exit_code}
}

clearVolumeRollbackCmd(){
  rm -rf ${rollbackCmdPath}
}

getCSIdriverInfo(){
  for i in {1..5} ; do
    csidriver=$(kubectl get csidriver 2>&1)
    if [[ $? -ne 0 ]];then
      log "get csi driver info failed"
      sleep 10
      continue
    fi
    echo "${csidriver}"
    return 0
  done
  return 1
}

isDeployCinderCSI() {
  csidriver=$(getCSIdriverInfo 2>&1)
  if [[ $? -ne 0 ]];then
    log "get csi driver info failed"
    return 2
  fi
  grep "cinder.csi.openstack.org" <<<${csidriver} >/dev/null
  rc=$?
  if [[ $rc -eq 0 ]];then
    log "cinder csi is deployed"
    return 0
  fi
  log "cinder csi is not deployed"
  return 1
}

isDeployCinderCSI
rst=$?
if  [[ $rst -eq 2 ]] ;then
  log "check isDeployCinderCSI failed!!!"
  exitscript 1
fi

if [[ $rst -eq 0 ]] ;then
  if ! unzipVolumeRollbackCmd; then
    log "unzip volume rollback cmd failed"
    exitscript 1
  fi

  if [ ! -f "$rollbackCmd" ]; then
    log "cinder volumeattachment rollback cmd is not exist"
    exitscript 1
  fi

  rst=$(chmod +x $rollbackCmd 2>&1)
  rc=$?
  if [[ $rc -ne 0 ]];then
    log "add x perm to rollback cmd failed, error is $rst"
    exitscript 1
  fi

  $rollbackCmd --v=6 >>$logFile 2>&1
  rc=$?
  if [[ $rc -ne 0 ]];then
    log "exec cinder volumeattachment rollback cmd failed!!!"
    exitscript 1
  fi

  log "exec cinder volumeattachment rollback cmd success"
  exitscript 0
fi

log "not need to exec cinder volumeattachment rollback"
exitscript 0
