import logging
import json
import os
from six.moves import configparser
import six

if six.PY2:
    ConfigParser = configparser.SafeConfigParser
else:
    ConfigParser = configparser.ConfigParser


LOG_DIR = "/paasdata/op-log/pdm-cli/keydata_rollback"
if not os.path.exists(LOG_DIR):
    os.makedirs(LOG_DIR, 0o750)
LOG_FILE = "%s/recreate_pods.log" % LOG_DIR
if not os.path.exists(LOG_FILE):
    os.mknod(LOG_FILE)
logging.basicConfig(filename=LOG_FILE,
                    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
                    level=logging.DEBUG)
LOG = logging.getLogger(__name__)
os.chmod(LOG_FILE, 0o640)

keydata_bak_dir = "/paasdata/op-data/paas_upgrade_backup_keydata/"


def need_delete_all_pods():
    paas_conf = ConfigParser()
    paas_conf.read("/etc/pdm/conf/paas.conf")
    flag = False
    if paas_conf.has_option("tcf", "tcf_scenario"):
        scenario = paas_conf.get("tcf", "tcf_scenario")
        if scenario in ["UME-standard", "fusionized-TCF"]:
            flag = True
    return flag


def read_conf(conf_path):
    try:
        with open(conf_path) as conf_file:
            conf = json.load(conf_file)
    except Exception:
        LOG.error("Load %s error!", conf_path)
        print("Load %s error!", conf_path)
        return False
    return conf


def delete_all_pods_with_ansible(master_ip):
    shell_cmd = ("kubectl delete pod "
                 "--all-namespaces --all || true")
    cmd = ('ansible -i "%s" all -m shell -a "%s" '
           '-u ubuntu -b >/dev/null'
           % (master_ip, shell_cmd))
    LOG.debug('delete all pods. \n%s', cmd)
    result = os.system(cmd)
    if result != 0:
        return False
    return True


def delete_all_pods():
    LOG.info("Start to delete all pods.")
    if not need_delete_all_pods():
        LOG.info("do not need delete all pods.")
        return True
    cluster_dir = keydata_bak_dir + "node_info/cluster/cluster_node"
    for cluster_file in os.listdir(cluster_dir):
        if "kubernetes_cluster" in cluster_file and cluster_file.endswith('.json'):
            load_file = cluster_dir + "/" + cluster_file
            cluster_data = read_conf(load_file)
            for node in cluster_data["nodes"]:
                if "master" in node["roles"]:
                    cluster_uuid = node["cluster_uuid"]
                    master_ip = \
                        node["node_ip"]["net_api"] + ","
                    result = delete_all_pods_with_ansible(master_ip)
                    if not result:
                        LOG.error("cluster %s delete all pods failed!" %
                                  cluster_uuid)
                        print("cluster %s delete all pods failed!" %
                              cluster_uuid)
                        return False
                    else:
                        break
    LOG.info("deleting all pods finished.")
    print("deleting all pods finished.")
    return True


if __name__ == "__main__":
    if delete_all_pods():
        exit(0)
    else:
        exit(1)
