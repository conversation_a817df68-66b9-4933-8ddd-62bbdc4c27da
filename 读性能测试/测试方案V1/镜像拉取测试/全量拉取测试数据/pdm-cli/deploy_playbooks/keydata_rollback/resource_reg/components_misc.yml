[
  {
  "res_name": "os_harden_tmp",
  "compo_owner": "op-sec-harden",
  "judge_method": "by_role",
  "roles": [usednodes],
  "backup_cmd": "components_misc/os_harden_tmp_bak.sh",
  "clear_cmd":"",
  "restore_cmd": "components_misc/os_harden_tmp_misc.sh"
 },
 {
  "res_name": "gpuscheduler_log_owner",
  "compo_owner": "op-gpu-scripts",
  "judge_method": "by_role",
  "roles": ["master"],
  "backup_cmd": "components_misc/gpuscheduler_log_owner_bak.sh",
  "clear_cmd":"",
  "restore_cmd": "components_misc/gpuscheduler_log_owner_restore.sh"
 },
  {
  "res_name": "vnm",
  "compo_owner": "vnm",
  "judge_method": "by_role",
  "roles": ["paas_controller"],
  "backup_cmd": "components_misc/vnm/back_up_vnm_python2_dependencies.sh",
  "clear_cmd":"",
  "restore_cmd": "components_misc/vnm/restore_vnm_python2_dependencies.sh"
 }
]
