#!/bin/bash
# ===Started by AICoder>>>

# Define paths
backup_file="/paasdata/op-data/paas_upgrade_backup_keydata/misc/gpuscheduler_log_owner/logdir_owner"
gpuscheduler_log_dir="/paasdata/op-log/op-gpu-scripts"

LOG_DIR="/paasdata/op-log/pdm-cli"
LOG_FILENAME="$LOG_DIR/op_gpu_scripts_keydata_rollback.log"

if [ -f "$LOG_FILENAME" ] && [ -s "$LOG_FILENAME" ]; then
    # Clear the file
    > "$LOG_FILENAME"
fi

log() {
    LOG_DATE=$(date "+%Y-%m-%d %H:%M:%S")
    script_name="gpuscheduler_log_owner_bak.sh"

    # Ensure the log directory exists
    if [ ! -d "$LOG_DIR" ]; then
        mkdir -m 750 -p "$LOG_DIR"
    fi
    # Ensure the log file exists and has the correct permissions
    if [ ! -f "$LOG_FILENAME" ]; then
        touch "$LOG_FILENAME"
        chmod 640 "$LOG_FILENAME"
    fi
    # Ensure the log file has the correct permissions
    mode=$(stat -c %a "$LOG_FILENAME")
    if [ "$mode" != "640" ]; then
        chmod 640 "$LOG_FILENAME"
    fi

    echo "$LOG_DATE $1 [$script_name] - [$(basename ${FUNCNAME[1]})]:$2" >> $LOG_FILENAME
}

log INFO "--- op_gpu_scripts log dir owner backup --- "

# Check if the target directory exists
if [ ! -d "$gpuscheduler_log_dir" ]; then
    log INFO "Target directory $gpuscheduler_log_dir does not exist, skip."
    exit 0
fi

# Ensure the directory for the backup file exists
backup_dir=$(dirname "$backup_file")
if [ ! -d "$backup_dir" ]; then
    log INFO "Backup directory $backup_dir does not exist, creating..."
    mkdir -m 750 -p "$backup_dir"
    if [ $? -ne 0 ]; then
        log ERROR "Failed to create backup directory $backup_dir, exiting."
        exit 1
    fi
    log INFO "Backup directory $backup_dir created successfully."
fi

# Retrieve the owner information uid:gid of the target directory
owner_info=$(stat -c "%u:%g" "$gpuscheduler_log_dir" 2>&1)
if [[ "$owner_info" == *"cannot"* || "$owner_info" == *"??"* ]]; then
    log ERROR "Failed to retrieve owner information or owner is undefined."
    exit 1
else
    log INFO "Owner information retrieved successfully: $owner_info"
    echo "$owner_info" > "$backup_file"
    chmod 640 $backup_file
fi

# <<<Ended by AICoder===