#!/bin/bash
# ===Started by AICoder>>>

# Define paths
backup_file="/paasdata/op-data/paas_upgrade_backup_keydata/misc/gpuscheduler_log_owner/logdir_owner"
gpuscheduler_log_dir="/paasdata/op-log/op-gpu-scripts"

log() {
    LOG_DATE=$(date "+%Y-%m-%d %H:%M:%S")
    script_name="gpuscheduler_log_owner_restore.sh"

    LOG_DIR="/paasdata/op-log/pdm-cli"
    LOG_FILENAME="$LOG_DIR/op_gpu_scripts_keydata_rollback.log"

    # Ensure the log directory exists
    if [ ! -d "$LOG_DIR" ]; then
        mkdir -m 750 -p "$LOG_DIR"
    fi
    # Ensure the log file exists and has the correct permissions
    if [ ! -f "$LOG_FILENAME" ]; then
        touch "$LOG_FILENAME"
        chmod 640 "$LOG_FILENAME"
    fi
    # Ensure the log file has the correct permissions
    mode=$(stat -c %a "$LOG_FILENAME")
    if [ "$mode" != "640" ]; then
        chmod 640 "$LOG_FILENAME"
    fi

    echo "$LOG_DATE $1 [$script_name] - [$(basename ${FUNCNAME[1]})]:$2" >> $LOG_FILENAME
}

log INFO "--- op_gpu_scripts log dir owner recovery --- "

# Check if the target directory exists
if [ ! -d "$gpuscheduler_log_dir" ]; then
    log INFO "Target directory $gpuscheduler_log_dir does not exist, skip."
    exit 0
fi

# Check if the backup file exists
if [ ! -f "$backup_file" ]; then
    log WARNING "Backup file $backup_file does not exist, exiting."
    exit 0
fi

# Read the owner information from the backup file
owner_info=$(cat "$backup_file")
if [[ ! "$owner_info" =~ ^[a-zA-Z0-9._-]+:[a-zA-Z0-9._-]+$ ]]; then
    log ERROR "Invalid owner format in backup file: $owner_info, exiting."
    exit 1
fi

# Retrieve the current owner of the target directory. Renaming and recreating directory if owner mismatch.
current_owner=$(stat -c "%u:%g" "$gpuscheduler_log_dir")
if [[ "$current_owner" != "$owner_info" ]]; then
    log INFO "Owner mismatch: $current_owner (current) vs $owner_info (backup). Delete log files in $gpuscheduler_log_dir."

    rm -rf “$gpuscheduler_log_dir/*”
    if [ $? -eq 0 ]; then
        log INFO "Successfully deleted log files in $gpuscheduler_log_dir."
    else
        log ERROR "Failed to deleted log files in $gpuscheduler_log_dir."
        exit 1
    fi
else
    log INFO "Owner match: $current_owner (current) vs $owner_info (backup). No action needed."
fi

# <<<Ended by AICoder===