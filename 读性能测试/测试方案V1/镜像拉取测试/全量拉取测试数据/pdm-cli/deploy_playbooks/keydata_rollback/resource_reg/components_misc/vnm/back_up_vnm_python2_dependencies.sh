#!/bin/bash

log_file="/var/log/prepare_for_the_upgrade_of_infra_nw.log"
cur_path=$(dirname $(readlink -f $0))
back_dir="/paasdata/paas_upgrade_backup"

create_log_file() {

    if [ ! -f "$log_file" ]; then
        touch "$log_file"
    else
        echo "Log file already exists: $log_file"
    fi

    chmod 640 "$log_file"
}

write_log() {

    local log_content="$1"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')

    echo "$timestamp $log_content" >> "$log_file"
}

generate_hosts_file() {
    cd "$(dirname "$0")" || exit 1
    if command -v pythonlatest &> /dev/null; then
        pythonlatest hosts.py
    else
        python hosts.py
    fi
    rc=$?
    if [ $rc -eq 0 ]; then
        write_log "generate hosts successfully."
    else
        write_log "generate hosts failed."
        cd "$cur_path" || exit 0
        exit 2
    fi
}

prepare_for_vnm() {
    generate_hosts_file
    filename="control_node_hosts_agent"
    ip_list=()
    while IFS= read -r line; do
        if [[ $line == "[nodes]" ]]; then
            continue
        fi
        ip_list+=("$line")
    done < "$filename"

    for ip in "${ip_list[@]}"; do
        ssh -o ConnectTimeout=3 ubuntu@"$ip" 'sudo docker exec vnm ps -ef | grep python2' > /dev/null 2>&1
        if [[ $? -eq 0 ]]; then
            if [ ! -d "$back_dir" ]; then
                mkdir -p "$back_dir"
                write_log "directory $back_dir created."
            fi
            chmod 0750 "$back_dir"
            write_log "permissions for $back_dir set to 0750."
            tar -cvf $back_dir/usr.tar /usr/lib/python2.7/site-packages/jinja2 /usr/lib/python2.7/site-packages/chardet /usr/lib/python2.7/site-packages/babel /usr/lib64/python2.7/xml /usr/lib64/python2.7/site-packages/Crypto /usr/lib64/python2.7/multiprocessing /usr/lib64/python2.7/encodings /usr/lib64/python2.7/email /usr/lib64/python2.7/distutils /usr/lib64/python2.7/ctypes
            write_log "package usr.tar."
            break
        else
            write_log "vnm is not running with python2 on $ip."
            rm -f $back_dir/usr.tar
        fi
    done
}


create_log_file
prepare_for_vnm

exit 0