#!/usr/bin/python

import httplib2
import json
import os
import time
import uuid
import yaml
from six.moves import http_client

SUCCESS = [http_client.OK, http_client.CREATED,
           http_client.ACCEPTED, http_client.NO_CONTENT]


def get_yaml_val(comvars, element):
    with open(comvars) as f:
        dataMap = yaml.safe_load(f)
    for var in dataMap:
        if element in var:
            element_value = var[element]
            break
    return element_value


def _request(url, method, header=None, data=None,
             session_id=None, timeout=60):
    try:
        if session_id is not None and data is not None:
            data['session_id'] = session_id
        http = httplib2.Http(timeout=timeout)
        if data is None:
            response, content = http.request(url,
                                             method,
                                             headers=header)
        else:
            response, content = http.request(url,
                                             method,
                                             body=json.dumps(data),
                                             headers=header)
        if '' != content and content is not None:
            content = json.loads(content)
        result = True if response.status in SUCCESS else False
        return result, content
    except Exception as e:
        print("Http request failed.(%s)", str(e))
        return False, None


def request(url, method, header=None, data=None,
            need_retry=True, with_session_id=False, timeout=None):
    result = False
    content = None

    try_count = 1
    if need_retry:
        try_count = 5

    session_id = None
    if with_session_id:
        session_id = str(uuid.uuid4())

    for _ in range(try_count):
        result, content = _request(url, method, header,
                                   data, session_id, timeout)
        if result:
            break
        time.sleep(5)
    return result, content


def get_nodelist_from_nodeworker(OP_SERVICE_URL):
    url = ("http://%s/nodeworker/v1/tenants/admin/nodes" %
           OP_SERVICE_URL)
    result, nodelist = request(url, 'GET')
    if not result or 'nodes' not in nodelist:
        print("Get nodelist error.")
        return False
    return nodelist


def get_nodelist(OP_SERVICE_URL):
    need_get_from_nodeworker = True
    nodes = "/paasdata/op-data/pdm-cli/node_info/nodes"
    file_exist = os.path.exists(nodes)
    if file_exist:
        try:
            with open(nodes, 'r') as f:
                nodelist = json.load(f)
                need_get_from_nodeworker = False
        except Exception as e:
            print("exception(%s) happens!" % e)
    if need_get_from_nodeworker:
        nodelist = get_nodelist_from_nodeworker(OP_SERVICE_URL)
    if not nodelist:
        return []
    return nodelist['nodes']


def get_control_nodelist(OP_SERVICE_URL):
    url = ("http://%s/nodeworker/v1/tenants/admin/nodes?roles=paas_controller" %
           OP_SERVICE_URL)
    result, nodelist = request(url, 'GET')
    if not result or 'nodes' not in nodelist:
        print("Get nodelist error.")
        return []
    return nodelist['nodes']


def write_all_nodes_ip(nodes, hosts_file):
    # hosts_file = 'node_hosts_agent'
    hosts = []
    for node in nodes:
        info = {}
        if "netinfo" in node and node["netinfo"].get("net_mgt"):
            info["man_ip"] = node["netinfo"]["net_mgt"]["ip"]
            hosts.append(info)
    if not hosts:
        cmd = ("rm -fr %s && cp %s %s"
               % (hosts_file, '/etc/pdm/all_nodes', hosts_file))
        res = os.system(cmd)
        if res != 0:
            print('cp /etc/pdm/all_nodes failed')
            return False
        else:
            print('cp /etc/pdm/all_nodes ok')
            return True
    fo = open(hosts_file, 'w')
    fo.write("[nodes]\n")
    for node_loop in hosts:
        fo.write(node_loop['man_ip'])
        fo.write("\n")
    fo.flush()
    fo.close()
    return True


def main():
    try:
        COM_VARS_FILE = "/root/common/com_vars.yml"
        PORT_VARS_FILE = "/root/common/port_vars.yml"
        ops_ip = get_yaml_val(COM_VARS_FILE, 'openpalette_service_ip')
        if not os.path.exists(PORT_VARS_FILE):
            ops_port = str(get_yaml_val(COM_VARS_FILE,
                                        'openpalette_service_port'))
        else:
            ops_port = str(get_yaml_val(PORT_VARS_FILE,
                                        'openpalette_service_port'))
        OP_SERVICE_URL = (ops_ip + ":" + ops_port)
        # nodes = get_nodelist(OP_SERVICE_URL)
        # res = write_all_nodes_ip(nodes, 'node_hosts_agent')
        control_nodes = get_control_nodelist(OP_SERVICE_URL)
        control_res = write_all_nodes_ip(control_nodes, 'control_node_hosts_agent')
        if not control_res:
            return False
    except Exception as e:
        print("generate hosts failed! except=%s" % str(e))
        return False
    print("generate hosts successfully!")
    return True


if __name__ == "__main__":
    main()