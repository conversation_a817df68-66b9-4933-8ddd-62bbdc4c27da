#!/bin/bash


USR_TAR_FILE="/paasdata/paas_upgrade_backup/usr.tar"
PYTHON_FILE="/usr/lib64/python2.7/email/parser.py"
LOG_FILE="/var/log/restore_vnm_python2_dependencies.log"


create_log_file() {

    if [ ! -f "$LOG_FILE" ]; then
        touch "$LOG_FILE"
    else
        write_log "Log file already exists: $LOG_FILE"
    fi
    chmod 640 "$LOG_FILE"
}

write_log() {

    local log_content="$1"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')

    echo "$timestamp $log_content" >> "$LOG_FILE"
}

restore_vnm() {

    if [ -f "$USR_TAR_FILE" ]; then
        write_log "usr.tar exists."
        
        if [ ! -f "$PYTHON_FILE" ]; then
            write_log "$PYTHON_FILE does not exist. Extracting $USR_TAR_FILE to /..."
            tar -xf "$USR_TAR_FILE" -C /
            
            if [ $? -eq 0 ]; then
                write_log "Successfully extracted $USR_TAR_FILE to /."
            else
                write_log "Failed to extract $USR_TAR_FILE."
                exit 1
            fi
        else
            write_log "$PYTHON_FILE exists. Skipping extraction of $USR_TAR_FILE."
        fi
    else
        write_log "usr.tar does not exist. No action taken."
    fi
}

create_log_file
restore_vnm