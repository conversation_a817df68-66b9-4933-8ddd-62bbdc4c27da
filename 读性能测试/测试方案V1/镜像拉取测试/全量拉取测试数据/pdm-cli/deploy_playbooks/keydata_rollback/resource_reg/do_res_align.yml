---
- hosts: nodes
  remote_user: ubuntu
  become: yes
  become_method: sudo
  any_errors_fatal: true
  gather_facts: no

  tasks:
    - name: check "{{ script_path }}" exist
      stat:
        path: "{{ script_path }}"
      register: script_path_exist

    - name: fail is {{ script_path }} not exist
      fail: msg="{{ script_path }} not exist"
      when: not ignore_file_exist

    - name: exec script_path
      shell: "bash {{ cmd }}"
      run_once: true
      when: script_path_exist.stat.exists == True
