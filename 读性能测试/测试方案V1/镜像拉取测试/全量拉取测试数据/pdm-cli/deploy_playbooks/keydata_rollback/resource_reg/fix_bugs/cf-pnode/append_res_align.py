import yaml

res_align_path = "/etc/pdm_bak/deploy_playbooks/keydata_rollback/resource_reg/res_align.yml"


new_res = {
    "path": "/paasdata/op-data/cf-pnode/keydata_rollback/csi_pvc_rollback.sh",
    "args": [],
    "desc": "tcf rollback to cpaas align csi pvc",
    "component": "cf-pnode",
    "role": "paas_controller",
    "not_exist_ignored": True
}


def append_align_resource(target_file):
    with open(target_file, 'r') as f:
        origin_res = yaml.safe_load(f.read())

    if origin_res is None:
        origin_res = []
    for res in origin_res:
        if "csi_pvc_rollback.sh" in res.get("path"):
            return
    origin_res.append(new_res)
    with open(target_file, 'w') as f:
        yaml.safe_dump(origin_res, f, encoding='utf-8', allow_unicode=True)


if __name__ == "__main__":
    append_align_resource(res_align_path)
