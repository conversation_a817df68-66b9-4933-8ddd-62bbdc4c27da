from cloudframe.common import service as my_service

import eventlet
eventlet.monkey_patch()

from eventlet import GreenPool
from eventlet import GreenPile
from eventlet import greenthread
import json
import logging
import os
import sys
import subprocess

from cloudframe.nodeworker.cmd import pg_decrypt
from cloudframe.nodeworker.conf import CONF
from cloudframe.nodeworker.common import context
from cloudframe.nodeworker.common import exception
from cloudframe.nodeworker import objects
from cloudframe.nodeworker.drivers import baremetal
from cloudframe.nodeworker.manager import NodeWorkerManager
from cloudframe.nodeworker import utils

from oslo_log import log


LOG = log.getLogger(__name__)

volume_api = baremetal.BareMetal()
MAX_VOLUME_NUM = 10


def subprocess_popen_readlines(cmds, shell=True, timeout=15):
    LOG.info("subprocess_popen_readlines is called for cmds(%s)" % cmds)
    proc = subprocess.Popen(cmds, shell=shell, stdout=subprocess.PIPE,
                            stderr=subprocess.PIPE)
    greenthread.sleep(0)
    try:
        outs, errs = proc.communicate(timeout=timeout)
    except subprocess.TimeoutExpired:
        LOG.warn("run cmds(%s) timeout" % cmds)
        outs, errs = proc.communicate()
    except Exception as e:
        LOG.error("run cmds(%s) error with(%s)" % (cmds, e))
        outs, errs = proc.communicate()
    if errs:
        LOG.warn("run cmds(%s) with err(%s)" % (cmds, errs.decode()))
    return outs.decode().strip().split("\n")


def analyze_ansible(cmds, timeout=300):
    lines = subprocess_popen_readlines(cmds, shell=True, timeout=timeout)
    result = False
    for line in lines:
        if "failed=1" in line or "unreachable=1" in line:
            return False
        if "ERROR! Syntax Error" in line:
            return False
        if "failed=0" in line and "unreachable=0" in line:
            result = True
    return result


def run_volume_playbook(node_uuid, db_target, action):
    filters = {"serverid": node_uuid}
    nodes = objects.Node.list(context, filters=filters)
    if len(nodes) == 0:
        return False
    hostfile = utils.generate_hostfile_baremetal(nodes[0])
    playbook_path = "/root/nodeworker/playbooks"
    cmds = ["ansible-playbook", "-i", hostfile, "VolumeMain.yml"]

    node_uuid = node_uuid.encode("utf-8").decode()

    extra = json.dumps(db_target.extra)
    connection_info = json.dumps(db_target.properties)
    extra_vars_str = {"extra": extra,
                      "connection_info": connection_info,
                      "node_uuid": node_uuid,
                      "action": action,
                      "do_scrub_residual":
                          db_target.extra.get("do_scrub_residual", True)
                      }
    extra_vars_str = \
        str(extra_vars_str).replace('"', "'").\
        replace("'{", "{").replace("}'", "}")
    cmds = cmds + ["--extra-vars", '"' + str(extra_vars_str) + '"']

    cmd_str = " ".join(cmds)
    LOG.info("ansible command is: %s" % cmd_str)
    os.chdir(playbook_path)
    if not analyze_ansible(cmd_str, timeout=300):
        LOG.error("Failed to %s volume %s on node %s with ansible-playbook"
                  " script" % (action, db_target.volume_id, node_uuid))
        return False
    LOG.info("Do volume %s on node %s Successfully" % (action, node_uuid))
    return True


def scan_volume(node_uuid, db_target):
    volume_id = db_target.volume_id
    LOG.info("start to execute node %s with %s volume %s, "
             "db_target.node_uuid %s" %
             (node_uuid, "attach", volume_id, db_target.node_uuid))
    res = run_volume_playbook(node_uuid, db_target, "attach")
    if not res:
        LOG.warn("run volume playbook failed for node %s with attach volume %s"
                 % (node_uuid, volume_id))
        msg = "failed to run playbook for volume attach"
        raise exception.VolumeAttachFailed(volume_id=volume_id, error=msg)

    LOG.info("run volume playbook success for node %s with "
             "attach volume %s" % (node_uuid, volume_id))


def initialize_connection(volume_id, connector):
    connection_info = volume_api.initialize_connection(context, volume_id,
                                                       connector)
    if 'serial' not in connection_info:
        connection_info['serial'] = volume_id
    return connection_info


def get_VolumeTarget_by_node_and_volume(node, volume):
    node_uuid = volume["attach_to"]
    volumeid = volume["volumeid"]
    args = (context, node.serverid, volumeid)
    try:
        volume_target = objects.VolumeTarget.get_by_node_and_volume(*args)
        return volume_target
    except Exception as e:
        LOG.error(msg='get volume target, volume(%s) in node(%s).reason:%s'
                  % (volumeid, node_uuid, e))
        if volume["name"].startswith("dynamic_lun"):
            LOG.info("delete dynamic volume in storage: %s" % volumeid)
            storages = objects.Storage.list(context,
                                            filters={"volumeid": volumeid,
                                                     "volume_status":
                                                     "attaching"})
            for storage in storages:
                storage.destroy()
            return None
        else:
            raise e


def correct_volume_action(volume):
    volumeid = volume["volumeid"]
    node_uuid = volume["attach_to"]
    LOG.info("[Start] correct volume action. node:%s, volume:%s"
             % (node_uuid, volumeid))
    greenthread.sleep(0)
    try:
        node = objects.Node.get_by_uuid(context, volume["attach_to"])

        args = (context, node.serverid)
        connector = objects.VolumeConnector.get_by_node_uuid(*args)

        connection_info = initialize_connection(volumeid, connector.extra)

        volume_target = get_VolumeTarget_by_node_and_volume(node, volume)
        if volume_target is None:
            LOG.info("[End] volume no correct. node:%s, volume:%s"
                     % (node_uuid, volumeid))
            return
        volume_target.properties = connection_info

        scan_volume(node.serverid, volume_target)
        volume_target.save()
    except Exception as e:
        LOG.error(msg='correct volume(%s) in node(%s).reason:%s'
                      % (volumeid, node_uuid, e))
        raise e
    LOG.info("[End] correct volume action. node:%s, volume:%s"
             % (node_uuid, volumeid))


def correct_comsrv_volume():
    resp = NodeWorkerManager.get_all_volumes(context, "admin", {})
    all_volumes = resp.get('volumes', [])
    all_volumes = sorted(all_volumes, key=lambda x: str(x["attach_to"]))

    _worker_pool = GreenPool(MAX_VOLUME_NUM)
    pile = GreenPile(_worker_pool)
    for volume in all_volumes:
        if not volume.get("attach_to"):
            continue
        pile.spawn(correct_volume_action, volume)
    pile_list = []
    for pi in pile:
        pile_list.append(pi)


def main():
    my_service.prepare_service1(sys.argv)
    pg_decrypt.decode_encrypt_passwd()

    LOG.debug("Configuration:")
    CONF.log_opt_values(LOG, logging.DEBUG)
    net_api_ver, _ = utils.get_api_mgt_default_ip_version()
    CONF.net_api_ver = net_api_ver
    correct_comsrv_volume()


if __name__ == '__main__':
    main()
