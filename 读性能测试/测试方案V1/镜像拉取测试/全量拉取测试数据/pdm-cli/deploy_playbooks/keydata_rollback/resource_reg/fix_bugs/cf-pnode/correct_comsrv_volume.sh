#!/bin/sh

log_info() {
    LOG_DATE=`date "+%Y-%m-%d %H:%M:%S"`
    LOG_FILENAME="/var/log/cloudframe/volume_keydata_rollback.log"
    echo "$LOG_DATE $1" >> $LOG_FILENAME
}

sed -i "s/umask 022/umask 027/g" /etc/profile
source /etc/profile

log_info "python setup.py install start"
cd /root/temp/cloudframe
python setup.py install --user
if [ $? -ne 0 ]; then
    log_info "python setup.py install failed, exit 1!"
    exit 1
fi
log_info "python setup install successfully"

\cp -r /root/correct_volume/correct_comsrv_volume.yml /root/nodeworker/playbooks/VolumeMain.yml || exit 1

for i in `seq 1 10`
    do
        python /root/correct_volume/correct_comsrv_volume.py \
        --config-file=/etc/cloudframe/cf-pnode.conf \
        1>>/var/log/cloudframe/volume_keydata_rollback.log \
        2>>/var/log/cloudframe/volume_keydata_rollback.log
        if [ $? -eq 0 ]; then
            log_info "correct comsrv volume finished"
            exit 0
        fi
        if [ ${i} -eq 10 ]; then
            log_info "correct comsrv volume failed, exit 1!"
            exit 1
        fi
        sleep 10
        log_info "correct comsrv volume fail ${i} times, need retry"
    done
