---
- hosts: nodes
  remote_user: ubuntu
  gather_facts: no
  become: yes
  become_method: sudo

  tasks:
    - name: create node info path
      file:
        path: /root/posd_init_scripts
        state: directory
        mode: 0750

    - name: copy volume_attachments.py to remote machine
      copy:
        src: posd_init_scripts/volume_attachments.py
        dest: /root/posd_init_scripts/
        mode: 0744

    - name: touch template for connection
      template:
        src: posd_init_scripts/connection_info.tmpl
        dest: /root/posd_init_scripts/{{connection_info["data"]["volume_id"]}}

    - name: execute python script to do volume attach
      shell: |
        if python2 -c 'import os_brick' > /dev/null 2>&1; then
          python2 /root/posd_init_scripts/volume_attachments.py "attach" /root/posd_init_scripts/{{connection_info["data"].volume_id}}
        else
          python3 /root/posd_init_scripts/volume_attachments.py "attach" /root/posd_init_scripts/{{connection_info["data"].volume_id}}
        fi
      register: attach_result
      async: 300
      poll: 10

    - debug:
        msg: "{{ attach_result }}"

    - name: get device name and device by id
      set_fact:
        device_name: "{{ attach_result.stdout_lines[0] }}"
        device_by_id: "{{ attach_result.stdout_lines[1] }}"
      failed_when: attach_result.stdout == ""

    - name: show device name
      debug:
        msg: "{{ device_name }}"

    - name: show device by id
      debug:
        msg: "{{ device_by_id }}"
