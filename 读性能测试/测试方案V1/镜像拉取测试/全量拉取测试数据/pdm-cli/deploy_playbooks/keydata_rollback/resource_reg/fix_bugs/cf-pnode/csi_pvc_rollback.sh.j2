#!/bin/bash
#shellcheck disable=SC1083,SC2034
export LANG=EN_US

swr_registry_ip="{{ swr_registry_ip }}"
swr_registry_port="{{ swr_registry_port }}"

LOG=/paasdata/op-data/cf-pnode/keydata_rollback/log_print.sh
csi_revert_flag="/paasdata/paas_upgrade_backup/cf-pnode/csi_revert_flag"
comsrv_vol_container_name="comsrv_volume_correct"
pkg_ver_list="/etc/pdm_bak/deploylist/pkg_ver.list"

op_host_localtime="/etc/localtime"
cf_base_datadir="/paasdata/op-data/op-ubs-python3lib"
component_confdir="/paasdata/op-conf/cf-pnode"
component_logdir="/paasdata/op-log/cf-pnode"
keydata_rollback_pnode_dir="/paasdata/op-data/cf-pnode/keydata_rollback"
common_vars_host_dir="/root/common"

stop_service()
{
  if rpm -q --quiet pcs;then
    pcs resource disable cf-pnode --retry_wait=12
  else
    crm resource stop cf-pnode
  fi
}

stop_pnode_service()
{
    ${LOG} INFO "$0.$LINENO" $$ "start to stop cf-pnode service"
    for(( i=0; i<5; i++ ))
    do
        stop_service
        for(( j=0; j<5; j++ ))
        do
            if crm resource status cf-pnode 2>&1 |grep "running on"; then
                ${LOG} INFO "$0.$LINENO" $$ "still wait for stop cf-pnode service... $j times"
                sleep 3s
            else
                ${LOG} INFO "$0.$LINENO" $$ "stop cf-pnode service successfully!"
                return 0
            fi
        done
        ${LOG} WARNING "$0.$LINENO" $$ "stop cf-pnode service failed for the $i times"
        sleep 3s
    done

    ${LOG} ERROR "$0.$LINENO" $$ "stop cf-pnode service failed for more than 5 times, exit 1!"
    exit 1
}

start_service()
{
  if rpm -q --quiet pcs;then
    pcs resource enable cf-pnode --retry_wait=12
  else
    crm resource start cf-pnode
  fi
}


start_pnode_service()
{
    ${LOG} INFO "$0.$LINENO" $$ "start to start cf-pnode service"
    for(( i=0; i<5; i++ ))
    do
        start_service
        for(( j=0; j<5; j++ ))
        do
            if crm resource status cf-pnode 2>&1 |grep "NOT running"; then
                ${LOG} INFO "$0.$LINENO" $$ "still wait for start cf-pnode service... $j times"
                sleep 3s
            else
                ${LOG} INFO "$0.$LINENO" $$ "start cf-pnode service successfully!"
                return 0
            fi
        done
        ${LOG} WARNING "$0.$LINENO" $$ "start cf-pnode service failed for the $i times"
        sleep 3s
    done

    ${LOG} ERROR "$0.$LINENO" $$ "start cf-pnode service failed for more than 5 times, exit 1!"
    exit 1
}

delete_comsrv_volume_correct_container()
{
    ${LOG} INFO "$0.$LINENO" $$ "start to delete $comsrv_vol_container_name container"
    for(( i=0; i<5; i++ ))
    do
        docker rm -f $comsrv_vol_container_name 2>/dev/null
        for(( j=0; j<5; j++ ))
        do
            if [[ -n $(docker ps -a -q -f "name=$comsrv_vol_container_name") ]]; then
                ${LOG} INFO "$0.$LINENO" $$ "still wait for delete $comsrv_vol_container_name container... $j times"
                sleep 3s
            else
                ${LOG} INFO "$0.$LINENO" $$ "delete $comsrv_vol_container_name container successfully!"
                return 0
            fi
        done
        ${LOG} WARNING "$0.$LINENO" $$ "delete $comsrv_vol_container_name container failed for the $i times"
        sleep 3s
    done

    ${LOG} ERROR "$0.$LINENO" $$ "delete $comsrv_vol_container_name container failed for more than 5 times, exit 1!"
    exit 1
}

run_comsrv_volume_correct_container()
{
    ${LOG} INFO "$0.$LINENO" $$ "<Begin> run comsrv_volume_correct container!"

    if [[ ! -f "$pkg_ver_list" ]];then
        ${LOG} ERROR "$0.$LINENO" $$ "$pkg_ver_list file is not extist, exit 1!"
        exit 1
    fi

    cp_version=$(jq -r '.[] | select(.name == "cf-pnode") | .version' $pkg_ver_list 2>/dev/null)
    container_image_url="$swr_registry_ip:$swr_registry_port/admin/cf-pnode:$cp_version"

    ${LOG} INFO "$0.$LINENO" $$ "container image url is $container_image_url!"
    docker run --name $comsrv_vol_container_name \
    --net host \
    -v $cf_base_datadir/usr:/usr \
    -v $cf_base_datadir/lib:/lib \
    -v $component_confdir:/etc/cloudframe \
    -v $component_logdir:/var/log/cloudframe \
    -v $op_host_localtime:/etc/localtime:ro \
    -v $keydata_rollback_pnode_dir:/root/correct_volume \
    -v $common_vars_host_dir:/root/common \
    $container_image_url /bin/sh /root/correct_volume/correct_comsrv_volume.sh > /dev/null 2>&1
    rc=$?
    ${LOG} INFO "$0.$LINENO" $$ "<Over> run comsrv_volume_correct container!"
    return $rc
}

correct_comsrv_volume()
{
    ${LOG} INFO "$0.$LINENO" $$ "Begin to correct comsrv volume!"

    delete_comsrv_volume_correct_container

    run_comsrv_volume_correct_container
    correct_result=$?

    if [[ "$correct_result" -eq 0 ]];then
        ${LOG} INFO "$0.$LINENO" $$ "correct comsrv volume successfully!"
        delete_comsrv_volume_correct_container
    else
        ${LOG} ERROR "$0.$LINENO" $$ "correct comsrv volume failed, result code is $correct_result. exit 1!"
        exit 1
    fi
}


main()
{
    ${LOG} INFO "$0.$LINENO" $$ "********* csi volume revert rollback start *********"
    if [ ! -f "${csi_revert_flag}" ]; then
        ${LOG} INFO "$0.$LINENO" $$ "no $csi_revert_flag file, skip"
        exit 0
    fi

    stop_pnode_service
    correct_comsrv_volume
    start_pnode_service

    ${LOG} INFO "$0.$LINENO" $$ "********* csi volume keydata rollback end ***********"
}

main
