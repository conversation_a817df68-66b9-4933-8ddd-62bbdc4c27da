- set_fact:
    volume_id: "{{ volume_info.volume_id }}"
    ori_device: "{{ volume_info.ori_device }}"

- name: try get local volume file for {{ volume_id }}
  stat:
    path: "{{ scripts_path }}/{{ volume_id }}"
  register: local_volume_file

- when: local_volume_file.stat.exists | bool
  block:
  - name: try get local device path
    shell: python {{scripts_path}}/volume_attachments.py get_device {{scripts_path}}/{{volume_id}}
    register: local_volume
    ignore_errors: yes

  - debug:
      msg: "{{ local_volume }}"

  - set_fact:
      local_volume_path: "{{ local_volume.stdout }}"
    when: local_volume.stdout != ""

  - when:
      - local_volume_path is defined
    block:
    - name: try get vg by "{{ local_volume_path }}"
      include: get_vg_by_device.yml device="{{ local_volume_path }}"

    - when: (vg_name == expect_vg and lv_name == expect_lv) or (vg_name == "" and maybe_new_lun | bool)
      block:
      - debug:
          msg: "[{{ inventory_hostname }}] get actually device of {{ volume_id }}: {{ local_volume_path }}"

      - name: collect info to {{result_path}}/check_volume_{{expect_vg}}
        shell: |
          echo '{"ip": "{{ip}}", "serverid": "{{serverid}}", "device": "{{local_volume_path}}", "volume_id":"{{volume_id}}", "ori_device":"{{ori_device}}"},' >> {{ result_path }}/{{result_file}}
        delegate_to: localhost

