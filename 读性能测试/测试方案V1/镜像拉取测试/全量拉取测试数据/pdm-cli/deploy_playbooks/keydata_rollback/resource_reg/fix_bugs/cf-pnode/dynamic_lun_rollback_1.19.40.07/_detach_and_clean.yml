- name: try detach by posd script
  shell: python {{scripts_path}}/volume_attachments.py "detach" {{scripts_path}}/{{volume_id}}
  register: detach_result
  ignore_errors: yes

- debug:
    msg: "{{ detach_result }}"

- name: get detached device
  shell: echo {{ detach_result.stdout }} |awk 'NR==1{print}'
  register: device_name_string
  ignore_errors: yes

- set_fact:
    device_name: "{{ device_name_string.stdout }}"
  when:
    - device_name_string.stdout not in ["", "None", "/dev/mapper/None"]

- when: device_name is defined
  block:
  - name: reload udevadm trigger
    shell: udevadm trigger

  - name: reload udevadm settle
    shell: udevadm settle

  - name: get ww-uuid from bingding file
    shell: |
        device=`echo {{ device_name }} | awk -F '/' '{print $NF}'`
        cat /etc/multipath/bindings | grep "$device" | awk '{print $2}'
    register: ww_uuid

  - debug:
      msg: "{{ ww_uuid }}"

  - name: clean the residual single path device
    shell: |
       sdX_name=`lsblk -ls {{ device_name }} -o NAME,TYPE --noheading |awk -F ' ' '$2=="disk"{print}' |awk -F ' ' '{print $1}'`
       for item in $sdX_name; do
          echo 1 > /sys/block/$item/device/delete
       done
       multipath -f {{ device_name }}
       is_clean=`multipath -l {{ device_name }}`
       if [ -n "${is_clean}" ];then
         exit 1
       else
         exit 0
       fi
    register: clean_mpath_result
    ignore_errors: yes

  - name: clear ww-uuid info about mount_dev in /etc/multipath/wwids
    lineinfile:
      dest: /etc/multipath/wwids
      regexp: "{{ ww_uuid.stdout }}"
      state: absent
    when:
      - ww_uuid is defined
      - clean_mpath_result is defined and clean_mpath_result.rc == 0

  - name: clear ww-uuid info about mount_dev in /etc/multipath/bindings
    lineinfile:
      dest: /etc/multipath/bindings
      regexp: "{{ ww_uuid.stdout }}"
      state: absent
    when:
      - ww_uuid is defined
      - clean_mpath_result is defined and clean_mpath_result.rc == 0

- name: clean file of volume target info
  file:
    path: "{{scripts_path}}/{{volume_id}}"
    state: absent