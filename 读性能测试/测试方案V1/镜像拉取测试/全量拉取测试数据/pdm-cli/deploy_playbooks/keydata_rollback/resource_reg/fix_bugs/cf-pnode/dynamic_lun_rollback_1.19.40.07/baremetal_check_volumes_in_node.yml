---
- hosts: nodes
  remote_user: ubuntu
  gather_facts: no
  become: yes
  become_method: sudo

  vars:
    scripts_path: "/root/posd_init_scripts"
    local_volume_path: ""
    vg_name: ""
    lv_name: ""

  tasks:
    - block:
      - name: mkdir for {{ result_path }}
        file:
          path: "{{ result_path }}"
          state: directory
          mode: '0750'

      - name: clean old result file
        file:
          path: "{{ result_path }}/{{result_file}}"
          state: absent
        ignore_errors: yes

      - name: create result file {{ result_path }}/{{result_file}}
        file:
          path: "{{ result_path }}/{{result_file}}"
          state: touch
          mode: "0640"

      - name: write start to {{ result_path }}/{{result_file}}
        shell: echo '{"result":[' >> {{ result_path }}/{{result_file}}
      delegate_to: localhost
      run_once: yes

    - name: mkdir for {{scripts_path}}
      file:
        path: "{{ scripts_path }}"
        state: directory
        mode: '0755'

    - name: copy volume_attachments.py to dest
      copy:
        src: volume_attachments.py
        dest: "{{scripts_path}}"
        mode: "0640"

    - include: _check_volume_in_node.yml volume_info="{{item}}"
      with_items: "{{ volumes_info }}"

    - block:
      - name: write end to {{ result_path }}/{{result_file}}
        shell: echo '{}]}' >> {{ result_path }}/{{result_file}}

      - name: show check result of "{{ result_path }}/{{result_file}}"
        shell: cat "{{ result_path }}/{{result_file}}"
        register: check_result
        ignore_errors: yes

      - debug:
          msg: "{{ check_result.stdout }}"
        when: check_result.stdout != ""
      delegate_to: localhost
      run_once: yes
