---
- hosts: nodes
  remote_user: ubuntu
  gather_facts: no
  become: yes
  become_method: sudo

  vars:
    scripts_path: "/root/posd_init_scripts"

  tasks:
#    - name: try umount "/dev/mapper/{{expect_vg}}-{{expect_lv}}"
#      shell: umount "/dev/mapper/{{expect_vg}}-{{expect_lv}}"
#      ignore_errors: yes
#
#    - name: try de-active vg
#      shell: vgchange -an {{ expect_vg }}
#      ignore_errors: yes
#
#    - name: copy volume_attachments.py to dest
#      copy:
#        src: volume_attachments.py
#        dest: "{{scripts_path}}"
#        mode: "0640"

    - include: _detach_and_clean.yml volume_id="{{ item }}"
      with_items: "{{ volumes }}"

