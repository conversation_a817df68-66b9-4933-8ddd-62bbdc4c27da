---
- hosts: nodes
  remote_user: ubuntu
  become: yes
  become_method: sudo
  gather_facts: no

  tasks:
    - debug:
        msg: "{{ device }}"

# device may be like /dev/vdc /dev/mapper/mpatha /dev/disk/by-id/virtio-234345455

    - name: check {{ deivce }} is exsits
      shell: if [[ -b "{{ device }}" ]]; then echo "exists"; fi
      register: is_device
      ignore_errors: yes

    - block:
      - name: try umount {{ device }} lvm
        shell: |
            mountpoints=`lsblk -l {{ device }} --noheading -o name,mountpoint | awk -F ' ' 'NF>1{print $2}'`
            for mountpoint in $mountpoints; do
                lvm_device=`mount |grep " $mountpoint " | awk -F ' ' '{print $1}'`
                dm_device=`realpath ${lvm_device} | awk -F '/' '{print $NF}'`
                umount $mountpoint
                umount_result=`ps -ef | grep "log/${dm_device}]" | grep -v grep`
                echo $umount_result
                if [[ -n $umount_result ]]; then
                    echo "umount failed for $mountpoint"
                fi
                sed -i "s@^[^#].*\s\+$mountpoint\s\+.*@@g" /etc/fstab
                sed -i '/^$/d' /etc/fstab
                new_mountpoint=$mountpoint"/"
                sed -i "s@^[^#].*\s\+$new_mountpoint\s\+.*@@g" /etc/fstab
                sed -i '/^$/d' /etc/fstab
            done
        register: umount_result_out
        retries: 10
        delay: 1
        until: umount_result_out.stdout.find("umount failed for") == -1
        ignore_errors: yes

      - debug:
          msg: "{{ umount_result_out.stdout_lines }}"

      - name: get mount info of device {{ device }} after umounted
        shell: |
          lv_name=`lsblk -nr  {{device}} |grep -w lvm  |awk '{ print $1 }'`
          if [ -n "${lv_name}" ];then
            lv_name="/dev/mapper/${lv_name}"
          else
            lv_name="{{device}}"
          fi
          if [ ! -b $lv_name ];then
            echo "$lv_name device not found"
            exit 0
          fi
          echo "=================mountinfo========================"
          loop_time=0
          for proc_i in  /proc/[0-9]*
          do
            mountinfo=`grep -w "${lv_name}"  $proc_i/mountinfo |grep -v grep`
            if [[ -n "$mountinfo" ]];then
              pid=${proc_i//\/proc\//}
              ps_info=`ps -q $pid -o pid,ppid,comm,args --no-headers |grep -v "\[*\]"`
              if [[ -n "$ps_info" ]];then
                loop_time=$(($loop_time+1))
                echo $ps_info
              fi
            fi
            if [ $loop_time -gt 10 ];then
              exit 0
            fi
          done
        ignore_errors: yes
        register: mountinfo

      - debug:
          msg: "{{mountinfo.stdout_lines}}"
        ignore_errors: yes

      - name: systemctl daemon-reload
        shell: systemctl daemon-reload
        ignore_errors: yes

      - name: get real device name by {{ device }}
        shell:  |
            like_disk_by_id=`echo $device | grep "/dev/disk/by-id"`
            if [[ $like_disk_by_id != "" ]]; then 
                device_name_sig=`readlink {{ device }} |awk -F '/' '{print $NF}'`
                device_name=/dev/$device_name_sig
            else 
                device_name={{ device }}
            fi
            echo $device_name
        register: device_name_out

      - debug:
          msg: "{{ device_name_out.stdout }}"

      - set_fact:
          device_name: "{{ device_name_out.stdout }}"

      - name: get vgname by {{ device }}
        shell:  |
            vg_name=`pvs -o pv_name,vg_name {{ device_name }} |grep {{ device_name }}| awk -F ' ' '{print $2}'`
            echo $vg_name
        register: vg_name

      - set_fact:
           vgname: "{{ vg_name.stdout }}"

      - debug:
          msg: "{{ vgname }}"

      - name: deactive vg {{ vgname }}
        shell: vgchange -a n {{ vgname }}
        when: vgname != "" and vgname not in ["ncl", "ncldata"]
        ignore_errors: yes

      - name: check deactive result
        shell: dmsetup info /dev/mapper/{{ vgname }}-*
        register: deactive_result
        ignore_errors: yes

      - debug:
          msg: "deactive vg failed {{ deactive_result.stdout }}"
        when: deactive_result.rc == 0

      when: is_device.rc==0 and is_device.stdout=="exists"
