#!/usr/bin/env python

import httplib2
import json
import logging
import os
import subprocess
import sys
import time
import yaml

from logging.handlers import RotatingFileHandler
from oslo_utils import uuidutils
# from six.moves import http_client

PY2 = sys.version_info[0] == 2
PY3 = sys.version_info[0] == 3
if PY2:
    import httplib as http_client
else:
    import http_client
NEW_VOLUME_TYPE = "storage_for_commonservice"


OP_SERVICE_URL = ""
g_TimedOutTimes = 0

SUCCESS = [http_client.OK,
           http_client.CREATED,
           http_client.ACCEPTED,
           http_client.NON_AUTHORITATIVE_INFORMATION,
           http_client.NO_CONTENT,
           http_client.RESET_CONTENT,
           http_client.PARTIAL_CONTENT,
           http_client.MULTI_STATUS,
           http_client.IM_USED]
LOG_FILE = "/var/log/ftp_pvc_rollback.log"
FTP_ROLLBACK_ALIGN_DIR = "/var/ftp_rollback_align"


def get_logger(name, logfile=LOG_FILE):
    return LogManager(logging.getLogger(name), logfile)


class InfoFilter(logging.Filter):
    def filter(self, rec):
        return rec.levelno in (logging.DEBUG, logging.INFO)


class MyLoggerAdapter(logging.LoggerAdapter):
    def warn(self, msg, *args, **kwargs):
        msg, kwargs = self.process(msg, kwargs)
        self.logger.warning(msg, *args, **kwargs)


class LogManager(MyLoggerAdapter):
    def __init__(self, logger, logfile=LOG_FILE):
        filefmt = logging.Formatter('%(asctime)s - %(levelname)s - %(name)s - '
                                    '%(lineno)d - p=%(process)d - %(message)s')
        fmt = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
        fh = RotatingFileHandler(logfile,
                                 maxBytes=50 * 1024 * 1024,
                                 backupCount=5)
        fh.setFormatter(filefmt)
        fh.setLevel(logging.DEBUG)
        # info to stdout
        self.sh1 = logging.StreamHandler(sys.stdout)
        self.sh1.setFormatter(fmt)
        self.sh1.setLevel(logging.INFO)
        self.sh1.addFilter(InfoFilter())
        # warning and error to stderr
        self.sh2 = logging.StreamHandler()
        self.sh2.setFormatter(fmt)
        self.sh2.setLevel(logging.WARNING)
        self.logger = logger
        self.logger.setLevel(logging.DEBUG)
        self.logger.addHandler(fh)
        self.logger.addHandler(self.sh1)
        self.logger.addHandler(self.sh2)
        self.extra = {}

    def set_stdout(self, enable):
        if enable:
            self.logger.addHandler(self.sh1)
            self.logger.addHandler(self.sh2)
        else:
            self.logger.removeHandler(self.sh1)
            self.logger.removeHandler(self.sh2)


LOG = get_logger("ftp-rollback-align",
                 logfile=LOG_FILE)
LOG.set_stdout(True)
log_file = get_logger("ftp-rollback-align",
                      logfile=LOG_FILE)
log_file.set_stdout(False)
os.chmod(LOG_FILE, 0o640)


def convert_mapping(name_string):
    path_mapping = {"paasdata": "_P",
                    "optenant": "_T",
                    "opdata": "_D",
                    "opcomsrv": "_C",
                    "oplog": "_L",
                    "PostgreSQL": "_PG",
                    "postgresql": "_pg",
                    "Zookeeper": "_Zk",
                    "Logstash": "_Ls",
                    "ApacheKafka": "_Kf",
                    "elasticsearch": "_es"
                    }
    for item in path_mapping:
        name_string = name_string.replace(item, path_mapping[item])
    return name_string


def get_lv_vg_name_postfix(mountpoint):
    name_res = ""
    for single_chr in mountpoint:
        if (single_chr >= 'a' and single_chr <= 'z') or \
           (single_chr >= 'A' and single_chr <= 'Z') or \
           (single_chr >= '0' and single_chr <= '9'):
            name_res = name_res + single_chr
    return convert_mapping(name_res)


def get_vg_name(mountpoint):
    vg_name = "vg" + get_lv_vg_name_postfix(mountpoint)
    return vg_name


def get_lv_name(mountpoint):
    lv_name = "lv" + get_lv_vg_name_postfix(mountpoint)
    return lv_name


def deal_request_base_on_method(method, http, url, headers, data):
    if method == 'GET' or method == 'DELETE':
        response, content = http.request(url, method, headers=headers)
    else:
        response, content = http.request(url, method,
                                         body=json.dumps(data),
                                         headers=headers)
    if isinstance(content, bytes):
        content = content.decode("utf-8")
    if method == 'DELETE':
        return response.status, content
    try:
        content_json = json.loads(content)
        return response.status, content_json
    except Exception:
        return response.status, content


def send_http_request(header=None, url='https://127.0.0.1:5001/servers',
                      method='GET', data=None):
    global g_TimedOutTimes
    http = httplib2.Http(timeout=60, disable_ssl_certificate_validation=True)
    # url = 'http://127.0.0.1:5001/servers'
    headers = {'Content-Type': 'application/json'}
    if header:
        headers['X-Auth-Token'] = header['X-Auth-Token']
    if not url:
        return
    try:
        status, content = \
            deal_request_base_on_method(method, http, url, headers, data)
        return status, content
    except Exception as exc:
        if str(exc) == 'timed out':
            if g_TimedOutTimes % 10 == 0:
                g_TimedOutTimes += 1
        else:
            pass
        return None, None


class Request(object):
    def __init__(self, retry_times=10, retry_interval=6):
        self.retry_times = retry_times
        self.retry_interval = retry_interval

    def retry_request(self, url, method, header=None, body=None):
        for _ in range(self.retry_times):
            rsp = send_http_request(header=header, url=url,
                                    method=method, data=body)
            if rsp[0] not in SUCCESS:
                time.sleep(self.retry_interval)
                continue
            else:
                break
        return rsp


class YmlFile(object):
    def __init__(self):
        self.portvar = "/root/common/port_vars.yml"
        self.comvar = "/root/common/com_vars.yml"

    def load(self, yml_file_name):
        try:
            with open(yml_file_name, "r") as f:
                data = yaml.safe_load(f)
                return data
        except Exception as e:
            print(e)
        return None

    def get_from_file(self, yml_file_name, varname):
        dataMap = self.load(yml_file_name)
        for data in dataMap:
            if varname in data:
                return data[varname]
        return None

    def get_from_comvars(self, varname):
        if '_port' in varname:
            res = self.get_from_file(self.portvar, varname)
            if not res:
                res = self.get_from_file(self.comvar, varname)
        else:
            res = self.get_from_file(self.comvar, varname)
        return res


class NodeWorkerAPI(object):
    def __init__(self):
        global OP_SERVICE_URL
        ip = YmlFile().get_from_comvars('openpalette_service_ip')
        port = str(YmlFile().get_from_comvars('openpalette_service_port'))
        if ip and port:
            OP_SERVICE_URL = "%s:%s" % (ip, port)
        self.url = OP_SERVICE_URL

    def get_all_nodes(self):
        url = ("http://%s/nodeworker/v1/tenants/admin/nodes"
               % (OP_SERVICE_URL))
        get_resp = Request().retry_request(url, 'GET')
        if get_resp[0] not in SUCCESS:
            return False
        else:
            return get_resp[1]

    def get_node(self, node_id):
        url = ("http://%s/nodeworker/v1/tenants/admin/nodes/%s"
               % (OP_SERVICE_URL, node_id))
        get_resp = Request().retry_request(url, 'GET')
        if get_resp[0] not in SUCCESS:
            log_file.info("get node %s failed." % node_id)
            return
        else:
            log_file.info("get node success")
            return get_resp[1]

    def delete_volume(self, volume_id):
        url = ("http://%s/nodeworker/v1/tenants/admin/volumes/%s"
               % (OP_SERVICE_URL, volume_id))
        del_resp = Request().retry_request(url, "DELETE")
        if del_resp[0] not in SUCCESS:
            log_file.info("delete_volume %s failed." % volume_id)
            return
        else:
            log_file.info("delete_volume %s success." % volume_id)
            return del_resp[0]

    def get_node_volumes(self, node_id):
        url = ("http://%s/nodeworker/v1/tenants/admin/nodes/%s/%s"
               % (OP_SERVICE_URL, node_id, "volume_attachments"))
        get_resp = Request().retry_request(url, "GET")
        if get_resp[0] not in SUCCESS:
            log_file.info("get node %s volumes failed." % node_id)
            return
        else:
            return get_resp[1]

    def get_node_volume(self, node_id, volume_id):
        url = ("http://%s/nodeworker/v1/tenants/admin/nodes/%s/%s/%s"
               % (OP_SERVICE_URL, node_id, "volume_attachments", volume_id))
        get_resp = Request().retry_request(url, "GET")
        if get_resp[0] not in SUCCESS:
            log_file.info("get node %s volume %s failed." %
                          (node_id, volume_id))
            return
        else:
            log_file.info("get node %s volume %s success." %
                          (node_id, volume_id))
            return get_resp[1]

    def attach_volume(self, node_id, volumeAttachment):
        url = ("http://%s/nodeworker/v1/tenants/admin/nodes/%s"
               % (OP_SERVICE_URL, node_id))
        result, node = Request().retry_request(url, "GET")
        if not result:
            log_file.info("Get node(%s) info error." % node_id)
            return
        url = ("http://%s/nodeworker/v1/tenants/admin/nodes/%s/action"
               % (OP_SERVICE_URL, node["uuid"]))
        data = {"volumeAttachment": volumeAttachment}
        att_resp = Request().retry_request(url, "POST", body=data)
        log_file.info("attach volume info. \nurl=%s\ndata=%s\nresult=%s\n"
                      "attach=%s" % (url, data, att_resp[0], att_resp[1]))
        if att_resp[0] not in SUCCESS:
            log_file.info("node %s attach volume failed." % node_id)
            return
        else:
            log_file.info("node %s attach volume success." % node_id)
            return att_resp[1]

    def detach_volume(self, node_id, volume_id):
        url = ("http://%s/nodeworker/v1/tenants/admin/nodes/%s/action"
               % (OP_SERVICE_URL, node_id))
        data = {"volumeDetachment": {"volume_id": volume_id}}
        det_resp = Request().retry_request(url, "POST", body=data)
        log_file.info("detach volume info. \nurl=%s\ndata=%s\nresult=%s\n"
                      "attach=%s" % (url, data, det_resp[0], det_resp[1]))
        if det_resp[0] not in SUCCESS:
            log_file.info("detach node %s volume failed." % node_id)
            return False
        else:
            log_file.info("detach node %s volume success." % node_id)
            return True

    def create_volume(self, volume):
        url = "http://%s/nodeworker/v1/tenants/admin/volumes" % OP_SERVICE_URL
        data = {
            "volume": {
                "name": volume.get('name'),
                "size": volume.get('size'),
                "availability_zone": volume.get('availability_zone'),
                "source_volid": volume.get('source_volid'),
                "description": volume.get('description'),
                "multiattach": volume.get('multiattach'),
                "snapshot_id": volume.get('snapshot_id'),
                "imageRef": volume.get('imageRef'),
                "volume_type": volume.get('volume_type'),
                "metadata": volume.get('metadata'),
                "source_replica": volume.get('source_replica'),
                "consistencygroup_id": volume.get('consistencygroup_id')
            }
        }
        resp = Request().retry_request(url, "POST", body=data)
        if resp[0] not in SUCCESS:
            log_file.info("create volume %s  failed." % volume.get('name'))
            return False
        else:
            log_file.info("create volume success")
            return resp[1]

    def get_volume(self, volume_id):
        url = ("http://%s/nodeworker/v1/tenants/admin/volumes/%s"
               % (OP_SERVICE_URL, volume_id))
        get_resp = Request().retry_request(url, "GET")
        if get_resp[0] not in SUCCESS:
            log_file.info("get volume %s volume failed." % volume_id)
            return False
        else:
            log_file.info("get volume %s volume success" % volume_id)
            return get_resp[1]

    def get_volumes(self, volume_name=None, startswith_match=False,
                    lvextend_match=False):
        url = ("http://%s/nodeworker/v1/tenants/admin/volumes"
               % (OP_SERVICE_URL))
        if volume_name:
            url = url + "?volume_name=%s" % volume_name
            if startswith_match:
                url = url + "&startswith_match=yes"
            if lvextend_match:
                url = url + "&lvextend_match=yes"
        get_resp = Request().retry_request(url, "GET")
        if get_resp[0] not in SUCCESS:
            log_file.info("get node volumes failed.")
            return
        else:
            return get_resp[1]

    def update_volume(self, volumeid, node_uuid):
        pass


NODEAPI = NodeWorkerAPI()


def generate_hostfile(hosts_file, ips):
    with open(hosts_file, 'w') as f:
        f.write('[nodes]\n')
        for ip in ips:
            f.write(ip + " " + '\n')
        f.flush()
        return hosts_file


def generate_hostfile_baremetal(node,
                                host_dir=FTP_ROLLBACK_ALIGN_DIR):
    if not os.path.exists(host_dir):
        os.mkdir(host_dir)
    try:
        LOG.info("Begin to generate hostfile for node %s" % (node['name']))
        serverid = node["serverid"]
        mgt_ip = get_host_ip(node)
        if '.' in mgt_ip:
            hostfile = host_dir + '/' + mgt_ip.replace('.', '-')
        elif ':' in mgt_ip:
            hostfile = host_dir + '/' + mgt_ip.replace(':', '-')
        else:
            raise Exception("Invalid format of mgt_ip: %s" % mgt_ip)
        fo = open(hostfile, 'w')
        fo.write("[nodes]\n")
        fo.write("%s ip=%s serverid=%s\n" % (mgt_ip, mgt_ip, serverid))
        fo.flush()
        fo.close()
        return hostfile
    except Exception as e:
        LOG.error("failed to generate hostfile for node %s, error is: %s" %
                  (node['name'], e))
        raise Exception(e)


def get_host_ip(node):
    host_ip = ''
    if 'net_mgt' in node['netinfo'] and \
            node['netinfo']['net_mgt'].get('ip'):
        host_ip = node['netinfo']['net_mgt'].get('ip')
    elif 'net_mgt_v6' in node['netinfo'] and \
            node['netinfo']['net_mgt_v6'].get('ip'):
        host_ip = node['netinfo']['net_mgt_v6'].get('ip')
    elif 'net_admin' in node['netinfo'] and \
            node['netinfo']['net_admin'].get('ip'):
        host_ip = node['netinfo']['net_admin'].get('ip')
    else:
        LOG.error(msg='net_mgt or net_mgt_v6 or net_admin flat not exits '
                      "for node: %s" % node['name'])
    return host_ip


def analyze_ansible_with_output(cmds):
    LOG.info("analyze_ansible_with_output is called")
    p = subprocess.Popen(cmds, shell=True, stdout=subprocess.PIPE,
                         stderr=subprocess.PIPE, close_fds=True)
    lines = p.stdout.readlines()
    if PY3:
        lines = [i.decode() for i in lines]
    for line in lines:
        if "failed=1" in line or "unreachable=1" in line:
            return False, lines
        if "ERROR! Syntax Error" in line:
            return False, lines
    return True, lines


def generate_nodes_hostfile(nodes_info=[],
                            host_dir=FTP_ROLLBACK_ALIGN_DIR):
    if not os.path.exists(host_dir):
        os.mkdir(host_dir)
    try:
        hostfile_uuid = uuidutils.generate_uuid()
        if not nodes_info:
            nodes = NODEAPI.get_all_nodes().get("nodes", [])
            for node in nodes:
                node_ip = get_host_ip(node)
                serverid = node["serverid"]
                nodes_info.append({"ip": node_ip,
                                   "serverid": serverid})

        hostfile = host_dir + '/' + hostfile_uuid
        LOG.info("Begin to generate nodes hostfile(%s)" % hostfile)
        with open(hostfile, 'w') as fo:
            fo.write("[nodes]\n")
            for node in nodes_info:
                fo.write(node["ip"])
                var = ""
                for k, v in node.items():
                    var += " %s=%s" % (str(k), str(v))
                fo.write(var + "\n")
            fo.flush()
        return hostfile
    except Exception as e:
        LOG.error("failed to generate hostfile for all nodes error is: %s" % e)
        raise Exception(e)


def run_ansible_playbook(playbook, hostfile,
                         playbook_dir=".",
                         tries=5, timeout=None, **extra_kwargs):
    LOG.info("run_ansible_playbook is called for %s, %s, %s"
             % (playbook, hostfile, extra_kwargs))
    os.chdir(playbook_dir)
    extar_vars = " --extra-vars '%s'" % json.dumps(extra_kwargs)
    run_playbook_cmd = "ansible-playbook -i %s %s %s" % (
        hostfile, playbook, extar_vars)
    LOG.info(run_playbook_cmd)
    for i in range(tries):
        rst, lines = analyze_ansible_with_output(run_playbook_cmd)
        if rst:
            return rst, lines
        else:
            LOG.error("run ansible playbook failed (%s/%s)" % (i, tries))
            time.sleep(2)
    LOG.error("run ansible playbook failed with times %s (%s)"
              % (tries, run_playbook_cmd))
    return False, lines


class FtpAlignController(object):
    def _deal_for_volume_dislocation(self,
                                     target_node_list,
                                     volumes_info,
                                     mountpoint):
        LOG.info("_deal_for_volume_dislocation is called for %s"
                 % target_node_list)
        expect_vg = 'vg' + get_lv_vg_name_postfix(mountpoint)
        expect_lv = 'lv' + get_lv_vg_name_postfix(mountpoint)
        host_dir = FTP_ROLLBACK_ALIGN_DIR
        hostfile = generate_nodes_hostfile(nodes_info=target_node_list,
                                           host_dir=host_dir)

        LOG.info("try umount volume(%s) for (%s)"
                 % (volumes_info, target_node_list))
        playbook = "deploy_umounting_by_device.yml"
        res, _ = run_ansible_playbook(playbook, hostfile)
        if not res:
            LOG.error("try umount volume(%s) for (%s) failed, but Ignore"
                      % (volumes_info, target_node_list))

        LOG.info("try detach volume(%s) for (%s)" % (volumes_info,
                                                     target_node_list))
        volumes = []
        for vol_info in volumes_info:
            volumes.append(vol_info["volume_id"])
        playbook = "baremetal_detach_error_volumes.yml"
        res, _ = run_ansible_playbook(playbook,
                                      hostfile,
                                      volumes=volumes,
                                      expect_vg=expect_vg,
                                      expect_lv=expect_lv)
        if not res:
            LOG.error("try detach volume(%s) for (%s) failed, but Ignore"
                      % (volumes_info, target_node_list))

        # terminate

    def _filter_redundancy_ip(self, hosts_list):
        new_hosts = []
        ips = []
        for host in hosts_list:
            ip = host.get("ip", "")
            if ip and ip not in ips:
                ips.append(ip)
                new_hosts.append(host)
        LOG.info("after filter list: %s" % new_hosts)
        return new_hosts

    def _search_volumes_in_all_nodes(self, volumes_info, mountpoint):
        LOG.info("_search_volumes_in_all_nodes is called volumes(%s)"
                 % (volumes_info))
        expect_vg = 'vg' + get_lv_vg_name_postfix(mountpoint)
        expect_lv = 'lv' + get_lv_vg_name_postfix(mountpoint)
        hostfile = generate_nodes_hostfile(host_dir=FTP_ROLLBACK_ALIGN_DIR)
        playbook = "baremetal_check_volumes_in_node.yml"
        result_path = FTP_ROLLBACK_ALIGN_DIR
        result_file = "check_volume_" + expect_vg
        res, _ = run_ansible_playbook(playbook,
                                      hostfile,
                                      volumes_info=volumes_info,
                                      expect_vg=expect_vg,
                                      expect_lv=expect_lv,
                                      result_path=result_path,
                                      result_file=result_file,
                                      maybe_new_lun="False")

        if res:
            LOG.info("run ansible for _search_volumes_in_all_nodes success")
            _result_file = result_path + "/" + result_file
            if os.path.exists(_result_file):
                with open(_result_file, "r") as f:
                    result_out = f.read()
                check_result = yaml.safe_load(result_out)
                LOG.info("get result: %s" % check_result)
                hosts_list = check_result.get("result", [])
                return self._filter_redundancy_ip(hosts_list)
        return []

    def deal_for_volume_miss(self, node, volumes, volumes_info, mountpoint):
        LOG.info("deal_for_volume_miss is called")
        target_node_list = self._search_volumes_in_all_nodes(volumes_info,
                                                             mountpoint)
        if target_node_list:
            self._deal_for_volume_dislocation(target_node_list,
                                              volumes_info,
                                              mountpoint)
        for vol in volumes:
            NODEAPI.detach_volume(node["uuid"], vol["volumeid"])
        raise Exception("get dislocation volume(%s) and detached it"
                        % volumes_info)

    def _get_devices_in_node(self, node, volumes_info, mountpoint):
        LOG.info("_check_device_changed is called for node(%s), "
                 "volumes_info(%s) mountpoint(%s)"
                 % (node["uuid"], volumes_info, mountpoint))
        expect_vg = 'vg' + get_lv_vg_name_postfix(mountpoint)
        expect_lv = 'lv' + get_lv_vg_name_postfix(mountpoint)
        host_dir = FTP_ROLLBACK_ALIGN_DIR
        playbook = "baremetal_check_volumes_in_node.yml"
        hostfile = generate_hostfile_baremetal(node, host_dir)
        result_path = FTP_ROLLBACK_ALIGN_DIR
        result_file = "check_volume_" + expect_vg
        res, _ = run_ansible_playbook(playbook,
                                      hostfile,
                                      volumes_info=volumes_info,
                                      expect_vg=expect_vg,
                                      expect_lv=expect_lv,
                                      result_path=result_path,
                                      result_file=result_file,
                                      maybe_new_lun="True")
        if res:
            LOG.info("run ansible for _get_devices_in_node success")
            _result_file = result_path + "/" + result_file
            if os.path.exists(_result_file):
                with open(_result_file, "r") as f:
                    result_out = f.read()
                check_result = yaml.safe_load(result_out)
                LOG.info("get result: %s" % check_result)
                cur_devices = []
                for item in check_result.get("result", []):
                    if item:
                        cur_devices.append(item)
                LOG.info("cur_devices: %s" % cur_devices)
                return cur_devices
        LOG.error("get error when _get_device_in_node")
        return False

    def _update_device_to_storage(self, node, volume_id, device):
        # TODO.
        pass

    def _check_devices_changed(self, node, volumes, mountpoint):
        volumes_info = []
        cur_device_list = []
        for vol in volumes:
            volumes_info.append({"ori_device": vol["device"],
                                 "volume_id": vol["volumeid"]})
        LOG.info("_check_device_changed is called for node(%s), %s"
                 % (node["uuid"], volumes_info))
        cur_devices = self._get_devices_in_node(node, volumes_info, mountpoint)
        if not cur_devices:
            self.deal_for_volume_miss(node, volumes, volumes_info, mountpoint)
        else:
            for vol_cur in cur_devices:
                if not vol_cur.get("volume_id"):
                    continue
                volume_id = vol_cur["volume_id"]
                cur_device = vol_cur["device"]
                for vol_old in volumes_info:
                    ori_device = vol_old["ori_device"]
                    if volume_id == vol_old["volume_id"] \
                            and ori_device != cur_device:
                        LOG.warn("get different device of volume(%s):"
                                 "%s -> %s" % (volume_id, ori_device,
                                               cur_device))
                        NODEAPI.detach_volume(node["uuid"], volume_id)
#                         self._update_device_to_storage(node,
#                                                        volume_id, cur_device)
                cur_device_list.append(cur_device)
        return cur_device_list

    def check_volumes_in_node(self):
        LOG.info("check_volume_in_node is called for node(%s)"
                 % (self.node["uuid"]))
        device_list = self._check_devices_changed(self.node,
                                                  self.volumes,
                                                  self.mountpoint)
        if len(device_list) < len(self.volumes):
            raise Exception("check volumes in node")
        LOG.info("check_volumes_in_node: %s" % device_list)
        return device_list

    def __init__(self, volumes, node_uuid):
        self.volumes = volumes
        self.node_uuid = node_uuid
        self.mountpoint = volumes[0]["mountpoint"]
        self.node = NODEAPI.get_node(node_uuid)


if __name__ == "__main__":
    dynamic_luns = \
        NODEAPI.get_volumes(volume_name='dynamic_lun_',
                            startswith_match=True).get("volumes", [])
    for lun in dynamic_luns:
        if lun["attach_to"]:
            lun_align = FtpAlignController([lun], lun["attach_to"])
            try:
                lun_align.check_volumes_in_node()
            except Exception as e:
                LOG.error("get error when check_volumes_in_node for %s as %s"
                          % (lun["volumeid"], e))
