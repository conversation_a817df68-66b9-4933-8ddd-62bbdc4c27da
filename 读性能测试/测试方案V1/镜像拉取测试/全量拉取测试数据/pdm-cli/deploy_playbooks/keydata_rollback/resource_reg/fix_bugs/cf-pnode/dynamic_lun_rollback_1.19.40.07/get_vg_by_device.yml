---
- debug:
    msg: "current vg: {{ vg_name }}"

- debug:
    msg: "current lv: {{ lv_name }}"

- name: get vg by device
  shell: pvs --noheadings -o vg_name {{ device }} | awk -F ' ' '{print $1}'
  register: device_vg

- debug:
    msg: "{{ device }}: {{ device_vg.stdout }}"

- fail:
    msg: "get too much vg"
  when: vg_name != "" and vg_name != device_vg.stdout

- set_fact:
    vg_name: "{{ device_vg.stdout }}"

- name: get lv by device
  shell: pvs --noheadings -o lv_name {{ device }} | awk -F ' ' '{print $1}'
  register: device_lv

- debug:
    msg: "{{ device }}: {{ device_lv.stdout }}"

- fail:
    msg: "get too much vg"
  when: lv_name != "" and lv_name != device_lv.stdout

- set_fact:
    lv_name: "{{ device_lv.stdout }}"