# All Rights Reserved.
#    Licensed under the Apache License, Version 2.0 (the "License"); you may
#    not use this file except in compliance with the License. You may obtain
#    a copy of the License at
#
#         http://www.apache.org/licenses/LICENSE-2.0
#
#    Unless required by applicable law or agreed to in writing, software
#    distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
#    WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
#    License for the specific language governing permissions and limitations
#    under the License.
import logging
import os
import re
import sys
import time

from logging.handlers import RotatingFileHandler
from os_brick import exception
from os_brick import executor
from os_brick.initiator import connector as brick_connector
from os_brick.initiator import host_driver
from os_brick.initiator import linuxfc
from os_brick.initiator import linuxscsi
from os_brick import utils
from oslo_concurrency import processutils as putils


MULTIPATH_ERROR_REGEX = re.compile("\w{3} \d+ \d\d:\d\d:\d\d \|.*$")
MULTIPATH_DEV_CHECK_REGEX = re.compile("\s+dm-\d+\s+")
MULTIPATH_PATH_CHECK_REGEX = re.compile("\s+\d+:\d+:\d+:\d+\s+")
MULTIPATH_WWID_REGEX = re.compile("\((?P<wwid>.+)\)")
MULTIPATH_DEVICE_ACTIONS = ['unchanged:', 'reject:', 'reload:',
                            'switchpg:', 'rename:', 'create:',
                            'resize:']
SUPPORT_STORAGE = ['IBM', 'ZTE', 'FUJITSU', '3PARdata', 'HUAWEI']
RESIDUAL_FLAG = ['SIBM', 'SZTE', 'SFUJ']

volume_use_multipath = True
num_iscsi_scan_tries = 3
libvirt_volume_drivers = ["iscsi", "fibre_channel"]


def get_logger(name, logfile="/var/log/pdm-cli.log"):
    return LogManager(logging.getLogger(name), logfile)


class InfoFilter(logging.Filter):
    def filter(self, rec):
        return rec.levelno in (logging.DEBUG, logging.INFO)


class MyLoggerAdapter(logging.LoggerAdapter):
    def warn(self, msg, *args, **kwargs):
        msg, kwargs = self.process(msg, kwargs)
        self.logger.warning(msg, *args, **kwargs)


class LogManager(MyLoggerAdapter):
    def __init__(self, logger, logfile="/var/log/volume_attachment.log"):
        filefmt = logging.Formatter('%(asctime)s - %(levelname)s - %(name)s - '
                                    '%(lineno)d - p=%(process)d - %(message)s')
        fmt = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
        fh = RotatingFileHandler(logfile,
                                 maxBytes=50 * 1024 * 1024,
                                 backupCount=5)
        fh.setFormatter(filefmt)
        fh.setLevel(logging.DEBUG)
        # info to stdout
        self.sh1 = logging.StreamHandler(sys.stdout)
        self.sh1.setFormatter(fmt)
        self.sh1.setLevel(logging.INFO)
        self.sh1.addFilter(InfoFilter())
        # warning and error to stderr
        self.sh2 = logging.StreamHandler()
        self.sh2.setFormatter(fmt)
        self.sh2.setLevel(logging.WARNING)
        self.logger = logger
        self.logger.setLevel(logging.DEBUG)
        self.logger.addHandler(fh)
        self.logger.addHandler(self.sh1)
        self.logger.addHandler(self.sh2)
        self.extra = {}

    def set_stdout(self, enable):
        if enable:
            self.logger.addHandler(self.sh1)
            self.logger.addHandler(self.sh2)
        else:
            self.logger.removeHandler(self.sh1)
            self.logger.removeHandler(self.sh2)


LOG = get_logger("volume_attachment",
                 logfile="/var/log/volume_attachment.log")
LOG.set_stdout(False)


def get_root_helper():
    cmd = 'sudo'
    return cmd


class MyLinuxSCSI(linuxscsi.LinuxSCSI):
    def run_multipath(self):
        try:
            (out, _err) = self._execute('multipath', '-l',
                                        run_as_root=True,
                                        root_helper=self._root_helper)
            LOG.debug("multipaht -l output is\n %s", out)
            return out
        except putils.ProcessExecutionError as exc:
            LOG.warning("multipath call failed exit %s" % exc.exit_code)
            raise exception.CommandExecutionFailed(cmd='multipath -l')

    def remove_scsi_device(self, device):
        """Removes a scsi device based upon /dev/sdX name."""

        path = "/sys/block/%s/device/delete" % device.replace("/dev/", "")
        if os.path.exists(path):
            # flush any outstanding IO first
            self.flush_device_io(device)

            LOG.debug("Remove SCSI device %(device)s with %(path)s",
                      {'device': device, 'path': path})
            self.echo_scsi_command(path, "1")

    @utils.retry(exceptions=exception.VolumePathNotRemoved, retries=3,
                 backoff_rate=2)
    def wait_for_volume_removal(self, volume_path):
        """This is used to ensure that volumes are gone."""
        LOG.debug("Checking to see if SCSI volume %s has been removed.",
                  volume_path)
        if os.path.exists(volume_path):
            LOG.debug("%(path)s still exists.", {'path': volume_path})
            raise exception.VolumePathNotRemoved(
                volume_path=volume_path)
        else:
            LOG.debug("SCSI volume %s has been removed.", volume_path)

    def remove_multipath_device(self, device):
        """This removes LUNs associated with a multipath device
        and the multipath device itself.
        """

        LOG.debug("remove multipath device %s", device)
        mpath_dev = self.find_multipath_device(device)
        if mpath_dev:
            self.flush_multipath_device(mpath_dev['name'])
            devices = mpath_dev['devices']
            LOG.debug("multipath LUNs to remove %s", devices)
            for device in devices:
                self.remove_scsi_device(device['device'])

    def _get_multipath_device_holders(self, dev):
        # /dev/dm-4
        dm = dev.split('/')[-1]
        if not dm.startswith("dm"):
            return []
        holders = list(os.walk('/sys/block/%s/holders/' % dm))
        if len(holders):
            holders = holders[0][1]
            all_holders = holders
            for holder in holders:
                temp_holders = self._get_multipath_device_holders(holder)
                if len(temp_holders):
                    all_holders = all_holders + temp_holders
            return all_holders
        return []

    def flush_multipath_devices(self):
        try:
            self._execute('multipath', '-F', run_as_root=True,
                          root_helper=self._root_helper)
        except putils.ProcessExecutionError as exc:
            LOG.warning("multipath call failed exit %s" % exc.exit_code)

    def get_scsi_id(self, device):
        # get scsi wwn
        # /lib/udev/scsi_id -g -u -d  /dev/sdX
        out, err = self._execute('/lib/udev/scsi_id', '-g', '-u', '-d', device,
                                 run_as_root=True,
                                 check_exit_code=False,
                                 root_helper=self._root_helper)
        LOG.debug("device %(dev)s scsi_id out = %(out)s",
                  {'dev': device, 'out': out})
        if out:
            return out.split("\n")[0]

    def lsscsi_output(self):
        out, err = self._execute('lsscsi', '-i',
                                 run_as_root=True,
                                 check_exit_code=False,
                                 root_helper=self._root_helper)
        LOG.debug("lsscsi out = %s", out)
        if out:
            return out

    def wait_for_device_removal(self, device):
        for tries in range(100):
            if os.path.exists(device):
                LOG.debug("%(device)s still exists.", {'device': device})
                time.sleep(0.2)
            else:
                LOG.debug("%(device)s removed after %(tries)s rescans.",
                          {'device': device, 'tries': tries})
                return

    def _scrub_residual_dev(self, line):
        def _scrub_residual_dev_others():
            scrub_flag = False
            dev = line.split()[-2]
            scsi_id = None
            try:
                scsi_id = self.get_scsi_id(dev)
                # out may be like these:
                # SZTE_ZXUSP
                # SIBM_2145_00c02043adb0XX25
                # SFUJITSU_ETERNUS_DXL_28097C
                if scsi_id and scsi_id[0:4] in RESIDUAL_FLAG:
                    scrub_flag = True
            except Exception as ex:
                LOG.info("Ignore error %s" % ex)
                scrub_flag = True
            if scrub_flag or (scsi_id and scsi_id != line.split()[-1]):
                LOG.info("Scrub residual %s" % dev)
                self.remove_scsi_device(dev)
                self.wait_for_device_removal(dev)

        if line.split()[-1] == '-':
            if line.split()[2] in SUPPORT_STORAGE:
                LOG.info("Remove residual sg:%s" % line)
                path = ("/sys/class/scsi_device/%s/device/delete" %
                        line.split()[0][1:-1])
                self.echo_scsi_command(path, '1')
        else:
            _scrub_residual_dev_others()

    def scrub_residual_sg_and_sd(self):
        try:
            lsscsi_out = self.lsscsi_output()
            if lsscsi_out:
                lines = lsscsi_out.strip()
                lines = lines.split("\n")
                for line in lines:
                    self._scrub_residual_dev(line)
        except Exception as ex:
            LOG.warning("Scrub Ignore error when scrub residual"
                        "sg device: %s" % ex)

    def flush_device_io(self, device):
        """This is used to flush any remaining IO in the buffers."""
        try:
            LOG.debug("Flushing IO for device %s", device)
            self._execute('blockdev', '--flushbufs', device, run_as_root=True,
                          root_helper=self._root_helper)
        except putils.ProcessExecutionError as exc:
            LOG.warning("Failed to flush IO buffers prior to removing "
                        "device: %s" % exc.exit_code)


class ResidualScrubberManager(executor.Executor):
    """Manages residual iscsi and fc devices scrubber."""

    def __init__(self, root_helper, execute=putils.execute,
                 use_multipath=False, *args, **kwargs):
        self.driver = host_driver.HostDriver()
        self._linuxscsi = MyLinuxSCSI(root_helper, execute=execute)
        self._linuxfc = linuxfc.LinuxFibreChannel(root_helper, execute=execute)
        super(ResidualScrubberManager, self).__init__(root_helper, execute,
                                                      use_multipath,
                                                      *args, **kwargs)
        self.use_multipath = use_multipath
        self.root_helper = root_helper

    def _get_multipath_devices_name(self):
        """get all multipath device names by using 'multipath -l'"""
        mpath_dev = None
        mpath_devices_name = []
        out = self._linuxscsi.run_multipath()
        if out:
            for line in out.splitlines():
                m = MULTIPATH_DEV_CHECK_REGEX.split(line)
                if len(m) >= 2:
                    mpath_dev = m[0].split(" ")[0]
                if mpath_dev and ("/dev/mapper/%s" % mpath_dev) \
                        not in mpath_devices_name:
                    mpath_devices_name.append("/dev/mapper/%s" % mpath_dev)

        return mpath_devices_name

    def _get_multipath_slaves(self, dm_device):
        """Get slaves of multipath based upon sdX name. """
        if not dm_device:
            return []
        path = "/sys/block/%s/slaves/" % dm_device.replace("/dev/", "")
        slaves = list(os.walk(path))
        return slaves[0][1] if len(slaves) else []

    def _get_scsi_device_realpaths(self):
        """Get scsi device names.

         Get scsi device names based upon /dev/sdX, correspond to
        /dev/disk/by-path/entries.
        """
        realpaths = []
        block_devices = self.driver.get_all_block_devices()
        for dev in block_devices:
            realpath = os.path.realpath(dev)
            realpaths.append(realpath) if realpath else None
        return realpaths

    def _is_residual_device(self, device):
        try:
            # get scsi_id from storage san
            san_scsi_id = self._linuxscsi.get_scsi_id(device)

            # get scsi_id from current host
            host_scsi_id = None
            out = self._linuxscsi.lsscsi_output()
            if out:
                LOG.info("lsscsi out = %s" % out)
                lines = out.strip()
                lines = lines.split("\n")
                for line in lines:
                    if device in line and line.split()[-2] == device:
                        host_scsi_id = line.split()[-1]
                        break

            if san_scsi_id is not None and san_scsi_id == host_scsi_id:
                return False
            else:
                return True
        except Exception as ex:
            LOG.warning("Ignore error: %s" % ex)
            return True

    def _scrub_residual_scsi_devices(self):
        """Scrub residual scsi devices based upon /dev/sdX name.

        Scrub devices belong to multipath devices but not correspond to
        /dev/disk/by_path/ip-PORTAL:PORT-iscsi-IQN-lun-LUN_ID.
        """
        if not self.use_multipath:
            return

        mdev_names = self._get_multipath_devices_name()
        device_realpaths = self._get_scsi_device_realpaths()
        for mdev in mdev_names:
            mdev_realpath = os.path.realpath(mdev)
            mdev_slaves = self._get_multipath_slaves(mdev_realpath)
            for slave in mdev_slaves:
                if ('/dev/%s' % slave) not in device_realpaths:
                    LOG.info("Scrub scsi device: %s" % slave)
                    self._linuxscsi.remove_scsi_device('/dev/%s' % slave)
                    self._linuxscsi.wait_for_device_removal('/dev/%s' % slave)

    def _scrub_multipath_device_shells(self):
        """Scrub multipath devices shells.

        Scrub multipath devices that without slaves in
        /sys/block/dm-X/slaves/ based upon sdX name.
        """
        if not self.use_multipath:
            return

        mdev_names = self._get_multipath_devices_name()
        for mdev in mdev_names:
            mdev_realpath = os.path.realpath(mdev)
            mdev_slaves = self._get_multipath_slaves(
                os.path.basename(mdev_realpath))
            if len(mdev_slaves) == 0:
                LOG.info("Scrub multipath device shell:%s" % mdev)
                self._linuxscsi.flush_multipath_device(mdev_realpath)
                self._linuxscsi.wait_for_device_removal(mdev)

    def _get_host_devices(self, conn):
        if not conn:
            return None
        host_devices = None
        protocol = conn['driver_volume_type']
        connector = brick_connector.InitiatorConnector.factory(
            protocol, self.root_helper, use_multipath=self.use_multipath)

        if protocol == 'fibre_channel':
            hbas = self._linuxfc.get_fc_hbas_info()
            host_devices = connector._get_possible_volume_paths(
                conn.get('data'), hbas)
        elif protocol == 'iscsi':
            host_devices = connector._get_device_path(
                conn.get('data'))
        return host_devices

    def scrub_residual_devices(self, conn=None):
        LOG.info("Enter method: scrub_residual_devices")
        self._linuxscsi.scrub_residual_sg_and_sd()
        self._scrub_residual_scsi_devices()
        self._scrub_multipath_device_shells()
        LOG.info("Exit_method: scrub_residual_devices")


class Iscsi_Driver(object):

    def __init__(self):
        self.iscsi_iface = False
        self.connector = brick_connector.InitiatorConnector.factory(
            'ISCSI', get_root_helper(),
            use_multipath=volume_use_multipath,
            device_scan_attempts=num_iscsi_scan_tries,
            transport=self._get_transport())

    def _get_transport(self):
        if self.iscsi_iface:
            transport = self.iscsi_iface
        else:
            transport = 'default'

        return transport


class Fibre_Channel_Driver(object):

    def __init__(self):
        self.connector = brick_connector.InitiatorConnector.factory(
            'FIBRE_CHANNEL', get_root_helper(),
            use_multipath=volume_use_multipath,
            device_scan_attempts=num_iscsi_scan_tries)


class VolumeConnector(executor.Executor):
    def __init__(self, connection_info, action):
        super(VolumeConnector, self).__init__(get_root_helper())
        self.connection_data = connection_info["data"]
        self.driver_type = self.get_volume_driver(connection_info)
        self.connector_driver = self.get_connector_driver(action)

    def get_volume_driver(self, connection_info):
        driver_type = connection_info.get('driver_volume_type')
        if driver_type not in libvirt_volume_drivers:
            raise Exception("volume driver notfound: %s" % driver_type)
        return driver_type

    def get_connector_driver(self, action):
        if self.driver_type == "fibre_channel":
            connector_driver = Fibre_Channel_Driver()
        elif self.driver_type == "iscsi":
            connector_driver = Iscsi_Driver()
            if not self.valid_iscsi_data(self.connection_data,
                                         connector_driver, action):
                raise Exception("get error when check iscsi parameters")
        else:
            raise Exception("Unsupported portal for volume attachments")
        return connector_driver

    def volume_attach(self):
        LOG.info("volume_attach is called")
        mpath = None
        result = self.connector_driver.connector.connect_volume(
            self.connection_data)
        if 'path' in result:
            for _ in range(10):
                mpath = self.get_mapper_path(result['path'],
                                             volume_use_multipath)
                if mpath is not None:
                    break
                time.sleep(3)
        if not mpath:
            raise Exception("cloud not get multipath of %s" % result['path'])
        LOG.info("volume_attach success: /dev/mapper/%s" % str(mpath))
        print("/dev/mapper/%s" % str(mpath))
        print("volume_attach success")

    def volume_detach(self):
        LOG.info("volume_detach is called")
        mpath = None
        device_path = self.connector_driver.connector.get_volume_paths(
            self.connection_data)
        if len(device_path) > 0:
            mpath = self.get_mapper_path(device_path, volume_use_multipath)
        LOG.info("volume_detach: /dev/mapper/%s, %s" % (str(mpath),
                                                        device_path))
        print("/dev/mapper/%s" % str(mpath))
        print(device_path)
        try:
            self.connector_driver.connector.disconnect_volume(
                self.connection_data, device_info=None, force=True)
        except Exception as e:
            raise Exception("detach failed as %s" % e)
        LOG.info("volume_detach success")
        print("volume_detach success")

    def volume_get_deivce(self):
        LOG.info("volume_get_deivce is called")
        mpath = None
        device_path = self.connector_driver.connector.get_volume_paths(
            self.connection_data)
        if len(device_path) > 0:
            mpath = self.get_mapper_path(device_path, volume_use_multipath,
                                         selector_by="mpath")
        if mpath in ["", "None", None]:
            raise Exception("can not get device")
        LOG.info("/dev/mapper/%s" % str(mpath))
        print("/dev/mapper/%s" % str(mpath))

    def volume_get_deivce_by_id(self):
        LOG.info("volume_get_deivce_by_id is called")
        mpath = None
        device_path = self.connector_driver.connector.get_volume_paths(
            self.connection_data)
        if len(device_path) > 0:
            mpath = self.get_mapper_path(device_path,
                                         volume_use_multipath,
                                         selector_by="uuid")
        if mpath in ["", "None", None]:
            raise Exception("can not get device by id")

        mpath = "/dev/disk/by-id/dm-uuid-mpath-%s" % str(mpath).strip("()")
        if not os.path.exists(mpath):
            raise Exception("get device by id failed %s is not exist" % mpath)
        LOG.info("volume_get_deivce_by_id: %s" % mpath)
        print(mpath)

    def _is_valid_iscsi_ip(self, iscsi_ip, action):
        if action != "attach":
            return True
        if not iscsi_ip:
            return False
        ip_version = "ipv6" if len(iscsi_ip.split(':')) > 2 else "ipv4"
        if ip_version == "ipv6":
            iscsi_ip = ":".join(iscsi_ip.split(':')[:-1])
            iscsi_ip = iscsi_ip.replace('[', '').replace(']', '')
            cmd = 'ping6'
        else:
            iscsi_ip = iscsi_ip.split(":")[0]
            cmd = 'ping'

        try:
            self._execute(cmd, '-c', '2', '-w', '1', '-i', '0.01',
                          iscsi_ip, run_as_root=True,
                          root_helper=self._root_helper)
        except:
            return False
        return True

    def valid_iscsi_data(self, iscsi_data, connector_driver, action="attach"):
        if 'target_portals' not in iscsi_data or\
                'target_iqns' not in iscsi_data or\
                'target_luns' not in iscsi_data:
            LOG.error("paramates error")
            print("paramates error")
            return False
        iscsi_data["target_luns"] = \
            connector_driver.connector._get_luns(iscsi_data)
        valid_portals = []
        valid_iqns = []
        valid_luns = []
        index = 0
        for ip in iscsi_data['target_portals']:
            if self._is_valid_iscsi_ip(ip, action):
                valid_portals.append(ip)
                valid_iqns.append(iscsi_data["target_iqns"][index])
                valid_luns.append(iscsi_data["target_luns"][index])
            index += 1
        iscsi_data['target_portals'] = valid_portals
        iscsi_data['target_iqns'] = valid_iqns
        iscsi_data['target_luns'] = valid_luns
        if len(valid_portals) > 0:
            iscsi_data['target_portal'] = valid_portals[0]
            iscsi_data['target_iqn'] = valid_iqns[0]
            iscsi_data['target_lun'] = valid_luns[0]
        else:
            LOG.error("not valid ip to Magnetic Array")
            print("not valid ip to Magnetic Array")
            return False
        return True

    def _get_mapper_by_device(self, device_path, selector_by="mpath"):
        LOG.info("_get_mapper_by_device is called for %s, selector_by(%s)"
                 % (device_path, selector_by))
        selector_items = {"mpath": 0,
                          "uuid": 1,
                          "dm": 2}
        if selector_by not in selector_items:
            LOG.warning("could not select deivce by: %s" % selector_by)
            return None
        selector = selector_items[selector_by]
        try:
            device = os.path.realpath(device_path)
            (out, _err) = self._execute('multipath', '-l', device,
                                        run_as_root=True,
                                        root_helper=self._root_helper,
                                        check_exit_code=[0, 1])
            LOG.debug("multipaht -l device output is\n %s", out)
            for line in out.splitlines():
                mapper = MULTIPATH_DEV_CHECK_REGEX.split(line)
                if len(mapper) >= 2:
                    return mapper[0].split(" ")[selector]
            return None
        except putils.ProcessExecutionError as exc:
            LOG.warning("multipath call failed exit %s" % exc.exit_code)
            return None

    def get_mapper_path(self, device_path_list,
                        use_multipath=True, selector_by="mpath"):
        LOG.info("get_mapper_path is called for %s, %s"
                 % (device_path_list, selector_by))
        if not isinstance(device_path_list, list):
            device_path_list = [device_path_list]
        if not device_path_list:
            return None
        if not use_multipath:
            return device_path_list[0]
        mpath = None

        for device_path in device_path_list:
            temp_path = self._get_mapper_by_device(device_path, selector_by)
            if mpath and temp_path and mpath != temp_path:
                raise Exception("get multipath error of %s more than one"
                                ": [%s, %s]" % (device_path_list, mpath,
                                                temp_path))
            mpath = temp_path
        return mpath


if __name__ == "__main__":
    action = sys.argv[1]
    connection_info_file = sys.argv[2]
    LOG.info("volume_attachment is called for connection_info_file(%s), "
             "action(%s)" % (connection_info_file, action))
    with open(connection_info_file, "r+") as file_obj:
        connection_str = file_obj.read()

    connection_info = eval(connection_str)
    volume_id = connection_info.get("data", {}).get("volume_id", "")
    volume_connector = VolumeConnector(connection_info, action)

    root_helper = get_root_helper()
    scruber = ResidualScrubberManager(root_helper,
                                      use_multipath=volume_use_multipath)
    try:
        scruber.scrub_residual_devices()
    except putils.ProcessExecutionError as ex:
        LOG.error("Scrub residual devices error: %s" % ex)

    try:
        if action == "attach":
            volume_connector.volume_attach()
        elif action == "detach":
            volume_connector.volume_detach()
        elif action == "get_device":
            volume_connector.volume_get_deivce()
        elif action == "get_device_by_id":
            volume_connector.volume_get_deivce_by_id()
    except Exception as e:
        err_msg = "get failed when do action(%s) for %s with err: %s" \
            % (action, volume_id, e)
        LOG.error(err_msg)
        raise Exception(err_msg)
