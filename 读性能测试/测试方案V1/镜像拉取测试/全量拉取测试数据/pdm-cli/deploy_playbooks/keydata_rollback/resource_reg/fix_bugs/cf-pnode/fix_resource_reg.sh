#!/bin/bash

version=$1
version_pre8=${version:0:8}
version_pre11=${version:0:11}
version_pre13=${version:0:13}

if [ ${version_pre13#*.} == "19.40.07.p" ] ; then
    cd $(dirname $0)
    ansible-playbook -i /etc/pdm/hosts  sync_rollback_scripts.yml
    exit $?
fi

# shellcheck disable=SC2072
if [[ ${version_pre8#*.} < "22.20" ]] ; then
    cd $(dirname $0)
    ansible-playbook -i /etc/pdm/hosts sync_revert_csi_db_scripts.yml
    exit $?
fi
