#!/bin/bash
#shellcheck disable=SC1083
#This is a standard log print for nwmaster deploy
TIME=$(date  +%Y-%m-%d\ %H:%M:%S)
ROLLBACK_LOG=/paasdata/op-log/cf-pnode/volume_keydata_rollback.log
TYPE="$1"
SOURCE=$2
PID=$3
OUTPUT=$4

if [ ! -f $ROLLBACK_LOG ];then
    touch $ROLLBACK_LOG
    chmod 640 $ROLLBACK_LOG
fi

if [ "$TYPE" == "INFO" ]; then
    echo "INFO $TIME    [$SOURCE] [PID:$PID] $OUTPUT" >> $ROLLBACK_LOG
elif [ "$TYPE" == "ERROR" ]; then
    echo "ERROR $TIME    [$SOURCE] [PID:$PID] $OUTPUT" >> $ROLLBACK_LOG
elif [ "$TYPE" == "WARNING" ]; then
    echo "WARNING $TIME    [$SOURCE] [PID:$PID] $OUTPUT" >> $ROLLBACK_LOG
fi
