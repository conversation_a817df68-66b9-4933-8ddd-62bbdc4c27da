---
- hosts : nodes
  remote_user: ubuntu
  become: yes
  become_method: sudo
  gather_facts: no

  vars_files:
    - /root/common/com_vars.yml
    - /root/common/port_vars.yml

  tasks:
    - set_fact:
        openpalette_service_ip_aotu_bracket: "{{ openpalette_service_ip | ipwrap }}"

    - name: append rask to old res_align.yml
      shell: python append_res_align.py
      args:
        chdir: /etc/pdm/deploy_playbooks/keydata_rollback/resource_reg/fix_bugs/cf-pnode

    - name: create /paasdata/op-data/cf-pnode/keydata_rollback
      file:
        path: "/paasdata/op-data/cf-pnode/keydata_rollback"
        state: directory
        mode: "0750"

    - name: generate csi_pvc_rollback.sh
      template:
        src: "csi_pvc_rollback.sh.j2"
        dest: "/paasdata/op-data/cf-pnode/keydata_rollback/csi_pvc_rollback.sh"
        mode: "0640"

    - name: copy log_print.sh
      copy:
        src: "log_print.sh"
        dest: "/paasdata/op-data/cf-pnode/keydata_rollback/"
        mode: "0750"

    - name: copy correct_comsrv_volume.sh
      copy:
        src: "correct_comsrv_volume.sh"
        dest: "/paasdata/op-data/cf-pnode/keydata_rollback/"
        mode: "0750"

    - name: copy correct_comsrv_volume.py
      copy:
        src: "correct_comsrv_volume.py"
        dest: "/paasdata/op-data/cf-pnode/keydata_rollback/"
        mode: "0640"

    - name: copy correct_comsrv_volume.yml
      copy:
        src: "correct_comsrv_volume.yml"
        dest: "/paasdata/op-data/cf-pnode/keydata_rollback/"
        mode: "0640"
