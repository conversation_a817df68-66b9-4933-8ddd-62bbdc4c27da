import json
import sys


res_path = sys.argv[1]

res_filedir_path = res_path + "/res_filedir.yml"

with open(res_filedir_path, 'r') as f:
    reg_list = json.load(f)
    for reg in reg_list:
        if reg["res_name"] == "cnrm_file":
            if "/usr/lib/systemd/system/cnrm.service" not in reg["inclu_dir"]:
                reg["inclu_dir"].append("/usr/lib/systemd/system/cnrm.service")
                with open(res_filedir_path, 'w') as f:
                    json.dump(reg_list, f, indent=4)
            break
