#!/bin/bash
new_base_dir="/etc/pdm/deploy_playbooks/keydata_rollback"
old_base_dir="/etc/pdm_bak/deploy_playbooks/keydata_rollback"
old_res_dir="$old_base_dir/resource_reg"

script_path="$new_base_dir/resource_reg/fix_bugs/cnrm/fix_res.py"

if command -v pythonlatest &> /dev/null; then
  python_cmd="pythonlatest"
  #echo "use latest"
else
  python_cmd="python"
fi

$python_cmd "$script_path" "$old_res_dir"