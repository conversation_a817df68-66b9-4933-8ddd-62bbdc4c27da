import json
import sys

res_filedir_path = sys.argv[1]
res_rpm_path = sys.argv[2]
res_service_path = sys.argv[3]

res_filedir = {
    "res_name": "docker",
    "compo_owner": "docker",
    "judge_method": "by_role",
    "roles": ["usednodes"],
    "inclu_dir": ["/etc/docker/", "/paasdata/op-conf/docker/",
                  "/usr/lib/systemd/system/docker.service",
                  "/usr/lib/systemd/system/ipv6nat.service"],
    "exclu_dir": [],
    "inclu_big_subdir": []
}
res_rpm = {
    "res_name": "docker",
    "compo_owner": "docker",
    "judge_method": "by_role",
    "optional_component": False,
    "roles": ["usednodes"],
    "rpm_name": ["rpms.tar.gz"],
    "clear_cmd": "rpm/docker_clear_rpm.sh",
    "restore_cmd": "rpm/docker_restore_rpm.sh"
}
res_service = {
    "res_name": "docker",
    "compo_owner": "docker",
    "judge_method": "by_role",
    "roles": ["usednodes"],
    "stop_cmd": "service/docker_stop_service.sh",
    "restore_cmd": "service/docker_restore_service.sh"
}

has_docker_file = False
has_docker_rpm = False
has_docker_service = False

with open(res_filedir_path, 'r') as f:
    reg_list = json.load(f)
    for reg in reg_list:
        if reg["res_name"] == "docker":
            has_docker_file = True
            break

if not has_docker_file:
    with open(res_filedir_path, 'w') as f:
        reg_list.append(res_filedir)
        json.dump(reg_list, f, indent=4)

with open(res_rpm_path, 'r') as f:
    reg_list = json.load(f)
    for reg in reg_list:
        if reg["res_name"] == "docker":
            has_docker_rpm = True
            break

if not has_docker_rpm:
    with open(res_rpm_path, 'w') as f:
        reg_list.append(res_rpm)
        json.dump(reg_list, f, indent=4)

with open(res_service_path, 'r') as f:
    reg_list = json.load(f)
    for reg in reg_list:
        if reg["res_name"] == "docker":
            has_docker_service = True
            break

if not has_docker_service:
    with open(res_service_path, 'w') as f:
        reg_list.append(res_service)
        json.dump(reg_list, f, indent=4)
