#!/bin/bash

version=$1
new_base_dir="/etc/pdm/deploy_playbooks/keydata_rollback"
old_base_dir="/etc/pdm_bak/deploy_playbooks/keydata_rollback"
res_filedir="$old_base_dir/resource_reg/res_filedir.yml"
res_rpm="$old_base_dir/resource_reg/res_rpm.yml"
res_service="$old_base_dir/resource_reg/res_service.yml"

if command -v pythonlatest &> /dev/null; then
    python_cmd="pythonlatest"
else
    python_cmd="python"
fi

if [[ "${version#*.}" < "21.30.05" ]];then
    cp -rf "$new_base_dir"/resource_reg/fix_bugs/docker/rpm/* "$old_base_dir"/resource_reg/rpm/
    cp -rf "$new_base_dir"/resource_reg/fix_bugs/docker/service/* "$old_base_dir"/resource_reg/service/

    script_path="$new_base_dir/resource_reg/fix_bugs/docker/fix_docker_res.py"
    "$python_cmd" "$script_path" "$res_filedir" "$res_rpm" "$res_service"
    exit $?
fi
