#!/usr/bin/env bash

dockerfile=$(rpm -qa | grep docker- | grep -v selinux)
if [[ $dockerfile =~ "docker-ce" ]]; then
    dockerfile="docker-ce"
elif [[ $dockerfile =~ "docker-engine" ]]; then
    dockerfile="docker-engine"
else
    dockerfile=""
fi

#Docker uninstall
function rpm_uninstall_docker() {
    if [ -n "$dockerfile" ]; then
        if ! rpm -e --nodeps "$dockerfile";then
            echo "Uninstall docker failed!"
            exit 1
        else
            echo "Uninstall docker successfully!"
        fi
    fi
}

function uninstall_docker_image() {
    docker_image_rpms=$(rpm -qa | grep docker-image)
    echo "${docker_image_rpms}" | while IFS='' read -r docker_image_rpms
    do
        if [ -n "${docker_image_rpms}" ]; then
            yum erase -y "${docker_image_rpms}"
            ret=${?}
            if [ ${ret} -ne 0 ]; then
                 echo "Uninstall docker-image-check tool failed!"
                 exit 1
            fi
        fi
    done
    echo "Uninstall docker-image-check tool successfully!"
}

function uninstall_ipv6nat() {
    ipv6nat_rpms=$(rpm -qa | grep ipv6nat)
    echo "${ipv6nat_rpms}" | while IFS='' read -r ipv6nat_rpms
    do
        if [ -n "${ipv6nat_rpms}" ]; then
            yum erase -y "${ipv6nat_rpms}"
            ret=${?}
            if [ ${ret} -ne 0 ]; then
                 echo "Uninstall ipv6nat failed!"
                 exit 1
            fi
        fi
    done
    echo "Uninstall ipv6nat successfully!"
}

function main() {
    rpm -qa | grep docker-
    local ret=${?}
    if [ ${ret} -eq 0 ]; then
        rpm_uninstall_docker
    fi
    rpm -qa | grep docker-image
    local ret=${?}
    if [ ${ret} -eq 0 ]; then
        uninstall_docker_image
    fi
    rpm -qa | grep ipv6nat
    local ret=${?}
    if [ ${ret} -eq 0 ]; then
        uninstall_ipv6nat
    fi
}

main

