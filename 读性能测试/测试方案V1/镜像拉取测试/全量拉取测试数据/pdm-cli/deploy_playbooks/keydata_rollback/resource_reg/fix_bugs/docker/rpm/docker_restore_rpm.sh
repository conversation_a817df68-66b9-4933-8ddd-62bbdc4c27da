#!/usr/bin/env bash
# shellcheck disable=SC2010

Restore_docker_dir="/paasdata/op-data/paas_upgrade_backup_keydata/rpm/docker"
gc_key="zncgsl6"

function decompress_docker() {
    cd ${Restore_docker_dir} || exit
    rm -rf ./rpms
    tar -zxvf ./rpms.tar.gz
    ret=${?}
    if [ "${ret}" -ne 0 ]; then
         echo "decompress docker failed!"
         exit 1
    else
         echo "decompress docker successfully!"
    fi
}

function get_os_version() {
    os_kernel=$(uname -r)
    if [[ "$os_kernel" =~ ${gc_key} ]]; then
        os_gc="$gc_key"
    else
        os_gc=""
    fi
}

function install_docker() {
    get_os_version
    if [ "$os_gc" = "$gc_key" ]; then
        docker_rpm=$(ls ${Restore_docker_dir}/rpms/ | grep "docker-ce.*.rpm" |grep "$gc_key")
    else
        docker_rpm=$(ls ${Restore_docker_dir}/rpms/ | grep "docker-ce.*.rpm" |grep -v "$gc_key")
    fi
    rpm -ivh "$Restore_docker_dir"/rpms/"$docker_rpm" --force
    ret=${?}
    if [ "${ret}" -ne 0 ]; then
         echo "Install docker failed!"
         exit 1
    else
         echo "Install docker successfully!"
    fi
}

function install_ipv6nat() {
    get_os_version
    if [ "$os_gc" = "$gc_key" ]; then
        ipv6nat_rpm=$(ls ${Restore_docker_dir}/rpms/ |  grep "ipv6nat-.*.rpm" |grep "$gc_key")
    else
        ipv6nat_rpm=$(ls ${Restore_docker_dir}/rpms/ |  grep "ipv6nat-.*.rpm" |grep -v "$gc_key")
    fi
    if < /etc/docker/daemon.json grep ipv6 | grep true; then
         rpm -ivh "$Restore_docker_dir"/rpms/"$ipv6nat_rpm" --force
         ret=${?}
         if [ "${ret}" -ne 0 ]; then
              echo "Install ipv6nat failed!"
              exit 1
         else
              echo "Install ipv6nat successfully!"
         fi
    fi
}

function main() {
    decompress_docker
    install_docker
    install_ipv6nat
    rm -rf ${Restore_docker_dir}/rpms
}

main
