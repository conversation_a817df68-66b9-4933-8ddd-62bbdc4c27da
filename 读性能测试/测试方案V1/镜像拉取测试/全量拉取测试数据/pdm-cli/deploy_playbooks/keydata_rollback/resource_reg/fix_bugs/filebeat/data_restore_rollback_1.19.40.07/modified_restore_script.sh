#!/bin/bash

if [ -f /paasdata/op-conf/filebeat/filebeat_restore_container.sh ];then
    grep "op-comsrv/log" /paasdata/op-conf/filebeat/filebeat_restore_container.sh
    status=$?
    if [ "$status" -ne 0 ];then
        \cp -a /paasdata/op-conf/filebeat/filebeat_restore_container.sh /paasdata/op-conf/filebeat/filebeat_restore_container.sh.bak
        sed -i 's/op-comsrv/op-comsrv\/log/g' /paasdata/op-conf/filebeat/filebeat_restore_container.sh.bak
        if [ -s /paasdata/op-conf/filebeat/filebeat_restore_container.sh.bak ];then
            \cp -a /paasdata/op-conf/filebeat/filebeat_restore_container.sh.bak /paasdata/op-conf/filebeat/filebeat_restore_container.sh
        fi
    fi
fi
