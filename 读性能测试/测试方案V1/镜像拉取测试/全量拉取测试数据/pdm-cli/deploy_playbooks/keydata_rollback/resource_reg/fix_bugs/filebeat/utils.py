#!/usr/bin/python

import ConfigParser
import json
import os
import yaml
import sys
from six.moves import http_client

sys.path.append("/etc/pdm")

from deploy_playbooks.common import common

OP_SERVICE_URL = ""

SUCCESS = [http_client.OK, http_client.CREATED,
           http_client.ACCEPTED, http_client.NO_CONTENT]

paas_conf = ConfigParser.ConfigParser()
paas_conf.read("/etc/pdm/conf/paas.conf")


class YamlFile(object):

    _dataMap = None
    _path = None

    def __init__(self, path=None):
        self._path = path

    def load_data(self):
        f = open(self._path)
        self._dataMap = yaml.safe_load(f)
        f.close()
        if self._dataMap is None:
            self._dataMap = []

    def save_data(self):
        f = open(self._path, "w")
        yaml.dump(self._dataMap, f)
        f.close()


def get_comvars(comvars, element):
    element_value = None
    yamlfile = YamlFile(comvars)
    yamlfile.load_data()
    for var in yamlfile._dataMap:
        if element in var:
            element_value = var[element]
            break
    return element_value


def nodelist():
    need_get_from_nodeworker = True
    nodes = "/paasdata/op-data/pdm-cli/node_info/nodes"
    file_exist = os.path.exists(nodes)
    if file_exist:
        try:
            with open(nodes, 'r') as f:
                nodelist = json.load(f)
                need_get_from_nodeworker = False
        except Exception as e:
            print("exception(%s) happens!" % e)
    if need_get_from_nodeworker:
        url = ("http://%s/nodeworker/v1/tenants/admin/nodes?isused=yes"
               % OP_SERVICE_URL)
        result, nodelist = common.request(url, 'GET')
        if not result:
            print("Get nodelist error.")
            return []
    if "nodes" not in nodelist:
        return []
    return nodelist['nodes']


def get_cluster_nodes():
    url = "http://%s/clusterworker/v1/tenants/admin/nodes" % OP_SERVICE_URL
    result, nodelist = common.request(url, 'GET')
    if not result:
        print("Get cluster nodes error.")
        return

    return nodelist['nodes']


def get_service_ip_port():
    comvars_file = "/root/common/com_vars.yml"
    portvars_file = "/root/common/port_vars.yml"

    if not os.path.exists(comvars_file):
        comvars_file = "/paasdata/op-data/op-sec-harden/com_vars.yml"

    if not os.path.exists(portvars_file):
        portvars_file = "/paasdata/op-data/op-sec-harden/port_vars.yml"

    openpalette_service_ip = get_comvars(comvars_file,
                                         'openpalette_service_ip')
    if not openpalette_service_ip:
        comvars_file = "/paasdata/op-data/op-sec-harden/com_vars.yml"
        openpalette_service_ip = get_comvars(comvars_file,
                                             'openpalette_service_ip')

    if os.path.exists(portvars_file):
        openpalette_service_port = str(get_comvars(portvars_file,
                                                   'openpalette_service_port'))
    else:
        openpalette_service_port = str(get_comvars(comvars_file,
                                                   'openpalette_service_port'))

    return openpalette_service_ip, openpalette_service_port


def set_op_service_url():
    global OP_SERVICE_URL
    openpalette_service_ip, openpalette_service_port = get_service_ip_port()

    if ":" in openpalette_service_ip:
        OP_SERVICE_URL = ("[" + openpalette_service_ip +
                          "]:" + openpalette_service_port)
    else:
        OP_SERVICE_URL = (openpalette_service_ip + ":" +
                          openpalette_service_port)


def get_hosts():
    set_op_service_url()

    hosts = []
    if paas_conf.get('app_scenario', 'application_scenario') != 'embpaas':
        nodes = nodelist()
        for node in nodes:
            if "netinfo" in node and node["netinfo"].get("net_mgt"):
                hosts.append(node["netinfo"]["net_mgt"]["ip"])
    else:
        cluster_nodes = get_cluster_nodes()
        for node in cluster_nodes:
            if "node_ip" in node and node["node_ip"].get("net_mgt"):
                hosts.append(node["node_ip"]["net_mgt"])

    return hosts
