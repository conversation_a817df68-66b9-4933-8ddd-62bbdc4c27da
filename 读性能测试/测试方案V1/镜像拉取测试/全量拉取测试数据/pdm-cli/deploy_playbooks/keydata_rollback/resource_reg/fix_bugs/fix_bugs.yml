- path: pdm-cli/fix_resource_reg.sh
  desc: fix pdm-cli resource registry
  component: cf-pdeploy

- path: pdm-cli/fix_resource_reg.sh
  desc: fix pdm-cli resource registry
  component: pdm-cli

- path: cf-pnode/fix_resource_reg.sh
  desc: fix cf-pnode resource registry
  component: cf-pnode

- path: storage/fix_resource_reg.sh
  desc: fix storage resource registry
  component: storage

- path: pacemaker/fix_resource_reg.sh
  desc: fix pacemaker backup recovery scripts
  component: pacemaker_cluster

- path: os_pkg_repo/fix_resource_reg.sh
  desc: fix os_depends nfs umount script
  component: os_pkg_repo

- path: oki-cli/fix_resource_reg.sh
  desc: fix oki-cli resource
  component: oki-cli

- path: op-containers-containerd/fix_resource_reg.sh
  desc: fix op-containers-containerd resource registry
  component: op-containers-containerd

- path: docker/fix_resource_reg.sh
  desc: fix docker resource
  component: docker

- path: k8s/fix_resource_reg.sh
  desc: fix k8s resource
  component: k8s

- path: k8s-kubeall/fix_resource_reg.sh
  desc: fix k8s-kubeall resource
  component: k8s-kubeall

- path: zart/fix_resource_reg.sh
  desc: fix zart resource
  component: zart

- path: cnrm/fix_resource_reg.sh
  desc: fix cnrm resource
  component: cnrm

- path: userkpi/fix_resource_reg.sh
  desc: fix userkpi resource
  component: userkpi

- path: provider/fix_resource_reg.sh
  desc: fix provider resource
  component: cf-pdeploy

- path: nwmaster/fix_resource_reg.sh
  desc: fix nw resource
  component: nwmaster

- path: inetagent/fix_resource_reg.sh
  desc: fix inetagent resource abnormal during rollback.
  component: inetagent

- path: vnm/fix_resource_reg.sh
  desc: fix vnm resource abnormal during rollback.
  component: vnm
  
- path: op-node-kubeagent/fix_resource_reg.sh
  desc: fix op-node-kubeagent resoure script
  component: op-node-kubeagent

- path: op-asd-swr/fix_resource_reg.sh
  desc: fix op-asd-swr resoure script
  component: op-asd-swr

- path: gpuscheduler/fix_resource_reg.sh
  desc: fix gpuscheduler resource
  component: op-gpu-scripts

- path: zenap_msb_consul_server/fix_resource_reg.sh
  desc: fix zenap_msb_consul_server resource
  component: zenap_msb_consul_server
