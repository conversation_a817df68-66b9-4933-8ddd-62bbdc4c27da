import yaml
import sys

components_misc_file = sys.argv[1]

res_log_owner = {
    "res_name": "gpuscheduler_log_owner",
    "compo_owner": "op-gpu-scripts",
    "judge_method": "by_role",
    "roles": ["master"],
    "backup_cmd": "components_misc/gpuscheduler_log_owner_bak.sh",
    "clear_cmd": "",
    "restore_cmd": "components_misc/gpuscheduler_log_owner_restore.sh"
}

has_log_owner = False

with open(components_misc_file, 'r') as f:
    reg_list = yaml.safe_load(f)
    for reg in reg_list:
        if reg["res_name"] == "gpuscheduler_log_owner":
            has_log_owner = True
            break

if not has_log_owner:
    with open(components_misc_file, 'w') as f:
        reg_list.append(res_log_owner)
        yaml.safe_dump(reg_list, f, default_flow_style=False)
