#!/bin/bash

version=$1
new_base_dir="/etc/pdm/deploy_playbooks/keydata_rollback"
old_base_dir="/etc/pdm_bak/deploy_playbooks/keydata_rollback"
components_misc="$old_base_dir/resource_reg/components_misc.yml"
reference_version="v7.22.30.11.f15p03"

if command -v pythonlatest &> /dev/null; then
    python_cmd="pythonlatest"
else
    python_cmd="python"
fi

log() {
    LOG_DATE=$(date "+%Y-%m-%d %H:%M:%S")
    script_name="fix_resource_reg.sh"

    LOG_DIR="/paasdata/op-log/pdm-cli"
    LOG_FILENAME="$LOG_DIR/op_gpu_scripts_keydata_rollback.log"

    # Ensure the log directory exists
    if [ ! -d "$LOG_DIR" ]; then
        mkdir -m 750 -p "$LOG_DIR"
    fi
    # Ensure the log file exists and has the correct permissions
    if [ ! -f "$LOG_FILENAME" ]; then
        touch "$LOG_FILENAME"
        chmod 640 "$LOG_FILENAME"
    fi
    # Ensure the log file has the correct permissions
    mode=$(stat -c %a "$LOG_FILENAME")
    if [ "$mode" != "640" ]; then
        chmod 640 "$LOG_FILENAME"
    fi

    echo "$LOG_DATE $1 [$script_name] - [$(basename ${FUNCNAME[1]})]:$2" >> $LOG_FILENAME
}

fix_old_res() {
    log INFO "Copy $new_base_dir/resource_reg/fix_bugs/gpuscheduler/components_misc/* to $old_base_dir/resource_reg/components_misc/"
    if ! cp -rf "$new_base_dir"/resource_reg/fix_bugs/gpuscheduler/components_misc/* "$old_base_dir"/resource_reg/components_misc/; then
        log ERROR "Failed to copy files from $new_base_dir/resource_reg/fix_bugs/gpuscheduler/components_misc/ to $old_base_dir/resource_reg/components_misc/"
        return 1
    fi

    log INFO "Add gpuscheduler_log_owner res to $components_misc"
    script_path="$new_base_dir/resource_reg/fix_bugs/gpuscheduler/fix_gpuscheduler_res.py"
    if ! "$python_cmd" "$script_path" "$components_misc"; then
        log ERROR "Failed to execute $python_cmd $script_path $components_misc"
        return 1
    fi

    log INFO "Successfully executed fix_old_res"
    return 0
}

# Versions prior to v7.24 need to be fixed
version_pre5=${version:0:5}
# shellcheck disable=SC2071
if [[ "${version_pre5#*.}" < "24" ]];then
    log INFO "--- fix components_misc in old version $version ---"
    fix_old_res
    if [ $? -ne 0 ]; then
        log ERROR "fix_old_res failed"
        exit 1
    fi
fi

exit 0