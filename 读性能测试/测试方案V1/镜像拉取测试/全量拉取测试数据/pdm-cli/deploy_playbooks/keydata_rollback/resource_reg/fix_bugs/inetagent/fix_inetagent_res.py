import json
import sys


res_filedir_path = sys.argv[1]
res_service_path = sys.argv[2]

res_service = {
    "res_name": "inetagent",
    "compo_owner": "inetagent",
    "judge_method": "by_role",
    "roles": ["usednodes"],
    "stop_cmd": "if [[ $(systemctl is-enabled inetagent.service 2>/dev/null) == \"enabled\" ]]; then (systemctl disable inetagent.service;systemctl stop inetagent.service); fi",
    "restore_cmd": "if [[ -d /paasdata/op-conf/inetagent-bak ]]; then (rm -rf /paasdata/op-conf/inetagent/inetagent_virenv;tar -zxvf /paasdata/op-conf/inetagent-bak/inetagent_conf.tar.gz --strip-components=1 -C /paasdata/op-conf/inetagent/); fi; if [[ -e /usr/lib/systemd/system/inetagent.service ]]; then (systemctl enable inetagent.service;systemctl restart inetagent.service); fi"
}

has_inetagent_service = False

with open(res_filedir_path, 'r') as f:
    reg_list = json.load(f)
    for reg in reg_list:
        if reg["res_name"] == "op-conf":
            if "/paasdata/op-conf/inetagent-bak" not in reg["exclu_dir"]:
                reg["exclu_dir"].append("/paasdata/op-conf/inetagent-bak")
                with open(res_filedir_path, 'w') as f:
                    json.dump(reg_list, f, indent=4)
            break
    for reg in reg_list:
        if reg["res_name"] == "inetagent":
            for file_path in ["/usr/lib/python3.6/site-packages/inetagent", "/usr/lib/python3.7/site-packages/inetagent", "/usr/lib/python3.11/site-packages/inetagent", "/usr/lib64/libpython2.7.so.1.0"]:
                if file_path not in reg["inclu_dir"]:
                    reg["inclu_dir"].append(file_path)
                    with open(res_filedir_path, 'w') as f:
                        json.dump(reg_list, f, indent=4)
            break

with open(res_service_path, 'r') as f:
    reg_list = json.load(f)
    for reg in reg_list:
        if reg["res_name"] == "inetagent":
            has_inetagent_service = True
            break

if not has_inetagent_service:
    with open(res_service_path, 'w') as f:
        reg_list.append(res_service)
        json.dump(reg_list, f, indent=4)
