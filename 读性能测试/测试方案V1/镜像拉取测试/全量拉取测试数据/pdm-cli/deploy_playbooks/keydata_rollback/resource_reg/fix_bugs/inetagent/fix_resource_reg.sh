#!/bin/bash

version=$1
version_pre8=${version:0:8}
new_base_dir="/etc/pdm/deploy_playbooks/keydata_rollback"
old_base_dir="/etc/pdm_bak/deploy_playbooks/keydata_rollback"
res_filedir="$old_base_dir/resource_reg/res_filedir.yml"
res_service="$old_base_dir/resource_reg/res_service.yml"
SCRIPT=$(readlink -f "$0")
SCRIPT_PATH=$(dirname "$SCRIPT")

log() {
    local log_info=$1
    LOG_DATE=$(date "+%Y-%m-%d %H:%M:%S.%6N")
    LOG_FILENAME="/var/log/inetagent_modify.log"
    if [ ! -f "$LOG_FILENAME" ]; then
       touch "$LOG_FILENAME"
       chmod 640 "$LOG_FILENAME"
    fi
    mode=$(stat -c %a "$LOG_FILENAME")
    if [ "$mode" != "640" ]; then
        chmod 640 "$LOG_FILENAME"
    fi
    echo "$LOG_DATE $log_info" >> $LOG_FILENAME
}

# cp -rf "$new_base_dir"/resource_reg/fix_bugs/inetagent/service/* "$old_base_dir"/resource_reg/service/
# shellcheck disable=SC2072
if [[ "${version_pre8#*.}" < "23.30" ]] || [[ "${version_pre8#*.}" == "23.30" ]];then
    script_path="$new_base_dir/resource_reg/fix_bugs/inetagent/fix_inetagent_res.py"
    $(command -v pythonlatest || echo python) "$script_path" "$res_filedir" "$res_service"

    cd "$SCRIPT_PATH" || exit
    $(command -v pythonlatest || echo python) generate_hosts.py
    result=$?
    if [ 0 -ne "$result" ]; then
        log "[FAILED] generate hosts failed! exit!"
        exit 1
    fi
    ansible-playbook -i all_node_hosts modify_inetagent_start_script.yml
    rc=$?
    if [[ $rc -ne 0 ]]; then
        log "modify inetagent start script fail!!"
        exit 1
    fi
    log "modify inetagent start script successfully !!"
    exit 0
fi