# Copyright 2012 OpenStack  Foundation
# All Rights Reserved.
#
#    Licensed under the Apache License, Version 2.0 (the "License"); you may
#    not use this file except in compliance with the License. You may obtain
#    a copy of the License at
#
#         http://www.apache.org/licenses/LICENSE-2.0
#
#    Unless required by applicable law or agreed to in writing, software
#    distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
#    WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
#    License for the specific language governing permissions and limitations
#    under the License.

import httplib2
import os
import yaml
import json
import sys


def get_from_comvars(varname, filepath=None):
    if not filepath:
        if os.path.exists('/root/common/port_vars.yml'):
            filepath = "/root/common/port_vars.yml"
        else:
            filepath = "/root/common/com_vars.yml"
    if not os.path.exists(filepath):
        return None
    f = open(filepath)
    dataMap = yaml.safe_load(f)
    f.close()
    for data in dataMap:
        if varname in data:
            return data[varname]
    return None


def get_nodes():
    need_get_from_nodeworker = True
    nodes = "/paasdata/op-data/pdm-cli/node_info/nodes"
    file_exist = os.path.exists(nodes)
    if file_exist:
        try:
            with open(nodes, 'r') as f:
                content = json.load(f)
                need_get_from_nodeworker = False
        except Exception as e:
            print("exception(%s) happens!" % e)
    if need_get_from_nodeworker:
        header = {'Content-Type': 'application/json'}
        msb_ip = get_from_comvars('openpalette_service_ip',
                                  '/root/common/com_vars.yml')
        msb__port = get_from_comvars('zenap_msb_apigateway_port')
        op_service_url = "%s:%s" % (msb_ip, msb__port)
        url = ('http://%s/nodeworker/v1/tenants/admin/nodes?isused=yes' %
               (op_service_url))
        http = httplib2.Http(timeout=60,
                             disable_ssl_certificate_validation=True)
        result, nodelist = http.request(url, 'GET',
                                        headers=header)
        if not result:
            return []
        content = json.loads(nodelist)
    if "nodes" not in content:
        return []
    return content["nodes"]


def generate_node_hosts():
    nodes = []
    try:
        nodes = get_nodes()
        res = write_all_nodes_ip(nodes)
        if not res:
            return False
    except Exception as ex:
        print("generate hosts failed! except=%s" % ex)
        return False
    print("generate hosts successfully!")
    return True


def write_all_nodes_ip(nodes):
    hosts_file = 'all_node_hosts'
    hosts = []
    for node in nodes:
        info = {}
        if "netinfo" in node and node["netinfo"].get("net_mgt"):
            info["man_ip"] = node["netinfo"]["net_mgt"]["ip"]
            hosts.append(info)
    if not hosts:
        cmd = ("rm -fr %s && cp %s %s"
               % (hosts_file, '/etc/pdm/hosts', hosts_file))
        res = os.system(cmd)
        if res != 0:
            print('cp /etc/pdm/hosts failed')
            return False
        else:
            print('cp /etc/pdm/hosts ok')
            return True
    fo = open(hosts_file, 'w')
    fo.write("[nodes]\n")
    for node_loop in hosts:
        fo.write(node_loop['man_ip'])
        fo.write("\n")
    fo.flush()
    fo.close()
    return True


def main():
    if generate_node_hosts():
        sys.exit(0)
    else:
        sys.exit(1)


if __name__ == '__main__':
    main()
