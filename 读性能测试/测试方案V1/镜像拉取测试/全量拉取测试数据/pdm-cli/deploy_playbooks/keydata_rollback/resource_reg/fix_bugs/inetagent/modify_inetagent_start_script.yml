---
- name: modify inetagent start script
  hosts: nodes
  gather_facts: false
  become: yes
  remote_user: ubuntu
  tasks:
    - name: modify inetagent start script task
      shell: |
        sed -i '/for i in {1..5}/d' /etc/init.d/inetagent
        sed -i '/\/paasdata\/op-conf\/inetagent\/inetagent_virenv\/inetagent_env\/bin\/python $file $conf/a \    for i in {1..5}; do agent_pid=$(ps -elf |grep inetagent_main | grep -v grep |awk '"'"'{print \$4}'"'"'); if [[ -z $agent_pid ]]; then sleep 3; else echo "$agent_pid"; break; fi; done' /etc/init.d/inetagent
      ignore_errors: yes
