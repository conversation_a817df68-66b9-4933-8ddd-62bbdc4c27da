---
- hosts : nodes
  remote_user: ubuntu
  become: yes
  become_method: sudo
  gather_facts: no
  tasks:
    # Started by AICoder, pid:48e48691b3nf0f41435508c310269a267a128dad
    - name: Check if the file exists
      stat:
        path: /paasdata/op-data/br-k8s-backup/br_etcd_recover.yml
      register: file_stat

    - name: Ensure the file contains the target string
      shell: >
        grep -q "rsync_path: \"try rsync\"" /paasdata/op-data/br-k8s-backup/br_etcd_recover.yml
      register: grep_result
      ignore_errors: true
      when: file_stat.stat.exists

    - name: Replace the string globally in the file
      replace:
        path: /paasdata/op-data/br-k8s-backup/br_etcd_recover.yml
        regexp: 'rsync_path: "try rsync"'
        replace: 'rsync_path: "TRY_ANSIBLE=1 try rsync"'
      ignore_errors: true
      when:
        - file_stat.stat.exists
        - grep_result.rc == 0
    # Ended by AICoder, pid:48e48691b3nf0f41435508c310269a267a128dad

    - debug:
        msg: "fix k8s conf end!"
