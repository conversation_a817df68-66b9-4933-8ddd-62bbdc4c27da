---
- hosts : nodes
  remote_user: ubuntu
  become: yes
  become_method: sudo
  gather_facts: no
  tasks:
# Started by AICoder, pid:a7408wd6ff3afea14c5d09a090a74b1f6e639668
    - name: Check /paasdata/op-log/k8s directory exists
      stat:
        path: "/paasdata/op-log/k8s"
      register: k8s_log_dir_stat

    - name: Create directory if /paasdata/op-log/k8s does not exist
      file:
        path: "/paasdata/op-log/k8s"
        state: directory
        mode: '0750'
        owner: root
        group: root
      when: not k8s_log_dir_stat.stat.exists
# Ended by AICoder, pid:a7408wd6ff3afea14c5d09a090a74b1f6e639668 

    - name: stat /paasdata/op-data/br-k8s-backup/br_k8s_recover.sh exists
      stat:
        path: "/paasdata/op-data/br-k8s-backup/br_k8s_recover.sh"
      register: br_k8s_recover_stat

    - name: fix br_k8s_recover.sh
      shell: sed -i "s/\"etcd_conf_ep\"/\"\$etcd_conf_ep\"/g" /paasdata/op-data/br-k8s-backup/br_k8s_recover.sh
      when: br_k8s_recover_stat.stat.exists
      ignore_errors: yes

    - name: stat /paasdata/op-data/br-k8s-backup/br_k8s_backup.sh exists
      stat:
        path: "/paasdata/op-data/br-k8s-backup/br_k8s_backup.sh"
      register: br_k8s_backup_stat

    - name: fix br_k8s_backup.sh
      shell: sed -i "s/\"etcd_conf_ep\"/\"\$etcd_conf_ep\"/g" /paasdata/op-data/br-k8s-backup/br_k8s_backup.sh
      when: br_k8s_backup_stat.stat.exists
      ignore_errors: yes

    - name: make sure log_dir exist in recover process
      ansible.builtin.lineinfile:
        path: /paasdata/op-data/br-k8s-backup/br_k8s_recover.sh
        line: "if ! stat /paasdata/op-log/k8s > /dev/null 2>&1; then mkdir -p /paasdata/op-log/k8s && chmod 0750 /paasdata/op-log/k8s && chown root:root /paasdata/op-log/k8s; fi"
        insertafter: "^#!/bin/bash"
      when: br_k8s_recover_stat.stat.exists

    - name: make sure log_dir exist in backup process
      ansible.builtin.lineinfile:
        path: /paasdata/op-data/br-k8s-backup/br_k8s_backup.sh
        line: "if ! stat /paasdata/op-log/k8s > /dev/null 2>&1; then mkdir -p /paasdata/op-log/k8s && chmod 0750 /paasdata/op-log/k8s && chown root:root /paasdata/op-log/k8s; fi"
        insertafter: "^#!/bin/bash"
      when: br_k8s_backup_stat.stat.exists

    # Started by AICoder, pid:48e48691b3nf0f41435508c310269a267a128dad
    - name: Check if the file exists
      stat:
        path: /paasdata/op-data/br-k8s-backup/br_etcd_recover.yml
      register: file_stat

    - name: Ensure the file contains the target string
      shell: >
        grep -q "rsync_path: \"try rsync\"" /paasdata/op-data/br-k8s-backup/br_etcd_recover.yml
      register: grep_result
      ignore_errors: true
      when: file_stat.stat.exists

    - name: Replace the string globally in the file
      replace:
        path: /paasdata/op-data/br-k8s-backup/br_etcd_recover.yml
        regexp: 'rsync_path: "try rsync"'
        replace: 'rsync_path: "TRY_ANSIBLE=1 try rsync"'
      ignore_errors: true
      when:
        - file_stat.stat.exists
        - grep_result.rc == 0
    # Ended by AICoder, pid:48e48691b3nf0f41435508c310269a267a128dad

    - debug:
        msg: "fix k8s conf end!"