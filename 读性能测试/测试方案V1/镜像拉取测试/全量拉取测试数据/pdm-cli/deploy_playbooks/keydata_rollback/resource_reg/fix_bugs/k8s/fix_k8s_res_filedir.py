# -*- coding: utf-8 -*-

import sys
import json
# Started by AICoder, pid:0609dy416fq0f911414e0a49c0b229294b072be1 
reg_path = sys.argv[1]

with open(reg_path, 'r') as file:
    data = json.load(file)

resource_name = "k8s-conf"
string_to_add = "/paasdata/op-data/k8s-etcd/set_etcd_disk_priority.sh"

for item in data:
    if item["res_name"] == resource_name:
        if string_to_add not in item["inclu_dir"]:
            item["inclu_dir"].append(string_to_add)
        break

with open(reg_path, 'w') as file:
    json.dump(data, file, indent=4)

# Ended by AICoder, pid:0609dy416fq0f911414e0a49c0b229294b072be1 