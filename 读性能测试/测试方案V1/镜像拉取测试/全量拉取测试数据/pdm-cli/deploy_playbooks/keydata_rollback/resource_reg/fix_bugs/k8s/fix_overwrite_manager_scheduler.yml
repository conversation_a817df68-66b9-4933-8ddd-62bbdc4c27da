---
# Started by AICoder, pid:z778dweb54m45981422c0b070066108dbc50fec2
- name: overwrite detect_kube-controller-manager.sh and detect_kube-scheduler.sh
  hosts : nodes
  remote_user: ubuntu
  become: yes
  become_method: sudo
  gather_facts: no
  tasks:
    - name: Check if /paasdata/op-conf/k8s directory exists
      stat:
        path: /paasdata/op-conf/k8s
      register: dir_stat

    - name: Check if detect_kube-controller-manager.sh exists
      stat:
        path: /paasdata/op-conf/k8s/detect_kube-controller-manager.sh
      when: dir_stat.stat.exists
      register: manager_file_stat

    - name: Clear and write the script to detect_kube-controller-manager.sh
      copy:
        dest: /paasdata/op-conf/k8s/detect_kube-controller-manager.sh
        content: |
          #!/usr/bin/env bash

          ip=`cat /etc/kubernetes/manifests/kube-controller-manager.yml | grep host: | awk '{print $2}' |  sed 's/\"//g'`

          echo "$ip" | grep -q ":"
          if [ $? -eq 0 ]; then
            ip="[$ip]"
          fi  
          n=1
          while [ $n -le 3 ]
          do
            if timeout 10 curl  --connect-timeout 10 -m 15 -g  -k  https://"$ip":10257/healthz
            then
              exit 0
            fi
            n=$((n+1))
            sleep 3s
          done
          exit 1
        mode: '0750'
      when: dir_stat.stat.exists and manager_file_stat.stat.exists


    - name: Check if detect_kube-scheduler.sh exists
      stat:
        path: /paasdata/op-conf/k8s/detect_kube-scheduler.sh
      when: dir_stat.stat.exists
      register: scheduler_file_stat

    - name: Clear and write the script to detect_kube-scheduler.sh
      copy:
        dest: /paasdata/op-conf/k8s/detect_kube-scheduler.sh
        content: |
          #!/usr/bin/env bash

          ip=`cat /etc/kubernetes/manifests/kube-scheduler.yml | grep host: | awk '{print $2}' |  sed 's/\"//g'`

          echo "$ip" | grep -q ":"
          if [ $? -eq 0 ]; then
            ip="[$ip]"
          fi
          n=1
          while [ $n -le 3 ]
          do
            if timeout 10 curl  --connect-timeout 10 -m 15 -g  -k  https://"$ip":10259/healthz
            then
              exit 0
            fi
            n=$((n+1))
            sleep 3s
          done
          exit 1
        mode: '0750'
      when: dir_stat.stat.exists and scheduler_file_stat.stat.exists

    - debug:
        msg: "fix_overwrite_manager_scheduler end "

# Ended by AICoder, pid:z778dweb54m45981422c0b070066108dbc50fec2 