#!/bin/bash

version=$1
version_pre8=${version:0:8}
new_base_dir="/etc/pdm/deploy_playbooks/keydata_rollback"
old_base_dir="/etc/pdm_bak/deploy_playbooks/keydata_rollback"
old_res="$old_base_dir/resource_reg"

#放开"/paasdata/op-data/br-k8s-backup","/paasdata/op-data/k8s-*"
sed -ri 's#\"\/paasdata\/op-data\/k8s-\*\",[^\"]*# #' $old_res/res_filedir.yml
sed -ri 's#\"\/paasdata\/op-data\/br-k8s-backup\",[^\"]*# #' $old_res/res_filedir.yml
#拷贝rpm的shell到rpm目录下
if ! [ -d "${old_res}/rpm" ];then
    mkdir -p "${old_res}/rpm"
fi
cp $new_base_dir/resource_reg/fix_bugs/k8s/rpm_sh/* $old_base_dir/resource_reg/rpm
cp -rf "$new_base_dir"/resource_reg/fix_bugs/k8s/service/* "$old_base_dir"/resource_reg/service/

# Started by AICoder, pid:589f14da01940d7140d40b2e30855626c6d1d778 
if command -v pythonlatest &> /dev/null; then
    python_cmd="pythonlatest"
else
    python_cmd="python"
fi

# 获取版本号
version="${version_pre8#*.}"

# 使用 bc 进行版本比较
# $1 < $2  返回1
# $1 >= $2  返回0
version_check() {
    echo "$1 < $2" | bc -l
}

if [[ $(version_check "$version" "20.40") -eq 0 ]] || [[ "$version" == "20.40" ]]; then
    # 40以及40以上版本:镜像化
    script_path="$new_base_dir/resource_reg/fix_bugs/k8s/with_image/fix_k8s_with_image.py"
else
    # 非镜像化 版本号小于20.40
    script_path="$new_base_dir/resource_reg/fix_bugs/k8s/with_service/fix_k8s_with_service.py"
fi

# 执行 Python 脚本
$python_cmd "$script_path" "$old_res"
# Ended by AICoder, pid:589f14da01940d7140d40b2e30855626c6d1d778 

res_path="$old_res/res_filedir.yml"
script_path="$new_base_dir/resource_reg/fix_bugs/k8s/fix_k8s_res_filedir.py"
$python_cmd "$script_path" "$res_path"

ansible-playbook -i /etc/pdm/hosts $new_base_dir/resource_reg/fix_bugs/k8s/fix_file_in_all_node.yml
ansible-playbook -i /etc/pdm/hosts $new_base_dir/resource_reg/fix_bugs/k8s/fix_overwrite_manager_scheduler.yml
exit $?