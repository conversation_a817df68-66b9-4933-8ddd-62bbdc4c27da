#!/bin/bash

set +e
target_curl_version='7.66.0'
local_curl_version=`curl --version | awk '{print $2}' | sed -n '1p'`
function version_ge() { test "$(echo "$@" | tr " " "\n" | sort -rV | head -n 1)" == "$1"; }
check_kube_proxy(){
  # wait 10 mins for kube-proxy to startup
  for((i=1;i<=120;i++));
  do
    if version_ge "$local_curl_version" "$target_curl_version"; then
      curl -g -s --http0.9 http://localhost:10256/healthz >&2
    else
      curl -g -s http://localhost:10256/healthz >&2
    fi
    if [ $? -eq 0 ]
    then
      return 0
    fi
    sleep 5s
  done
  return 1
}

check_kube_apiserver(){
  # wait 10 mins for kube-apiserver to startup
  kube_apiserver_vip=$(cat /etc/kubernetes/proxy.kubeconfig | grep "server:" | awk -F '://' '{print $2}' |tr -d ' ')
  for((i=1;i<=120;i++));
  do
    if version_ge "$local_curl_version" "$target_curl_version"; then
      data=`timeout 5 curl -g --cacert /etc/kubernetes/certs/ca.crt --key /etc/kubernetes/certs/kubecfg.key --cert /etc/kubernetes/certs/kubecfg.crt --http0.9 https://${kube_apiserver_vip}/readyz`
    else
      data=`timeout 5 curl -g --cacert /etc/kubernetes/certs/ca.crt --key /etc/kubernetes/certs/kubecfg.key --cert /etc/kubernetes/certs/kubecfg.crt https://${kube_apiserver_vip}/readyz`
    fi
    if [ "${data}" == "ok" ]
    then
      return 0
    fi
    sleep 5s
  done
  return 1
}

if [[ -e /etc/systemd/system/kubelet.service ]]; then
  echo "kubelet.service exist,restart kubelet"
  systemctl enable kubelet
  systemctl restart kubelet
fi

if [ ! -f ~/kube-proxy-image ]; then
  echo "kube-proxy mark file not found, skip cleanup iptables rules" >&2
  exit 0
fi

old_proxy_image=$(cat ~/kube-proxy-image| tr -d ' "')
if [ "${old_proxy_image}" = "" ]; then
  echo "empty image from kube-proxy mark file, skip cleanup iptables rules" >&2
  rm -fr ~/kube-proxy-image
  exit 0
fi

if grep "container-runtime=remote" /etc/kubernetes/kubelet; then
  nerdctl -n k8s.io images --format={{.Repository}}:{{.Tag}} |grep "${old_proxy_image}"
else
  docker images --format={{.Repository}}:{{.Tag}} |grep "${old_proxy_image}"
fi
if [ $? -ne 0 ]; then
  echo "kube-proxy image before rollback not found, skip cleanup iptables rules" >&2
  rm -fr ~/kube-proxy-image
  exit 0
fi

if [ -f /etc/kubernetes/manifests/kube-proxy.yml ]; then
  backup_proxy_image=$(cat /etc/kubernetes/manifests/kube-proxy.yml |grep image: | awk -F'image:' '{print $2}' | tr -d ' "')
  if grep "container-runtime=remote" /etc/kubernetes/kubelet; then
    nerdctl -n k8s.io run --rm --net=host --privileged "${backup_proxy_image}" sh -c 'iptables --version' |grep nf_tables >> /dev/null 2>&1
  else
    docker run --rm --rm --net=host --privileged "${backup_proxy_image}" sh -c 'iptables --version' |grep nf_tables >> /dev/null 2>&1
  fi
  backup_proxy_iptable_mode=$?
else
  echo "rollback to proxy service mode, skip cleanup iptables rules" >&2
  rm -fr ~/kube-proxy-image
  exit 0
fi

iptables --version |grep nf_tables >> /dev/null 2>&1
host_iptable_mode=$?

if [ "${backup_proxy_iptable_mode}" = "${host_iptable_mode}" ]; then
  echo "iptables mode between host and current kube-proxy image stay same, skip cleanup iptables rules" >&2
  rm -fr ~/kube-proxy-image
  exit 0
fi

if [ -f /etc/kubernetes/manifests/kube-proxy.yml ]; then
  check_kube_proxy
  if [ $? -ne 0 ]; then
    echo "Failed waiting kube-proxy startup, error exits" >&2
    exit 1
  fi
  check_kube_apiserver
  if [ $? -ne 0 ]; then
    echo "Failed waiting kube-proxy startup, error exits" >&2
    exit 1
  fi
fi

echo "Start to cleanup residual iptables rules..."  >&2
if grep "container-runtime=remote" /etc/kubernetes/kubelet; then
  timeout 600 nerdctl -n k8s.io run --rm --privileged --net=host --name kube-proxy-cleanup \
      -v /lib/modules:/lib/modules \
      -v /run/xtables.lock:/run/xtables.lock \
      -v /paasdata/op-log/k8s:/paasdata/op-log/k8s \
      -v /etc/kubernetes:/etc/kubernetes \
      ${old_proxy_image} \
      /usr/local/bin/kube-proxy --cleanup=true \
      --v=0 --log-file=/paasdata/op-log/k8s/kube-proxy.klog --logtostderr=true --alsologtostderr=true \
      --kubeconfig=/etc/kubernetes/proxy.kubeconfig \
      --bind-address=127.0.0.1
else
  timeout 600 docker run --rm --privileged --name kube-proxy-cleanup \
      -v /lib/modules:/lib/modules \
      -v /run/xtables.lock:/run/xtables.lock \
      -v /paasdata/op-log/k8s:/paasdata/op-log/k8s \
      -v /etc/kubernetes:/etc/kubernetes \
      --net=host \
      ${old_proxy_image} \
      /usr/local/bin/kube-proxy \
      --cleanup=true \
      --v=0 \
      --log-file=/paasdata/op-log/k8s/kube-proxy.klog \
      --logtostderr=true \
      --alsologtostderr=true \
      --kubeconfig=/proxy.kubeconfig \
      --bind-address=127.0.0.1
fi

if [ $? -ne 0 ]; then
  echo "Failed to execute cleanup iptables rules, error exits" >&2
  exit 1
fi

rm -fr ~/kube-proxy-image
echo "Cleanup residual iptables rules Completed." >&2