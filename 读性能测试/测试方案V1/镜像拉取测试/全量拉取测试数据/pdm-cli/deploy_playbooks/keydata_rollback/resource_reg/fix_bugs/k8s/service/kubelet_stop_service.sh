#!/bin/bash

if [[ $(systemctl is-enabled kubelet 2>/dev/null) == \"enabled\" ]]; then
  systemctl stop kubelet;
fi

if [ -f ~/kube-proxy-image ]; then
  echo "kube-proxy image flag(file) exists, skip remark" >&2
  exit 0
fi

if [ ! -f /etc/kubernetes/manifests/kube-proxy.yml ]; then
  echo "kube-proxy manifest not found, skip mark kube-proxy image" >&2
  exit 0
fi

proxy_mode=$(cat /etc/kubernetes/manifests/kube-proxy.yml | grep proxy-mode | awk -F'=' '{print $2}' | tr -d ' "')
if [ "${proxy_mode}" = "ipvs" ]; then
  echo "kube-proxy run on ipvs mode, skip mark kube-proxy image" >&2
  exit 0
fi

old_proxy_image=$(cat /etc/kubernetes/manifests/kube-proxy.yml |grep image: | awk -F'image:' '{print $2}' | tr -d ' "')
if [ "${old_proxy_image}" = "" ]; then
  echo "current kube-proxy image is empty, skip mark kube-proxy image" >&2
  exit 0
fi

if [ -f /paasdata/op-data/paas_upgrade_backup_keydata/filedir/k8s-conf/etc/kubernetes/manifests/kube-proxy.yml ]; then
  backup_proxy_image=$(cat /paasdata/op-data/paas_upgrade_backup_keydata/filedir/k8s-conf/etc/kubernetes/manifests/kube-proxy.yml |grep image: | awk -F'image:' '{print $2}' | tr -d ' "')
fi

if [ "${old_proxy_image}" = "${backup_proxy_image}" ]; then
  echo "same kube-proxy image version, skip mark kube-proxy image" >&2
  exit 0
fi

echo "${old_proxy_image}" > ~/kube-proxy-image || true
