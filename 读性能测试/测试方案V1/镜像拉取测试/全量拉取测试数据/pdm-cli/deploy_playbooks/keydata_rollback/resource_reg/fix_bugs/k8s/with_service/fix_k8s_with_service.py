import json
import sys
# res_filedir.yml
reg_filedir_path = sys.argv[1] + "/res_filedir.yml"
k8s_filedir_res = {
    "res_name": "k8s-conf",
    "compo_owner": "k8s",
    "judge_method": "by_role",
    "roles": ["master", "minion"],
    "inclu_dir": [
        "/etc/kubernetes",
        "/usr/bin/external-apiservice", "/usr/bin/daemon-task",
        "/etc/systemd/system/external-apiservice.service",
        "/etc/systemd/system/docker.service.d/50-docker-trigger-kubelet.conf",
        "/etc/systemd/system/system.conf.d/kubernetes-accounting.conf",
        "/usr/lib/tmpfiles.d/kubernetes.conf",
        "/etc/systemd/system/float-etcd.service",
        "/etc/systemd/system/daemon-task.service",
        "/etc/systemd/system/etcd.service",
        "/etc/etcd", "/paasdata/docker/cpu_manager_state", "/opt/kubernetes",
        "/var/lib/kubelet", "/etc/systemd/system/kube-apiserver.service",
        "/etc/systemd/system/kube-controller-manager.service",
        "/etc/systemd/system/kube-scheduler.service",
        "/etc/systemd/system/kubelet.service",
        "/etc/systemd/system/kube-proxy.service",
        "/opt/cni/bin", "/etc/cni/net.d",
        "/var/spool/cron/kube",
        "/usr/bin/kube-apiserver", "/usr/bin/kube-controller-manager",
        "/usr/bin/kubelet", "/usr/bin/kube-proxy", "/usr/bin/kube-scheduler",
        "/usr/bin/kubectl", "/usr/bin/etcd",
        "/usr/bin/etcdctl", "/usr/bin/kubectl-extra", "/home/<USER>/ca.crt",
        "/home/<USER>/k8s_bin", "/home/<USER>/kubectl", "/home/<USER>/kubectl.kubeconfig"],
    "exclu_dir": [],
    "inclu_big_subdir": []
}
has_k8s = False
has_excluded_k8s_etcd = False

with open(reg_filedir_path, 'r') as f:
    reg_list = json.load(f)
    for reg in reg_list:
        if reg["res_name"] == "k8s-conf":
            has_k8s = True
            break
    for reg in reg_list:
        if reg["res_name"] == "op-data":
            for excludeDir in reg["exclu_dir"]:
                if excludeDir == "/paasdata/op-data/k8s-etcd":
                    has_excluded_k8s_etcd = True
                    break
            if not has_excluded_k8s_etcd:
                reg["exclu_dir"].append("/paasdata/op-data/k8s-etcd")

if not has_k8s:
    reg_list.append(k8s_filedir_res)

with open(reg_filedir_path, 'w') as f:
    json.dump(reg_list, f, indent=4)

# res_service.yml
reg_service_path = sys.argv[1] + "/res_service.yml"
k8s_kubelet_service = {
    "res_name": "kubelet",
    "compo_owner": "k8s",
    "judge_method": "by_role",
    "roles": ["minion", "master"],
    "stop_cmd": "service/kubelet_stop_service.sh",
    "restore_cmd": "service/kubelet_restore_service.sh"
}
has_k8s_kubelet_service = False

k8s_daemon_task_service = {
    "res_name": "daemon-task",
    "compo_owner": "k8s",
    "judge_method": "by_role",
    "roles": ["master"],
    "stop_cmd": "if [[ $(systemctl is-enabled daemon-task 2>/dev/null)"
                " == \"enabled\" ]]; then systemctl stop daemon-task; fi",
    "restore_cmd": "if [[ -e /etc/systemd/system/daemon-task.service"
                   " ]]; then (systemctl enable daemon-task;"
                   "systemctl restart daemon-task); fi"
}
has_k8s_daemon_task_service = False

k8s_external_apiservice_service = {
    "res_name": "external-apiservice",
    "compo_owner": "k8s",
    "judge_method": "by_role",
    "roles": ["master"],
    "stop_cmd": "if [[ $(systemctl is-enabled external-apiservice"
                " 2>/dev/null) == \"enabled\" ]]; then systemctl stop"
                " external-apiservice; fi",
    "restore_cmd": "if [[ $(systemctl is-enabled external-apiservice"
                   " 2>/dev/null) == \"enabled\" ]]; then systemctl restart"
                   " external-apiservice; fi"
}
has_k8s_external_apiservice_service = False

k8s_etcd_service = {
    "res_name": "etcd",
    "compo_owner": "k8s",
    "judge_method": "by_role",
    "roles": ["master"],
    "stop_cmd": "if [[ $(systemctl is-enabled etcd 2>/dev/null)"
                " == \"enabled\" ]]; then systemctl stop etcd; fi",
    "restore_cmd": ":"
}
has_k8s_etcd_service = False

k8s_kube_apiserver_service = {
    "res_name": "kube-apiserver",
    "compo_owner": "k8s",
    "judge_method": "by_role",
    "roles": ["master"],
    "stop_cmd": "if [[ $(systemctl is-enabled kube-apiserver"
                " 2>/dev/null) == \"enabled\" ]]; then systemctl stop"
                " kube-apiserver; fi",
    "restore_cmd": "if [[ -e /etc/systemd/system/kube-apiserver.service"
                   " ]]; then (systemctl enable kube-apiserver;"
                   "systemctl restart kube-apiserver); fi"
}
has_k8s_kube_apiserver_service = False

k8s_kube_scheduler_service = {
    "res_name": "kube-scheduler",
    "compo_owner": "k8s",
    "judge_method": "by_role",
    "roles": ["master"],
    "stop_cmd": "if [[ $(systemctl is-enabled kube-scheduler "
                "2>/dev/null) == \"enabled\" ]]; then systemctl stop "
                "kube-scheduler; fi",
    "restore_cmd": "if [[ -e /etc/systemd/system/kube-scheduler.service"
                   " ]]; then (systemctl enable kube-scheduler;"
                   "systemctl restart kube-scheduler); fi"
}
has_k8s_kube_scheduler_service = False

k8s_kube_controller_manager_service = {
    "res_name": "kube-controller-manager",
    "compo_owner": "k8s",
    "judge_method": "by_role",
    "roles": ["master"],
    "stop_cmd": "if [[ $(systemctl is-enabled kube-controller-manager"
                " 2>/dev/null) == \"enabled\" ]]; then systemctl stop "
                "kube-controller-manager; fi",
    "restore_cmd": "if [[ -e /etc/systemd/system/"
                   "kube-controller-manager.service ]]; then "
                   "(systemctl enable kube-controller-manager;"
                   "systemctl restart kube-controller-manager); fi"
}
has_k8s_kube_controller_manager_service = False

k8s_kube_proxy_service = {
    "res_name": "kube-proxy",
    "compo_owner": "k8s",
    "judge_method": "by_role",
    "roles": ["minion", "master"],
    "stop_cmd": "if [[ $(systemctl is-enabled kube-proxy 2>/dev/null) "
                "== \"enabled\" ]]; then systemctl stop kube-proxy; fi",
    "restore_cmd": "if [[ -e /etc/systemd/system/kube-proxy.service ]]; "
                   "then (systemctl enable kube-proxy;"
                   "systemctl restart kube-proxy); fi"
}
has_k8s_kube_proxy_service = False

with open(reg_service_path, 'r') as f:
    reg_service_list = json.load(f)
    for reg in reg_service_list:
        if reg["res_name"] == "kubelet":
            reg["stop_cmd"] = "service/kubelet_stop_service.sh"
            reg["restore_cmd"] = "service/kubelet_restore_service.sh"
            has_k8s_kubelet_service = True
        elif reg["res_name"] == "daemon-task":
            has_k8s_daemon_task_service = True
        elif reg["res_name"] == "external-apiservice":
            has_k8s_external_apiservice_service = True
        elif reg["res_name"] == "etcd":
            reg["restore_cmd"] = ":"
            has_k8s_etcd_service = True
        elif reg["res_name"] == "kube-apiserver":
            has_k8s_kube_apiserver_service = True
        elif reg["res_name"] == "kube-scheduler":
            has_k8s_kube_scheduler_service = True
        elif reg["res_name"] == "kube-controller-manager":
            has_k8s_kube_controller_manager_service = True
        elif reg["res_name"] == "kube-proxy":
            has_k8s_kube_proxy_service = True

if not has_k8s_kubelet_service:
    reg_service_list.append(k8s_kubelet_service)
if not has_k8s_daemon_task_service:
    reg_service_list.append(k8s_daemon_task_service)
if not has_k8s_external_apiservice_service:
    reg_service_list.append(k8s_external_apiservice_service)
if not has_k8s_etcd_service:
    reg_service_list.append(k8s_etcd_service)
if not has_k8s_kube_apiserver_service:
    reg_service_list.append(k8s_kube_apiserver_service)
if not has_k8s_kube_scheduler_service:
    reg_service_list.append(k8s_kube_scheduler_service)
if not has_k8s_kube_controller_manager_service:
    reg_service_list.append(k8s_kube_controller_manager_service)
if not has_k8s_kube_proxy_service:
    reg_service_list.append(k8s_kube_proxy_service)
with open(reg_service_path, 'w') as f:
    json.dump(reg_service_list, f, indent=4)

# res_rpm.yml
reg_rpm_path = sys.argv[1] + "/res_rpm.yml"
k8s_rpm_res = {
    "res_name": "k8s",
    "compo_owner": "k8s",
    "judge_method": "by_role",
    "optional_component": True,
    "roles": ["minion", "master"],
    "rpm_name": ["files.tar.gz"],
    "clear_cmd": "rpm/k8s_clear_rpm.sh",
    "restore_cmd": "rpm/k8s_restore_rpm.sh"
}
has_k8s_rpm = False

with open(reg_rpm_path, 'r') as f:
    reg_rpm_list = json.load(f)
    for reg in reg_rpm_list:
        if reg["res_name"] == "k8s":
            has_k8s_rpm = True
            break

if not has_k8s_rpm:
    with open(reg_rpm_path, 'w') as f:
        reg_rpm_list.append(k8s_rpm_res)
        json.dump(reg_rpm_list, f, indent=4)
