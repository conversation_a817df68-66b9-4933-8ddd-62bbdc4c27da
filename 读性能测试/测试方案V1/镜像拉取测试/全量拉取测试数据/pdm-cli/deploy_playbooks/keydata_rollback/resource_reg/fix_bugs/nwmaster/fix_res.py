import json
import sys

res_path = sys.argv[1]

op_conf_res_filedir = {
    "res_name": "op-conf",
    "compo_owner": "common",
    "judge_method": "by_role",
    "roles": ["uesdnodes"],
    "inclu_dir": ["/paasdata/op-conf"],
    "exclu_dir": ["/paasdata/op-conf/nwmaster/keydata_rollback"],
    "inclu_big_subdir": []
}

op_data_res_filedir = {
    "res_name": "op-data",
    "compo_owner": "common",
    "judge_method": "by_role",
    "roles": ["usednodes"],
    "inclu_dir": ["/paasdata/op-data"],
    "exclu_dir": ["/paasdata/op-data/nwnode", "/paasdata/op-data/op-nw-multus"],
    "inclu_big_subdir": []
 }

is_overwrite = False
res_filedir_path = res_path + "/res_filedir.yml"
have_res_names = set()

class AddItem:
    def __init__(self,pos,default_config,add_dir_list):
        self.pos = pos
        self.default_config = default_config
        self.add_dir_list = add_dir_list



add_dir_map = {
    "op-conf":AddItem(0,op_conf_res_filedir,["/paasdata/op-conf/nwmaster/keydata_rollback"]),
    "op-data":AddItem(1,op_data_res_filedir,["/paasdata/op-data/nwnode", "/paasdata/op-data/op-nw-multus"])
}



# Started by AICoder, pid:0463308da09247a1a87181fca66ebbbc
with open(res_filedir_path, 'r') as f:
    reg_list = json.load(f)
    for reg in reg_list:
        res_name = reg["res_name"]
        have_res_names.add(res_name)
        if res_name in add_dir_map.keys():
            if "exclu_dir" in reg:
                for add_exclu_dir in add_dir_map[res_name].add_dir_list:
                    if add_exclu_dir not in reg["exclu_dir"]:
                        reg["exclu_dir"].append(add_exclu_dir)
                        is_overwrite = True
            else:
                reg["exclu_dir"] = add_dir_map[res_name].add_dir_list
                is_overwrite = True
# Ended by AICoder, pid:0463308da09247a1a87181fca66ebbbc# This is a sample Python script.


for key,value in add_dir_map.items():
    if key not in have_res_names:
        reg_list.insert(value.pos, value.default_config)
        is_overwrite = True

if is_overwrite:
    with open(res_filedir_path, 'w') as f:
        json.dump(reg_list, f, indent=4)
