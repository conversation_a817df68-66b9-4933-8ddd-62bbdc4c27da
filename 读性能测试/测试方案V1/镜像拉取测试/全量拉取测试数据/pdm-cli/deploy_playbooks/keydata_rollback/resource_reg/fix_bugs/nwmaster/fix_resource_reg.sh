#!/bin/bash

version=$1
version_pre11=${version:0:11}
new_base_dir="/etc/pdm/deploy_playbooks/keydata_rollback"
old_base_dir="/etc/pdm_bak/deploy_playbooks/keydata_rollback"
old_res_dir="$old_base_dir/resource_reg"
fix_bug_nwmaster_hosts="/etc/pdm/fix_bug_nwmaster_hosts"
script_path="$new_base_dir/resource_reg/fix_bugs/nwmaster/fix_res.py"
com_vars_file="/root/common/com_vars.yml"
is_tcf="true"
tcf_scenario=""
CURRENT_DIR=$(dirname "$(realpath "$0")")


function get_is_tcf()
{
    is_tcf=$(grep -w "is_tcf" "$com_vars_file" |awk '{print $3}' | sed 's/\}//g')
}


function get_tcf_scenario()
{
    tcf_scenario=$(grep -w "tcf_scenario" "$com_vars_file" |awk '{print $3}' | sed 's/\}//g')
}


function fix_ume_restart_nwmaster()
{
    if [[ "${version_pre11#*.}" < "23.40.03" ]] || [[ "${version_pre11#*.}" == "23.40.03" ]];then
        for((i=1;i<=5;i++))
        do
            if command -v pythonlatest &> /dev/null; then
                python_cmd="pythonlatest"
            else
                python_cmd="python"
            fi
            $python_cmd generate_hosts.py "paas_controller" "$fix_bug_nwmaster_hosts"
            rc=$?
            if [ $rc -ne 0 ];then
                echo "generate fix_bug_nwmaster_hosts failed, try again latter"
                sleep 1
                continue
            fi
            if [ -z $(sed -n '/\[nodes\]/{n;p}' $fix_bug_nwmaster_hosts) ];then
                echo "no paas_controller role hosts"
                break
            fi
            ansible-playbook -i $fix_bug_nwmaster_hosts fix_bug_nwmaster.yml
            rc=$?
            if [ $rc -ne 0 ];then
                echo "ansible fix_bug_nwmaster failed, try again latter"
                sleep 1
                continue
            fi
            echo "fix_bug_nwmaster successfully"
            break
        done
    fi
}

main()
{
    cd ${CURRENT_DIR} || exit
#    fix reg files
    if command -v pythonlatest &> /dev/null; then
        python_cmd="pythonlatest"
    else
        python_cmd="python"
    fi
    $python_cmd "$script_path" "$old_res_dir"

    get_is_tcf
    get_tcf_scenario
    if [[ "$is_tcf" == "true" && "$tcf_scenario" == "UME-standard" ]] || [[ "$is_tcf" == "false" ]]; then
        fix_ume_restart_nwmaster
    fi
}

main
