# Copyright 2012 OpenStack  Foundation
# All Rights Reserved.
#
#    Licensed under the Apache License, Version 2.0 (the "License"); you may
#    not use this file except in compliance with the License. You may obtain
#    a copy of the License at
#
#         http://www.apache.org/licenses/LICENSE-2.0
#
#    Unless required by applicable law or agreed to in writing, software
#    distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
#    WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
#    License for the specific language governing permissions and limitations
#    under the License.

import httplib2
import os
import yaml
import json
import sys
import time
from IPy import IP


def get_from_comvars(varname, filepath=None):
    if not filepath:
        if os.path.exists('/root/common/port_vars.yml'):
            filepath = "/root/common/port_vars.yml"
        else:
            filepath = "/root/common/com_vars.yml"
    if not os.path.exists(filepath):
        return None
    f = open(filepath)
    dataMap = yaml.safe_load(f)
    f.close()
    for data in dataMap:
        if varname in data:
            return data[varname]
    return None


def get_nodes_info():
    need_get_from_nodeworker = True
    nodes = "/paasdata/op-data/pdm-cli/node_info/nodes"
    file_exist = os.path.exists(nodes)
    if file_exist:
        try:
            with open(nodes, 'r') as f:
                cnt = json.load(f)
                need_get_from_nodeworker = False
        except Exception as e:
            print("exception(%s) happens!" % e)
    if need_get_from_nodeworker:
        header = {'Content-Type': 'application/json'}
        msb_ip = get_from_comvars('openpalette_service_ip',
                                  '/root/common/com_vars.yml')
        msb__port = get_from_comvars('zenap_msb_apigateway_port')
        if IP(msb_ip).version() == 4:
            op_service_url = "%s:%s" % (msb_ip, msb__port)
        else:
            op_service_url = "[%s]:%s" % (msb_ip, msb__port)
        http = httplib2.Http(timeout=60,
                             disable_ssl_certificate_validation=True)
        url = ('http://%s/nodeworker/v1/tenants/admin/nodes?isused=yes' %
               (op_service_url))
        result, nodelist = http.request(url, 'GET',
                                        headers=header)
        if not result:
            return {}
        if isinstance(nodelist, bytes):
            nodelist = nodelist.decode("utf-8")
        cnt = json.loads(nodelist)
    if "nodes" not in cnt:
        return {}
    return cnt


def get_nodes():
    try:
        cnt = get_nodes_info()
        return cnt.get("nodes", [])
    except Exception as ex:
        print("get_nodes except=%s" % ex)
        print("http req to cf-pnode failed.  maybe cf-pnode not running.")
        print("cp /etc/pdm/hosts as all_node_hosts")
        return []


def generate_node_hosts():
    nodes = []
    try:
        nodes = get_nodes()
        result = write_all_nodes_ip(nodes)
        if not result:
            return False
    except Exception as ex:
        print("generate hosts failed! except = %s" % ex)
        return False
    print("generate hosts successfully!")
    return True


def write_all_nodes_ip(nodes):
    hosts = []
    hosts_file = 'all_node_hosts'
    for node in nodes:
        info = {}
        if "netinfo" in node and node["netinfo"].get("net_mgt"):
            info["man_ip"] = node["netinfo"]["net_mgt"]["ip"]
            hosts.append(info)
    if not hosts:
        cmd = ("rm -fr %s && cp %s %s"
               % (hosts_file, '/etc/pdm/hosts', hosts_file))
        res = os.system(cmd)
        if res != 0:
            print('cp /etc/pdm/hosts failed')
            return False
        else:
            print('cp /etc/pdm/hosts ok')
            return True
    fo = open(hosts_file, 'w')
    fo.write("[nodes]\n")
    for node_loop in hosts:
        fo.write(node_loop['man_ip'])
        fo.write("\n")
    fo.flush()
    fo.close()
    return True


def _write_ip_to_file(ips, file_name):
    with open(file_name, "w") as f:
        f.write("[nodes]\n")
        for ip in ips:
            f.write(ip)
            f.write("\n")


def _get_role_nodes_ip(role):
    nodes = []
    for _ in range(5):
        nodes = get_nodes()
        if not nodes:
            time.sleep(3)
            continue
        break
    node_ips = []
    for node in nodes:
        if role in node.get("roles", []):
            node_ips.append(node["netinfo"]["net_api"]["ip"])
    return node_ips


def generate_hosts(role_name, host_file):
    try:
        host_ips = _get_role_nodes_ip(role_name)
        if len(host_ips) == 0:
            print("hosts ip none!")
            return False
        _write_ip_to_file(host_ips, host_file)
        return True
    except Exception as e:
        print("exception happens {}".format(str(e)))
        return False


def main():
    role_name = sys.argv[1]
    host_file = sys.argv[2]
    if generate_hosts(role_name, host_file):
        sys.exit(0)
    else:
        sys.exit(1)


if __name__ == '__main__':
    main()
