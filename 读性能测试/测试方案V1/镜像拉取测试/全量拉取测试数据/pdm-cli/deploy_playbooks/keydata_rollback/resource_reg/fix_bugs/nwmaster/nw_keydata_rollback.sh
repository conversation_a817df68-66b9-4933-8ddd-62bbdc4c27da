#!/bin/bash
#shellcheck disable=SC1083,SC2034,SC2002,SC1037,SC1083,SC1090,SC1097,SC2002,SC2006,SC2007,SC2012,SC2028,SC2034,SC2045,SC2046,SC2050,SC2061,SC2062,SC2086,SC2115,SC2119,SC2120,SC2124,SC2126,SC2154,SC2164,SC2181,SC2219
export LANG=en_US.UTF-8

LOG=/paasdata/op-conf/nwmaster/log_print.sh
DEPLOY_LOG=/paasdata/op-log/nwmaster/nwmaster_deploy.log
current_path=$(readlink -f .)
nw_keydata_rollback_path=/paasdata/op-conf/nwmaster/keydata_rollback
pgctl_tool=/paasdata/op-conf/nwmaster/pgctl-nw
openpalette_service_ip=""
openpalette_service_port=""
keydata_restore_rollback_url=""
input_first_parameter=$1
SCENE=""


get_openpalette_service_ip()
{
    openpalette_service_ip=$(cat /root/common/com_vars.yml | grep openpalette_service_ip | awk -F ': ' '{print $2}' | awk -F '}' '{print $1}')
    if [ -z "$openpalette_service_ip" ];then
        ${LOG} ERROR "$0.$LINENO" $$ "openpalette_service_ip empty, exit 1!"
        exit 1
    fi
    if [[ $openpalette_service_ip =~ "'" ]];then
        openpalette_service_ip=$(echo "$openpalette_service_ip" | awk -F "'" '{print $2}')
    fi
}


get_openpalette_service_port()
{
    openpalette_service_port=$(grep 'openpalette_service_port' /root/common/port_vars.yml | sed -n 's/.*openpalette_service_port:[[:space:]]*\([0-9]*\).*/\1/p')
}


set_keydata_restore_rollback_url()
{
    get_openpalette_service_ip
    get_openpalette_service_port

    if [[ "$openpalette_service_ip" =~ ^[0-9]+\.[0-9]+\.[0-9]+\.[0-9]+$ ]]; then
        keydata_restore_rollback_url="http://${openpalette_service_ip}:${openpalette_service_port}/nw/v1/tenants/admin/restore/keydata_rollback"
    else
        keydata_restore_rollback_url="http://[${openpalette_service_ip}]:${openpalette_service_port}/nw/v1/tenants/admin/restore/keydata_rollback"
    fi

    ${LOG} INFO "$0.$LINENO" $$ "keydata_restore_rollback_url is $keydata_restore_rollback_url"
}


generate_hosts(){
    ${LOG} INFO "$0.$LINENO" $$ "start to generate hosts"
    for(( i=0; i<5; i++ ))
    do
        if command -v pythonlatest &> /dev/null; then
            python_cmd="pythonlatest"
        else
            python_cmd="python"
        fi
        $python_cmd generate_hosts.py
        rc=$?
        if [[ 0 -eq $rc ]]; then
            ${LOG} INFO "$0.$LINENO" $$ "generate hosts successfully!"
            return 0
        fi
        ${LOG} WARNING "$0.$LINENO" $$ "generate hosts failed for the $i times"
        sleep 3s
    done
    ${LOG} ERROR "$0.$LINENO" $$ "generate hosts failed for more than five times, exit 1!"
    exit 1
}

stop_nw_service()
{
    ${LOG} INFO "$0.$LINENO" $$ "start to stop nw service"
    for(( i=0; i<5; i++ ))
    do
        ${LOG} INFO "$0.$LINENO" $$ "all_node_hosts: $(cat all_node_hosts)"
        ansible-playbook -i all_node_hosts -e scene="$SCENE" stop_nw_service.yml >> ${DEPLOY_LOG}
        rc=$?
        if [[ 0 -eq $rc ]]; then
            ${LOG} INFO "$0.$LINENO" $$ "stop nw service successfully!"
            return 0
        fi
        ${LOG} WARNING "$0.$LINENO" $$ "stop nw service failed for the $i times"
        sleep 3s
    done

    ${LOG} ERROR "$0.$LINENO" $$ "stop nw service failed for more than five times, exit 1!"
    exit 1
}

start_nw_service()
{
    ${LOG} INFO "$0.$LINENO" $$ "start to start nw service"
    for(( i=0; i<5; i++ ))
    do
        ${LOG} INFO "$0.$LINENO" $$ "all_node_hosts: $(cat all_node_hosts)"
        ansible-playbook -i all_node_hosts -e scene="$SCENE" start_nw_service.yml >> ${DEPLOY_LOG}
        rc=$?
        if [[ 0 -eq $rc ]]; then
            ${LOG} INFO "$0.$LINENO" $$ "start nw service successfully!"
            return 0
        fi
        ${LOG} WARNING "$0.$LINENO" $$ "start nw service failed for the $i times"
        sleep 3s
    done

    ${LOG} ERROR "$0.$LINENO" $$ "start nw service failed for more than five times, exit 1!"
    exit 1
}

delete_nwmonitor_data_from_pg()
{
    ${LOG} INFO "$0.$LINENO" $$ "start to delete nwmonitor data from postgresql database"
    for(( i=0; i<5; i++ ))
    do
        del_cmd=$(${pgctl_tool} nwmonitor del --prefix=true --key="" >> ${DEPLOY_LOG})\
        rc=$?
        if [[ 0 -eq $rc ]]; then
            ${LOG} INFO "$0.$LINENO" $$ "delete nwmonitor data from pg successfully!"
            return 0
        fi
        ${LOG} WARNING "$0.$LINENO" $$ "delete nwmonitor data from pg failed for the $i times"
        sleep 3s
    done

    ${LOG} ERROR "$0.$LINENO" $$ "delete nwmonitor data from pg failed for more than five times, exit 1!"
    exit 1
}

start_restore_job()
{
    ${LOG} INFO "$0.$LINENO" $$ "start to restore job"
    while true
    do
        curl_code=$(curl -g -s -XPOST ${keydata_restore_rollback_url} -o /dev/null -w %{http_code})
        ${LOG} INFO "$0.$LINENO" $$ "curl POST keydata_restore_rollback status_code: ${curl_code}"

        if [[ "${curl_code}" = "200" ]]; then
            ${LOG} INFO "$0.$LINENO" $$ "POST keydata_restore_rollback status_code 200, notify restore success"
            return 0
        elif [[ "${curl_code}" = "409" ]]; then
            ${LOG} INFO "$0.$LINENO" $$ "POST keydata_restore_rollback status_code 409, restore job is running"
            return 0
        fi

        ${LOG} ERROR "$0.$LINENO" $$ "POST keydata_restore_rollback status_code ${curl_code}, retry POST"
        sleep 3s

    done

    return 0
}

check_restore_job()
{
    ${LOG} INFO "$0.$LINENO" $$ "start to check restore job"
    local restore_status=""

    while true
    do
        curl_code=$(curl -g -s -XGET ${keydata_restore_rollback_url} -o /dev/null -w %{http_code})
        ${LOG} INFO "$0.$LINENO" $$ "curl GET keydata_restore_rollback status_code: ${curl_code}"
        curl_result=$(curl -g -s -XGET ${keydata_restore_rollback_url})
        ${LOG} INFO "$0.$LINENO" $$ "curl GET keydata_restore_rollback result: ${curl_result}"

        if [[ "${curl_code}" -ne "200" ]]; then
            ${LOG} ERROR "$0.$LINENO" $$ "GET keydata_restore_rollback status_code ${curl_code}, retry POST"
            return 1
        fi

        restore_status=$(echo "${curl_result}" | sed 's/.*"status":"//;s/".*//')
        if [[ "${restore_status}" = "success" ]]; then
            ${LOG} INFO "$0.$LINENO" $$ "restore keydata_restore_rollback success"
            return 0
        elif [[ "${restore_status}" = "running" ]]; then
            ${LOG} INFO "$0.$LINENO" $$ "restore keydata_restore_rollback running, retry check"
            sleep 10s
        else
            ${LOG} WARNING "$0.$LINENO" $$ "restore keydata_restore_rollback failed, retry POST"
            return 1
        fi

    done

    return 0
}

restore_keydata_rollback()
{
    ${LOG} INFO "$0.$LINENO" $$ "start to restore keydata rollback"
    while true
    do
        start_restore_job
        check_restore_job
        rc=$?
        if [[ "$rc" -eq 0 ]];then
            ${LOG} INFO "$0.$LINENO" $$ "restore_keydata_rollback success"
            return 0
        fi
        ${LOG} ERROR "$0.$LINENO" $$ "restore_keydata_rollback failed, retry restore job"
        sleep 3s
    done
    return 0
}

check_state(){
    local resource_name=$1
    local expect=$2
    local keywords
    local state

    if [ "${expect}" == "enable" ]; then
        keywords="is running"
    else
        keywords="is NOT running"
    fi

    for i in $(seq 1 60); do
        state=$(crm_resource -r "${resource_name}" -W 2>&1)
        if [[ "${state}" == *"${keywords}"* ]]; then
            return 0
        fi
        sleep 3
    done
    echo "${state}"
    return 1
}

restart_resource(){
    local resource_name=$1
    for i in $(seq 1 10); do
        ${LOG} INFO "$0.$LINENO" $$ "crm_resource ${resource_name} restart start"
        crm_resource -r ${resource_name} --restart -T 300s >> ${DEPLOY_LOG} 2>&1
        rc=$?
        if [ ${rc} -ne 0 ]; then
            ${LOG} ERROR "$0.$LINENO" $$ "crm_resource ${resource_name} restart failed, rc=${rc}"
            sleep 5
            continue
        else
            ${LOG} INFO "$0.$LINENO" $$ "crm_resource ${resource_name} restart success"
            return 0
        fi
    done
    exit 1
}

restart_nwmaster()
{
    ${LOG} INFO "$0.$LINENO" $$ "restart nwmaster start"
    restart_resource nwmaster
}

main()
{
    ${LOG} INFO "$0.$LINENO" $$ "********* nw keydata rollback start *********"
    cd ${nw_keydata_rollback_path} || exit
    if [[ "${input_first_parameter}" == "br" ]]; then
        SCENE="br"
    else
        SCENE=""
    fi
    set_keydata_restore_rollback_url
    generate_hosts
    stop_nw_service
    delete_nwmonitor_data_from_pg
    restore_keydata_rollback
    start_nw_service
    restart_nwmaster
    cd "${current_path}" || exit

    ${LOG} INFO "$0.$LINENO" $$ "********* nw keydata rollback end ***********"
}

main
