#!/bin/bash

version=$1
new_base_dir="/etc/pdm/deploy_playbooks/keydata_rollback"
old_base_dir="/etc/pdm_bak/deploy_playbooks/keydata_rollback"
old_res="$old_base_dir/resource_reg"

if [[ "${version#*.}" < "22.20.05" ]];then
    res_path="$old_res/res_filedir.yml"
    script_path="$new_base_dir/resource_reg/fix_bugs/oki-cli/fix_oki-cli_filedir_res.py"
    if command -v pythonlatest &> /dev/null; then
        python_cmd="pythonlatest"
    else
        python_cmd="python"
    fi
    $python_cmd "$script_path" "$res_path"
fi