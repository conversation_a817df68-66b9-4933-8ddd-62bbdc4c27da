#!/bin/bash
version=$1
echo "$version"

if [[ $(command -v jq-linux64) ]]; then
    jq_cmd=jq-linux64
else
    jq_cmd=jq
fi

new_base_dir="/etc/pdm/deploy_playbooks/keydata_rollback"
scriptPath="$new_base_dir/resource_reg/fix_bugs/op-asd-swr"
chmod +x $scriptPath/import_art_record_to_swr.sh

for i in {1..3}
do
  $scriptPath/import_art_record_to_swr.sh $jq_cmd $scriptPath
  result=$?
  if [[ "${result}" -eq 0 ]]; then
    echo "zart fix_resource_reg success"
    break
  fi
  if [[ "$i" -eq 3 ]]; then
    echo "zart fix_resource_reg fail"
    exit 1
  fi
  sleep 5
done
