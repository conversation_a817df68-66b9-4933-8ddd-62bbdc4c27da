#!/bin/bash -e

jq_cmd=$1
scriptPath=$2

dbip=$(grep -w 'postgresql_ip:' /root/common/com_vars.yml|awk '{print $3}'|tr -d "'}")
msbip=$(grep -w openpalette_service_ip /root/common/com_vars.yml|awk '{print $3}'|tr -d "'}")
dbport=$(grep -w 'postgresql_port:' /root/common/port_vars.yml|awk '{print $7}'|tr -d ',')
msbport=$(grep -w openpalette_service_port /root/common/port_vars.yml|awk '{print $7}'|tr -d ',')
chmod +x $scriptPath/ImportArtRecordToSwr

semver_pattern="^(0|[1-9][0-9]*)\.(0|[1-9][0-9]*)\.(0|[1-9][0-9]*)(\-[0-9A-Za-z-]+(\.[0-9A-Za-z-]+)*)?(\+[0-9A-Za-z-]+(\.[0-9A-Za-z-]+)*)?$"

custom_pattern="^[0-9][a-zA-Z0-9_.-]{0,62}[a-zA-Z0-9]$"
pattern="\[.*\]"
registry_url="https://swr:2512"

repositories=$(curl --connect-timeout 30 -m 30 -sk "${registry_url}/v2/_catalog?n=1000000" | $jq_cmd -r '.repositories[]')

for repo in $repositories; do
  tagList=$(curl --connect-timeout 3 -m 3 -sk "${registry_url}/v2/${repo}/tags/list" | $jq_cmd -r '.tags')
  tags=""
  if [[ $tagList =~ $pattern ]]; then
    tags=($(echo $tagList | $jq_cmd -r '.[]'))
  fi
  for tag in $tags; do
    mediaType=$(curl --connect-timeout 3 -m 3 -sk -H "Accept: application/vnd.oci.image.manifest.v1+json" "${registry_url}/v2/${repo}/manifests/${tag}" | $jq_cmd -r '.config.mediaType' )
    if [ ${mediaType} = "application/vnd.cncf.helm.config.v1+json" ];then
       if [[ $tag =~ $semver_pattern ]] && [[ $tag =~ $custom_pattern ]]; then
           echo "Repository: $repo version: $tag no need deal...."
       else
           size=$(curl --connect-timeout 3 -m 3 -sk -H "Accept: application/vnd.oci.image.manifest.v1+json" "${registry_url}/v2/${repo}/manifests/${tag}" | $jq_cmd '.config.size + .layers[].size' )
           echo " $repo $tag insert to swr db... size $size"
           $scriptPath/ImportArtRecordToSwr -dbip="${dbip}" -dbport="${dbport}" -msbip="${msbip}" -msbport="${msbport}" -artinfo="$repo,$tag,$size"   
       fi
    fi
  done
done

echo "import art record end"
