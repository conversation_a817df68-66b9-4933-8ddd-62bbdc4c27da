#!/bin/bash

version=$1
echo "fix version is $version"

new_base_dir="/etc/pdm/deploy_playbooks/keydata_rollback"
old_base_dir="/etc/pdm_bak/deploy_playbooks/keydata_rollback"
old_res="$old_base_dir/resource_reg"

if command -v pythonlatest &> /dev/null; then
    python_cmd="pythonlatest"
else
    python_cmd="python"
fi

if [[ "${version#*.}" < "21.30.05" ]] && [[ "${version#*.}" > "20.30.01" ]]; then
    cp -rf "$new_base_dir"/resource_reg/fix_bugs/op-containers-containerd/rpm/* "$old_base_dir"/resource_reg/rpm/
    cp -rf "$new_base_dir"/resource_reg/fix_bugs/op-containers-containerd/service/* "$old_base_dir"/resource_reg/service/

    res_filedir_path="$old_res/res_filedir.yml"
    script_path="$new_base_dir/resource_reg/fix_bugs/op-containers-containerd/fix_filedir_res.py"
    "$python_cmd" "$script_path" "$res_filedir_path"

    res_rpm_path="$old_res/res_rpm.yml"
    script_path="$new_base_dir/resource_reg/fix_bugs/op-containers-containerd/fix_rpm_res.py"
    "$python_cmd" "$script_path" "$res_rpm_path"

    res_service_path="$old_res/res_service.yml"
    script_path="$new_base_dir/resource_reg/fix_bugs/op-containers-containerd/fix_service_res.py"
    "$python_cmd" "$script_path" "$res_service_path"
fi

# 合并条件
if { [[ "${version}" > "v7.24.20.01.17495150" ]] && [[ "${version}" < "v7.24.20.04.18091133" ]]; } || \
   { [[ "${version}" > "v7.24.10.06.17611199" ]] && [[ "${version}" < "v7.24.10.06.f20.18125120" ]]; } || \
   { [[ "${version}" > "v7.24.20.00.17633017" ]] && [[ "${version}" < "v7.24.20.04.18091133" ]]; } || \
   { [[ "${version}" > "v7.22.30.11.f35.17622841" ]] && [[ "${version}" < "v7.22.30.11.f37.18125203" ]]; } || \
   { [[ "${version}" > "v7.23.30.06.f20p01.17623915" ]] && [[ "${version}" < "v7.23.30.06.f20p02.18125160" ]]; }
then
    cp -rf "$new_base_dir"/resource_reg/fix_bugs/op-containers-containerd/rpm_nerdctl_20240531/* "$old_base_dir"/resource_reg/rpm/
    res_rpm_path="$old_res/res_rpm.yml"
    script_path="$new_base_dir/resource_reg/fix_bugs/op-containers-containerd/fix_rpm_res.py"
    "$python_cmd" "$script_path" "$res_rpm_path"
fi

if [[ "${version%.*}" == "v7.24.30.00" || "${version%.*}" > "v7.24.30.00" ]] && [[ "${version%.*}" == "v7.24.30.02" || "${version%.*}" < "v7.24.30.02" ]]; then
    cp -rf "$new_base_dir"/resource_reg/fix_bugs/op-containers-containerd/rpm_imagecheck_20240712/* "$old_base_dir"/resource_reg/rpm/

    res_rpm_path="$old_res/res_rpm.yml"
    script_path="$new_base_dir/resource_reg/fix_bugs/op-containers-containerd/fix_rpm_res.py"
    "$python_cmd" "$script_path" "$res_rpm_path"
fi