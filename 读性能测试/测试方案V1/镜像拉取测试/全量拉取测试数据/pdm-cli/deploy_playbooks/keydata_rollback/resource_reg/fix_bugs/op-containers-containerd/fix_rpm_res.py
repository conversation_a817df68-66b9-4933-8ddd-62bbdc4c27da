import json
import sys

reg_path = sys.argv[1]
containerd_res = {
    "res_name": "op-containers-containerd",
    "compo_owner": "op-containers-containerd",
    "judge_method": "by_role",
    "optional_component": False,
    "roles": ["usednodes"],
    "rpm_name": ["rpms.tar.gz"],
    "clear_cmd": "rpm/op_containers_containerd_clear_rpm.sh",
    "restore_cmd": "rpm/op_containers_containerd_restore_rpm.sh"
}
has_containerd = False

with open(reg_path, 'r') as f:
    reg_list = json.load(f)
    for reg in reg_list:
        if reg["res_name"] == "op-containers-containerd":
            has_containerd = True
            break

if not has_containerd:
    with open(reg_path, 'w') as f:
        reg_list.append(containerd_res)
        json.dump(reg_list, f, indent=4)
