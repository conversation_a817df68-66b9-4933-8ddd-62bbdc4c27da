#!/usr/bin/bash

containerd_rpm="containerd.io"

function uninstall_containerd() {
    yum erase -y ${containerd_rpm}
    rc=${?}
    if [ ${rc} -ne 0 ]; then
        echo "Uninstall containerd failed!"
        exit 1
    fi
    echo "Uninstall containerd successfully!"
}

function main() {
    rpm -qa | grep ${containerd_rpm}
    local rc=${?}
    if [ ${rc} -eq 0 ]; then
        uninstall_containerd
    fi
}

main
