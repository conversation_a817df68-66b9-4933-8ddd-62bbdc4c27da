#!/usr/bin/bash
# shellcheck disable=SC2010

restore_dir="/paasdata/op-data/paas_upgrade_backup_keydata/rpm/op-containers-containerd"
gc_key="zncgsl6"

function decompress_containerd() {
    cd ${restore_dir} || exit 1
    tar -zxvf ./rpms.tar.gz
    rc=${?}
    if [ "${rc}" -ne 0 ]; then
         echo "decompress containerd rpms failed!"
         exit 1
    else
         echo "decompress containerd rpms successfully!"
    fi
}

function get_os_version() {
    os_kernel=$(uname -r)
    if [[ "$os_kernel" =~ ${gc_key} ]]; then
        os_gc="$gc_key"
    else
        os_gc=""
    fi
}

function install_containerd() {
    get_os_version
    if [ "$os_gc" = "$gc_key" ]; then
        rpm_containerd=$(ls "$restore_dir" | grep "containerd.*.rpm" |grep "$gc_key")
    else
        rpm_containerd=$(ls "$restore_dir" | grep "containerd.*.rpm" |grep -v "$gc_key")
    fi

    rpm -ivh "${rpm_containerd}" --force
    rc=${?}
    if [ "${rc}" -ne 0 ]; then
         echo "Install containerd failed!"
         exit 1
    else
         echo "Install containerd successfully!"
    fi
}

function main() {
	decompress_containerd
	install_containerd
}

main
