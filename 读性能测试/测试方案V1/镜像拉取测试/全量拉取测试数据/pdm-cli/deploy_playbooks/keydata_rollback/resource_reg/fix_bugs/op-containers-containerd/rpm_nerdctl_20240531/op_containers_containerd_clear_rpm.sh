#!/usr/bin/bash

containerd_rpm="containerd.io"
nerdctl_rpm="nerdctl"

function uninstall_containerd() {
    yum erase -y ${containerd_rpm}
    rc=${?}
    if [ ${rc} -ne 0 ]; then
        echo "Uninstall containerd failed!"
        exit 1
    fi
    echo "Uninstall containerd successfully!"
}

function uninstall_nerdctl() {
    yum erase -y ${nerdctl_rpm}
    rc=${?}
    if [ ${rc} -ne 0 ]; then
        echo "Uninstall nerdctl failed!"
        exit 1
    fi
    echo "Uninstall nerdctl successfully!"

}

function main() {
    rpm -qa | grep ${containerd_rpm}
    local rc=${?}
    if [ ${rc} -eq 0 ]; then
        uninstall_containerd
    fi
    rpm -qa | grep ${nerdctl_rpm}
    local rc=${?}
    if [ ${rc} -eq 0 ]; then
        uninstall_nerdctl
    fi
}

main
