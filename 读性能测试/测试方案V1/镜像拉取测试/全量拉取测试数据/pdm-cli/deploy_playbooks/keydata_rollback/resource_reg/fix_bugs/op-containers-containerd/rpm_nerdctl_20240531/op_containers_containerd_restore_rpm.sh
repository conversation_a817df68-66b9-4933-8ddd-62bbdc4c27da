#!/usr/bin/bash

restore_dir="/paasdata/op-data/paas_upgrade_backup_keydata/rpm/op-containers-containerd"
gc_key="zncgsl6"
os_version=centos8
os_kylin=""
os_gc=""

function decompress_containerd() {
    cd ${restore_dir} || exit 1
    tar -zxvf ./rpms.tar.gz
    rc=${?}
    if [ "${rc}" -ne 0 ]; then
         echo "decompress containerd rpms failed!"
         exit 1
    else
         echo "decompress containerd rpms successfully!"
    fi
}

function get_os_version() {
    if [[ -e /etc/cgsl-release ]];then
        if sudo cat /etc/os-release | grep -w "^VERSION_ID" |grep "6.06";then
            os_gc="$gc_key"
        elif sudo cat /etc/os-release | grep -w "^VERSION_ID" |grep "5.04";then
            os_version=centos7
        fi
    else
        if sudo uname -r | grep "^3\.10";then
            os_version=centos7
        elif sudo uname -r |grep "^4\.19";then
            os_kylin=ky10
        elif sudo uname -r | grep "^5\.10";then
            os_gc="$gc_key"
        fi
    fi
}

function get_containerd_target_version() {
    if [ "$os_gc" = "$gc_key" ]; then
        rpm_containerd=$(ls "$restore_dir" | grep "containerd.*.rpm" |grep "$gc_key")
        return
    fi
    if [ "$os_kylin" = ky10 ]; then
        rpm_containerd=$(ls "$restore_dir" | grep "containerd.*.rpm" |grep "\.el8" |grep "ky10")
        return
    fi
    if [ "$os_version" = centos8 ]; then
        rpm_containerd=$(ls "$restore_dir" | grep "containerd.*.rpm" |grep "\.el8" |grep -v "ky10")
        if [ -z "$rpm_containerd" ]; then 
            rpm_containerd=$(ls "$restore_dir" | grep "containerd.*.rpm" |grep "\.cgsl6_2")
        fi
        return
    fi
    rpm_containerd=$(ls "$restore_dir" | grep "containerd.*.rpm" |grep "\.el7" |grep -v "ky10")
}

function install_containerd() {
    get_containerd_target_version

    rpm -ivh "$restore_dir"/"$rpm_containerd" --force
    rc=${?}
    if [ "${rc}" -ne 0 ]; then
         echo "Install containerd failed!"
         exit 1
    else
         echo "Install containerd successfully!"
    fi
}

function get_nerdctl_target_version() {
    if [ "$os_gc" = "$gc_key" ]; then
        rpm_nerdctl=$(ls "$restore_dir" | grep "nerdctl-.*.rpm" |grep "$gc_key")
        return
    fi
    if [ "$os_kylin" = ky10 ]; then
        rpm_nerdctl=$(ls "$restore_dir" | grep "nerdctl-.*.rpm" |grep "\-el8" |grep "ky10")
        return
    fi
    if [ "$os_version" = centos8 ]; then
        rpm_nerdctl=$(ls "$restore_dir" | grep "nerdctl-.*.rpm" |grep "\-el8" |grep -v "ky10")
        if [ -z "$rpm_nerdctl" ]; then
            rpm_nerdctl=$(ls "$restore_dir" | grep "nerdctl-.*.rpm" |grep "\.cgsl6_2")
        fi
        return
    fi
    rpm_nerdctl=$(ls "$restore_dir" | grep "nerdctl-.*.rpm" |grep "\-el7" |grep -v "ky10")
}

function install_nerdctl() {
    get_nerdctl_target_version

    rpm -ivh "$restore_dir"/"$rpm_nerdctl" --force
    rc=${?}
    if [ "${rc}" -ne 0 ]; then
         echo "Install nerdctl failed!"
         exit 1
    else
         echo "Install nerdctl successfully!"
    fi
}

function main() {
    decompress_containerd
    get_os_version
    install_containerd
    install_nerdctl
}

main
