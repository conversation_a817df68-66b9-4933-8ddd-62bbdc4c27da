#!/bin/bash
# shellcheck disable=SC2009

if [[ $(systemctl is-enabled containerd.service 2>/dev/null) == "enabled" ]];then
    systemctl disable containerd.service
    if ! timeout 300 systemctl stop containerd.service; then
        echo "systemctl stop containerd failed"
        timeout 300 kill -9 "$(ps -ef | grep -v grep | grep '/usr/bin/containerd' | awk '{print $2}')"
        echo "forced to killed"
    fi
fi
