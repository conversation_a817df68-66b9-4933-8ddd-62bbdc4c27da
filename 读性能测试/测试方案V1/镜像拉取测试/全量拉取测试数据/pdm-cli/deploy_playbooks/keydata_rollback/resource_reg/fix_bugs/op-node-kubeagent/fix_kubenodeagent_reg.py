import json
import sys


res_filedir_path = sys.argv[1]
res_service_path = sys.argv[2]

res_service = {
  "res_name": "op-node-kubeagent",
  "compo_owner": "op-node-kubeagent",
  "judge_method": "by_role",
  "roles": ["usednodes"],
  "stop_cmd": "if [[ $(systemctl is-enabled kubeNodeAgent.service " +
               "2>/dev/null) == \"enabled\" ]]; then systemctl stop " +
               "kubeNodeAgent.service; fi",
  "restore_cmd": "if [[ $(systemctl is-enabled kubeNodeAgent.service " +
                "2>/dev/null) == \"enabled\" ]]; then systemctl restart " +
                "kubeNodeAgent.service; fi"
}

res_file = {
  "res_name": "op-node-kubeagent_file",
  "compo_owner": "op-node-kubeagent",
  "judge_method": "by_role",
  "roles": ["usednodes"],
  "inclu_dir": ["/usr/lib/systemd/system/kubeNodeAgent.service",
                "/etc/init.d/kubeNodeAgent"],
  "exclu_dir": [],
  "inclu_big_subdir": []
}


def has_res_in_file(file_name, res_name):
    try:
        with open(file_name, 'r') as f:
            reg_list = json.load(f)
            for reg in reg_list:
                if reg["res_name"] == res_name:
                    return True
        return False
    except IOError:
        print("Error: Unable to read {}.".format(file_name))
        raise


def add_res_to_file(file_name, res):
    try:
        with open(file_name, 'r+') as f:
            reg_list = json.load(f)
            reg_list.append(res)
            f.seek(0)
            json.dump(reg_list, f, indent=4)
            f.truncate()
    except IOError:
        print("Error: Unable to write {}.".format(file_name))
        raise


def has_wrong_res_in_file(file_name, res_name, judge_method):
    try:
        with open(file_name, 'r') as f:
            reg_list = json.load(f)
            for reg in reg_list:
                if reg["res_name"] == res_name and reg["judge_method"] == judge_method:
                    return True
        return False
    except IOError:
        print("Error: Unable to read {}.".format(file_name))
        raise


def del_res_from_file(file_name, res_name, judge_method):
    try:
        with open(file_name, 'r+') as f:
            reg_list = json.load(f)
            for index, reg in enumerate(reg_list):
                if reg['res_name'] == res_name and reg["judge_method"] == judge_method:
                    del reg_list[index]
                    break
            f.seek(0)
            json.dump(reg_list, f, indent=4)
            f.truncate()
    except IOError:
        print("Error: Unable to write {}.".format(file_name))
        raise


def main():
    try:
        if has_wrong_res_in_file(res_filedir_path, "op-node-kubeagent_file", "by_component"):
            del_res_from_file(res_filedir_path, "op-node-kubeagent_file", "by_component")
        if has_wrong_res_in_file(res_service_path, "op-node-kubeagent", "by_component"):
            del_res_from_file(res_service_path, "op-node-kubeagent", "by_component")
        if not has_res_in_file(res_filedir_path, "op-node-kubeagent_file"):
            add_res_to_file(res_filedir_path, res_file)
        if not has_res_in_file(res_service_path, "op-node-kubeagent"):
            add_res_to_file(res_service_path, res_service)
    except Exception:
        print("fix op-node-kubeagent failed.")
        sys.exit(1)


if __name__ == "__main__":
    main()
