#!/bin/bash

version=$1
version_pre8=${version:0:8}
version_pre14=${version:0:14}
new_base_dir="/etc/pdm/deploy_playbooks/keydata_rollback"
old_base_dir="/etc/pdm_bak/deploy_playbooks/keydata_rollback"
fix_script="$new_base_dir/resource_reg/fix_bugs/op-node-kubeagent/fix_kubenodeagent_reg.py"
res_filedir="$old_base_dir/resource_reg/res_filedir.yml"
res_service="$old_base_dir/resource_reg/res_service.yml"

function need_fix_bug()
{
    if [[ ! -f /usr/lib/systemd/system/kubeNodeAgent.service ]];then
        return 1
    fi
    if [[ "${version_pre14#*.}" == "22.30.11.f2" ]];then
        return 0
    fi
    if [[ "${version_pre14#*.}" == "22.30.11.f3" ]];then
        return 0
    fi
    if [[ "${version_pre8#*.}" == "23.30" ]] && [[ "${version#*.}" < "23.30.06.f20p02" ]];then
        return 0
    fi
    return 1
}

if ! need_fix_bug;then
    exit 0
fi

if command -v pythonlatest &> /dev/null; then
    pythoncmd="pythonlatest"
else
    pythoncmd="python"
fi

if ! "$pythoncmd" "$fix_script" "$res_filedir" "$res_service";then
    echo "fix op-node-kubeagent failed."
    exit 1
fi

