#!/bin/bash

version=$1
echo "fix version is $version"

if [[ "${version#*.}" < "20.30.03" ]] ; then
    cd "$(dirname "$0")" || exit 1
    if command -v pythonlatest &> /dev/null; then
        pythonlatest generate_hosts.py
    else
        python generate_hosts.py
    fi
    rc=$?
    if [ $rc -ne 0 ]; then
        echo "generate hosts failed! exit!"
        exit 1
    fi
    ansible-playbook -i all_node_hosts fix_nfs_umount_script_20.06.p02.yml
    rc=$?
    rm -f all_node_hosts
    if [ $rc -ne 0 ];then
        echo "fix nfs_umount script failed"
        exit $rc
    fi
    echo "fix nfs_umount script successfully"
    exit 0
fi
