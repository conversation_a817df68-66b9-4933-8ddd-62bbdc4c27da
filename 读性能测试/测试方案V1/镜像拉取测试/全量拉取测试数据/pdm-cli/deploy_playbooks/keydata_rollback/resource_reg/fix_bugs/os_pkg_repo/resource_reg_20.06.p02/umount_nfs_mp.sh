#!/bin/bash

if [ -x "/usr/bin/timeout" ]; then
   TIMEOUT="/usr/bin/timeout 60"
else
   TIMEOUT=""
fi

function logs() {
   LOG_DATE=$(date "+%Y-%m-%d %H:%M:%S")
   LOG_FILENAME="/var/log/umount_nfs_mp.log"
   PID=$(printf "%05d" $$)
   echo "$LOG_DATE [$PID] - [$1] $2" >> $LOG_FILENAME
   access=$(stat -c %a $LOG_FILENAME)
   if [ "$access" != "640" ]; then
      chmod 640 $LOG_FILENAME
   fi
}

function umount_mountpoint()
{
   local mp=$1
   local umount_info=""
   logs info "umount $mp start"
   umount_info=$($TIMEOUT umount -lf "$mp" 2>&1)
   local rc=$?
   if [ $rc -eq 0 ];then
      logs info "umount ${mp} successfully [rc=${rc}] ${umount_info}"
   else
      logs error "umount ${mp} failed [rc=${rc}] ${umount_info}"
   fi
}

function get_nfs_mountpoints()
{
   nfs_mount_info=$($TIMEOUT nfsstat -m |grep -w "from")
   logs info "nfs_mount_info [ ${nfs_mount_info} ]"
   echo "${nfs_mount_info}" |awk '{print $1}'
}

function umount_local_nfs_mountpoints()
{
   local umount_rst=0
   local mount_points=""
   local try_umount_times=3
   for try_time in $(seq 1 $try_umount_times)
   do
      mount_points=$(get_nfs_mountpoints)
      for mp in $mount_points
      do
        umount_mountpoint "$mp" >/dev/null 2>&1 &
      done
      wait
      mount_points=$(get_nfs_mountpoints)
      if [ -z "$mount_points" ];then
        logs info "all nfs mountpoints is umounted"
        umount_rst=0
        break
      fi
      if [ "$try_time" -eq "$try_umount_times" ];then
        logs error "umount nfs mountpoints failed, not umounted points [${mount_points}]"
        umount_rst=1
        break
      fi
      sleep 5
   done
   return $umount_rst
}

umount_local_nfs_mountpoints
