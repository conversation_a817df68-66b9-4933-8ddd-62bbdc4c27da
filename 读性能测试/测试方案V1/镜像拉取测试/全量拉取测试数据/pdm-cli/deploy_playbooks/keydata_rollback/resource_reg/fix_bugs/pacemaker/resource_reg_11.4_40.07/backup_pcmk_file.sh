#!/bin/bash
function backup(){
    rm -rf "$BAKDIR"
    mkdir "$BAKDIR"
    cp -a /usr/lib/systemd/system/pacemaker.service "$BAKDIR"/
    cp -a /usr/lib/systemd/system/corosync.service "$BAKDIR"/
    cp -a /usr/bin/startPacemakerPre "$BAKDIR"/
    cp -a /usr/bin/startCorosyncPre "$BAKDIR"/
    cp -a /etc/corosync/corosync.conf "$BAKDIR"/
    # EC-WX:614007991946
    #cp -a /etc/hostname "$BAKDIR"/
    #cp -a /etc/hosts "$BAKDIR"/
    cp -a -r /usr/lib/ocf "$BAKDIR"/
    cp -a -r /var/lib/pacemaker "$BAKDIR"/
    cp -a -r /usr/share/my_modules "$BAKDIR"/
    cp -a /usr/local/bin/*.sh "$BAKDIR"/
}

BAKDIR="$1"
COMP_NAME="pacemaker_cluster"
LOG_PATH="/paasdata/op-log/${COMP_NAME}"
LOG_FILENAME="${LOG_PATH}/pcmk_external.log"

if [ ! -d "$LOG_PATH" ]; then
    mkdir -p $LOG_PATH
fi

log_info(){
    LOG_DATE=$(date "+%Y-%m-%d %H:%M:%S")
    PID_INFO=$$
    SCRIPT_NAME=${0##*/}
    echo "$LOG_DATE [$PID_INFO] [$SCRIPT_NAME] ${FUNCNAME[*]} - $1" >> "$LOG_FILENAME"
    local access
    access=$(stat -c %a "$LOG_FILENAME")
    if [ "$access" != "640" ]; then
        chmod 640 "$LOG_FILENAME"
    fi
}

log_info "backup file starting"
backup
log_info "backup file ok"
exit 0
