#!/bin/bash

COMP_NAME="pacemaker_cluster"
LOG_PATH="/paasdata/op-log/${COMP_NAME}"
LOG_FILENAME="${LOG_PATH}/pcmk_external.log"

if [ ! -d "$LOG_PATH" ]; then
    mkdir -p $LOG_PATH
fi

log_info(){
    LOG_DATE=$(date "+%Y-%m-%d %H:%M:%S")
    PID_INFO=$$
    SCRIPT_NAME=${0##*/}
    echo "$LOG_DATE [$PID_INFO] [$SCRIPT_NAME] ${FUNCNAME[*]} - $1" >> "$LOG_FILENAME"
    local access
    access=$(stat -c %a "$LOG_FILENAME")
    if [ "$access" != "640" ]; then
        chmod 640 "$LOG_FILENAME"
    fi
}

timeoutFunc()
{
    waitfor=$1
    command=$2
    $command &
    commandpid=$!
    (sleep "$waitfor" ; kill -9 "$commandpid"  >/dev/null 2>&1)&
    watchdog=$!
    log_info "waiting for $commandpid exit..."
    wait "$commandpid" >/dev/null 2>&1
    rc=$?
    log_info "$commandpid exit: $rc"
    kill "$watchdog" >/dev/null 2>&1
    return $rc
}


run_func(){
    time_start=$(date +%s)
    time_end=$time_start
    while [[ $time_end -lt $(( time_start + 600)) ]]; do
        restorePcmk
        # shellcheck disable=SC2181
        if [[ $? -eq 0 ]]; then
            return 0
        fi
        sleep 3
        time_end=$(date +%s)
    done
    test_log info "Error: run timeout 600s"
    return 1
}


function stopService(){
    log_info "stopping corosync..."
    systemctl daemon-reload
    #service pacemaker stop
    service corosync stop
    log_info "stopping corosync ok"
}

function startServiceOk(){
    log_info "waiting pacemaker service ok ..."
    while true; do
        crm status >/dev/null 2>&1
        # shellcheck disable=SC2181
        if [[ $? -eq 0 ]]; then
            break
        fi
        sleep 3
    done
    log_info "pacemaker service ok"
}

function startService(){
    log_info "starting pacemaker service ..."
    systemctl daemon-reload
    service corosync start
    service pacemaker start
    startServiceOk
}

function waitForOnline(){
    o1=$(crm_node -l|wc -l)
    (( o2 = o1 + 1 ))
    log_info "waiting cluster $o1 node online ..."
    # shellcheck disable=SC2126
    while [[ $o2 -ne $(crm status | grep Online | cut -d '[' -f 2 | cut -d ']' -f 1 | grep -o ' ' | wc -l) ]]; do
        sleep 2
    done
    log_info "cluster $o1 node online"
}

restorePcmk(){
    stopService
    startService
    waitForOnline
}

function restoreFile(){
    if [[ -d "/var/lib/pacemaker" ]]; then
        rm -rf /var/lib/pacemaker
    fi
    cp -a -r "$FILEDIR"/pacemaker /var/lib/
    cp -a "$FILEDIR"/pacemaker.service /usr/lib/systemd/system/
    cp -a "$FILEDIR"/corosync.service /usr/lib/systemd/system/
    # modify corosync.service support corosync version after 2.4.5
    sed -i 's/ExecStart=\/usr\/share\/corosync/ExecStart=\/usr\/sbin/g' /usr/lib/systemd/system/corosync.service
    sed -i 's/ExecStop=\/usr\/share\/corosync/ExecStop=\/usr\/sbin/g' /usr/lib/systemd/system/corosync.service
    cp -a "$FILEDIR"/startPacemakerPre /usr/bin/
    cp -a "$FILEDIR"/startCorosyncPre /usr/bin/
    cp -a "$FILEDIR"/corosync.conf /etc/corosync/
    # EC-WX:614007991946
    #cp -a "$FILEDIR"/hostname /etc/
    #cp -a "$FILEDIR"/hosts /etc/
    cp -a -r "$FILEDIR"/ocf /usr/lib/
    cp -a -r "$FILEDIR"/my_modules /usr/share/
    # 使用当前版本的restore_pcmk.sh脚本，而不是备份
    cp -a /usr/local/bin/restore_pcmk.sh "$FILEDIR"/
    cp -a "$FILEDIR"/*.sh /usr/local/bin/
}

FILEDIR=$1
log_info "restore pcmk file starting"
restoreFile
log_info "restore pcmk file ok"
log_info "sleep 30s..."
sleep 30

log_info "restore pcmk starting"
# run_func >/dev/null 2>&1
timeoutFunc 600 restorePcmk >/dev/null 2>&1
# shellcheck disable=SC2181
if [[ $? -ne 0 ]]; then
    log_info "restore pcmk fail"
    exit 1
fi
log_info "restore pcmk ok"
exit 0
