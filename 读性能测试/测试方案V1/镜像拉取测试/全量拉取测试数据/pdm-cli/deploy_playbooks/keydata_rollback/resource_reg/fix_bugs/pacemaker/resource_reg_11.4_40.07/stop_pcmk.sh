#!/bin/bash
################ Version Info #####################
# Create Date: 2019-10-29
# Version:     1.0
# Attention:   stop ha(pacemaker/corosync) service
###################################################

COMP_NAME="pacemaker_cluster"
LOG_PATH="/paasdata/op-log/${COMP_NAME}"
LOG_FILENAME="${LOG_PATH}/pcmk_external.log"

if [ ! -d "$LOG_PATH" ]; then
    mkdir -p $LOG_PATH
fi

log_info(){
    LOG_DATE=$(date "+%Y-%m-%d %H:%M:%S")
    PID_INFO=$$
    SCRIPT_NAME=${0##*/}
    echo "$LOG_DATE [$PID_INFO] [$SCRIPT_NAME] ${FUNCNAME[*]} - $1" >> "$LOG_FILENAME"
    local access
    access=$(stat -c %a "$LOG_FILENAME")
    if [ "$access" != "640" ]; then
        chmod 640 "$LOG_FILENAME"
    fi
}

timeoutFunc()
{
    waitfor=$1
    command=$2
    $command &
    commandpid=$!
    (sleep "$waitfor" ; kill -9 "$commandpid"  >/dev/null 2>&1)&
    watchdog=$!
    log_info "waiting for $commandpid exit..."
    wait "$commandpid" >/dev/null 2>&1
    rc=$?
    log_info "$commandpid exit: $rc"
    kill "$watchdog" >/dev/null 2>&1
    return $rc
}

function stop_service(){
    systemctl daemon-reload
    log_info "stop pacemaker starting..."
    service pacemaker stop
    log_info "waiting pacemaker stop ok..."
    stop_pacemaker_service_ok
    log_info "stop pacemaker ok"
    log_info "stop corosync starting..."
    service corosync stop
    log_info "waiting corosync stop ok..."
    stop_coroysnc_service_ok
    log_info "stop corosync ok"
}

function is_service_ok()
{
    service=$1
    ret=$(systemctl is-enabled "$service")
    if [ "X$ret" != "Xenabled" ]
    then
        # log_info "pacemaker service does not need to run on the node"
        return 0
    fi

    ret=$(systemctl status "$service" |grep "Active: active")
    if [ "X$ret" != "X" ]
    then
        # log_info "service is OK"
        return 0
    else
        # log_info "service is not OK"
        return 1
    fi
}

function stop_pacemaker_service_ok(){
    while true; do
        is_service_ok pacemaker
        if [[ $? -eq 1 ]]; then
            break
        fi
        sleep 3
    done
}

function stop_coroysnc_service_ok(){
    while true; do
        is_service_ok corosync
        if [[ $? -eq 1 ]]; then
            break
        fi
        sleep 3
    done
}

# run stop_service function
timeoutFunc 1200 stop_service >/dev/null 2>&1
# shellcheck disable=SC2181
if [[ $? -ne 0 ]]; then
    log_info "stop fail"
    exit 1
fi
log_info "stop ok"
exit 0
