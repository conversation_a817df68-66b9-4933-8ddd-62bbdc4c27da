#!/bin/bash
#######################################
# @Date    : 2019-12-19 10:08:31
# @Version : 1.0.0
#######################################
if [ -x "/usr/bin/timeout" ]; then
   TIMEOUT="/usr/bin/timeout 120"
else
   TIMEOUT=""
fi

ROLL_TIMEOUT=$1
TIMEOUT=${ROLL_TIMEOUT:-240}

CRM_CONF_SHOW="crm configure show"
CRM_RESOURCE_STOP="crm resource stop"
CRM_RESOURCE_SHOW="crm resource show"

COMP_NAME="pacemaker_cluster"
LOG_PATH="/paasdata/op-log/${COMP_NAME}"
LOG_FILENAME="${LOG_PATH}/pcmk_external.log"

if [ ! -d "$LOG_PATH" ]; then
    mkdir -p $LOG_PATH
fi

pcs cluster help --retry_wait=1 >/dev/null
pcs_flag=$?

log_info(){
    LOG_DATE=$(date "+%Y-%m-%d %H:%M:%S")
    PID_INFO=$$
    SCRIPT_NAME=${0##*/}
    echo "$LOG_DATE [$PID_INFO] [$SCRIPT_NAME] ${FUNCNAME[*]} - $1" >> "$LOG_FILENAME"
    local access
    access=$(stat -c %a "$LOG_FILENAME")
    if [ "$access" != "640" ]; then
        chmod 640 "$LOG_FILENAME"
    fi
}

timeoutFunc()
{
    waitfor=$1
    command=$2
    $command &
    commandpid=$!
    (sleep "$waitfor" ; kill -9 "$commandpid"  >/dev/null 2>&1)&
    watchdog=$!
    log_info "waiting for $commandpid exit..."
    wait "$commandpid" >/dev/null 2>&1
    rc=$?
    log_info "$commandpid exit: $rc"
    kill "$watchdog" >/dev/null 2>&1
    return $rc
}

function exit_pcmk_maintenance(){
    start_time=0
    while [ $start_time -lt 20 ]; do
        is_pcmk_maintenance
        rc=$?
        # 在维护模式或者命令超时，则执行退出维护模式
        if [[ $rc -eq 0 || $rc -eq 2 ]]; then
            if [[ $pcs_flag -eq 0 ]]
            then
                $TIMEOUT pcs property set unmanaged=false --retry_wait=12 >/dev/null 2>&1
            else
                $TIMEOUT crm_attribute --name maintenance-mode --update false >/dev/null 2>&1
            fi

            sleep 3
            (( start_time = start_time + 1 ))
            continue
        else
            return 0
        fi
    done
    return 1
}

function is_pcmk_maintenance(){
    mtc_value=$($TIMEOUT crm_attribute -n maintenance-mode -G -q)
    rc=$?
    # 命令执行超时，状态不确定
    if [[ $rc -eq 124 ]]; then
        return 2
    fi
    # 命令执行不超时，且为集群属性值true，则在维护模式
    if [[ "$mtc_value" == "true" ]]; then
        return 0
    fi
    # 命令执行不超时，属性值为false,则不在维护模式
    return 1
}

function waitStopOk(){
    while [[ $(${CRM_RESOURCE_SHOW} |grep -cE "Started|Masters|Slaves") -ne 0 ]]; do
        log_info "stop not ok,waiting..."
        sleep 3
    done
}

function stopPcmkRes(){
    output=$(${CRM_CONF_SHOW}|grep -E '^primitive'|awk '{print $2}')
    # shellcheck disable=SC2206
    pri_rsc=(${output})
    # 退出维护模式
    exit_pcmk_maintenance
    # 打印待停止的资源
    log_info "${pri_rsc[*]}"
    #停止所有资源
    # shellcheck disable=SC2086
    ${CRM_RESOURCE_STOP} ${pri_rsc[*]}
    waitStopOk
}

log_info "stop pcmk res starting"
timeoutFunc "${TIMEOUT}" stopPcmkRes >/dev/null 2>&1
rc=$?
if [[ $rc -ne 0 ]]; then
    if [[ "X$ROLL_TIMEOUT" != "X" ]]; then
        log_info "stop res timeout ${TIMEOUT}s"
        exit 1
    else
        log_info "stop res timeout ${TIMEOUT}s, ignore..."
        exit 0
    fi
fi
log_info "stop pcmk res ok!"
exit 0
