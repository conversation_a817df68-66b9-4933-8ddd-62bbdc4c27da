#!/bin/bash

version=$1
version_pre8=${version:0:8}
version_pre11=${version:0:11}
new_base_dir="/etc/pdm/deploy_playbooks/keydata_rollback"
old_base_dir="/etc/pdm_bak/deploy_playbooks/keydata_rollback"
old_res="$old_base_dir/resource_reg"

if command -v pythonlatest &> /dev/null; then
  python_cmd="pythonlatest"
else
  python_cmd="python"
fi

if [ "${version_pre8#*.}" == "19.30" ] || [ "${version_pre11#*.}" == "19.40.01" ]; then
    cp -r $new_base_dir /etc/pdm_bak/deploy_playbooks
    new_reg="$new_base_dir/resource_reg/fix_bugs/pdm-cli/resource_reg_11.3"
    rm -rf $old_res
    cp -r $new_reg $old_res
fi

if [ "${version_pre8#*.}" == "19.40" ] && [[ ${version#*.} < "19.40.07.p01" ]]; then
    new_reg="$new_base_dir/resource_reg/fix_bugs/pdm-cli/resource_reg_11.4_40.07"
    cp -rf $new_reg/* $old_res
fi

# copying res_align.yml is applied before v1.20.20.05
old_res_align="$old_res/res_align.yml"
if [ ! -e $old_res_align ]; then
    new_res_align="$new_base_dir/resource_reg/res_align.yml"
    cp $new_res_align $old_res
fi

if [[ ! "${version_pre11#*.}" > "20.30.06" ]];then
    new_reg="$new_base_dir/resource_reg/fix_bugs/pdm-cli/resource_reg_sysconfig_20.3"
    cp -rf $new_reg/* $old_res
fi

if [[ ! "${version_pre11#*.}" > "21.20.05" ]];then
    new_reg="$new_base_dir/resource_reg/fix_bugs/pdm-cli/resource_reg_sysconfig_21.2"
    cp -rf $new_reg/* $old_res
fi

if [[ ! "${version#*.}" < "19.40.07.p01" ]] && [[ "${version#*.}" < "20.30.06.p01" ]];then
    new_reg="$new_base_dir/resource_reg/fix_bugs/pdm-cli/resource_reg_misc_20.3"
    cp -rf $new_reg/* $old_res
fi

sync_pdm_file="$old_base_dir/keydata_scripts/sync_pdm-cli.yml"
if [ ! -e $sync_pdm_file ]; then
    ansible -i /etc/pdm/hosts nodes -m copy -a "src=$new_base_dir/keydata_scripts/sync_pdm-cli.yml dest=$old_base_dir/keydata_scripts" -u ubuntu -b >/dev/null
fi

if [[ "${version#*.}" < "21.10.06" ]];then
    res_path="$old_res/res_filedir.yml"
    script_path="$new_base_dir/resource_reg/fix_bugs/pdm-cli/resource_reg_21.10.06/fix_inetagent_filedir_res.py"
    $python_cmd "$script_path" "$res_path"
fi

if [[ "${version#*.}" < "21.30.04" ]];then
    new_reg="$new_base_dir/resource_reg/fix_bugs/pdm-cli/resource_reg_filedir_extra_21.30.01/res_filedir_extra.yml"
    cp -f $new_reg $old_res
fi

if [[ "${version#*.}" < "23.20.04" ]];then
    res_path="$old_res/res_filedir.yml"
    script_path="$new_base_dir/resource_reg/fix_bugs/pdm-cli/resource_reg_filedir_23.20.03/fix_processd_filedir_res.py"
    $python_cmd "$script_path" "$res_path"
fi

old_res_firedie_kubeall="$old_res/res_filedir_kubeall.yml"
if [ ! -e $old_res_firedie_kubeall ]; then
    new_res_firedie_kubeall="$new_base_dir/resource_reg/res_filedir_kubeall.yml"
    cp $new_res_firedie_kubeall $old_res_firedie_kubeall
fi

restore_resolv_conf_path="$new_base_dir/resource_reg/fix_bugs/pdm-cli/resource_reg_resolv_conf/restore_resolv_conf.py"
$python_cmd "$restore_resolv_conf_path"

if [[ "${version#*.}" < "23.40.04" ]];then
    script_path="$new_base_dir/resource_reg/fix_bugs/pdm-cli/resource_reg_align_recreate_pods/fix_recreate_pods.py"
    $python_cmd "$script_path" "$old_res_align"
fi
