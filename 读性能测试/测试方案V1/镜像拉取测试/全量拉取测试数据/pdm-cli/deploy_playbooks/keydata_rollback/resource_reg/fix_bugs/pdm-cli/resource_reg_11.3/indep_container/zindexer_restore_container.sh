#!/bin/sh

souce_file=/paasdata/op-conf/zindexer/zindexer_restore_container.sh
backup_file=/paasdata/op-conf/zindexer/zindexer_restore_container.sh.bak

if [ -f ${souce_file} ];then
    grep "timeout 120" ${souce_file}
    result=$?
    if [ "$result" -ne 0 ];then
        cp -a ${souce_file} ${backup_file}
        sed -i 's/timeout\s*\S*/timeout 120/g' ${backup_file}
        if [ -s ${backup_file} ];then
            cp -a ${backup_file} ${souce_file}
        fi
    fi
    sh ${souce_file}
else
    echo "zindexer restore container script does not exist"
    exit 1
fi
