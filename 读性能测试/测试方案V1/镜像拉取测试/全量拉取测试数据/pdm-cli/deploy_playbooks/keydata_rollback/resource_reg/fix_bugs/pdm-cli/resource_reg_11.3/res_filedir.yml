[{"res_name": "op-conf", "compo_owner": "", "judge_method": "by_role", "roles": ["usednodes"], "inclu_dir": ["/paasdata/op-conf"], "exclu_dir": ["/paasdata/op-conf/docker"], "inclu_big_subdir": []}, {"res_name": "op-data", "compo_owner": "", "judge_method": "by_role", "roles": ["usednodes"], "inclu_dir": ["/paasdata/op-data"], "exclu_dir": ["/paasdata/op-data/cf-pdeploy", "/paasdata/op-data/etcd", "/paasdata/op-data/pdm-cli", "/paasdata/op-data/postgresql", "/paasdata/op-data/zart", "/paasdata/op-data/k8s-*", "/paasdata/op-data/br-k8s-backup", "/paasdata/op-data/keydata", "/paasdata/op-data/paas_upgrade_backup_keydata", "/paasdata/op-data/prometheus", "/paasdata/op-data/logstash", "/paasdata/op-data/elasticsearch", "/paasdata/op-data/kafka", "/paasdata/op-data/collect_data", "/paasdata/op-data/paas-os-package"], "inclu_big_subdir": []}, {"res_name": "root-common", "compo_owner": "", "judge_method": "by_role", "roles": ["paas_controller"], "inclu_dir": ["/root/common"], "exclu_dir": [], "inclu_big_subdir": []}, {"res_name": "root-zart", "compo_owner": "", "judge_method": "by_role", "roles": ["paas_controller"], "inclu_dir": ["/root/zartcli"], "exclu_dir": [], "inclu_big_subdir": []}, {"res_name": "posd_file", "compo_owner": "posd", "judge_method": "by_role", "roles": ["paas_controller"], "inclu_dir": ["/etc/httpd/conf.d/http_posd_image.conf"], "exclu_dir": [], "inclu_big_subdir": []}, {"res_name": "nwmaster_file", "compo_owner": "nwmaster", "judge_method": "by_component", "roles": [], "inclu_dir": ["/usr/bin/jj", "/usr/bin/jq-linux64"], "exclu_dir": [], "inclu_big_subdir": []}, {"res_name": "nwmonitor_file", "compo_owner": "nwmonitor", "judge_method": "by_component", "roles": [], "inclu_dir": ["/usr/bin/jj", "/usr/bin/jq-linux64"], "exclu_dir": [], "inclu_big_subdir": []}, {"res_name": "nwnode_file", "compo_owner": "nwnode", "judge_method": "by_component", "roles": [], "inclu_dir": ["/etc/paasnw", "/etc/network", "/etc/knitter", "/etc/cni", "/opt/cni", "/usr/bin/jj", "/usr/bin/jq-linux64", "/usr/lib/systemd/system/knitter-agent.service"], "exclu_dir": [], "inclu_big_subdir": []}, {"res_name": "underpan_file", "compo_owner": "underpan", "judge_method": "by_component", "roles": [], "inclu_dir": ["/etc/logrotate.d/*", "/etc/systemd/system/underpan.service", "/usr/lib/systemd/system/underpan.service", "/etc/init.d/underpan.conf"], "exclu_dir": [], "inclu_big_subdir": []}, {"res_name": "heartbeat_file", "compo_owner": "heartbeat", "judge_method": "by_component", "roles": ["usednodes"], "inclu_dir": ["/usr/lib/systemd/system/heartbeat.service"], "exclu_dir": [], "inclu_big_subdir": []}, {"res_name": "opslet_file", "compo_owner": "<PERSON><PERSON><PERSON>", "judge_method": "by_component", "roles": ["usednodes"], "inclu_dir": ["/usr/lib/systemd/system/opslet.service"], "exclu_dir": [], "inclu_big_subdir": []}, {"res_name": "monitor_file", "compo_owner": "monitor", "judge_method": "by_component", "roles": ["paas_controller"], "inclu_dir": ["/usr/lib/systemd/system/monitor.service"], "exclu_dir": [], "inclu_big_subdir": []}, {"res_name": "userkpi", "compo_owner": "userkpi", "judge_method": "by_role", "roles": ["minion"], "inclu_dir": ["/usr/lib/systemd/system/userkpi.service"], "exclu_dir": [], "inclu_big_subdir": []}, {"res_name": "cnrm_file", "compo_owner": "cnrm", "judge_method": "by_component", "roles": ["minion"], "inclu_dir": ["/etc/paasnw/cnrm", "/opt/paasnw/cnrm", "/usr/bin/jj", "/usr/bin/jo", "/usr/bin/jq-linux64"], "exclu_dir": [], "inclu_big_subdir": []}, {"res_name": "cnrm-manager_file", "compo_owner": "cnrm-manager", "judge_method": "by_component", "roles": ["paas_controller"], "inclu_dir": ["/etc/paasnw/cnrm-manager", "/etc/cnrm-cli", "/usr/bin/cnrm-cli"], "exclu_dir": [], "inclu_big_subdir": []}, {"res_name": "op_ops_tools_file", "compo_owner": "op-ops-tools", "judge_method": "by_role", "roles": ["paas_controller"], "inclu_dir": ["/paasdata/ops-tools", "/bin/collect-cli"], "exclu_dir": ["/paasdata/ops-tools/collect_data/*"], "inclu_big_subdir": []}, {"res_name": "in<PERSON><PERSON><PERSON>", "compo_owner": "in<PERSON><PERSON><PERSON>", "judge_method": "by_component", "roles": ["usednodes"], "inclu_dir": ["/usr/bin/inetrules", "/usr/bin/globalinetrules", "/etc/network/inetrules_switch.sh"], "exclu_dir": [], "inclu_big_subdir": []}, {"res_name": "inetagent", "compo_owner": "inetagent", "judge_method": "by_component", "roles": ["usednodes"], "inclu_dir": ["/etc/init.d/inetagent", "/usr/lib/systemd/system/inetagent.service", "/usr/lib/python2.7/site-packages/inetagent"], "exclu_dir": [], "inclu_big_subdir": []}, {"res_name": "nfs_agent", "compo_owner": "nfs_agent", "judge_method": "by_role", "roles": ["nfs_server"], "inclu_dir": ["/usr/sbin/fsck.xfs_new", "/usr/bin/pacemaker_manage_nfs", "/usr/lib/systemd/system/pacemaker_manage_nfs.service"], "exclu_dir": [], "inclu_big_subdir": []}]