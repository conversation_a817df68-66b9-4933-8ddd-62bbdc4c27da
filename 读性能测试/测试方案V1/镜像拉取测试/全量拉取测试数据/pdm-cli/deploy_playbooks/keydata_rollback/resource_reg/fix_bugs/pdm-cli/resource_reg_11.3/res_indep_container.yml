[{"res_name": "sample", "compo_owner": "sample", "judge_method": "by_component", "roles": [], "clear_cmd": "indep_container/sample_clear_container.sh", "restore_cmd": "indep_container/sample_restore_container.sh"}, {"res_name": "zindexer", "compo_owner": "zindexer", "judge_method": "by_component", "roles": [], "clear_cmd": "indep_container/zindexer_clear_container.sh", "restore_cmd": "indep_container/zindexer_restore_container.sh"}, {"res_name": "filebeat", "compo_owner": "filebeat", "judge_method": "by_component", "roles": [], "clear_cmd": "indep_container/filebeat_clear_container.sh", "restore_cmd": "indep_container/filebeat_restore_container.sh"}]