[{"res_name": "sample", "compo_owner": "sample", "judge_method": "by_component", "roles": [], "stop_cmd": "service/sample_stop_service.sh", "restore_cmd": "service/sample_restore_service.sh"}, {"res_name": "httpd", "compo_owner": "posd", "judge_method": "by_role", "roles": ["paas_controller"], "stop_cmd": "if [[ $(systemctl is-enabled httpd 2>/dev/null) == \"enabled\" ]] && rpm -qi httpd; then systemctl stop httpd; fi", "restore_cmd": "if [[ $(systemctl is-enabled httpd 2>/dev/null) == \"enabled\" ]] && rpm -qi httpd; then systemctl restart httpd; fi"}, {"res_name": "knitter-agent", "compo_owner": "nwnode", "judge_method": "by_component", "roles": [], "stop_cmd": "service knitter-agent stop", "restore_cmd": "service knitter-agent start"}, {"res_name": "underpan", "compo_owner": "underpan", "judge_method": "by_component", "roles": [], "stop_cmd": "service underpan stop", "restore_cmd": "service underpan start"}, {"res_name": "heartbeat", "compo_owner": "heartbeat", "judge_method": "by_component", "roles": ["usednodes"], "stop_cmd": "systemctl stop heartbeat", "restore_cmd": "systemctl start heartbeat"}, {"res_name": "<PERSON><PERSON><PERSON>", "compo_owner": "<PERSON><PERSON><PERSON>", "judge_method": "by_component", "roles": ["usednodes"], "stop_cmd": "systemctl stop opslet", "restore_cmd": "systemctl start opslet"}, {"res_name": "monitor", "compo_owner": "monitor", "judge_method": "by_component", "roles": ["paas_controller"], "stop_cmd": "systemctl stop monitor", "restore_cmd": "systemctl start monitor"}, {"res_name": "userkpi", "compo_owner": "userkpi", "judge_method": "by_role", "roles": ["minion"], "stop_cmd": "systemctl stop userkpi", "restore_cmd": "systemctl start userkpi"}, {"res_name": "cnrm", "compo_owner": "cnrm", "judge_method": "by_component", "roles": ["minion"], "stop_cmd": "service cnrm stop", "restore_cmd": "service/run_cnrm.sh"}, {"res_name": "nfs_agent", "compo_owner": "nfs_agent", "judge_method": "by_role", "roles": ["nfs_server"], "stop_cmd": "systemctl stop pacemaker_manage_nfs.service", "restore_cmd": "systemctl start pacemaker_manage_nfs.service"}]