[
 {
  "res_name": "sample",
  "compo_owner": "sample",
  "judge_method": "by_component",
  "roles": [],
  "backup_cmd": "",
  "clear_cmd":"",
  "restore_cmd": "components_misc/sample_components_misc.sh"
 },
  {
  "res_name": "zenap_kms",
  "compo_owner": "zenap_kms",
  "judge_method": "by_component",
  "roles": [],
  "backup_cmd": "",
  "clear_cmd":"",
  "restore_cmd": "components_misc/zenap_kms_misc.sh"
 },
  {
  "res_name": "os_harden_tmp",
  "compo_owner": "op-sec-harden",
  "judge_method": "by_role",
  "roles": [usednodes],
  "backup_cmd": "components_misc/os_harden_tmp_bak.sh",
  "clear_cmd":"",
  "restore_cmd": "components_misc/os_harden_tmp_misc.sh"
 }
]
