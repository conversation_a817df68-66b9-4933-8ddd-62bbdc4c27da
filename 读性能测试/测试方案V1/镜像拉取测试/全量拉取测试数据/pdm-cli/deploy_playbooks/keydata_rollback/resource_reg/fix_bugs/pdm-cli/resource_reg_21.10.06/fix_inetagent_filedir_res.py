import json
import sys

reg_path = sys.argv[1]
exclud_path = "/paasdata/op-conf/inetagent/inetagent_tag.conf"
with open(reg_path, 'r') as f:
    reg_list = json.load(f)
for reg in reg_list:
    if reg["res_name"] == "op-conf" and exclud_path not in reg["exclu_dir"]:
        reg["exclu_dir"].append(exclud_path)
        with open(reg_path, 'w', ) as f:
            json.dump(reg_list, f, indent=4)
        break
