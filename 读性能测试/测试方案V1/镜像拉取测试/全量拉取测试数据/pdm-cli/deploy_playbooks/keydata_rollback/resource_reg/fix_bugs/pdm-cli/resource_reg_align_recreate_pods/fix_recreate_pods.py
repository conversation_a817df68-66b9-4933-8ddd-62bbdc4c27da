import yaml
import sys

PODS_PATH = '/etc/pdm/deploy_playbooks/keydata_rollback/recreate_pods/recreate_pods.sh'
NW_PATH = '/paasdata/op-conf/nwmaster/keydata_rollback/nw_keydata_rollback.sh'
new_entry = {
    "path": "/etc/pdm/deploy_playbooks/keydata_rollback"
    "/recreate_pods/recreate_pods.sh",
    "args": [],
    "desc": "recreate pods align",
    "component": "cf-pdeploy",
    "role": "paas_controller",
    "not_exist_ignored": False
}


def add_entry_to_data(data, entry):
    if need_fix(data):
        index = next((i for i, d in enumerate(data) if d['path'] == NW_PATH), None)
        if index is not None:
            data.insert(index + 1, entry)


def need_fix(data):
    for res in data:
        if PODS_PATH in res.get("path"):
            return False
    return True


def main(res_yml):
    with open(res_yml, 'r') as file:
        data = yaml.safe_load(file)
    add_entry_to_data(data, new_entry)
    with open(res_yml, 'w') as file:
        yaml.safe_dump(data, file)


if __name__ == "__main__":
    main(sys.argv[1])