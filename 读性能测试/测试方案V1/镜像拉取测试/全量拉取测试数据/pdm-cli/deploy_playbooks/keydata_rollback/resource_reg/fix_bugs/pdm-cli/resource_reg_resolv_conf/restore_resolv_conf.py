import json
import os

filedir_yml_bak = "/etc/pdm_bak/deploy_playbooks/keydata_rollback/" \
                  "resource_reg/res_filedir.yml"
new_filedir = {
   "res_name": "resolv-conf",
   "compo_owner": "pdm-cli",
   "judge_method": "by_role",
   "roles": ["usednodes"],
   "inclu_dir": ["/etc/resolv.conf"],
   "exclu_dir": [],
   "inclu_big_subdir": []
 }


def add_to_filedir_yml():
    with open(filedir_yml_bak, 'r') as file:
        data = json.load(file)
    for res in data:
        if (res.get("res_name") == "resolv-conf" and
                "/etc/resolv.conf" in res.get("inclu_dir", [])):
            return
    data.append(new_filedir)
    with open(filedir_yml_bak, 'w') as file:
        json.dump(data, file, indent=4)
    return


def main():
    if os.path.exists("/etc/pdm_bak/deploy_playbooks/"
                      "keydata_rollback/resource_reg"):
        add_to_filedir_yml()


if __name__ == "__main__":
    main()
