[
 {
  "res_name": "sample",
  "compo_owner": "sample",
  "judge_method": "by_component",
  "roles": [],
  "config_path": "/etc/sysctl.conf",
  "config_type": "sysctl",
  "clear_cmd": "sysconfig/sample_clear_sysconfig.sh",
  "restore_cmd": "sysconfig/sample_restore_sysconfig.sh"
 },
 {
  "res_name": "crontab",
  "compo_owner": "underpan",
  "judge_method": "by_component",
  "roles": [],
  "config_path": "/etc/crontab",
  "config_type": "cron",
  "clear_cmd": "sysconfig/crontab_clear_sysconfig.sh",
  "restore_cmd": "sysconfig/crontab_restore_sysconfig.sh"
 },
 {
  "res_name": "sudoers",
  "compo_owner": "common",
  "judge_method": "by_role",
  "roles": [usednodes],
  "config_path": "/etc/sudoers",
  "config_type": "sudoers",
  "clear_cmd": "",
  "restore_cmd": "sysconfig/sudoers_restore_sysconfig.sh"
 }
]
