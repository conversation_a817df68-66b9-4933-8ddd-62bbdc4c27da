import json
import sys


res_path = sys.argv[1]

res_filedir_path = res_path + "/res_filedir.yml"
exclude_dirs = [
    "/paasdata/op-data/provider_backup_path",
    "/paasdata/op-data/op-psd-provider-cpaas/exportDebug",
    "/paasdata/op-data/op-psd-provider-cpaas/otherlog",
    "/paasdata/op-data/op-psd-provider-cpaas/provider-ftp/adapter",
    "/paasdata/op-data/op-psd-provider-cpaas/provider-ftp/c7Pusher",
    "/paasdata/op-data/op-psd-provider-cpaas/provider-ftp/localPimCm",
    "/paasdata/op-data/op-psd-provider-cpaas/provider-ftp/plat",
    "/paasdata/op-data/op-psd-provider-cpaas/provider-ftp/pusher",
    "/paasdata/op-data/op-psd-provider-cpaas/provider-ftp/totalExport",
    "/paasdata/op-data/op-psd-provider-cpaas/provider-ftp/vsanPuller",
    "/paasdata/op-data/op-psd-provider-cpaas/provider-ftp/vsanPusher",
    "/paasdata/op-data/op-psd-provider-cpaas/provider-ftp/ztepm/sftp/data"
]


def get_reg_list_from_res_file():
    try:
        with open(res_filedir_path, 'r') as f:
            reg_list = json.load(f)
            return reg_list
    except Exception as e:
        print("open file failed, exception is {}".format(e))
        raise e


def modify_reg_list_to_res_file(reg_list):
    try:
        with open(res_filedir_path, 'w') as f:
            json.dump(reg_list, f, indent=4)
    except Exception as e:
        print("open file failed, exception is {}".format(e))
        raise e


def add_exclu_dirs_for_op_data_path():
    try:
        reg_list = get_reg_list_from_res_file()
        need_modify = False
        for reg in reg_list:
            if reg["res_name"] != "op-data":
                continue
            for exlude_dir in exclude_dirs:
                if exlude_dir not in reg["exclu_dir"]:
                    need_modify = True
                    reg["exclu_dir"].append(exlude_dir)

        if need_modify:
            print('start modify file {}'.format(res_filedir_path))
            modify_reg_list_to_res_file(reg_list)
    except Exception as e:
            print("Error: exception is {}.".format(e))
            raise


def main():
    try:
        add_exclu_dirs_for_op_data_path()
    except Exception:
        print("add exclu dirs failed.")
        sys.exit(1)


if __name__ == "__main__":
    main()
