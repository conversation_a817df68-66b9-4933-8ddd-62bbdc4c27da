#!/bin/bash
# 该脚本由root用户执行

version=$1
new_base_dir="/etc/pdm/deploy_playbooks/keydata_rollback"
old_base_dir="/etc/pdm_bak/deploy_playbooks/keydata_rollback"
old_res_dir="$old_base_dir/resource_reg"
script_path="$new_base_dir/resource_reg/fix_bugs/provider/fix_res.py"


if command -v pythonlatest >/dev/null 2>&1; then
    PYTHON_EXEC=pythonlatest
else
    PYTHON_EXEC=python3
fi

if [[ "${version#*.}" < "24.10.01" ]];then
    "${PYTHON_EXEC}" "$script_path" "$old_res_dir"
fi
