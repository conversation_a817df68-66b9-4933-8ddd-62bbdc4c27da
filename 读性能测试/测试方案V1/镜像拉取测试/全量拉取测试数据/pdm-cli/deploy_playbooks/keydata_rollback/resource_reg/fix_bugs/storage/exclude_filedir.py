#!/usr/bin/python

import json
import sys

reg_filedir_path = sys.argv[1] + "/res_filedir.yml"

conf_dir_need_exclude = "/paasdata/op-conf/op-storage-ceph_csi_driver"
has_excluded = False
reg_list = None

with open(reg_filedir_path, 'r') as f:
    reg_list = json.load(f)
    for reg in reg_list:
        if reg["res_name"] == "op-conf":
            for excludeDir in reg["exclu_dir"]:
                if excludeDir == conf_dir_need_exclude:
                    has_excluded = True
                    break
            if not has_excluded:
                reg["exclu_dir"].append(conf_dir_need_exclude)

if not has_excluded:
    with open(reg_filedir_path, 'w') as f:
        json.dump(reg_list, f, indent=4)
