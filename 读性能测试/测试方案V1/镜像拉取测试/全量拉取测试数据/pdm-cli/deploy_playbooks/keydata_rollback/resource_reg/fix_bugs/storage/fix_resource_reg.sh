#!/bin/bash
#shellcheck disable=SC2072
#shellcheck disable=SC2086
#shellcheck disable=SC2046
version=$1

if [[ ${#version} -lt 8 ]]; then
	echo "version illegal"
	exit 1
fi

PYTHON_CMD="python"
if command -v pythonlatest &> /dev/null; then
  PYTHON_CMD="pythonlatest"
fi

version_pre8=${version:0:8}
if [[ "${version_pre8#*.}" < "19.30" ]]; then
	echo "only support keydata rollback after r11.3"
	exit 0
fi

if [ ! -d "/paasdata/op-data/storage" ];then
  echo "have no storage"
  exit 0
fi

if [[ $(command -v jq-linux64) ]]; then
    jq_cmd=jq-linux64
else
    jq_cmd=jq
fi

cd "$(dirname "$0")" || exit 1

storage_info=$(pdm-cli controllergroupinfo get storage)
rc=$?
if [ $rc -ne 0 ];then
  echo "pdm-cli controllergroupinfo get storage failed"
  exit $rc
fi

#generate hosts temp file
echo "[nodes]" > ./tmp_hosts
echo $storage_info |$jq_cmd -r .controllergroupinfo[].ips[] >> ./tmp_hosts

ansible-playbook -i ./tmp_hosts sync_rollback_scripts_11.4_40.07.yml
rc=$?
if [ $rc -ne 0 ];then
  exit $rc
fi

# replace all sp_nfs_agent to support keydata rollback
version_pre11=${version:0:11}
if [[ "${version_pre11#*.}" < "22.30.06" ]]; then
  for((i=1;i<=5;i++))
  do
    $PYTHON_CMD generate_hosts.py
    rc=$?
    if [ $rc -ne 0 ];then
      echo "generate fix_nfs_server_sp_hosts failed, try again latter"
      sleep 1
      continue
    fi

    if [ -z $(sed -n '/\[nodes\]/{n;p}' /etc/pdm/fix_nfs_server_sp_hosts) ];then
        echo "sp nfs is not deployed"
        break
    fi

    ansible-playbook -i /etc/pdm/fix_nfs_server_sp_hosts replace_sp_nfs_agent.yml
    rc=$?
    if [ $rc -ne 0 ];then
      echo "replace sp_nfs_agent failed, try again latter"
      sleep 1
      continue
    fi

    echo "replace sp_nfs_agent successfully"
    break
  done
fi

old_base_dir="/etc/pdm_bak/deploy_playbooks/keydata_rollback"
new_base_dir="/etc/pdm/deploy_playbooks/keydata_rollback"
old_res="$old_base_dir/resource_reg"
script_path="$new_base_dir/resource_reg/fix_bugs/storage/exclude_filedir.py"

if [[ "${version_pre11#*.}" < "23.40.03" ]]; then
    if [ -d "/paasdata/op-conf/op-storage-ceph_csi_driver" ];then
        $PYTHON_CMD "$script_path" "$old_res"
    fi
fi

old_res_align="${old_res}"/res_align.yml
if [[ "${version_pre11#*.}" < "24.10.00" ]]; then
    script_path="${new_base_dir}"/resource_reg/fix_bugs/storage/fix_start_glusterfs_server.py
    ${PYTHON_CMD} "${script_path}" "${old_res_align}"
fi
