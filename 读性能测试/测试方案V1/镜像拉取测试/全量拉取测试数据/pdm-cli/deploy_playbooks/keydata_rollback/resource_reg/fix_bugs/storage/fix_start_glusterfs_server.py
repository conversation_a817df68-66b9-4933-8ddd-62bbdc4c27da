import yaml
import sys

ROLL_BACK = '/paasdata/op-data/storage/volume_rollback'\
    '/glusterfs_keydata_rollback.sh'
NW_PATH = '/paasdata/op-conf/nwmaster/keydata_rollback/nw_keydata_rollback.sh'
new_entry = {
    "path": "/paasdata/op-data/storage/volume_rollback"
    "/glusterfs_keydata_rollback.sh",
    "args": [],
    "desc": "start glusterfs_server",
    "component": "storage",
    "role": "paas_controller",
    "not_exist_ignored": True
}


def add_entry_to_data(data, entry):
    if data is None:
        return
    if not need_fix(data):
        return
    index = next((i for i, d in enumerate(data) if d['path'] == NW_PATH), None)
    if index is not None:
        data.insert(index + 1, entry)


def need_fix(data):
    for res in data:
        if ROLL_BACK in res.get("path"):
            return False
    return True


def main(res_yml):
    data = None
    with open(res_yml, 'r') as file:
        data = yaml.safe_load(file)
    add_entry_to_data(data, new_entry)
    if data is not None:
        with open(res_yml, 'w') as file:
            yaml.safe_dump(data, file)


if __name__ == "__main__":
    main(sys.argv[1])
