#!/usr/bin/python
import os
import sys

pdm_common_path = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))))
sys.path.append(pdm_common_path)
from pdm_common import log
from pdm_common.generate_hosts import generate_hosts

DEBUG_LOG_FILE = "/paasdata/op-log/storage/generate_hosts.log"
logger = log.get_logger(log_name="default_log", log_file=DEBUG_LOG_FILE)


if __name__ == '__main__':
    role = "nfs_server_sp"
    host_file = "/etc/pdm/fix_nfs_server_sp_hosts"
    if generate_hosts(role, host_file):
        logger.info("generate hosts file successfully")
        sys.exit(0)
    else:
        logger.error("generate hosts file failed")
        sys.exit(1)
