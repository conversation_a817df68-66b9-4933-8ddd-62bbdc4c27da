---
- hosts : nodes
  remote_user: ubuntu
  become: yes
  become_method: sudo
  gather_facts: no
  tasks:

    - name: stop sp nfs server
      command: "systemctl stop sp_nfs_agent.service"

    - name: unarchive sp_nfs_agent pkg
      unarchive:
        src: "sp_nfs_agent_22_10_10/sp_nfs_agent.tar.gz"
        dest: "/paasdata/op-data/op-storage-sp_nfs_agent/"
        copy: yes
        mode: 0750

    - name: start sp nfs server
      command: "systemctl start sp_nfs_agent.service"
