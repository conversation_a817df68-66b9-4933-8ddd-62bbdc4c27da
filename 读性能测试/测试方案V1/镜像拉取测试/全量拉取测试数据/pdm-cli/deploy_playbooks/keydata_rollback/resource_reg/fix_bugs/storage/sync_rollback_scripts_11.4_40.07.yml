---
- hosts : nodes
  remote_user: ubuntu
  become: yes
  become_method: sudo
  gather_facts: no
  tasks:
    - name: unarchive volume_rollback pkg
      unarchive:
        src: "resource_reg_11.4_40.07/volume_rollback.tar.gz"
        dest: "/paasdata/op-data/storage/"
        copy: yes
        mode: 0755

    - name: unarchive stoman pkg
      unarchive:
        src: "resource_reg_11.4_40.07/stoman.tar.gz"
        dest: "/paasdata/op-data/storage/"
        copy: yes
        mode: 0755

    - name: unarchive storage-deploy pkg
      unarchive:
        src: "resource_reg_11.4_40.07/storage-deploy.tar.gz"
        dest: "/paasdata/op-data/storage/"
        copy: yes
        mode: 0755
