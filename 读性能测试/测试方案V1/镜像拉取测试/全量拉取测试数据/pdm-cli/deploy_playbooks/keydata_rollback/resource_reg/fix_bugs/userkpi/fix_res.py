import json
import sys
import shutil

new_base_dir = sys.argv[1]
old_base_dir = sys.argv[2]

src_file = new_base_dir + "/resource_reg/fix_bugs/userkpi/service/userkpi_restore_service.sh"
dst_file = old_base_dir + "/resource_reg/service/userkpi_restore_service.sh"

res_filedir_path = old_base_dir + "/resource_reg/res_service.yml"

with open(res_filedir_path, 'r') as f:
    reg_list = json.load(f)
    for reg in reg_list:
        if reg["res_name"] == "userkpi":  
            shutil.copy(src_file, dst_file)                 
            reg["restore_cmd"] = "service/userkpi_restore_service.sh"
            with open(res_filedir_path, 'w') as f:
                json.dump(reg_list, f, indent=4)
            break

            
