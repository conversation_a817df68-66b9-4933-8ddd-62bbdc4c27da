#!/bin/bash

version=$1
version_pre8=${version:0:8}
new_base_dir="/etc/pdm/deploy_playbooks/keydata_rollback"
old_base_dir="/etc/pdm_bak/deploy_playbooks/keydata_rollback"
res_filedir="$old_base_dir/resource_reg/res_filedir.yml"
res_service="$old_base_dir/resource_reg/res_service.yml"
components_misc="$old_base_dir/resource_reg/components_misc.yml"

SCRIPT=$(readlink -f "$0")
SCRIPT_PATH=$(dirname "$SCRIPT")

log() {
    local log_info=$1
    LOG_DATE=$(date "+%Y-%m-%d %H:%M:%S.%6N")
    LOG_FILENAME="/var/log/vnm_modify.log"
    if [ ! -f "$LOG_FILENAME" ]; then
       touch "$LOG_FILENAME"
       chmod 640 "$LOG_FILENAME"
    fi
    mode=$(stat -c %a "$LOG_FILENAME")
    if [ "$mode" != "640" ]; then
        chmod 640 "$LOG_FILENAME"
    fi
    echo "$LOG_DATE $log_info" >> $LOG_FILENAME
}

# shellcheck disable=SC2072
if [[ "${version_pre8#*.}" > "24.30" ]] || [[ "${version_pre8#*.}" == "24.30" ]];then
    script_path="$new_base_dir/resource_reg/fix_bugs/vnm/fix_vnm_res.py"
    $(command -v pythonlatest || echo python) "$script_path" "$res_filedir" "$res_service" "$components_misc"
    mkdir -p $old_base_dir/resource_reg/components_misc/vnm
    cp -f $new_base_dir/resource_reg/components_misc/vnm/* $old_base_dir/resource_reg/components_misc/vnm
    chmod -R 755 $old_base_dir/resource_reg/components_misc/vnm
    exit 0
fi