import json
import sys
import yaml

res_filedir_path = sys.argv[1]
res_service_path = sys.argv[2]
components_misc_file = sys.argv[3]

res_vnm_owner = {
    "res_name": "vnm",
    "compo_owner": "vnm",
    "judge_method": "by_role",
    "roles": ["paas_controller"],
    "backup_cmd": "components_misc/vnm/back_up_vnm_python2_dependencies.sh",
    "clear_cmd": "",
    "restore_cmd": "components_misc/vnm/restore_vnm_python2_dependencies.sh"
}

inclu_dir = ["/usr/lib/python2.7/site-packages/jinja2",
             "/usr/lib/python2.7/site-packages/chardet",
             "/usr/lib/python2.7/site-packages/babel",
             "/usr/lib64/python2.7/xml",
             "/usr/lib64/python2.7/site-packages/Crypto",
             "/usr/lib64/python2.7/multiprocessing",
             "/usr/lib64/python2.7/encodings",
             "/usr/lib64/python2.7/email",
             "/usr/lib64/python2.7/distutils",
             "/usr/lib64/python2.7/ctypes"]

has_vnm_owner = False

try:
    reg_list = None
    with open(res_filedir_path, 'r', encoding='utf-8') as f:
        reg_list = json.load(f)
    is_need_fix = False
    if reg_list:
        for reg in reg_list:
            if reg["res_name"] == "vnm":
                reg["inclu_dir"] = [dir_name for dir_name in reg["inclu_dir"] if dir_name not in inclu_dir] # noqa
                is_need_fix = True
                break
        if is_need_fix:
            with open(res_filedir_path, 'w', encoding='utf-8') as f:
                json.dump(reg_list, f, indent=4)

except Exception:
    pass

with open(components_misc_file, 'r') as f:
    reg_list = yaml.safe_load(f)
    for reg in reg_list:
        if reg["res_name"] == "vnm":
            has_vnm_owner = True
            break

if not has_vnm_owner:
    with open(components_misc_file, 'w') as f:
        reg_list.append(res_vnm_owner)
        yaml.safe_dump(reg_list, f, default_flow_style=False)
