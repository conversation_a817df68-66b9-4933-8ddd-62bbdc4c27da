import json
import sys

res_path = sys.argv[1]

zart_res_filedir = {
    "res_name": "zart",
    "compo_owner": "zart",
    "judge_method": "by_role",
    "roles": ["soft-repo"],
    "inclu_dir": ["/etc/systemd/system/syncthing.service"],
    "exclu_dir": [],
    "inclu_big_subdir": []
}

has_zart_res_filedir = False
res_filedir_path = res_path + "/res_filedir.yml"

with open(res_filedir_path, 'r') as f:
    reg_list = json.load(f)
    for reg in reg_list:
        if reg["res_name"] == "zart":
            has_zart_res_filedir = True
            break

if not has_zart_res_filedir:
    with open(res_filedir_path, 'w') as f:
        reg_list.append(zart_res_filedir)
        json.dump(reg_list, f, indent=4)

zart_res_service = {
    "res_name": "zart",
    "compo_owner": "zart",
    "judge_method": "by_role",
    "roles": ["soft-repo"],
    "stop_cmd": "if [[ $(systemctl is-enabled syncthing 2>/dev/null) "
                "== \"enabled\" ]]; then systemctl stop syncthing; fi",
    "restore_cmd": "if [[ -e /etc/systemd/system/syncthing.service ]]; "
    "then (systemctl enable syncthing;systemctl restart syncthing); fi"
}

has_zart_res_service = False
res_service_path = res_path + "/res_service.yml"

with open(res_service_path, 'r') as f:
    reg_list = json.load(f)
    for reg in reg_list:
        if reg["res_name"] == "zart":
            has_zart_res_service = True
            break

if not has_zart_res_service:
    with open(res_service_path, 'w') as f:
        reg_list.append(zart_res_service)
        json.dump(reg_list, f, indent=4)
