#!/bin/bash
# Started by AICoder, pid:07a71kc22dzc7d414621086d400e802f86f16650

old_reg="/etc/pdm_bak/deploy_playbooks/keydata_rollback/resource_reg/res_filedir_extra.yml"
if [[ $(command -v jq-linux64) ]]; then
    jq_cmd=jq-linux64
else
    jq_cmd=jq
fi

new_resource='{
  "res_name": "zenap_msb_consul_server_data",
  "compo_owner": "zenap_msb_consul_server",
  "judge_method": "by_component",
  "roles": [],
  "inclu_dir": ["/paasdata/op-data/zenap_msb_consul_server"],
  "exclu_dir": [],
  "inclu_big_subdir": []
}'


if ! $jq_cmd -e '.[] | select(.res_name == "zenap_msb_consul_server_data")' "$old_reg" &>/dev/null; then
    $jq_cmd --arg<PERSON><PERSON> new_resource "$new_resource" '. + [$new_resource]' "$old_reg" > temp.yml && mv temp.yml "$old_reg"
    echo "Resource 'zenap_msb_consul_server_data' added."
else
    echo "Resource 'zenap_msb_consul_server_data' already exists."
fi
# Ended by AICoder, pid:07a71kc22dzc7d414621086d400e802f86f16650
