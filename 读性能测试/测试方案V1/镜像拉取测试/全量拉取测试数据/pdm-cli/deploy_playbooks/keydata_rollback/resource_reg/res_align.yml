- path: /paasdata/op-data/cf-pnode/keydata_rollback/csi_pvc_rollback.sh
  args: []
  desc: tcf rollback to cpaas align csi pvc
  component: cf-pnode
  role: paas_controller
  not_exist_ignored: true

- path: /paasdata/op-data/cf-pnode/dynamic_lun_rollback/dynamic_lun_rollback.sh
  args: []
  desc: cf-pnode resource align
  component: cf-pnode
  role: paas_controller
  not_exist_ignored: true

- path: /paasdata/op-conf/nwmaster/keydata_rollback/nw_keydata_rollback.sh
  args: []
  desc: nw resource align
  component: nwmaster
  role: paas_controller
  not_exist_ignored: false

- path: /paasdata/op-data/storage/volume_rollback/glusterfs_keydata_rollback.sh
  args: []
  desc: start glusterfs server
  component: storage
  role: paas_controller
  not_exist_ignored: true

- path: /etc/pdm/deploy_playbooks/keydata_rollback/recreate_pods/recreate_pods.sh
  args: []
  desc: recreate pods align
  component: cf-pdeploy
  role: paas_controller
  not_exist_ignored: false

- path: /paasdata/op-data/storage/volume_rollback/bottom_rollback.sh
  args: []
  desc: storage resource align
  component: storage
  role: paas_controller
  not_exist_ignored: true

- path: /paasdata/op-conf/zart/swr/rollback_correctswrdata.sh
  args: []
  desc: zart resource align
  component: zart
  role: paas_controller
  not_exist_ignored: true
