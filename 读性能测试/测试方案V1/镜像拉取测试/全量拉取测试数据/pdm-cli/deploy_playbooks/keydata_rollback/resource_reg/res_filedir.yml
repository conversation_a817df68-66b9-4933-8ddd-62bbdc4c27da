[{"res_name": "op-conf", "compo_owner": "common", "judge_method": "by_role", "roles": ["usednodes"], "inclu_dir": ["/paasdata/op-conf"], "exclu_dir": ["/paasdata/op-conf/op-ubs-daisy-pg", "/paasdata/op-conf/inetagent/inetagent_tag.conf", "/paasdata/op-conf/inetagent-bak", "/paasdata/op-conf/nwmaster/keydata_rollback", "/paasdata/op-conf/op-storage-ceph_csi_driver"], "inclu_big_subdir": []}, {"res_name": "op-data", "compo_owner": "common", "judge_method": "by_role", "roles": ["usednodes"], "inclu_dir": ["/paasdata/op-data"], "exclu_dir": ["/paasdata/op-data/cf-pdeploy", "/paasdata/op-data/etcd", "/paasdata/op-data/pdm-cli", "/paasdata/op-data/postgresql", "/paasdata/op-data/zart", "/paasdata/op-data/keydata", "/paasdata/op-data/processd", "/paasdata/op-data/paas_upgrade_backup_keydata", "/paasdata/op-data/prometheus", "/paasdata/op-data/logstash", "/paasdata/op-data/elasticsearch", "/paasdata/op-data/kafka", "/paasdata/op-data/collect_data", "/paasdata/op-data/paas-os-package", "/paasdata/op-data/nvidia-driver", "/paasdata/op-data/op-ubs-daisy-pg", "/paasdata/op-data/provider-pg", "/paasdata/op-data/op-node-volume/attachments", "/paasdata/op-data/k8s-etcd", "/paasdata/op-data/provider_backup_path", "/paasdata/op-data/releaseNotes", "/paasdata/op-data/op-psd-provider-cpaas/otherlog", "/paasdata/op-data/op-psd-provider-cpaas/exportDebug", "/paasdata/op-data/op-psd-provider-cpaas/provider-ftp/adapter", "/paasdata/op-data/op-psd-provider-cpaas/provider-ftp/c7Pusher", "/paasdata/op-data/op-psd-provider-cpaas/provider-ftp/localPimCm", "/paasdata/op-data/op-psd-provider-cpaas/provider-ftp/plat", "/paasdata/op-data/op-psd-provider-cpaas/provider-ftp/pusher", "/paasdata/op-data/op-psd-provider-cpaas/provider-ftp/totalExport", "/paasdata/op-data/op-psd-provider-cpaas/provider-ftp/vsan<PERSON>uller", "/paasdata/op-data/op-psd-provider-cpaas/provider-ftp/vsanPusher", "/paasdata/op-data/op-psd-provider-cpaas/provider-ftp/ztepm/sftp/data", "/paasdata/op-data/nwnode", "/paasdata/op-data/op-nw-multus"], "inclu_big_subdir": []}, {"res_name": "root-common", "compo_owner": "common", "judge_method": "by_role", "roles": ["paas_controller"], "inclu_dir": ["/root/common"], "exclu_dir": [], "inclu_big_subdir": []}, {"res_name": "root-zart", "compo_owner": "common", "judge_method": "by_role", "roles": ["paas_controller"], "inclu_dir": ["/root/zartcli"], "exclu_dir": [], "inclu_big_subdir": []}, {"res_name": "posd_file", "compo_owner": "posd", "judge_method": "by_role", "roles": ["paas_controller"], "inclu_dir": ["/etc/httpd/conf.d/http_posd_image.conf"], "exclu_dir": [], "inclu_big_subdir": []}, {"res_name": "nwmaster_file", "compo_owner": "nwmaster", "judge_method": "by_component", "roles": [], "inclu_dir": ["/usr/bin/jj", "/usr/bin/jq-linux64"], "exclu_dir": [], "inclu_big_subdir": []}, {"res_name": "nwmonitor_file", "compo_owner": "nwmonitor", "judge_method": "by_component", "roles": [], "inclu_dir": ["/usr/bin/jj", "/usr/bin/jq-linux64"], "exclu_dir": [], "inclu_big_subdir": []}, {"res_name": "nwnode_file", "compo_owner": "nwnode", "judge_method": "by_component", "roles": [], "inclu_dir": ["/etc/paasnw", "/etc/network", "/etc/knitter", "/etc/cni", "/opt/cni", "/usr/bin/jj", "/usr/bin/jq-linux64", "/usr/lib/systemd/system/knitter-agent.service"], "exclu_dir": [], "inclu_big_subdir": []}, {"res_name": "underpan_file", "compo_owner": "underpan", "judge_method": "by_component", "roles": [], "inclu_dir": ["/etc/logrotate.d/*", "/etc/systemd/system/underpan.service", "/usr/lib/systemd/system/underpan.service", "/etc/init.d/underpan.conf"], "exclu_dir": [], "inclu_big_subdir": []}, {"res_name": "heartbeat_file", "compo_owner": "heartbeat", "judge_method": "by_component", "roles": ["usednodes"], "inclu_dir": ["/usr/lib/systemd/system/heartbeat.service"], "exclu_dir": [], "inclu_big_subdir": []}, {"res_name": "opslet_file", "compo_owner": "<PERSON><PERSON><PERSON>", "judge_method": "by_component", "roles": ["usednodes"], "inclu_dir": ["/usr/lib/systemd/system/opslet.service"], "exclu_dir": [], "inclu_big_subdir": []}, {"res_name": "monitor_file", "compo_owner": "monitor", "judge_method": "by_component", "roles": ["paas_controller"], "inclu_dir": ["/usr/lib/systemd/system/monitor.service"], "exclu_dir": [], "inclu_big_subdir": []}, {"res_name": "cnrm_file", "compo_owner": "cnrm", "judge_method": "by_component", "roles": ["minion"], "inclu_dir": ["/etc/paasnw/cnrm", "/opt/paasnw/cnrm", "/usr/bin/jj", "/usr/bin/jo", "/usr/bin/jq-linux64", "/usr/lib/systemd/system/cnrm.service"], "exclu_dir": [], "inclu_big_subdir": []}, {"res_name": "cnrm-manager_file", "compo_owner": "cnrm-manager", "judge_method": "by_component", "roles": ["paas_controller"], "subdomain": "nw.computingenchancement", "inclu_dir": ["/etc/paasnw/cnrm-manager", "/etc/cnrm-cli", "/usr/bin/cnrm-cli"], "exclu_dir": [], "inclu_big_subdir": []}, {"res_name": "op_ops_tools_file", "compo_owner": "op-ops-tools", "judge_method": "by_role", "roles": ["paas_controller"], "subdomain": "ops.ops-tools", "inclu_dir": ["/paasdata/ops-tools", "/bin/collect-cli"], "exclu_dir": ["/paasdata/ops-tools/collect_data/*"], "inclu_big_subdir": []}, {"res_name": "in<PERSON><PERSON><PERSON>", "compo_owner": "in<PERSON><PERSON><PERSON>", "judge_method": "by_component", "roles": ["usednodes"], "inclu_dir": ["/usr/bin/inetrules", "/usr/bin/globalinetrules", "/etc/network/inetrules_switch.sh"], "exclu_dir": [], "inclu_big_subdir": []}, {"res_name": "inetagent", "compo_owner": "inetagent", "judge_method": "by_role", "roles": ["usednodes"], "inclu_dir": ["/etc/init.d/inetagent", "/usr/lib/systemd/system/inetagent.service", "/usr/lib/python2.7/site-packages/inetagent", "/usr/lib/python3.6/site-packages/inetagent", "/usr/lib/python3.7/site-packages/inetagent", "/usr/lib/python3.11/site-packages/inetagent", "/usr/lib64/libpython2.7.so.1.0"], "exclu_dir": [], "inclu_big_subdir": []}, {"res_name": "inetproxy", "compo_owner": "inetproxy", "judge_method": "by_component", "roles": ["paas_controller"], "inclu_dir": ["/etc/init.d/inetproxy", "/usr/lib/systemd/system/inetproxy.service"], "exclu_dir": [], "inclu_big_subdir": []}, {"res_name": "nfs_agent", "compo_owner": "nfs_agent", "judge_method": "by_role", "roles": ["nfs_server"], "inclu_dir": ["/usr/sbin/fsck.xfs_new", "/usr/bin/pacemaker_manage_nfs", "/usr/lib/systemd/system/pacemaker_manage_nfs.service"], "exclu_dir": [], "inclu_big_subdir": []}, {"res_name": "op-storage-sp_nfs_agent", "compo_owner": "op-storage-sp_nfs_agent", "judge_method": "by_component", "roles": [], "inclu_dir": ["/usr/lib/systemd/system/sync_nfs_dir.service", "/usr/lib/systemd/system/sp_nfs_agent.service"], "exclu_dir": [], "inclu_big_subdir": []}, {"res_name": "op-node-agent", "compo_owner": "op-node-agent", "judge_method": "by_role", "roles": ["usednodes"], "inclu_dir": ["/etc/init.d/nodeagent", "/usr/lib/systemd/system/nodeagent.service", "/etc/node_agent", "/var/log/node_agent"], "exclu_dir": [], "inclu_big_subdir": []}, {"res_name": "op-containers-containerd", "compo_owner": "op-containers-containerd", "judge_method": "by_role", "roles": ["usednodes"], "inclu_dir": ["/etc/containerd", "/paasdata/op-conf/op-containers-containerd", "/usr/lib/systemd/system/containerd.service", "/usr/lib/systemd/system/cgsl-containerd-image-check.service"], "exclu_dir": [], "inclu_big_subdir": []}, {"res_name": "k8s-conf", "compo_owner": "k8s", "judge_method": "by_role", "roles": ["minion", "master"], "inclu_dir": ["/etc/kubernetes", "/usr/bin/external-apiservice", "/usr/bin/daemon-task", "/etc/systemd/system/external-apiservice.service", "/etc/systemd/system/docker.service.d/50-docker-trigger-kubelet.conf", "/etc/systemd/system/system.conf.d/kubernetes-accounting.conf", "/usr/lib/tmpfiles.d/kubernetes.conf", "/etc/systemd/system/float-etcd.service", "/etc/systemd/system/daemon-task.service", "/etc/systemd/system/etcd.service", "/paasdata/op-data/k8s-etcd/set_etcd_disk_priority.sh", "/etc/systemd/system/kubelet.service", "/etc/etcd", "/paasdata/docker/cpu_manager_state", "/usr/bin/kubectl", "/usr/bin/etcd", "/usr/bin/etcdctl", "/var/spool/cron/kube", "/usr/bin/kubelet", "/var/lib/kubelet", "/usr/bin/kubectl-extra", "/home/<USER>/ca.crt", "/home/<USER>/k8s_bin", "/home/<USER>/kubectl", "/home/<USER>/kubectl.kubeconfig"], "exclu_dir": [], "inclu_big_subdir": []}, {"res_name": "docker", "compo_owner": "docker", "judge_method": "by_role", "roles": ["usednodes"], "inclu_dir": ["/etc/docker/", "/paasdata/op-conf/docker/", "/usr/lib/systemd/system/docker.service", "/usr/lib/systemd/system/ipv6nat.service", "/usr/lib/systemd/system/image-check.service"], "exclu_dir": [], "inclu_big_subdir": []}, {"res_name": "zart", "compo_owner": "zart", "judge_method": "by_role", "roles": ["soft-repo"], "inclu_dir": ["/etc/systemd/system/syncthing.service"], "exclu_dir": [], "inclu_big_subdir": []}, {"res_name": "resolv-conf", "compo_owner": "pdm-cli", "judge_method": "by_role", "roles": ["usednodes"], "inclu_dir": ["/etc/resolv.conf"], "exclu_dir": [], "inclu_big_subdir": []}, {"res_name": "op-nw-netinsight-analyzer", "compo_owner": "op-nw-netinsight-analyzer", "judge_method": "by_role", "roles": ["paas_controller"], "inclu_dir": ["/etc/systemd/system/syncthing.sync_netinsight.service"], "exclu_dir": [], "inclu_big_subdir": []}, {"res_name": "op-nw-netinsight-agent", "compo_owner": "op-nw-netinsight-agent", "judge_method": "by_role", "roles": ["minion", "paas_controller"], "inclu_dir": ["/etc/systemd/system/multi-user.target.wants/skydive-agent.service"], "exclu_dir": [], "inclu_big_subdir": []}, {"res_name": "op-nw-netinsight-conf", "compo_owner": "op-nw-netinsight-agent", "judge_method": "by_role", "roles": ["minion", "paas_controller"], "inclu_dir": ["/etc/skydive"], "exclu_dir": [], "inclu_big_subdir": []}, {"res_name": "op-node-kubeagent_file", "compo_owner": "op-node-kubeagent", "judge_method": "by_role", "roles": ["usednodes"], "inclu_dir": ["/usr/lib/systemd/system/kubeNodeAgent.service", "/etc/init.d/kubeNodeAgent"], "exclu_dir": [], "inclu_big_subdir": []}, {"res_name": "userkpi", "compo_owner": "userkpi", "judge_method": "by_role", "roles": ["minion"], "inclu_dir": ["/usr/lib/systemd/system/userkpi.service"], "exclu_dir": [], "inclu_big_subdir": []}]