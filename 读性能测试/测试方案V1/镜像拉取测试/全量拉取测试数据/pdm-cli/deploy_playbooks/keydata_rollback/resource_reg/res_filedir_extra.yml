[{"res_name": "etc_logrotate", "compo_owner": "common", "judge_method": "by_role", "roles": ["usednodes"], "inclu_dir": ["/etc/logrotate.d"], "exclu_dir": [], "inclu_big_subdir": []}, {"res_name": "zenap_msb_consul_server_data", "compo_owner": "zenap_msb_consul_server", "judge_method": "by_component", "roles": [], "inclu_dir": ["/paasdata/op-data/zenap_msb_consul_server"], "exclu_dir": [], "inclu_big_subdir": []}]