[{"res_name": "kube<PERSON>", "compo_owner": "common", "judge_method": "by_role", "roles": ["usednodes"], "inclu_dir": ["/paasdata/op-data/processd/kubeall/version/bin/kubeall-boot-config-latest", "/paasdata/op-data/processd/kubeall/version/bin/kubeall-initiator-bp-latest", "/paasdata/op-data/processd/kubeall/run/conf/boot"], "exclu_dir": [], "inclu_big_subdir": []}, {"res_name": "root-common", "compo_owner": "common", "judge_method": "by_role", "roles": ["paas_controller"], "inclu_dir": ["/root/common"], "exclu_dir": [], "inclu_big_subdir": []}]