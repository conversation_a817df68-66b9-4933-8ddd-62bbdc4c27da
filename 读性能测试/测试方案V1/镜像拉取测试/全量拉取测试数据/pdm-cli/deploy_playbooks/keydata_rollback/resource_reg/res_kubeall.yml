- path: /paasdata/op-conf/nwmaster/keydata_rollback/nw_keydata_rollback.sh
  args: []
  desc: nw resource restore
  component: nwmaster
  role: paas_controller
  not_exist_ignored: false

- path: /etc/pdm/deploy_playbooks/keydata_rollback/recreate_pods/recreate_pods_need.sh
  args: []
  desc: pdm-cli delete pod
  component: pdm-cli
  role: paas_controller
  not_exist_ignored: false

- path: /etc/pdm/deploy_playbooks/keydata_rollback/keydata_scripts/restore_pg_bugfix.sh
  args: []
  desc: rollback pg bugfix
  component: op-ubs-daisy-pg
  role: paas_controller
  not_exist_ignored: false

- path: /etc/pdm/deploy_playbooks/keydata_rollback/keydata_scripts/storage/rollback_cinder_volumeattachment.sh
  args: []
  desc: storage resource
  component: storage
  role: paas_controller
  not_exist_ignored: false

- path: /paasdata/op-conf/op-asd-swr/swr/rollback_correctswrdata.sh
  args: []
  desc: zart resource align
  component: zart
  role: paas_controller
  not_exist_ignored: false

- path: /etc/pdm/deploy_playbooks/keydata_rollback/keydata_scripts/rollback_refresh_node_serverid.sh
  args: []
  desc: rollback refresh node serverid
  component: assembler
  role: paas_controller
  not_exist_ignored: false