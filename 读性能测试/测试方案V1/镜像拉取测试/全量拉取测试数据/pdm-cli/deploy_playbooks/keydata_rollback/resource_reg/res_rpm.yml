[{"res_name": "sample", "compo_owner": "sample", "judge_method": "by_component", "optional_component": false, "roles": [], "rpm_name": ["sample.tar.gz"], "clear_cmd": "rpm/sample_clear_rpm.sh", "restore_cmd": "rpm/sample_restore_rpm.sh"}, {"res_name": "op-containers-containerd", "compo_owner": "op-containers-containerd", "judge_method": "by_role", "optional_component": false, "roles": ["usednodes"], "rpm_name": ["rpms.tar.gz"], "clear_cmd": "rpm/op_containers_containerd_clear_rpm.sh", "restore_cmd": "rpm/op_containers_containerd_restore_rpm.sh"}, {"res_name": "k8s", "compo_owner": "k8s", "judge_method": "by_role", "optional_component": true, "roles": ["minion", "master"], "rpm_name": ["files.tar.gz"], "clear_cmd": "rpm/k8s_clear_rpm.sh", "restore_cmd": "rpm/k8s_restore_rpm.sh"}, {"res_name": "docker", "compo_owner": "docker", "judge_method": "by_role", "optional_component": false, "roles": ["usednodes"], "rpm_name": ["rpms.tar.gz"], "clear_cmd": "rpm/docker_clear_rpm.sh", "restore_cmd": "rpm/docker_restore_rpm.sh"}, {"res_name": "op-nw-netinsight-analyzer", "compo_owner": "op-nw-netinsight-analyzer", "judge_method": "by_role", "optional_component": false, "roles": ["paas_controller"], "rpm_name": ["files.tar.gz"], "clear_cmd": "rpm/op_nw_netinsight_analyzer_clear_rpm.sh", "restore_cmd": "rpm/op_nw_netinsight_analyzer_restore_rpm.sh"}, {"res_name": "op-nw-netinsight-agent", "compo_owner": "op-nw-netinsight-agent", "judge_method": "by_role", "optional_component": false, "roles": ["minion"], "rpm_name": ["files.tar.gz"], "clear_cmd": "rpm/op_nw_netinsight_agent_clear_rpm.sh", "restore_cmd": "rpm/op_nw_netinsight_agent_restore_rpm.sh"}]