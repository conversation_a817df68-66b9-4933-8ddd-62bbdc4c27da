[{"res_name": "sample", "compo_owner": "sample", "judge_method": "by_component", "roles": [], "stop_cmd": "service/sample_stop_service.sh", "restore_cmd": "service/sample_restore_service.sh"}, {"res_name": "httpd", "compo_owner": "posd", "judge_method": "by_role", "roles": ["paas_controller"], "stop_cmd": "if [[ $(systemctl is-enabled httpd 2>/dev/null) == \"enabled\" ]] && rpm -qi httpd; then systemctl stop httpd; fi", "restore_cmd": "if [[ $(systemctl is-enabled httpd 2>/dev/null) == \"enabled\" ]] && rpm -qi httpd; then systemctl restart httpd; fi"}, {"res_name": "knitter-agent", "compo_owner": "nwnode", "judge_method": "by_component", "roles": [], "stop_cmd": "if [[ $(systemctl is-enabled knitter-agent 2>/dev/null) == \"enabled\" ]]; then systemctl stop knitter-agent; fi", "restore_cmd": "if [[ $(systemctl is-enabled knitter-agent 2>/dev/null) == \"enabled\" ]]; then systemctl restart knitter-agent; fi"}, {"res_name": "underpan", "compo_owner": "underpan", "judge_method": "by_component", "roles": [], "stop_cmd": "if [[ $(systemctl is-enabled underpan 2>/dev/null) == \"enabled\" ]]; then systemctl stop underpan; fi", "restore_cmd": "if [[ $(systemctl is-enabled underpan 2>/dev/null) == \"enabled\" ]]; then systemctl restart underpan; fi"}, {"res_name": "heartbeat", "compo_owner": "heartbeat", "judge_method": "by_component", "roles": ["usednodes"], "stop_cmd": "if [[ $(systemctl is-enabled heartbeat 2>/dev/null) == \"enabled\" ]]; then systemctl stop heartbeat; fi", "restore_cmd": "if [[ $(systemctl is-enabled heartbeat 2>/dev/null) == \"enabled\" ]]; then systemctl restart heartbeat; fi"}, {"res_name": "<PERSON><PERSON><PERSON>", "compo_owner": "<PERSON><PERSON><PERSON>", "judge_method": "by_component", "roles": ["usednodes"], "stop_cmd": "if [[ $(systemctl is-enabled opslet 2>/dev/null) == \"enabled\" ]]; then systemctl stop opslet; fi", "restore_cmd": "if [[ $(systemctl is-enabled opslet 2>/dev/null) == \"enabled\" ]]; then systemctl restart opslet; fi"}, {"res_name": "monitor", "compo_owner": "monitor", "judge_method": "by_component", "roles": ["paas_controller"], "stop_cmd": "if [[ $(systemctl is-enabled monitor 2>/dev/null) == \"enabled\" ]]; then systemctl stop monitor; fi", "restore_cmd": "if [[ $(systemctl is-enabled monitor 2>/dev/null) == \"enabled\" ]]; then systemctl restart monitor; fi"}, {"res_name": "cnrm", "compo_owner": "cnrm", "judge_method": "by_component", "roles": ["minion"], "stop_cmd": "if [[ $(systemctl is-enabled cnrm 2>/dev/null) == \"enabled\" ]]; then systemctl stop cnrm; fi", "restore_cmd": "service/run_cnrm.sh"}, {"res_name": "nfs_agent", "compo_owner": "nfs_agent", "judge_method": "by_role", "roles": ["nfs_server"], "stop_cmd": "if [[ $(systemctl is-enabled pacemaker_manage_nfs.service 2>/dev/null) == \"enabled\" ]]; then systemctl stop pacemaker_manage_nfs.service; fi", "restore_cmd": "if [[ $(systemctl is-enabled pacemaker_manage_nfs.service 2>/dev/null) == \"enabled\" ]]; then systemctl restart pacemaker_manage_nfs.service; fi"}, {"res_name": "op-storage-sp_nfs_agent", "compo_owner": "op-storage-sp_nfs_agent", "judge_method": "by_component", "roles": [], "stop_cmd": "if [[ $(systemctl is-enabled sp_nfs_agent.service 2>/dev/null) == \"enabled\" ]]; then systemctl stop sp_nfs_agent.service; fi", "restore_cmd": "if [[ $(systemctl is-enabled sp_nfs_agent.service 2>/dev/null) == \"enabled\" ]]; then systemctl restart sp_nfs_agent.service; fi"}, {"res_name": "op-node-agent", "compo_owner": "op-node-agent", "judge_method": "by_role", "roles": ["usednodes"], "stop_cmd": "if [[ $(systemctl is-enabled nodeagent.service 2>/dev/null) == \"enabled\" ]]; then systemctl disable nodeagent.service && systemctl stop nodeagent.service; fi", "restore_cmd": "if [ -f /usr/lib/systemd/system/nodeagent.service ]; then bash /etc/node_agent/recreate_env.sh && systemctl enable nodeagent.service && systemctl restart nodeagent.service;fi"}, {"res_name": "op-containers-containerd", "compo_owner": "op-containers-containerd", "judge_method": "by_role", "roles": ["usednodes"], "stop_cmd": "service/op_containers_containerd_stop_service.sh", "restore_cmd": "service/op_containers_containerd_restore_service.sh"}, {"res_name": "kubelet", "compo_owner": "k8s", "judge_method": "by_role", "roles": ["minion", "master"], "stop_cmd": "service/kubelet_stop_service.sh", "restore_cmd": "service/kubelet_restore_service.sh"}, {"res_name": "daemon-task", "compo_owner": "k8s", "judge_method": "by_role", "roles": ["minion", "master"], "stop_cmd": "service/daemon_task_stop_service.sh", "restore_cmd": "if [[ -e /etc/systemd/system/daemon-task.service ]]; then (systemctl enable daemon-task;systemctl restart daemon-task); fi"}, {"res_name": "external-apiservice", "compo_owner": "k8s", "judge_method": "by_role", "roles": ["master"], "stop_cmd": "if [[ $(systemctl is-enabled external-apiservice 2>/dev/null) == \"enabled\" ]]; then systemctl stop external-apiservice; fi", "restore_cmd": "if [[ $(systemctl is-enabled external-apiservice 2>/dev/null) == \"enabled\" ]]; then systemctl restart external-apiservice; fi"}, {"res_name": "etcd", "compo_owner": "k8s", "judge_method": "by_role", "roles": ["paas_controller"], "stop_cmd": "service/etcd_stop_service.sh", "restore_cmd": ":"}, {"res_name": "docker", "compo_owner": "docker", "judge_method": "by_role", "roles": ["usednodes"], "stop_cmd": "service/docker_stop_service.sh", "restore_cmd": "service/docker_restore_service.sh"}, {"res_name": "zart", "compo_owner": "zart", "judge_method": "by_role", "roles": ["soft-repo"], "stop_cmd": "if [[ $(systemctl is-enabled syncthing 2>/dev/null) == \"enabled\" ]]; then systemctl stop syncthing; fi", "restore_cmd": "if [[ -e /etc/systemd/system/syncthing.service ]]; then (systemctl enable syncthing;systemctl restart syncthing); fi"}, {"res_name": "op-nw-netinsight-analyzer", "compo_owner": "op-nw-netinsight-analyzer", "judge_method": "by_role", "roles": ["paas_controller"], "stop_cmd": "if [[ $(systemctl is-enabled syncthing.sync_netinsight 2>/dev/null) == \"enabled\" ]]; then systemctl stop syncthing.sync_netinsight; fi", "restore_cmd": "if [[ -e /etc/systemd/system/syncthing.sync_netinsight.service ]]; then (systemctl enable syncthing.sync_netinsight;systemctl restart syncthing.sync_netinsight); fi"}, {"res_name": "op-nw-netinsight-agent", "compo_owner": "op-nw-netinsight-agent", "judge_method": "by_role", "roles": ["minion", "paas_controller"], "stop_cmd": "if [[ $(systemctl is-enabled skydive-agent 2>/dev/null) == \"enabled\" ]]; then systemctl stop skydive-agent; fi", "restore_cmd": "if [[ -e /etc/systemd/system/multi-user.target.wants/skydive-agent.service ]]; then (systemctl enable skydive-agent;systemctl restart skydive-agent); fi"}, {"res_name": "inetagent", "compo_owner": "inetagent", "judge_method": "by_component", "roles": ["usednodes"], "stop_cmd": "if [[ $(systemctl is-enabled inetagent.service 2>/dev/null) == \"enabled\" ]]; then (systemctl disable inetagent.service;systemctl stop inetagent.service); fi", "restore_cmd": "if [[ -d /paasdata/op-conf/inetagent-bak ]]; then (rm -rf /paasdata/op-conf/inetagent/inetagent_virenv;tar -zxvf /paasdata/op-conf/inetagent-bak/inetagent_conf.tar.gz --strip-components=1 -C /paasdata/op-conf/inetagent/); fi; if [[ -e /usr/lib/systemd/system/inetagent.service ]]; then (systemctl enable inetagent.service;systemctl restart inetagent.service); fi"}, {"res_name": "op-node-kubeagent", "compo_owner": "op-node-kubeagent", "judge_method": "by_role", "roles": ["usednodes"], "stop_cmd": "if [[ $(systemctl is-enabled kubeNodeAgent.service 2>/dev/null) == \"enabled\" ]]; then systemctl stop kubeNodeAgent.service; fi", "restore_cmd": "if [[ $(systemctl is-enabled kubeNodeAgent.service 2>/dev/null) == \"enabled\" ]]; then systemctl restart kubeNodeAgent.service; fi"}, {"res_name": "userkpi", "compo_owner": "userkpi", "judge_method": "by_role", "roles": ["minion"], "stop_cmd": "if [[ $(systemctl is-enabled userkpi 2>/dev/null) == \"enabled\" ]]; then systemctl stop userkpi; fi", "restore_cmd": "if [[ $(systemctl is-enabled userkpi 2>/dev/null) == \"enabled\" ]]; then systemctl restart userkpi; fi"}]