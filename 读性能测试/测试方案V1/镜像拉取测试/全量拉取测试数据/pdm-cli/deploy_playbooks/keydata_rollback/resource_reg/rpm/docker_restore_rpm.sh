#!/usr/bin/env bash
# shellcheck disable=SC2002
# shellcheck disable=SC2010

Restore_docker_dir="/paasdata/op-data/paas_upgrade_backup_keydata/rpm/docker"
gc_key="zncgsl6"
os_version=centos8
os_kylin=""
os_gc=""

function decompress_docker() {
    cd ${Restore_docker_dir} || exit
    rm -rf ./rpms
    tar -zxvf ./rpms.tar.gz
    ret=${?}
    if [ "${ret}" -ne 0 ]; then
         echo "decompress docker failed!"
         exit 1
    else
         echo "decompress docker successfully!"
    fi
}

function get_os_version() {
    if [[ -e /etc/cgsl-release ]];then
        if sudo cat /etc/os-release | grep -w "^VERSION_ID" |grep "6.06";then
            os_gc="$gc_key"
        elif sudo cat /etc/os-release | grep -w "^VERSION_ID" |grep "5.04";then
            os_version=centos7
        fi
    else
        if sudo uname -r | grep "^3\.10";then
            os_version=centos7
        elif sudo uname -r |grep "^4\.19";then
            os_kylin=ky10
        elif sudo uname -r | grep "^5\.10";then
            os_gc="$gc_key"
        fi
    fi
}

function docker_target_version_rollback() {
    if [ "$os_gc" = "$gc_key" ]; then
        rpm_docker=$(ls ${Restore_docker_dir}/rpms/ | grep "docker-ce.*.rpm" |grep "$gc_key")
        return
    fi
    if [ "$os_kylin" = ky10 ]; then
        rpm_docker=$(ls ${Restore_docker_dir}/rpms/ | grep "docker-ce.*.rpm" | grep "ky10")
    else
        if [ "$os_version" = centos8 ]; then
            rpm_docker=$(ls ${Restore_docker_dir}/rpms/ | grep "docker-ce.*.rpm" |grep "\.el8"|grep -v "ky10")
            if [ -z "$rpm_docker" ]; then
                rpm_docker=$(ls ${Restore_docker_dir}/rpms/ | grep "docker-ce.*.rpm" |grep "\.cgsl6"| grep -v "$gc_key" |grep -v "ky10")
            fi
        else
            rpm_docker=$(ls ${Restore_docker_dir}/rpms/ | grep "docker-ce.*.rpm" |grep "\.el7"|grep -v "ky10")
        fi
    fi

    if [ ! -f "$Restore_docker_dir"/rpms/"$rpm_docker" ]; then
        echo "No docker rpm exist"
        exit 1
    fi
}

function install_docker() {
    docker_target_version_rollback

    echo "Get target docker rpm is $rpm_docker"
    rpm -ivh "$Restore_docker_dir"/rpms/"$rpm_docker" --force
    ret=${?}
    if [ "${ret}" -ne 0 ]; then
         echo "Install docker failed!"
         exit 1
    else
         echo "Install docker successfully!"
    fi
}

function docker_image_target_version_rollback() {
    if [ "$os_gc" = "$gc_key" ]; then
        docker_image_rpm=$(ls ${Restore_docker_dir}/rpms/ | grep "docker-image-check-.*.rpm" |grep "$gc_key")
        return
    fi
    if [ "$os_kylin" = ky10 ]; then
        docker_image_rpm=$(ls ${Restore_docker_dir}/rpms/ | grep "docker-image-check-.*.rpm" |grep "ky10")
    else
        docker_image_rpm=$(ls ${Restore_docker_dir}/rpms/ | grep "docker-image-check-.*.rpm" |grep -v "ky10" |grep -v "$gc_key")
    fi
}

function install_docker_image() {
    docker_image_target_version_rollback

    rpm -ivh "$Restore_docker_dir"/rpms/"$docker_image_rpm" --force
    ret=${?}
    if [ "${ret}" -ne 0 ]; then
         echo "Install docker-image failed!"
         exit 1
    else
         echo "Install docker-image successfully!"
    fi
}

function ipv6nat_target_version_rollback() {
    if [ "$os_gc" = "$gc_key" ]; then
        ipv6nat_rpm=$(ls ${Restore_docker_dir}/rpms/ |  grep "ipv6nat-.*.rpm" |grep "$gc_key")
        return
    fi
    if [ "$os_kylin" = ky10 ]; then
        ipv6nat_rpm=$(ls ${Restore_docker_dir}/rpms/ | grep "ipv6nat-.*.rpm" |grep "ky10")
    else
        ipv6nat_rpm=$(ls ${Restore_docker_dir}/rpms/ | grep "ipv6nat-.*.rpm" |grep -v "ky10" |grep -v "$gc_key")
    fi
}

function install_ipv6nat() {
    ipv6nat_target_version_rollback

    if < /etc/docker/daemon.json grep ipv6 | grep true; then
         rpm -ivh "$Restore_docker_dir"/rpms/"$ipv6nat_rpm" --force
         ret=${?}
         if [ "${ret}" -ne 0 ]; then
              echo "Install ipv6nat failed!"
              exit 1
         else
              echo "Install ipv6nat successfully!"
         fi
    fi
}

function main() {
    decompress_docker
    get_os_version
    install_docker
    install_docker_image
    install_ipv6nat
    # postfix工具不考虑回退
    rm -rf ${Restore_docker_dir}/rpms
}

main
