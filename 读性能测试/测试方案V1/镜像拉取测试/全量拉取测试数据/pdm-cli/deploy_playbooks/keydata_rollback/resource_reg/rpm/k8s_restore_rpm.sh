#!/usr/bin/env bash

k8s_rpm_dir="/paasdata/op-data/paas_upgrade_backup_keydata/rpm/k8s"

function decompress_files_tar(){
    # 进入目录否则退出
    cd ${k8s_rpm_dir} || exit
    # shellcheck disable=SC2010
    # shellcheck disable=SC2046
    rm -fr $(ls . | grep -v "files.tar.gz")
    tar -zxvf ./files.tar.gz
    ret=${?}
    if [ "${ret}" -ne 0 ];then
        echo "decompress files.tar.gz for k8s failed!"
    else
        echo "decompress files.tar.gz for k8s successfully!"
    fi
}

path_of_group_info="/paasdata/op-data/paas_upgrade_backup_keydata/node_info/current_node_role.list"

function install_rpm_bin(){
    # shellcheck disable=SC2010
    if ls ${k8s_rpm_dir}/files | grep k8sbinrpm-manager;
    then
        k8s_bin_rpm_path="${k8s_rpm_dir}/files/k8sbinrpm-manager-*.rpm"
        # shellcheck disable=SC2086
        bin_name=$(ls $k8s_bin_rpm_path)
        yum -y install "${bin_name}" --disablerepo=*
        ret=${?}
        if [ "${ret}" -ne 0 ]; then
             echo "Install ${bin_name} failed!"
             exit 1
        else
             echo "Install ${bin_name} successfully!"
        fi
    fi
}

function install_rpm_file() {
    # shellcheck disable=SC2002
    group_info=$(cat $path_of_group_info | grep -w "master")
    if [ "${group_info}" = "master" ];then
        # shellcheck disable=SC2010
        if ls ${k8s_rpm_dir}/files | grep k8sfilerpm-manager;
        then
            k8s_file_rpm_path="${k8s_rpm_dir}/files/k8sfilerpm-manager-*.rpm"
            # shellcheck disable=SC2086
            file_name=$(ls $k8s_file_rpm_path)
            yum -y install "${file_name}" --disablerepo=*
            ret=${?}
            if [ "${ret}" -ne 0 ]; then
                 echo "Install ${file_name} failed!"
                 exit 1
            else
                 echo "Install ${file_name} successfully!"
            fi
        fi
    fi
}


function main() {
    decompress_files_tar
    install_rpm_file
    install_rpm_bin
}

main

