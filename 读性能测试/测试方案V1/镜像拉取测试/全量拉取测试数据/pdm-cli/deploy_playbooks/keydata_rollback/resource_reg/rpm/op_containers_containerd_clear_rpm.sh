#!/usr/bin/bash

containerd_rpm="containerd.io"
nerdctl_rpm="nerdctl"
cgsl_containerd_imagecheck_rpm="cgsl-containerd-imagecheck"
restore_dir="/paasdata/op-data/paas_upgrade_backup_keydata/rpm/op-containers-containerd"

log_info() {
    LOG_DIR="/paasdata/op-log/op-containers-containerd"
    LOG_DATE=$(date "+%Y-%m-%d %H:%M:%S,%3N")
    LOG_FILENAME="$LOG_DIR/containerd_scripts.log"
    filename=$(basename "$0")

    if [ ! -d "$LOG_DIR" ]; then
        mkdir -p "$LOG_DIR"
        chmod 750 "$LOG_DIR"
    fi
    if [ ! -f "$LOG_FILENAME" ]; then
        touch "$LOG_FILENAME"
        chmod 640 "$LOG_FILENAME"
    fi
    echo "${LOG_DATE} - ${filename} - $1" | tee -a "$LOG_FILENAME"
}

# 在日志信息前面添加 [Err] 或 [Info] 标记来区分错误和信息输出
function uninstall_containerd() {
    yum erase -y ${containerd_rpm}
    rc=${?}
    if [ ${rc} -ne 0 ]; then
        log_info "[Err] Uninstall containerd failed!"
        echo "[Err] Uninstall containerd failed!"
        exit 1
    fi
    log_info "[Info] Uninstall containerd successfully!"
    echo "[Info] Uninstall containerd successfully!"
}

function uninstall_nerdctl() {
    yum erase -y ${nerdctl_rpm}
    rc=${?}
    if [ ${rc} -ne 0 ]; then
        log_info "[Err] Uninstall nerdctl failed!"
        echo "[Err] Uninstall nerdctl failed!"
        exit 1
    fi
    log_info "[Info] Uninstall nerdctl successfully!"
    echo "[Info] Uninstall nerdctl successfully!"
}

function uninstall_cgsl_containerd_imagecheck() {
    # 使用yum尝试删除软件包，如果软件包未安装会报错误吗？
    # yum erase 命令会输出一个警告信息  package containerd.io is not installed
    yum erase -y ${cgsl_containerd_imagecheck_rpm}
    rc=${?}
    if [ ${rc} -ne 0 ]; then
        log_info "[Err] Uninstall cgsl_containerd_imagecheck failed!"
        echo "[Err] Uninstall cgsl_containerd_imagecheck failed!"
        exit 1
    fi
    log_info "[Info] Uninstall cgsl_containerd_imagecheck successfully!"
    echo "[Info] Uninstall cgsl_containerd_imagecheck successfully!"
}

function main() {
    rpm -q ${containerd_rpm}
    local rc=${?}
    if [ ${rc} -eq 0 ]; then
        uninstall_containerd
    fi
    rpm -q ${nerdctl_rpm}
    local rc=${?}
    if [ ${rc} -eq 0 ]; then
        uninstall_nerdctl
    fi
    rpm -q ${cgsl_containerd_imagecheck_rpm}
    local rc=${?}
    if [ ${rc} -eq 0 ]; then
        uninstall_cgsl_containerd_imagecheck
    fi
}

main
