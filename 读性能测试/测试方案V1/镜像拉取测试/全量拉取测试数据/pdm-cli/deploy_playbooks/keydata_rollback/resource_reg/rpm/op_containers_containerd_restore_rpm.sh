#!/usr/bin/bash

restore_dir="/paasdata/op-data/paas_upgrade_backup_keydata/rpm/op-containers-containerd"
gc_key="zncgsl6"
os_version=centos8
os_kylin=""
os_gc=""
containerd_imagecheck_keyword="cgsl-containerd-imagecheck"

log_info() {
    LOG_DIR="/paasdata/op-log/op-containers-containerd"
    LOG_DATE=$(date "+%Y-%m-%d %H:%M:%S,%3N")
    LOG_FILENAME="$LOG_DIR/containerd_scripts.log"
    filename=$(basename "$0")

    if [ ! -d "$LOG_DIR" ]; then
        mkdir -p "$LOG_DIR"
        chmod 750 "$LOG_DIR"
    fi
    if [ ! -f "$LOG_FILENAME" ]; then
        touch "$LOG_FILENAME"
        chmod 640 "$LOG_FILENAME"
    fi
    echo "${LOG_DATE} - ${filename} - $1" | tee -a "$LOG_FILENAME"
}

function decompress_containerd() {
    cd ${restore_dir} || exit 1
    tar -zxvf ./rpms.tar.gz
    rc=${?}
    if [ "${rc}" -ne 0 ]; then
         log_info "[Err] decompress containerd rpms failed!"
         echo "decompress containerd rpms failed!"
         exit 1
    else
         log_info "[Info] decompress containerd rpms successfully!"
         echo "decompress containerd rpms successfully!"
    fi
}

function get_os_version() {
    if [[ -e /etc/cgsl-release ]];then
        if sudo cat /etc/os-release | grep -w "^VERSION_ID" |grep "6.06";then
            os_gc="$gc_key"
        elif sudo cat /etc/os-release | grep -w "^VERSION_ID" |grep "5.04";then
            os_version=centos7
        fi
    else
        if sudo uname -r | grep "^3\.10";then
            os_version=centos7
        elif sudo uname -r |grep "^4\.19";then
            os_kylin=ky10
        elif sudo uname -r | grep "^5\.10";then
            os_gc="$gc_key"
        fi
    fi
}

function get_containerd_target_version() {
    if [ "$os_gc" = "$gc_key" ]; then
        rpm_containerd=$(ls "$restore_dir" | grep "containerd.*.rpm" |grep "$gc_key" | grep -v "$containerd_imagecheck_keyword")
        return
    fi
    if [ "$os_kylin" = ky10 ]; then
        rpm_containerd=$(ls "$restore_dir" | grep "containerd.*.rpm" |grep "\.el8" |grep "ky10" | grep -v "$containerd_imagecheck_keyword")
        return
    fi
    if [ "$os_version" = centos8 ]; then
        rpm_containerd=$(ls "$restore_dir" | grep "containerd.*.rpm" |grep "\.el8" |grep -v "ky10" | grep -v "$containerd_imagecheck_keyword")
        if [ -z "$rpm_containerd" ]; then
            rpm_containerd=$(ls "$restore_dir" | grep "containerd.*.rpm" |grep "\.cgsl6_2" | grep -v "$containerd_imagecheck_keyword")
        fi
        if [ -z "$rpm_containerd" ]; then
            rpm_containerd=$(ls "$restore_dir" | grep "containerd.*.rpm" |grep "$gc_key" | grep -v "$containerd_imagecheck_keyword")
        fi
        return
    fi
    rpm_containerd=$(ls "$restore_dir" | grep "containerd.*.rpm" |grep "\.el7" |grep -v "ky10" | grep -v "$containerd_imagecheck_keyword")
}

function install_containerd() {
    get_containerd_target_version

    rpm -ivh "$restore_dir"/"$rpm_containerd" --force
    rc=${?}
    if [ "${rc}" -ne 0 ]; then
         log_info "[Err] Install containerd failed!"
         echo "Install containerd failed!"
         exit 1
    else
         log_info "[Info] Install containerd successfully!"
         echo "Install containerd successfully!"
    fi
}

function get_nerdctl_target_version() {
    if [ "$os_gc" = "$gc_key" ]; then
        rpm_nerdctl=$(ls "$restore_dir" | grep "nerdctl-.*.rpm" |grep "$gc_key")
        return
    fi
    if [ "$os_kylin" = ky10 ]; then
        rpm_nerdctl=$(ls "$restore_dir" | grep "nerdctl-.*.rpm" |grep "\-el8" |grep "ky10")
        return
    fi
    if [ "$os_version" = centos8 ]; then
        rpm_nerdctl=$(ls "$restore_dir" | grep "nerdctl-.*.rpm" |grep "\-el8" |grep -v "ky10")
        if [ -z "$rpm_nerdctl" ]; then
            rpm_nerdctl=$(ls "$restore_dir" | grep "nerdctl-.*.rpm" |grep "\.cgsl6_2")
        fi
        if [ -z "$rpm_nerdctl" ]; then
            rpm_nerdctl=$(ls "$restore_dir" | grep "nerdctl-.*.rpm" |grep "$gc_key")
        fi
        return
    fi
    rpm_nerdctl=$(ls "$restore_dir" | grep "nerdctl-.*.rpm" |grep "\-el7" |grep -v "ky10")
}

function install_nerdctl() {
    get_nerdctl_target_version

    rpm -ivh "$restore_dir"/"$rpm_nerdctl" --force
    rc=${?}
    if [ "${rc}" -ne 0 ]; then
         log_info "[Err] Install nerdctl failed!"
         echo "Install nerdctl failed!"
         exit 1
    else
         log_info "[Info] Install nerdctl successfully!"
         echo "Install nerdctl successfully!"
    fi
}

function get_cgsl_containerd_imagecheck_target_version() {
    if [ "$os_gc" = "$gc_key" ]; then
        rpm_image_check=$(ls "$restore_dir" | grep "cgsl-containerd-imagecheck-.*.rpm" |grep "zncgsl6")
        return
    fi
    if [ "$os_kylin" = ky10 ]; then
        rpm_image_check=$(ls "$restore_dir" | grep "cgsl-containerd-imagecheck-.*.rpm" |grep "\-el8" |grep "ky10")
        return
    fi
    if [ "$os_version" = centos8 ]; then
        rpm_image_check=$(ls "$restore_dir" | grep "cgsl-containerd-imagecheck-.*.rpm" |grep "cgsl6_2" |grep -v "ky10")
        if [ -z "$rpm_image_check" ]; then
            rpm_image_check=$(ls "$restore_dir" | grep "cgsl-containerd-imagecheck-.*.rpm" |grep "\-el8" |grep -v "ky10")
        fi
        return
    fi
    rpm_image_check=$(ls "$restore_dir" | grep "cgsl-containerd-imagecheck-.*.rpm" |grep "\-el7" |grep -v "ky10")
}

function install_cgsl_containerd_imagecheck(){
    get_cgsl_containerd_imagecheck_target_version

    if [ -z "$rpm_image_check" ]; then
        log_info "[Info] \$rpm_image_check is empty, exiting function."
        echo "\$rpm_image_check is empty, exiting function."
        exit 0
    fi
    
    if [ "$os_kylin" = "ky10" ] && [ -z "$rpm_image_check" ]; then
        log_info "[Info] $os_kylin = ky10 exit 0"
        echo "$os_kylin = ky10 直接 exit 0"
        exit 0
    fi

    rpm -ivh "$restore_dir"/"$rpm_image_check" --force
    rc=${?}
    if [ "${rc}" -ne 0 ]; then
         log_info "[Err] Install cgsl_containerd_imagecheck failed!"
         echo "Install cgsl_containerd_imagecheck failed!"
         exit 1
    else
         log_info "[Info] Install cgsl_containerd_imagecheck successfully!"
         echo "Install cgsl_containerd_imagecheck successfully!"
    fi
}

function main() {
    decompress_containerd
    get_os_version
    install_containerd
    install_nerdctl
    install_cgsl_containerd_imagecheck
}

main
