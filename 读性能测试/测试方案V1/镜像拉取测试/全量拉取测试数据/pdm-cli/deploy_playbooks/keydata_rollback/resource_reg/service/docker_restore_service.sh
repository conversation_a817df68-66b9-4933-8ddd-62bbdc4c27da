#!/bin/bash

docker_service_file="/usr/lib/systemd/system/docker.service"
ipv6nat_service_file="/usr/lib/systemd/system/ipv6nat.service"
image_check_service_file="/usr/lib/systemd/system/image-check.service"

if [ -f "$docker_service_file" ];then
    systemctl enable docker.service
    systemctl restart docker.service
fi

if [ -f "$ipv6nat_service_file" ];then
    systemctl enable ipv6nat.service
    systemctl restart ipv6nat.service
fi

if [ -f "$image_check_service_file" ]; then
    systemctl enable image-check.service
fi