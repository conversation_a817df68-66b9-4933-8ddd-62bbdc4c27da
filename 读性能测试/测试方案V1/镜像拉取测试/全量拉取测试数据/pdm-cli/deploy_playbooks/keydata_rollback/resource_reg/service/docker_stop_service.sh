#!/bin/bash
# shellcheck disable=SC2009

if [[ $(systemctl is-enabled docker.service 2>/dev/null) == "enabled" ]];then
    systemctl disable docker.service
    if ! timeout 300 systemctl stop docker.service;then
        timeout 300 kill -9 "$(ps -ef | grep -v grep | grep '/usr/bin/dockerd' | awk '{print $2}')"
    fi
fi

if [[ $(systemctl is-enabled ipv6nat.service 2>/dev/null) == "enabled" ]];then
    systemctl disable ipv6nat.service
    systemctl stop ipv6nat.service
fi

if [[ $(systemctl is-enabled image-check.service 2>/dev/null) == "enabled" ]]; then
    systemctl disable image-check.service
    systemctl stop image-check.service
fi
