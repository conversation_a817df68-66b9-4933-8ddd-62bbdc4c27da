#!/bin/sh

if [ -f /etc/paas-release ];then
    if [ -f /etc/crontab ];then
        \cp -a /etc/crontab /etc/crontab.bak
        sed -i '/\/etc\/logrotate/d' /etc/crontab.bak
        if [ -s /etc/crontab.bak ];then
            \cp -a /etc/crontab.bak /etc/crontab
        else
            echo "crontab clear sysconfig failed"
            exit 1
        fi
    else
        echo "crontab sysconfig file does not exist"
        exit 0
    fi
fi
