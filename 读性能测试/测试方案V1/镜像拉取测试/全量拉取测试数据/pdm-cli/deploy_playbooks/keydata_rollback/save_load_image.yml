---
- hosts: nodes
  remote_user: ubuntu
  become: yes
  become_method: sudo
  gather_facts: no
  max_fail_percentage: 0
  vars:
    - image_backup_dir: /paasdata/op-data/paas_upgrade_backup_keydata/image
  tasks:
    - name: create image backup dir
      file:
        path: "{{image_backup_dir}}/{{pkg_name}}"
        state: directory
        mode: 0755
        recurse: yes
      ignore_errors: yes
      tags: save_image

    - name: "{{pkg_name}} - pull and archive image"
      docker_image:
        state: present
        name: "{{local_registry_address}}/{{reponame}}/{{pkg_name}}"
        tag: "{{version}}"
        archive_path: "{{image_backup_dir}}/{{pkg_name}}/image.tar"
        source: pull
        timeout: 300
      ignore_errors: yes
      tags: save_image

    - name: "{{pkg_name}} - load and tag image"
      docker_image:
        state: present
        name: "{{local_registry_address}}/{{reponame}}/{{pkg_name}}"
        tag: "{{version}}"
        load_path: "{{image_backup_dir}}/{{pkg_name}}/image.tar"
        source: load
        timeout: 300
      ignore_errors: yes
      tags: load_image
