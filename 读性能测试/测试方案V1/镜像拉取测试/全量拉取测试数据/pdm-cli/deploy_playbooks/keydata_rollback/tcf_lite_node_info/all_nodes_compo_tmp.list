["op-containers-containerd", "docker", "op-ubs-python3lib", "pacemaker_cluster", "cf-zartcli", "zenap_msb_consul_server", "zenap_msb_sdclient", "zenap_msb_apigateway", "zenap_msb_router", "postgresql", "ubu_rabbit", "op-ubs-dbtools", "k8s", "op-nw-kubecni", "op-containers-coredns", "op-nw-multus", "op-nw-flannel", "op-security-certcenter", "op-asd-swr", "op-asm-operator", "op-asm-helm", "op-security-k8shook", "op-ops-monitor-hawkeye", "op-ops-tools", "op-asd-cosg", "op-ops-fm-mgt", "op-ops-toposervice", "op-ops-event-eventmgt", "inetproxy", "op-security-psecret", "op-portal-tcfportal", "op-ubs-daisy-pg", "op-security-gateway", "op-psd-keystone", "op-ops-log-collectormgt", "op-asm-asm", "op-security-project", "op-psd-pconf", "op-ops-metric-victoriametrics-single", "op-cnrm-manager", "op-security-serviceaccount", "op-security-certcenter", "op-portal-regiui", "op-psd-provider", "op-nw-nwmaster", "op-ops-event-eventagt", "op-nw-nwnode", "op-nw-policy-chart", "op-nw-knitter-vpn-chart", "dpdk_deps", "op-containers-eviction", "op-containers-k8sconfig-webhook", "op-containers-k8sconfig", "op-cnrm-cemk", "openjdk", "in<PERSON><PERSON><PERSON>", "underpan", "op-ops-monitor-hawkeye-agt"]