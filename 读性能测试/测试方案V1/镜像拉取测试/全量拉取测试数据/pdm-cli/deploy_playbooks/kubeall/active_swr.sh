#!/bin/bash

# Started by AICoder, pid:uf52fw35c01c41014d7b09539076f3783ea6c066

swr_port=$6
ZARTPORT=$swr_port

log_file=/var/log/pdm-cli.log

log_error() {
  LOG_DATE=$(date "+%Y-%m-%d %H:%M:%S,%N")
  # shellcheck disable=SC2145
  echo "$LOG_DATE - ERROR - $@" >>$log_file
}

log_info() {
  LOG_DATE=$(date "+%Y-%m-%d %H:%M:%S,%N")
  # shellcheck disable=SC2145
  echo "$LOG_DATE - INFO - $@" >>$log_file
}

function wait_for_swr_readyness() {
  swr_svc_ready=
  while [[ -z $swr_svc_ready ]]; do
    current_time=$(date +%s)
    elapsed_time=$((current_time - start_time))
    if ((elapsed_time > timeout_seconds)); then
      log_error "Timeout of $timeout_seconds seconds reached. Exiting script."
      exit 1
    fi

    swr_svc_ip=$(kubectl get service/swr -n admin -o jsonpath='{.spec.clusterIP}')
    if [[ $swr_svc_ip =~ .*:.* ]]; then
      # IPv6
      IP="[$swr_svc_ip]"
    else
      IP=$swr_svc_ip
    fi
    swr_status=$(curl --connect-timeout 5 -o /dev/null -s -w "%{http_code}\n" http://$IP:$ZARTPORT/swr/v1/readiness)
    if [ "$swr_status" -eq 200 ]; then
      swr_svc_ready=ready
    else
      log_info "swr status not ready yet. Retrying in 5 seconds..."
      sleep 5
    fi
  done
}

timeout_seconds=3600
start_time=$(date +%s)


log_info "Waiting for swr service to be ready..."

swr_svc_ready=
while [[ -z $swr_svc_ready ]]; do
  current_time=$(date +%s)
  elapsed_time=$((current_time - start_time))
  if ((elapsed_time > timeout_seconds)); then
    log_error "Timeout of $timeout_seconds seconds reached. Exiting script."
    exit 1
  fi

  kubectl get service/swr -n admin >>/dev/null 2>&1
  if [ $? -ne 0 ]; then
    log_info "swr service not ready yet. Retrying in 5 seconds..."
    sleep 5
  else
    swr_svc_ready="ready"
  fi
done

wait_for_swr_readyness

log_info "The swr is ready!"
exit 0

# Ended by AICoder, pid:uf52fw35c01c41014d7b09539076f3783ea6c066
