- cp_name:
- component_port:
- domain_name: "psm.psm"
- deploy_type: "service"
- replicas: -1

- op_scale:
- resource:
    cpu_shares:
    cpu_quota:
    memory_limit:

- liveness:
    check_period:
    check_type:
      type:
      value:
    recovery_script:

- component_confdir: "{{op_platcom_confdir}}/{{cp_name}}"
- component_datadir: "{{op_platcom_datadir}}/{{cp_name}}"
- component_logdir: "{{op_platcom_logdir}}/{{cp_name}}"

- component_datadir_cfg:
    quota:
    clean_script:
