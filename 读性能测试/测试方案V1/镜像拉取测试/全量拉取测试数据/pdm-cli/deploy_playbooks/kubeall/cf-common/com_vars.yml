---
- op_platcom_confdir: /paasdata/op-conf
- op_platcom_logdir: /paasdata/op-log
- op_platcom_datadir: /paasdata/op-data
- op_host_localtime: /etc/localtime
- op_platcom_tmpdatadir: /paasdata/op-tmp
- op_platcom_commondir: /paasdata/op-common
- op_commonsrv_workdir: /paasdata/op-comsrv
- op_tenant_workdir: /paasdata/op-tenant

- netapi_ip_stack: v4
- netapi_default_ip_version: v4
- netapi_netmask:
- netapi_v4_netmask:
- netapi_v6_netmask:

- overlay_netapi_cidr:
- overlay_netapi_cidr_v4:
- overlay_netapi_cidr_v6:

- netmgt_ip_stack: v4
- netmgt_default_ip_version: v4
- netmgt_netmask:
- netmgt_v4_netmask:
- netmgt_v6_netmask:

- netsto_ip_version: v4
- netsto_mgt_ip_version: v4
- netadmin_ip_version: v4

- services_range_start: 28001
- services_range_end: 29800

- common_services_range_start: 4500
- common_services_range_end: 4999

- uniview_range_start: 8021
- uniview_range_end: 8035

- kafka_ip:
- kafka_mgt_ip:

- elasticsearch_ip:

- log_agent_ip:

- tcfs_aerospike_ip:
- tcfs_database_ip:

- k8s_ip:

- openshift_ip:

- iportal_ip:
- iportaladmin_ip:
- utm_ip:

- cfy_srv_ip:

- nwmaster_ip:

- vp_ip:
- ubu_rabbit_ip:
- ubu_mysql_ip:

- etcd_ip:
- cradle_master_ip:

- pm_mgt_ip:

- tcfs_server_ip:

- fm_mgt_ip:

- swr_registry_ip:

- eps_agent_ip:

- eps_ip:

- zenap_msb_consul_server_ip:

- zenap_msb_sdclient_ip:

- zenap_msb_router_ip:
- zenap_msb_router_ip_v6:

- zenap_modeldesign_ip:

- keybox_ip:

- swr_ip:

- harvestor_ip:

- storage_ip:

- nfs_server_ip:

- proton_ip:

- monitor_ip:

- openpalette_service_ip:

- openpalette_service_query_ip:

- log_mgt_ip:

- lpim_ip:

- oamproxy_ip:

- logserver_ip:

- ha_enable: false

- network_mode: underlay

- bearer_mode: iaas

- logstash_shipper_ip:

- toposerver_ip:

- pmc_ip:

- eventmgt_ip:

- vnm_vip:

- dns_search_domains:

- br_ip:

- br_mysql_agent_ip:

- scale: large
- scale_out: false
- all_in_one: no
- use_elk: true
- TZ: Asia/Shanghai
- OpenPalette_Day0_env: normal
- use_app_router: false
- app_router_floatingip:
- app_router_vip:
- router_node_number:
- oplog_language: l1:CN,l2:EN
- paas_storage_type: local
- iaas_auth_url:
- iaas_username:
- iaas_password:
- iaas_region:
- iaas_tenant_id:
- iaas_tenantname:
- slb_enable: false
- lvs_enable: false

- k8s_cinder:

- etcd_vip:
- zenap_cos_postgresql_ip:
- com_rule_change: false
- mysql_password_change: false


- application_scenario: default
- op_commonsrv_logdir: /paasdata/op-comsrv/log
- op_commonsrv_datadir: /paasdata/op-comsrv/data
- cs_cipher: off
- inetmanager_vip:

- db_scheme: postgresql
- secret_key:
- netapi_cidr:
- netapi_v4_cidr:
- netapi_v6_cidr:
- netmgt_cidr:
- netmgt_v4_cidr:
- netmgt_v6_cidr:
- netsto_cidr:
- netadmin_cidr:
- http_disable: false
- reboot_nodes_dir: /paasdata/op-data/pdm-cli/reboot_nodes
- zenap_msb_router_disable_listen_ports: '22,67,123,162,547,9131,9141,49602,49603,49604,49605'
- postgresql_ip:
- slb_deploy_timeout: 7200
- net_plane_changed: false
- pgpwdneedencryption: false

- share_config_path: /paasdata/op-conf/share
- share_config_ca_path: /paasdata/op-conf/share/ca
- ca_ubu_rabbit_dir: /paasdata/op-conf/share/ca/ubu_rabbit
- ubu_rabbit_ssl: false

- minion_user_reserved_default_cpu:
- minion_user_reserved_default_mem:
- controller_user_reserved_default_cpu:
- controller_user_reserved_default_mem:

- infraserver: false
- neutron_service_ip:
- neutron_service_port:
- ironic_service_ip:
- ironic_service_port:
- public_vip:
- keystone_security_auth: enable
