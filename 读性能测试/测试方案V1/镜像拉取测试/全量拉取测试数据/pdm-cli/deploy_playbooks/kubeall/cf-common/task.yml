---
# do not change dir mode if exists
- name: create {{cf_common_path}}
  file:
    path: "{{cf_common_path}}"
    state: directory
    owner: tcf
    group: tcf
    mode: '0755'
- name: check for com_vars.yml at {{ cf_common_path }} on local host
  stat:
    path: "{{ cf_common_path }}/com_vars.yml"
  delegate_to: localhost
  register: com_vars_local
- name: set com_vars_file to use
  set_fact:
    com_vars_file_to_use: "{{ com_vars_local.stat.exists | ternary(cf_common_path + '/com_vars.yml', 'com_vars.yml') }}"
- name: copy com_vars.yml to {{ cf_common_path }}
  copy:
    src: "{{ com_vars_file_to_use }}"
    dest: "{{ cf_common_path }}/com_vars.yml"
    owner: tcf
    group: tcf
    mode: 0755

- name: unarchive volume info
  unarchive: src=info.tar.gz dest={{dest_path}} extra_opts="-h" copy=yes mode=0750
- name: mkdir /etc/hostip
  file: path=/etc/hostip state=directory mode=0750
- name: generate api ip file
  template: src=api.j2 dest=/etc/hostip/api
- name: generate mgt ip file
  template: src=mgt.j2 dest=/etc/hostip/mgt

- name: check for pdm-cli at {{ logrotate_path }} on local host
  stat:
    path: "{{ logrotate_path }}/pdm-cli"
  delegate_to: localhost
  register: pdm_cli_local
- name: set pdm_cli_file_to_use
  set_fact:
    pdm_cli_file_to_use: "{{ pdm_cli_local.stat.exists | ternary(logrotate_path + '/pdm-cli', 'pdm-cli.logrotate') }}"
- name: copy pdm-cli.logrotate to {{ logrotate_path }}
  copy:
    src: "{{ pdm_cli_file_to_use }}"
    dest: "{{ logrotate_path }}/pdm-cli"
    mode: 0644
    owner: root
    group: root

- name: change file mode to 640
  file:
    path: /var/log/ansible.log
    state: touch
    mode: 0640
