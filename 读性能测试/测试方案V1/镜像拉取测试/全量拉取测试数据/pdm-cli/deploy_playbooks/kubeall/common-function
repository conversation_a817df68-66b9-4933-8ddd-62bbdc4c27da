#!/bin/bash

log_file=/var/log/pdm-cli.log
log_path=/var/log/pdm-cli.log

log_info() {
  LOG_DATE=$(date "+%Y-%m-%d %H:%M:%S,%N")
  echo "$LOG_DATE - INFO - $@" >>$log_file
}

log_debug() {
  LOG_DATE=$(date "+%Y-%m-%d %H:%M:%S,%N")
  echo "$LOG_DATE - DEBUG - $@" >>$log_file
}

log_error() {
  LOG_DATE=$(date "+%Y-%m-%d %H:%M:%S,%N")
  echo "$LOG_DATE - ERROR - $@" >>$log_file
}

function cmd_retry_with_interval {
  log_debug "重试次数: $retry_times" "重试间隔: $retry_interval"
  local n=1
  local max=5
  local delay=1
  if [[ -n "$retry_times" ]]; then
    local max=$retry_times
  fi
  if [[ -n "$retry_interval" ]]; then
    local delay=$retry_interval
  fi
  while true; do
    "$@" >>$log_file 2>&1 && break || {
      if [[ $n -lt $max ]]; then
        ((n++))
        sleep $delay
      else
        log_error "$@"
        exit 1
      fi
    }
  done
}
