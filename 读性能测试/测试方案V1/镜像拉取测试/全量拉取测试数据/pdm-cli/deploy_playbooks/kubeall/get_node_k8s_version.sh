#!/bin/bash
set -x

if command -v jq &> /dev/null; then
    JQ_CMD="jq"
elif command -v jq-linux64; then
    JQ_CMD="jq-linux64"
else
    echo "Error: jq is not installed and jq-linux64 is not found."
    exit 1
fi
vers=$(curl --unix-socket /paasdata/op-data/processd/kubeall/run/hub/processd/sock/forPod/processd.sock -X GET http://localhost/api/v1/process/all/all/list 2>/dev/null | $JQ_CMD -e '.[] | select(.name == "k8s" and (.["live-status"] // "") == "running")' | $JQ_CMD -r ".version")

if [ "$vers" == "$1" ]; then
    exit 0
else
    exit 1
fi
