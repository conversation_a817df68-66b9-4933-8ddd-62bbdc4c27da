#!/bin/bash
export KUBECONFIG=~/.kube/config

chart_name=$1
chart_version=$2
desired_status=$3


desired_chart_version="${chart_name}-${chart_version}"
current_chart_info=$(helm list -n admin --filter "^"${chart_name}"$" -o json 2>/dev/null)
current_chart_version=$(echo ${current_chart_info} | jq -r '.[0]["chart"]')
if [ "$desired_chart_version" != "$current_chart_version" ];then
  exit 1
fi

current_status=$(echo ${current_chart_info} | jq -r '.[0]["status"]')

if [ "$desired_status" != "$current_status" ];then
  exit 1
fi

exit 0
