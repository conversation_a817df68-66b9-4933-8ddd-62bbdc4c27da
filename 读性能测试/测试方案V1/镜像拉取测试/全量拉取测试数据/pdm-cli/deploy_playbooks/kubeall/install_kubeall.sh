#!/bin/bash

openpalette_version=$1
op_platcom_chart_url_prefix=$2
extra_values_yaml=$3

process_ctrl_image_name=$4
process_ctrl_image_version=$5

SCRIPT_DIR=$(
  cd "$(dirname "$0")"
  pwd
)
cd $SCRIPT_DIR
chmod +x common-function
. common-function

# 编排器、控制器部署
retry_times=180
retry_interval=5

function apply_processd_cm() {
  kubectl apply --kubeconfig=/root/.kube/config -f - <<EOF
apiVersion: v1
kind: ConfigMap
metadata:
  name: op-platcom-vars-processd
  namespace: admin
  annotations:
    meta.helm.sh/release-name: processd
    meta.helm.sh/release-namespace: admin
  labels:
    app.kubernetes.io/managed-by: Helm
    openpalette.vars/op-platcom: "true"
data:
  op-platcom-vars: |
    process_ctrl_image_name: "$process_ctrl_image_name"
    process_ctrl_image_version: "$process_ctrl_image_version"
    process_ctrl_lifecycle:
      postStart:
        exec:
          command:
            - kurl
            - -X
            - POST
            - --url
            - http://localhost:5001/api/v1/process/my/self
      preStop:
        exec:
          command:
            - kurl
            - -X
            - DELETE
            - --url
            - http://localhost:5001/api/v1/process/my/self
    process_ctrl_livenessProbe:
      httpGet:
        path: /api/v1/process/my/self/liveness
        port: 5001
        scheme: HTTP
      initialDelaySeconds: 15
      periodSeconds: 40
      timeoutSeconds: 10
    process_ctrl_readinessProbe:
      httpGet:
        path: /api/v1/process/my/self/readiness
        port: 5001
        scheme: HTTP
      initialDelaySeconds: 15
      periodSeconds: 40
      timeoutSeconds: 10
    process_ctrl_volumeMounts:
      - mountPath: /paasdata/op-data/processd/kubeall/run/hub/processd/sock/forPod
        name: kubeall-volume
      - mountPath: /paasdata/op-data/kubeall/sock
        name: kubeconfig-volume
      - mountPath: /paasdata/op-log/kubeall-process-ctrl/
        name: op-log
    process_ctrl_volumes:
      - name: kubeall-volume
        hostPath:
          path: /paasdata/op-data/processd/kubeall/run/hub/processd/sock/forPod
      - name: kubeconfig-volume
        hostPath:
          path: /paasdata/op-data/kubeall/sock
      - name: op-log
        hostPath:
          path: /paasdata/op-log/kubeall-process-ctrl/
EOF
}

cmd_retry_with_interval apply_processd_cm

function wait_for_kubeall_ready() {
  if kubectl get configmap -n admin op-component-vars-by-dbtools; then
    log_info "The kubeall is is ready!"
  else
    log_info "The kubeall is is not ready!"
    return 1
  fi
}

cmd_retry_with_interval wait_for_kubeall_ready

exit 0
