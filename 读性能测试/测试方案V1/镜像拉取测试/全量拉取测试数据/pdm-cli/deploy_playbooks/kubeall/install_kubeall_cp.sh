#!/bin/bash
openpalette_version=${1}
op_platcom_chart_url_prefix=${2}
extra_values_yaml=${3}

cp_name=${4}
cp_version=${5}
chart_version=${6}
subdomain=${7}
controller_group=${8}
controller_group_ha_count=${9}
reponame=${10}

SCRIPT_DIR=$(
  cd "$(dirname "$0")" || exit
  pwd
)
cd "$SCRIPT_DIR" || exit
chmod +x common-function

# shellcheck source=/dev/null
. common-function

# 定义获取hook参数的函数
function get_hooks() {
  local component_name=$1
  # 先执行kubectl命令并捕获错误信息
  hooks=$(kubectl get cm -n admin op-component-kubeall-hook-vars -o jsonpath="{.data.${component_name}}" 2>&1)
  local ret_code=$?

  # 判断返回码
  if [ $ret_code -eq 0 ]; then
    # 正常获取到ConfigMap，提取hook参数
    cp_pre_upgrade_hook=$(echo "$hooks" | grep "cp_pre_upgrade_hook" | awk -F": " '{print $2}' | tr -d ' ' | sed 's/"//g')
    cp_post_upgrade_hook=$(echo "$hooks" | grep "cp_post_upgrade_hook" | awk -F": " '{print $2}' | tr -d ' ' | sed 's/"//g')
    cp_pre_rollback_hook=$(echo "$hooks" | grep "cp_pre_rollback_hook" | awk -F": " '{print $2}' | tr -d ' ' | sed 's/"//g')
    cp_post_rollback_hook=$(echo "$hooks" | grep "cp_post_rollback_hook" | awk -F": " '{print $2}' | tr -d ' ' | sed 's/"//g')
    return 0
  elif [[ $ret_code -ne 0 && "$hooks" == *"NotFound"* ]]; then
    # ConfigMap未找到，保持现有逻辑
    log_debug "Helm install: op-component-kubeall-hook-vars NotFound"
    cp_pre_upgrade_hook=no
    cp_post_upgrade_hook=no
    cp_pre_rollback_hook=no
    cp_post_rollback_hook=no
    return 0
  else
    # 其他错误返回码并且错误信息不包含NotFound
    log_error "Helm install: Error occurred while fetching op-component-kubeall-hook-vars with exit code $ret_code"
    log_error "Error details: $hooks"
    return $ret_code
  fi
}

check_task_status() {
    local configmap_name="$1"
    local namespace="$2"
    local ret_code

    # Try to get the tasks from the configmap
    configmap_output=$(kubectl get configmap "$configmap_name" -n "$namespace" -o jsonpath='{.data.tasks}' 2>&1)
    ret_code=$?

    if [[ $ret_code -ne 0 ]]; then
        if [[ "$configmap_output" == *"NotFound"* ]]; then
            log_debug "ConfigMap '$configmap_name' does not exist in namespace '$namespace'. Exiting check."
            return 0
        else
            log_debug "Error occurred while executing 'kubectl get configmap': $configmap_output. Exiting check."
            return 2
        fi
    fi

    # Check if tasks contain failed status
    if echo "$configmap_output" | grep -q "status: failed"; then
        log_error "Task failed in ConfigMap '$configmap_name'. Exiting with status 1."
        exit 1
    else
        log_debug "No failed tasks found in ConfigMap '$configmap_name'."
        return 0
    fi
}


function deploy_chart() {
  # 获取hook参数
  get_hooks "$cp_name" || return $?

  helm_command="helm upgrade --install -n admin \"$cp_name\" oci://\"$op_platcom_chart_url_prefix\"/\"$cp_name\" \
    --version \"$chart_version\" \
    --kubeconfig /root/.kube/config \
    --set openpalette_version=\"$openpalette_version\" \
    --set cp_name=\"$cp_name\" \
    --set cp_version=\"$cp_version\" \
    --set subdomain=\"$subdomain\" \
    --set controller_group=\"$controller_group\" \
    --set controller_group_ha_count=\"$controller_group_ha_count\" \
    --set reponame=\"$reponame\" \
    --set cp_pre_upgrade_hook=\"$cp_pre_upgrade_hook\" \
    --set cp_post_upgrade_hook=\"$cp_post_upgrade_hook\" \
    --set cp_pre_rollback_hook=\"$cp_pre_rollback_hook\" \
    --set cp_post_rollback_hook=\"$cp_post_rollback_hook\" \
    -f \"$extra_values_yaml\" 2>&1"

  # 打印完整命令
  echo "Executing command: $helm_command"

  # 执行命令
  upgrade_output=$(eval $helm_command)
  upgrade_status=$?

  # 检查helm upgrade是否成功
  if [ $upgrade_status -eq 0 ]; then
    log_debug "Helm upgrade successful."
    return 0
  else
    log_debug "Helm upgrade failed with status $upgrade_status, output: ($upgrade_output)"

    # 检查输出是否包含特定的错误
    if echo "$upgrade_output" | grep -q "is in progress"; then
      sleep 2
      log_debug "Detected a \"in progress\" error, uninstalling the release..."
      helm uninstall -n admin "$cp_name" --kubeconfig /root/.kube/config
    else
      log_error "An error occurred that cannot be handled by retry logic."
    check_task_status "workflow-resource-process-cim" "admin"
    fi
    return $upgrade_status
  fi
}

retry_times=180
retry_interval=5
cmd_retry_with_interval deploy_chart

exit 0
