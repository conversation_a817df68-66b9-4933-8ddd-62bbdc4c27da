#!/bin/bash

# kube_vip_name=net-debug-paas-vip-v4
# vip_name=net_debug_paas_vip_v4
# vip_addr=************
# vip_netmask=16

SCRIPT_DIR=$(
  cd "$(dirname "$0")"
  pwd
)
cd $SCRIPT_DIR
chmod +x common-function
. common-function



kube_vip_name=$1
vip_name=$2
vip_addr=$3
vip_netmask=$4

function apply_kube_vip() {
kubectl apply --kubeconfig=/root/.kube/config -f - <<EOF
apiVersion: kubeall.io/v1
kind: KubeallVirtualIp
metadata:
  name: $kube_vip_name
  namespace: admin
  labels:
    openpalette.cp/managed-by: pdm-cli
spec:
  args:
  - --vip_name=$vip_name
  nodeAffinity:
    requiredDuringSchedulingIgnoredDuringExecution:
      nodeSelectorTerms:
      - matchExpressions:
        - key: openpalette.role/paas_controller
          operator: In
          values:
          - "true"
  vIPMode: pacemaker
  vIPs:
  - ip: $vip_addr
    netmask: $vip_netmask
EOF
}

retry_times=180
retry_interval=5
cmd_retry_with_interval apply_kube_vip



exit 0
