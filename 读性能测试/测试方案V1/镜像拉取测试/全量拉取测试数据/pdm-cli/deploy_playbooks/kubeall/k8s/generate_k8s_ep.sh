#!/bin/bash



configFileName=$1
paas_controller_mgt_ip=$2

templateFile=${configFileName}.template
tmpFile=${configFileName}.tmp


rm -f $tmpFile
cp $templateFile $tmpFile
paas_controller_mgt_ip=$paas_controller_mgt_ip
ips=$(echo $paas_controller_mgt_ip | tr -d ' ')
export IFS=","

for ip in $ips; do
    sed -i "/- addresses:/a\\  - ip: ${ip}"   $tmpFile
done


cp -f $tmpFile $configFileName
rm -f $tmpFile
