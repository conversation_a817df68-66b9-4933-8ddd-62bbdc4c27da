---
- hosts: nodes
  remote_user: ubuntu
  become: yes
  become_method: sudo
  gather_facts: no

  tasks:
    - name: Check if current node is not a controller
      debug:
        msg: "This node is a paas controller, skipping tasks."
      when: inventory_hostname in groups['paas_controllers']

    - name: Move kube-proxy manifest and kill existing kube-proxy process
      shell: |
        mv /etc/kubernetes/manifests/kube-proxy.yml /etc/kubernetes/kube-proxy.yml
        ps -ef|grep kube-proxy|grep -v grep|awk -F' ' '{print $2}'|xargs -rI {} kill -9 {}
      when: inventory_hostname not in groups['paas_controllers']
      ignore_errors: yes  # 防止没有进程时报错

    - name: Extract image from kube-proxy manifest
      shell: |
        cat /etc/kubernetes/kube-proxy.yml |grep image:|awk -F' ' '{print $2}'
      register: kube_proxy_image
      when: inventory_hostname not in groups['paas_controllers']

    - name: Extract bind-address from kube-proxy manifest
      shell: |
        cat /etc/kubernetes/kube-proxy.yml |grep "\-\-bind-address"|awk -F '=' '{print $2}'
      register: kube_proxy_ipaddr
      when: inventory_hostname not in groups['paas_controllers']

    - name: Run kube-proxy cleanup using Docker
      shell: |
        docker run --rm --privileged --name kube-proxy-cleanup \
          -v /lib/modules:/lib/modules \
          -v /paasdata/op-log/k8s/kube-proxy.klog:/paasdata/op-log/k8s/kube-proxy.klog \
          -v /etc/kubernetes/proxy.kubeconfig:/etc/kubernetes/proxy.kubeconfig \
          --net=host \
          {{ kube_proxy_image.stdout }} \
          /usr/local/bin/kube-proxy \
          --cleanup=true \
          --log-file=/paasdata/op-log/k8s/kube-proxy.klog \
          --logtostderr=false \
          --alsologtostderr=false \
          --kubeconfig=/etc/kubernetes/proxy.kubeconfig \
          --bind-address={{ kube_proxy_ipaddr.stdout }} || echo "kube-proxy-cleanup failed"
      ignore_errors: yes
      when: inventory_hostname not in groups['paas_controllers']
