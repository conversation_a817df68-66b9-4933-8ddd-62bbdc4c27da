apiVersion: v1
kind: Service
metadata:
  annotations:
    com.zte.zenap.msb/service: '{ "namespace": "default","version": "v1","publish_port":
      "2601","protocol":"TCP", "visualRange":"0" }'
  labels:
    component: apiserver
    provider: kubernetes
  name: kubernetes-admin
  namespace: admin
spec:
  internalTrafficPolicy: Cluster
  ports:
    - name: https
      port: 443
      protocol: TCP
      targetPort: 6443
  sessionAffinity: None
  type: ClusterIP