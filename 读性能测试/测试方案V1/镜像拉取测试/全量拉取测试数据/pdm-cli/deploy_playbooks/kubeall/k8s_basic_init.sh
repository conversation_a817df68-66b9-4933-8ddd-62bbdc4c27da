#!/bin/bash

paas_controller_mgt_ip=${1}
initial_swr_ip=${2}
controller_nodes_num=${3}
openpalette_version=${4}
target_cim_scale=${5}
controllergroup_01_mgt_ip=${6}
controllergroup_02_mgt_ip=${7}
controllergroup_03_mgt_ip=${8}
controllergroup_04_mgt_ip=${9}
controllergroup_05_mgt_ip=${10}
controllergroup_06_mgt_ip=${11}
need_add_taint="false"

log_file=/var/log/pdm-cli.log

log_info() {
  LOG_DATE=$(date "+%Y-%m-%d %H:%M:%S,%N")
  # shellcheck disable=SC2145
  echo "$LOG_DATE - INFO - $@" >>$log_file
}

log_error() {
  LOG_DATE=$(date "+%Y-%m-%d %H:%M:%S,%N")
  # shellcheck disable=SC2145
  echo "$LOG_DATE - ERROR - $@" >>$log_file
}

function cmd_retry_with_interval {
  local n=1
  local max=5
  local delay=1
  if [[ -n "$retry_times" ]]; then
    local max=$retry_times
  fi
  if [[ -n "$retry_interval" ]]; then
    local delay=$retry_interval
  fi
  while true; do
    "$@" >>$log_file 2>&1 && break || {
      if [[ $n -lt $max ]]; then
        ((n++))
        sleep $delay
      else
        log_error "$@"
        exit 1
      fi
    }
  done
}

if [[ "${target_cim_scale}" == "scale_1" ]] || [[ "${target_cim_scale}" == "scale_3" ]]; then
  log_info "target_cim_scale is $target_cim_scale"
else
  log_error "target_cim_scale is $target_cim_scale, not support"
  exit 1
fi

# 命名空间
function create_admin_ns() {
  kubectl get namespace default >/dev/null 2>&1 || return
  kubectl get namespace admin >/dev/null 2>&1 || kubectl create namespace admin
}
retry_times=720
retry_interval=5
cmd_retry_with_interval create_admin_ns

function get_is_paas_controller() {
  local node_ip_info=${1}
  is_paas_controller="no"
  local paas_controller_mgt_ip=$(echo ${paas_controller_mgt_ip} | tr "," '\n')
  for ip in $paas_controller_mgt_ip; do
    # shellcheck disable=SC2076
    for node_ip in $node_ip_info; do
      if [[ "${node_ip}" == "${ip}" ]]; then
        is_paas_controller="yes"
      fi
    done
  done
  echo "$is_paas_controller"
}

function ip_is_in_controllergroup() {
  node_ip_info=$1
  controllergroup_ips=$2
  local ips=$(echo ${controllergroup_ips} | tr "," '\n')
  in_group="no"
  for ip in $ips; do
    # shellcheck disable=SC2076
    for node_ip in $node_ip_info; do
      if [[ "${node_ip}" == "${ip}" ]]; then
        in_group="yes"
      fi
    done
  done
  echo "$in_group"
}

function get_controller_group() {
  local node_ip_info=${1}
  if [[ $(ip_is_in_controllergroup "$node_ip_info" "$controllergroup_01_mgt_ip") == "yes" ]]; then
    echo "controllergroup-01"
  fi
  if [[ $(ip_is_in_controllergroup "$node_ip_info" "$controllergroup_02_mgt_ip") == "yes" ]]; then
    echo "controllergroup-02"
  fi
  if [[ $(ip_is_in_controllergroup "$node_ip_info" "$controllergroup_03_mgt_ip") == "yes" ]]; then
    echo "controllergroup-03"
  fi
  if [[ $(ip_is_in_controllergroup "$node_ip_info" "$controllergroup_04_mgt_ip") == "yes" ]]; then
    echo "controllergroup-04"
  fi
  if [[ $(ip_is_in_controllergroup "$node_ip_info" "$controllergroup_05_mgt_ip") == "yes" ]]; then
    echo "controllergroup-05"
  fi
  if [[ $(ip_is_in_controllergroup "$node_ip_info" "$controllergroup_06_mgt_ip") == "yes" ]]; then
    echo "controllergroup-06"
  fi
}

function label_nodes() {
  node_num=$(kubectl get nodes -o json | jq '.items | length')
  for ((k = 0; k < ${node_num}; k++)); do
    node_name=$(kubectl get nodes -o json | jq --argjson n "$k" '.items[$n].metadata.name')
    node_ip_info=$(kubectl get nodes -o json | jq --argjson name "$node_name" '.items[].status.addresses |  select(.[].address==$name)' | jq -r '.[].address')
    is_paas_controller=$(get_is_paas_controller "$node_ip_info")
    controller_group=$(get_controller_group "$node_ip_info")
    node_name=$(echo "${node_name}" | tr -d '"')
    node_labels=$(kubectl get node ${node_name} -o jsonpath='{.metadata.labels}')
    if echo "${node_labels}" | grep -q 'nodename-nodecr\.zedge\.io\/edge'; then
      # 如果节点包含标签"nodename-nodecr.zedge.io/edge"，则跳过此节点
      continue
    fi

    if [ "${is_paas_controller}" = "yes" ]; then
      kubectl label nodes ${node_name} openpalette.role/paas_controller=true --overwrite || return
      kubectl label nodes ${node_name} openpalette.role/controllergroup=$controller_group --overwrite || return
      kubectl label nodes ${node_name} openpalette.io/version=$openpalette_version --overwrite || return
      if [ "${target_cim_scale}" = "scale_1" ]; then
        kubectl label nodes ${node_name} openpalette.role/soft-repo=true --overwrite || return
        kubectl label nodes ${node_name} openpalette.role/master=true --overwrite || return
      fi
      if [ "${target_cim_scale}" = "scale_3" ]; then
        if [ "${controller_group}" = "controllergroup-01" ]; then
          kubectl label nodes ${node_name} openpalette.role/soft-repo=true --overwrite || return
        fi
        if [ "${controller_group}" = "controllergroup-02" ]; then
          kubectl label nodes ${node_name} openpalette.role/soft-repo-  || return
          kubectl label nodes ${node_name} openpalette.role/master=true --overwrite || return
        fi
      fi
      if [ "${need_add_taint}" = "true" ]; then
        kubectl label nodes ${node_name} openpalette.node/platform=true --overwrite  || return
        kubectl taint nodes ${node_name} openpalette.node/platform=:NoExecute --overwrite || return
      fi
    fi

    # shellcheck disable=SC2076
    if [[ "${node_ip_info}" =~ "${initial_swr_ip}" ]]; then
      kubectl label nodes ${node_name} openpalette.roles/initial-soft-repo-node=true --overwrite
    fi
  done
}

function check_need_add_taint(){
  node_role=$(cat /etc/paas/config.d/meta_data.json| jq -r '.basic.node_role')
  if [[ "$node_role" =~ "Minion" ]]; then
    need_add_taint="false"
  else
    need_add_taint="true"
  fi
  log_info "need_add_taint=$need_add_taint"
}

function check_lable_nodes_ok() {
  check_need_add_taint || return
  label_nodes || return
  ready_num=$(kubectl get nodes -l openpalette.role/paas_controller=true -o json | jq '.items | length')
  if [[ ${ready_num} < ${controller_nodes_num} ]]; then
    return 1
  fi
  return 0
}

retry_times=180
retry_interval=5
cmd_retry_with_interval check_lable_nodes_ok

function create_priorityclass() {
  # create priorityclass
  kubectl get priorityclass plat-priority-high >/dev/null 2>&1 || kubectl create priorityclass plat-priority-high --value=999 || return
  kubectl get priorityclass plat-priority-middle >/dev/null 2>&1 || kubectl create priorityclass plat-priority-middle --value=989 || return
  kubectl get priorityclass plat-priority-low >/dev/null 2>&1 || kubectl create priorityclass plat-priority-low --value=979 || return
  kubectl get priorityclass cs-priority-high >/dev/null 2>&1 || kubectl create priorityclass cs-priority-high --value=599 || return
  kubectl get priorityclass cs-priority-middle >/dev/null 2>&1 || kubectl create priorityclass cs-priority-middle --value=589 || return
  kubectl get priorityclass cs-priority-low >/dev/null 2>&1 || kubectl create priorityclass cs-priority-low --value=579 || return
  kubectl get priorityclass app-priority-10 >/dev/null 2>&1 || kubectl create priorityclass app-priority-10 --value=100 || return
  kubectl get priorityclass app-priority-9 >/dev/null 2>&1 || kubectl create priorityclass app-priority-9 --value=99 || return
  kubectl get priorityclass app-priority-8 >/dev/null 2>&1 || kubectl create priorityclass app-priority-8 --value=98 || return
  kubectl get priorityclass app-priority-7 >/dev/null 2>&1 || kubectl create priorityclass app-priority-7 --value=97 || return
  kubectl get priorityclass app-priority-6 >/dev/null 2>&1 || kubectl create priorityclass app-priority-6 --value=96 || return
  kubectl get priorityclass app-priority-5 >/dev/null 2>&1 || kubectl create priorityclass app-priority-5 --value=95 || return
  kubectl get priorityclass app-priority-4 >/dev/null 2>&1 || kubectl create priorityclass app-priority-4 --value=94 || return
  kubectl get priorityclass app-priority-3 >/dev/null 2>&1 || kubectl create priorityclass app-priority-3 --value=93 || return
  kubectl get priorityclass app-priority-2 >/dev/null 2>&1 || kubectl create priorityclass app-priority-2 --value=92 || return
  kubectl get priorityclass app-priority-1 >/dev/null 2>&1 || kubectl create priorityclass app-priority-1 --value=91 || return
}

cmd_retry_with_interval create_priorityclass

function create_sa_rbac() {
  kubectl apply -f - <<EOF
apiVersion: v1
kind: ServiceAccount
metadata:
  name: openpalettek8s-admin
  namespace: admin
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: openpalettek8s-admin
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: cluster-admin
subjects:
  - kind: ServiceAccount
    name: openpalettek8s-admin
    namespace: admin
EOF
}

cmd_retry_with_interval create_sa_rbac

function create_sa_rbac_in_kubesystem() {
  kubectl apply -f - <<EOF
apiVersion: v1
kind: ServiceAccount
metadata:
  name: openpalettek8s-admin
  namespace: kube-system
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: openpalettek8s-kubesystem
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: cluster-admin
subjects:
  - kind: ServiceAccount
    name: openpalettek8s-admin
    namespace: kube-system
EOF
}

cmd_retry_with_interval create_sa_rbac_in_kubesystem

exit 0
