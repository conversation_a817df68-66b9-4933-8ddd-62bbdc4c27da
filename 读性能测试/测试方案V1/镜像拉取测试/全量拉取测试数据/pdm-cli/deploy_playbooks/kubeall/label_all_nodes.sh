#!/bin/bash

op_version=${1}

log_file=/var/log/pdm-cli.log

log_info() {
  LOG_DATE=$(date "+%Y-%m-%d %H:%M:%S,%N")
  # shellcheck disable=SC2145
  echo "$LOG_DATE - INFO - $@" >>$log_file
}

log_error() {
  LOG_DATE=$(date "+%Y-%m-%d %H:%M:%S,%N")
  # shellcheck disable=SC2145
  echo "$LOG_DATE - ERROR - $@" >>$log_file
}

function cmd_retry_with_interval {
  local n=1
  local max=5
  local delay=1
  if [[ -n "$retry_times" ]]; then
    local max=$retry_times
  fi
  if [[ -n "$retry_interval" ]]; then
    local delay=$retry_interval
  fi
  while true; do
    "$@" >>$log_file 2>&1 && break || {
      if [[ $n -lt $max ]]; then
        ((n++))
        sleep $delay
      else
        log_error "$@"
        exit 1
      fi
    }
  done
}

retry_times=30
retry_interval=5

function label_node {
  local version=$1
  log_info "version is $version"
  if [[ -z "$version" ]]; then
    log_error "op_version is required"
    return 1
  fi

  # 获取所有节点，过滤掉边侧标签的节点
  local nodes=$(kubectl get nodes -o json | jq -r '.items[] | select(.metadata.labels."nodename-nodecr.zedge.io/edge" == null) | .metadata.name')

  # 检查是否找到非边侧的节点
  if [[ -z "$nodes" ]]; then
    log_error "No nodes found in the PaaS cluster."
    return 1
  fi

  local all_labels_applied=true
  for node in $nodes; do
    cmd_retry_with_interval kubectl label nodes "$node" "openpalette.io/version=$version" --overwrite || all_labels_applied=false
  done

  if $all_labels_applied; then
    log_info "All nodes have been successfully labeled with openpalette.io/version=$version"
    return 0
  else
    log_error "Some nodes could not be labeled with openpalette.io/version=$version"
    return 1
  fi
}

label_node "$op_version"
