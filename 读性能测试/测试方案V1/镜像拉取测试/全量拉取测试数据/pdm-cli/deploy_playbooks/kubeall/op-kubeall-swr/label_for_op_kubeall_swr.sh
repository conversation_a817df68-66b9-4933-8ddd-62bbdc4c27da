#!/bin/bash

log_file=/var/log/pdm-cli.log

log_error() {
  LOG_DATE=$(date "+%Y-%m-%d %H:%M:%S,%N")
  # shellcheck disable=SC2145
  echo "$LOG_DATE - ERROR - $@" >>$log_file
}

log_info() {
  LOG_DATE=$(date "+%Y-%m-%d %H:%M:%S,%N")
  # shellcheck disable=SC2145
  echo "$LOG_DATE - INFO - $@" >>$log_file
}

function cmd_retry_with_interval {
  local n=1
  local max=5
  local delay=1
  if [[ -n "$retry_times" ]]; then
    local max=$retry_times
  fi
  if [[ -n "$retry_interval" ]]; then
    local delay=$retry_interval
  fi
  while true; do
    "$@" >>$log_file 2>&1 && break || {
      if [[ $n -lt $max ]]; then
        ((n++))
        sleep $delay
      else
        log_error "$@"
        exit 1
      fi
    }
  done
}

function label_node_for_plat_soft_repo()
{
  # 获取第一组控制节点列表，排除过滤掉带边侧标签的节点
  nodes=$(kubectl get nodes -l 'openpalette.role/soft-repo=true,!nodename-nodecr.zedge.io/edge' -o jsonpath='{range .items[*]}{@.metadata.name}:{end}')

  # 遍历第一组控制节点列表，打标签
  for node in ${nodes//:/ }; do
      kubectl label node $node openpalette.role/plat-soft-repo-node=true --overwrite || return
  done
  plat_swr_nodes=$(kubectl get nodes -l openpalette.role/plat-soft-repo-node=true  --no-headers 2>/dev/null)
  if [[ -z "$plat_swr_nodes" ]];then
    return 1
  fi
}

retry_times=60
retry_interval=5
log_info "重试次数: $retry_times" "重试间隔: $retry_interval"
cmd_retry_with_interval label_node_for_plat_soft_repo

exit 0
