#!/bin/bash

log_file=/var/log/pdm-cli.log


log_error() {
  LOG_DATE=$(date "+%Y-%m-%d %H:%M:%S,%N")
  # shellcheck disable=SC2145
  echo "$LOG_DATE - ERROR - $@" >>$log_file
}

log_info() {
  LOG_DATE=$(date "+%Y-%m-%d %H:%M:%S,%N")
  # shellcheck disable=SC2145
  echo "$LOG_DATE - INFO - $@" >>$log_file
}

function cmd_retry_with_interval {
  local n=1
  local max=5
  local delay=1
  if [[ -n "$retry_times" ]]; then
    local max=$retry_times
  fi
  if [[ -n "$retry_interval" ]]; then
    local delay=$retry_interval
  fi
  while true; do
    "$@" >>$log_file 2>&1 && break || {
      if [[ $n -lt $max ]]; then
        ((n++))
        sleep $delay
      else
        log_error "$@"
        exit 1
      fi
    }
  done
}

function generate_hosts()
{
    echo -e "[nodes]\n" > rsync_swr_data_hosts
    # 获取满足条件的节点，排除边侧标签
    kubectl get nodes -l 'openpalette.role/soft-repo=true,!nodename-nodecr.zedge.io/edge' -o jsonpath='{range .items[*]}{.status.addresses[?(@.type=="InternalIP")].address}{"\n"}' | awk '{print $1}' >> rsync_swr_data_hosts
    len=`cat rsync_swr_data_hosts | wc -l`
    if [[ len -gt 1 ]]
    then
        return 0
    else
        return 1
    fi
}


op_swrplat_workdir_old=$1
op_swrplat_workdir_new=$2

retry_times=60
retry_interval=5
log_info "重试次数: $retry_times" "重试间隔: $retry_interval"
cmd_retry_with_interval generate_hosts

retry_times=3
retry_interval=20
cmd_retry_with_interval ansible-playbook -i rsync_swr_data_hosts \
                        rsync_swr_data_from_old.yml \
                        --extra-vars \
                        op_swrplat_workdir_old=$op_swrplat_workdir_old \
                        --extra-vars \
                        op_swrplat_workdir_new=$op_swrplat_workdir_new

log_info "rsync_swr_data_from_old success old $op_swrplat_workdir_old new $op_swrplat_workdir_new"
exit 0
