---
- hosts: nodes
  remote_user: ubuntu
  become: yes
  become_method: sudo
  tasks:
    - name: rsync old swr-data to op-kubeall-swr when upgrade
      shell: |
        old_swr_registry_datadir="/paasdata/op-data/op-asd-swr/docker"
        if [ -d /paasdata/op-data/op-asd-swr/docker ];then
          old_swr_registry_datadir="/paasdata/op-data/op-asd-swr/docker"
        fi
        if [ -d /paasdata/op-data/op-kubeall-swr/swr-data/docker ];then
          old_swr_registry_datadir="/paasdata/op-data/op-kubeall-swr/swr-data/docker"
        fi
        if [ -n {{ op_swrplat_workdir_old }} ];then
          old_swr_registry_datadir="{{ op_swrplat_workdir_old }}/swr-data/docker"
        fi

        new_op_kubeall_swr_registry_workdir="{{ op_swrplat_workdir_new }}/op-kubeall-swr/swr-data/"
        sync_old_to_new_done_flag="{{ op_swrplat_workdir_new }}/op-kubeall-swr/sync_old_to_new_done_flag"

        echo $old_swr_registry_datadir
        echo $new_op_kubeall_swr_registry_workdir

        # 源目录不存在，不需要同步
        if [ ! -d "$old_swr_registry_datadir" ];then
          exit 0
        fi

        # 已经同步完毕，无需再次同步
        if [ -d "$new_op_kubeall_swr_registry_workdir" ] && [ -f "$sync_old_to_new_done_flag" ];then
          exit 0
        fi

        mkdir -p $new_op_kubeall_swr_registry_workdir
        chmod 750  {{ op_swrplat_workdir_new }}
        chmod 750  "{{ op_swrplat_workdir_new }}/op-kubeall-swr"
        chmod 750  $new_op_kubeall_swr_registry_workdir
        chown -R swr:swr "{{ op_swrplat_workdir_new }}/op-kubeall-swr" || exit 1

        # 同步老软仓中的数据到新软仓工作目录下
        # 理论上，只需要同步boot-config node-config，这里先简单实现，同步整个目录
        rsync -aX $old_swr_registry_datadir $new_op_kubeall_swr_registry_workdir || exit 1
        chown -R swr:swr "{{ op_swrplat_workdir_new }}/op-kubeall-swr" || exit 1
        touch $sync_old_to_new_done_flag && chmod 640 $sync_old_to_new_done_flag || exit 1
