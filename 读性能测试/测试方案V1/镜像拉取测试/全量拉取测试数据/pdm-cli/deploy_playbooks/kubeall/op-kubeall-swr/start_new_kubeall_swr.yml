---
- hosts: nodes
  remote_user: ubuntu
  become: yes
  become_method: sudo
  tasks:
    - name: mkdir new op-kubeall-swr.service
      file:
        path: "/paasdata/op-tmp/op-kubeall-swr-bin"
        state: directory
        mode: 0750

    - name: copy new op-kubeall-swr-bin
      copy:
        src: /paasdata/offline/paas/pdm-cli/pkgs/kubeall-pkg/registry
        dest: /paasdata/op-tmp/op-kubeall-swr-bin


    - name: start new op-kubeall-swr.service
      shell: |
          cd /paasdata/op-tmp/op-kubeall-swr-bin/registry
          bash start.sh {{ initial_swr_ip }} {{ swr_plat_registry_port }} {{ initial_swr_ip }}  {{ swr_plat_registry_https_port }} {{ op_swrplat_workdir }}
          rm -rf /paasdata/op-tmp/op-kubeall-swr-bin/registry
