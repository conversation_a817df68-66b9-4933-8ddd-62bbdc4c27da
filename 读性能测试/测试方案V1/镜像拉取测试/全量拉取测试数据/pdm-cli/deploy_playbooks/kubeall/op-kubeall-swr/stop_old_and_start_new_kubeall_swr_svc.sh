#!/bin/bash
log_file=/var/log/pdm-cli.log


log_error() {
  LOG_DATE=$(date "+%Y-%m-%d %H:%M:%S,%N")
  # shellcheck disable=SC2145
  echo "$LOG_DATE - ERROR - $@" >>$log_file
}

log_info() {
  LOG_DATE=$(date "+%Y-%m-%d %H:%M:%S,%N")
  # shellcheck disable=SC2145
  echo "$LOG_DATE - INFO - $@" >>$log_file
}

function cmd_retry_with_interval {
  local n=1
  local max=5
  local delay=1
  if [[ -n "$retry_times" ]]; then
    local max=$retry_times
  fi
  if [[ -n "$retry_interval" ]]; then
    local delay=$retry_interval
  fi
  while true; do
    "$@" >>$log_file 2>&1 && break || {
      if [[ $n -lt $max ]]; then
        ((n++))
        sleep $delay
      else
        log_error "$@"
        exit 1
      fi
    }
  done
}

function generate_hosts_stop_old()
{
    echo -e "[nodes]\n" > hosts_stop_old

    # 获取满足条件的节点，排除边侧标签
    kubectl get nodes -l 'openpalette.role/soft-repo=true,!nodename-nodecr.zedge.io/edge' -o jsonpath='{range .items[*]}{.status.addresses[?(@.type=="InternalIP")].address}{"\n"}' | awk '{print $1}' >> hosts_stop_old

    len=`cat hosts_stop_old | wc -l`
    if [[ len -gt 1 ]]
    then
        return 0
    else
        return 1
    fi
}

initial_swr_ip=$1
swr_plat_registry_port=$2
swr_plat_registry_https_port=$3
op_swrplat_workdir=$4
first_deploy=$5


function generate_hosts_start_new()
{
    echo -e "[nodes]\n" > hosts_start_new
    echo "$initial_swr_ip" >> hosts_start_new
}


if [[ -n "$first_deploy" ]] &&  [[ "${first_deploy}" == "no" ]];then
  retry_times=60
  retry_interval=2
  cmd_retry_with_interval generate_hosts_stop_old
  retry_times=3
  retry_interval=10
  cmd_retry_with_interval ansible-playbook -i hosts_stop_old stop_old_kubeall_swr.yml
  log_info "stop_old_kubeall_swr success"
fi

retry_times=3
retry_interval=10
cmd_retry_with_interval generate_hosts_start_new
cmd_retry_with_interval ansible-playbook -i hosts_start_new start_new_kubeall_swr.yml \
  --extra-vars initial_swr_ip=$initial_swr_ip \
  --extra-vars swr_plat_registry_port=$swr_plat_registry_port \
  --extra-vars swr_plat_registry_https_port=$swr_plat_registry_https_port \
  --extra-vars op_swrplat_workdir=$op_swrplat_workdir


log_info "start_new_kubeall_swr success"



exit 0



