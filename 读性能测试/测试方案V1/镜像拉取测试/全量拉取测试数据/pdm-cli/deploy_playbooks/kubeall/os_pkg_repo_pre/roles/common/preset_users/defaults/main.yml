---
preset_users:
  - name: utm
    group: utm
    uid: 1010
    gid: 1010
    shell: /sbin/nologin
    sudo: no
    non_unique: yes
    create_home: yes
    sudo_whitelist: none

  - name: iportaladmin
    group: iportaladmin
    uid: 1011
    gid: 1011
    shell: /sbin/nologin
    sudo: no
    non_unique: yes
    create_home: yes
    sudo_whitelist: none

  - name: managercert
    group: managercert
    uid: 1012
    gid: 1012
    shell: /sbin/nologin
    sudo: no
    non_unique: yes
    create_home: yes
    sudo_whitelist: none

  - name: myportal
    group: myportal
    uid: 1013
    gid: 1013
    shell: /sbin/nologin
    sudo: no
    non_unique: yes
    create_home: yes
    sudo_whitelist: none

  - name: iportal
    group: iportal
    uid: 1014
    gid: 1014
    shell: /sbin/nologin
    sudo: no
    non_unique: yes
    create_home: yes
    sudo_whitelist: none

  - name: pconf
    group: pconf
    uid: 1015
    gid: 1015
    shell: /sbin/nologin
    sudo: no
    non_unique: yes
    create_home: yes
    sudo_whitelist: none

  - name: eps
    group: eps
    uid: 1020
    gid: 1020
    shell: /sbin/nologin
    sudo: no
    non_unique: yes
    create_home: yes
    sudo_whitelist: none

  - name: userkpi
    group: userkpi
    uid: 1021
    gid: 1021
    shell: /sbin/nologin
    sudo: no
    non_unique: yes
    create_home: yes
    sudo_whitelist: none

  - name: cf-vnpm
    group: cf-vnpm
    uid: 1030
    gid: 1030
    shell: /sbin/nologin
    sudo: no
    non_unique: yes
    create_home: yes
    sudo_whitelist: none

  - name: swr
    group: swr
    uid: 1050
    gid: 1050
    shell: /sbin/nologin
    sudo: no
    non_unique: yes
    create_home: yes
    sudo_whitelist: none

  - name: tcfs
    group: tcfs
    uid: 1060
    gid: 1060
    shell: /sbin/nologin
    sudo: no
    non_unique: yes
    create_home: yes
    sudo_whitelist: none

  - name: ops1
    group: ops1
    uid: 1070
    gid: 1070
    shell: /sbin/nologin
    sudo: no
    non_unique: yes
    create_home: yes
    sudo_whitelist: none

  - name: ops2
    group: ops2
    uid: 1080
    gid: 1080
    shell: /sbin/nologin
    sudo: no
    non_unique: yes
    create_home: yes
    sudo_whitelist: none

  - name: ops3
    group: ops3
    uid: 1090
    gid: 1090
    shell: /sbin/nologin
    sudo: no
    non_unique: yes
    create_home: yes
    sudo_whitelist: none

  - name: nwmaster
    group: nwmaster
    uid: 1100
    gid: 1100
    shell: /sbin/nologin
    sudo: no
    non_unique: yes
    create_home: yes
    sudo_whitelist: none

  - name: nwmonitor
    group: nwmonitor
    uid: 1101
    gid: 1101
    shell: /sbin/nologin
    sudo: no
    non_unique: yes
    create_home: yes
    sudo_whitelist: none

  - name: inetdeploy
    group: inetdeploy
    uid: 1110
    gid: 1110
    shell: /sbin/nologin
    sudo: no
    non_unique: yes
    create_home: yes
    sudo_whitelist: none

  - name: inetagent
    group: inetagent
    uid: 1111
    gid: 1111
    shell: /sbin/nologin
    sudo: no
    non_unique: yes
    create_home: yes
    sudo_whitelist: none

  - name: inetrules
    group: inetrules
    uid: 1112
    gid: 1112
    shell: /sbin/nologin
    sudo: no
    non_unique: yes
    create_home: yes
    sudo_whitelist: none

  - name: inetmanager
    group: inetmanager
    uid: 1113
    gid: 1113
    shell: /sbin/nologin
    sudo: no
    non_unique: yes
    create_home: yes
    sudo_whitelist: none

  - name: vnm
    group: vnm
    uid: 1114
    gid: 1114
    shell: /sbin/nologin
    sudo: no
    non_unique: yes
    create_home: yes
    sudo_whitelist: none

  - name: dev
    group: dev
    uid: 1120
    gid: 1120
    shell: /sbin/nologin
    sudo: no
    non_unique: yes
    create_home: yes
    sudo_whitelist: none

  - name: storage
    group: storage
    uid: 1130
    gid: 1130
    shell: /sbin/nologin
    sudo: no
    non_unique: yes
    create_home: yes
    sudo_whitelist: none

  - name: modeldesign
    group: modeldesign
    uid: 1140
    gid: 1140
    shell: /sbin/nologin
    sudo: no
    non_unique: yes
    create_home: yes
    sudo_whitelist: none

  - name: msb
    group: msb
    uid: 1150
    gid: 1150
    shell: /sbin/nologin
    sudo: no
    non_unique: yes
    create_home: yes
    comment: "MSB User"
    sudo_whitelist: none

  - name: msb-dexmesh
    group: msb-dexmesh
    uid: 1151
    gid: 1151
    shell: /sbin/nologin
    sudo: no
    non_unique: yes
    create_home: yes
    sudo_whitelist: none

  - name: zenap_cos
    group: zenap_cos
    uid: 1160
    gid: 1160
    shell: /sbin/nologin
    sudo: no
    non_unique: yes
    create_home: yes
    sudo_whitelist: none

  - name: zenap_kms
    group: zenap_kms
    uid: 1170
    gid: 1170
    shell: /sbin/nologin
    sudo: no
    non_unique: yes
    create_home: yes
    sudo_whitelist: none

  - name: cf-csm
    group: cf-csm
    uid: 1190
    gid: 1190
    shell: /sbin/nologin
    sudo: no
    non_unique: yes
    create_home: yes
    sudo_whitelist: none

  - name: br
    group: br
    uid: 1200
    gid: 1200
    shell: /sbin/nologin
    sudo: no
    non_unique: yes
    create_home: yes
    sudo_whitelist: none

  - name: rca
    group: rca
    uid: 1210
    gid: 1210
    shell: /sbin/nologin
    sudo: no
    non_unique: yes
    create_home: yes
    sudo_whitelist: none

  - name: op-common
    group: op-common
    uid: 1220
    gid: 1220
    shell: /sbin/nologin
    sudo: no
    non_unique: yes
    create_home: yes
    sudo_whitelist: none

  - name: cdalog
    group: cdalog
    uid: 1230
    gid: 1230
    shell: /sbin/nologin
    sudo: no
    non_unique: yes
    create_home: yes
    sudo_whitelist: none

  - name: filebeat
    group: filebeat
    uid: 1231
    gid: 1231
    shell: /sbin/nologin
    sudo: no
    non_unique: yes
    create_home: yes
    sudo_whitelist: none

  - name: automation
    group: automation
    uid: 1240
    gid: 1240
    shell: /sbin/nologin
    sudo: no
    non_unique: yes
    create_home: yes
    sudo_whitelist: none

  - name: opstools
    group: opstools
    uid: 1250
    gid: 1250
    shell: /sbin/nologin
    sudo: no
    non_unique: yes
    create_home: yes
    sudo_whitelist: none

  - name: rancm
    group: rancm
    uid: 1260
    gid: 1260
    shell: /sbin/nologin
    sudo: no
    non_unique: yes
    create_home: yes
    sudo_whitelist: none

  - name: uniview
    group: uniview
    uid: 1270
    gid: 1270
    shell: /sbin/nologin
    sudo: no
    non_unique: yes
    create_home: yes
    sudo_whitelist: none

  - name: prometheus
    group: prometheus
    uid: 1280
    gid: 1280
    shell: /sbin/nologin
    sudo: no
    non_unique: yes
    create_home: yes
    sudo_whitelist: none

  - name: aceagent
    group: aceagent
    uid: 1290
    gid: 1290
    shell: /sbin/nologin
    sudo: no
    non_unique: yes
    create_home: yes
    sudo_whitelist: none

  - name: ops
    group: ops
    uid: 1300
    gid: 1300
    shell: /sbin/nologin
    sudo: no
    non_unique: yes
    create_home: yes
    sudo_whitelist: none

  - name: cf-nbm
    group: cf-nbm
    uid: 1310
    gid: 1310
    shell: /sbin/nologin
    sudo: no
    non_unique: yes
    create_home: yes
    sudo_whitelist: none

  - name: dexmeshpilot
    group: dexmeshpilot
    uid: 1330
    gid: 1330
    shell: /sbin/nologin
    sudo: no
    non_unique: yes
    create_home: yes
    sudo_whitelist: none

  - name: meshresource
    group: meshresource
    uid: 1334
    gid: 1334
    shell: /sbin/nologin
    sudo: no
    non_unique: yes
    create_home: yes
    sudo_whitelist: none

  - name: dexmeshconfig
    group: dexmeshconfig
    uid: 1335
    gid: 1335
    shell: /sbin/nologin
    sudo: no
    non_unique: yes
    create_home: yes
    sudo_whitelist: none

  - name: dexmeshcanary
    group: dexmeshcanary
    uid: 1336
    gid: 1336
    shell: /sbin/nologin
    sudo: no
    non_unique: yes
    create_home: yes
    sudo_whitelist: none

  - name: istio-proxy
    group: istio-proxy
    uid: 1337
    gid: 1337
    shell: /sbin/nologin
    sudo: no
    non_unique: yes
    create_home: yes
    sudo_whitelist: none

  - name: cnrm-manager
    group: cnrm-manager
    uid: 1340
    gid: 1340
    shell: /sbin/nologin
    sudo: no
    non_unique: yes
    create_home: yes
    sudo_whitelist: none

  - name: adrm
    group: adrm
    uid: 1342
    gid: 1342
    shell: /sbin/nologin
    sudo: no
    non_unique: yes
    create_home: yes
    sudo_whitelist: none

  - name: etcd
    group: etcd
    uid: 1352
    gid: 1352
    shell: /sbin/nologin
    sudo: no
    non_unique: yes
    create_home: yes
    sudo_whitelist: none

  - name: docker
    group: docker
    uid: 1353
    gid: 1353
    shell: /sbin/nologin
    sudo: no
    non_unique: yes
    create_home: no
    sudo_whitelist: none

  - name: dbtools
    group: dbtools
    uid: 1360
    gid: 1360
    shell: /sbin/nologin
    sudo: no
    non_unique: yes
    create_home: no
    sudo_whitelist: none
