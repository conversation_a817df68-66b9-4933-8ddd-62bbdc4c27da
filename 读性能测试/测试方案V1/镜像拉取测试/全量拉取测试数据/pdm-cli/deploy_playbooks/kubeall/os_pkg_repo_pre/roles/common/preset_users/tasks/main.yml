---
- name: create directory os_pkg_flag
  file:
     path: /tmp/os_pkg_flag
     state: directory
  
- name: rm dot tag file
  file:
     path: /tmp/os_pkg_flag/os_init_preset_users
     state: absent

- name: check os_init_preset_users_tag
  stat: path=/var/os_init_preset_users_tag
  register: tag_preset_users

- name: touch dot tag file upgrade
  file:
     path: /tmp/os_pkg_flag/os_init_preset_users
     state: touch
     mode: 640
  when: tag_preset_users.stat.exists == True or ( action_todo is defined and action_todo == "upgrade" )

- block:
  - name: Added group for paas
    shell: |
      grep -q "^{{item.group}}:" /etc/group
      if [[ $? -eq 0 ]]; then
        groupmod -g {{item.gid}} -o {{item.group}}
      else
        groupadd -g {{item.gid}} -o {{item.group}}
      fi
    with_items: "{{preset_users}}"
    ignore_errors: yes

  - name: Added user for paas
    user:
      name: "{{item.name}}"
      uid: "{{item.uid}}"
      shell: "{{item.shell}}"
      group: "{{item.group}}"
      non_unique: "{{item.non_unique}}"
      createhome: "{{item.create_home}}"
      comment: "{{ item.comment | default('') }}"
    with_items: "{{preset_users}}"

  - name: Allow user to have passwordless sudo privilege
    lineinfile:
      dest: /etc/sudoers
      state: present
      regexp: '^{{item.name}} '
      line: '{{item.name}} ALL=(ALL) NOPASSWD: ALL'
      validate: 'visudo -cf %s'
    when: item.sudo
    with_items: "{{preset_users}}"

  - name: remove user info from /etc/sudoers
    lineinfile:
      dest: /etc/sudoers
      state: absent
      regexp: '^{{item.name}} .*=.*'
      validate: 'visudo -cf %s'
    when: not item.sudo | bool
    with_items: "{{preset_users}}"

  - name: check rabbitmq exist
    shell: |
      id rabbitmq  >/dev/null 2>&1
      if [[ $? -eq 0 ]]; then
        echo "user_exist"
      else
        echo "user_not_exist"
        groupadd -g 106 -o rabbitmq >/dev/null 2>&1
      fi
    register: rabbitmq_exist

  - name: Added rabbitmq user for paas
    user:
      name: "rabbitmq"
      group: "rabbitmq"
      uid: "102"
      shell: /sbin/nologin
      non_unique: "yes"
      createhome: "yes"
      comment: ""
    when: rabbitmq_exist.stdout == "user_not_exist"

  - name: check cinder exist
    shell: |
      id cinder  >/dev/null 2>&1
      if [[ $? -eq 0 ]]; then
        echo "user_exist"
      else
        echo "user_not_exist"
        groupadd -g 1131 -o cinder >/dev/null 2>&1
      fi
    register: cinder_exist

  - name: Added cinder user for paas
    user:
      name: "cinder"
      group: "cinder"
      uid: "1131"
      shell: /sbin/nologin
      non_unique: "yes"
      createhome: "yes"
      comment: ""
    when: cinder_exist.stdout == "user_not_exist"

  - name: add tecs to group pict
    shell: |
      id tecs  >/dev/null 2>&1
      if [[ $? -ne 0 ]]; then
        echo "user tecs not exist"
        exit
      fi
      id -g pict  >/dev/null 2>&1
      if [[ $? -ne 0 ]]; then
        echo "group pict not exist"
        exit
      fi
      usermod  -a -G pict tecs
  when: tag_preset_users.stat.exists == False

- name: touch dot tag file
  file:
     path: /tmp/os_pkg_flag/os_init_preset_users
     state: touch
     mode: 640
