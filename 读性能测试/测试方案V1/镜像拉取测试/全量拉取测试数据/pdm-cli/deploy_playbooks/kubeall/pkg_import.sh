#!/bin/bash

log_file=/var/log/pdm-cli.log
import_bin_dir=/paasdata/op-data/pdm-cli/pdm/tools/swr_snapshot_import
import_log_file=$import_bin_dir/swr_snapshot_import.log

log_error() {
  LOG_DATE=$(date "+%Y-%m-%d %H:%M:%S,%N")
  # shellcheck disable=SC2145
  echo "$LOG_DATE - ERROR - $@" >>$log_file
}

log_info() {
  LOG_DATE=$(date "+%Y-%m-%d %H:%M:%S,%N")
  # shellcheck disable=SC2145
  echo "$LOG_DATE - INFO - $@" >>$log_file
}

cd "$import_bin_dir" || exit 1

REGISTRY_PATH=$1
HOST=$2
PORT=$3
# shellcheck disable=2034
ZARTPORT=$4

function touch_and_chmod_import_log() {
  if [ ! -f "$import_log_file" ]; then
    touch $import_log_file
  fi

  LOG_DATE=$(date "+%Y-%m-%d %H:%M:%S,%N")
  echo "$LOG_DATE - begin pkg import" >>$import_log_file
  chmod 640 $import_log_file
}
touch_and_chmod_import_log






for ((i = 1; i <= 10; i++)); do
  bash kubeall_snapshotimport.sh $REGISTRY_PATH $HOST $PORT >>$import_log_file 2>&1
  res=$?
  if [ $res -eq 0 ]; then
    log_info "pkg import suuccess"
    exit 0
  else
    sleep 5
  fi
done

log_error "pkg import failed"

exit 1
