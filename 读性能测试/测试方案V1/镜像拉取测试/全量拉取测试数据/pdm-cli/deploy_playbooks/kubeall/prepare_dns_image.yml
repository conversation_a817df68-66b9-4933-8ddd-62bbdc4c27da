---
- hosts: all
  remote_user: ubuntu
  become: yes
  become_method: sudo
  tasks:
    - block:
      - name: start registry service
        shell: >
          /bin/bash -c "cd /paasdata/offline/paas/pdm-cli/pkgs/kubeall-pkg/registry && nohup ./registry serve {{ registry_config }} > /dev/null 2>&1 &"
        async: 30
        poll: 0

      - name: get registry pid
        shell: "lsof -t -i:2524"
        register: registry_pid
        retries: 5
        delay: 2
        until: registry_pid.stdout != ""
        failed_when: registry_pid.stdout == ""
        changed_when: false

      - name: set registry pid
        set_fact:
          pid: "{{ registry_pid.stdout }}"
        when: registry_pid.stdout != ""

      - name: write swr-plat to /etc/hosts
        lineinfile:
          path: /etc/hosts
          line: "127.0.0.1 swr-plat"
          state: present

      - name: pull, tag and push swr image for coredns
        shell: >
          {{ 'docker pull' if k8s_runtime == 'docker' else 'nerdctl -n k8s.io pull --insecure-registry' }} {{ swr_plat_image }} &&
          {{ 'docker tag' if k8s_runtime == 'docker' else 'nerdctl -n k8s.io tag' }} {{ swr_plat_image }} {{ swr_image }} &&
          {{ 'docker push' if k8s_runtime == 'docker' else 'nerdctl -n k8s.io push' }} {{ swr_image }}

      - name: remove swr-plat from /etc/hosts
        lineinfile:
          path: /etc/hosts
          line: "127.0.0.1 swr-plat"
          state: absent

      - name: stop registry service
        shell: "kill -9 {{ pid }}"

      delegate_to: localhost
      run_once: true

    - name: pull, tag and delete swr image in all nodes
      shell: >
        {{ 'docker pull' if k8s_runtime == 'docker' else 'nerdctl -n k8s.io pull --insecure-registry' }} {{ swr_image }} &&
        {{ 'docker tag' if k8s_runtime == 'docker' else 'nerdctl -n k8s.io tag' }} {{ swr_image }} {{ swr_plat_image }} &&
        {{ 'docker rmi' if k8s_runtime == 'docker' else 'nerdctl -n k8s.io rmi' }} {{ swr_image }}
