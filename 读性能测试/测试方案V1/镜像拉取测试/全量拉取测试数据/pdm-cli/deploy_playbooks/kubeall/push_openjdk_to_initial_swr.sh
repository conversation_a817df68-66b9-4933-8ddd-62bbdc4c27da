#!/bin/bash

src_verdir=$1
cp_name=$2
oci_version=$3
openjdk_file=$4
swr_ip=$5
swr_port=$6

if [[ $swr_ip =~ .*:.* ]]; then
  # IPv6
  swr_ip="[$swr_ip]"
fi

mkdir -p /paasdata/op-tmp/$cp_name-$oci_version

cd /paasdata/op-tmp/$cp_name-$oci_version || exit 1
oras pull --insecure $swr_ip:$swr_port/admin/oci/$cp_name:$oci_version || exit 1
if [ ! -f $src_verdir/$openjdk_file ]; then
  echo "$src_verdir/$openjdk_file not exist"
  exit 1
fi
cp -f $src_verdir/$openjdk_file .
oras push --insecure $swr_ip:$swr_port/admin/oci/$cp_name:$oci_version * || exit 1

cd ~ || exit 1
rm -rf /paasdata/op-tmp/$cp_name-$oci_version
exit 0
