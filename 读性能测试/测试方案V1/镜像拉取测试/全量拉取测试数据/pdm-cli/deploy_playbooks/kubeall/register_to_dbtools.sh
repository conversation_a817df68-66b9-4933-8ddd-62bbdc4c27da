#!/bin/bash


log_info() {
    LOG_DATE=$(date "+%Y-%m-%d %H:%M:%S")
    PID_INFO=$$

    echo "$LOG_DATE [$PID_INFO] - $1: $2" >>  /var/log/pdm-cli.log
}

COMMON_VARS="/root/common/com_vars.yml"
PORT_VARS="/root/common/port_vars.yml"

OP_SERVICE_IP=$(grep -w openpalette_service_ip ${COMMON_VARS}|awk '{print $NF}' |tr -d "'}" |head -1)
OP_SERVICE_PORT=$(grep -w openpalette_service_port ${PORT_VARS}|awk '{print $7}'|tr -d ','|head -1)
DAISY_PG_IP=$(grep -w daisy_pg_ip ${COMMON_VARS}|awk '{print $NF}' |tr -d "'}" |head -1)
DAISY_PG_PORT=$(grep -w daisy_pg_port ${COMMON_VARS}|awk '{print $NF}' |tr -d "'}" |head -1)
if [[ $(echo "${OP_SERVICE_IP}" |grep ':') != "" ]]; then
    OP_SERVICE_IP=$(echo "${OP_SERVICE_IP}" |awk '{print "["$0"]"}')
fi
DBTOOLS_REST_API="http://${OP_SERVICE_IP}:${OP_SERVICE_PORT}/dbtools/v1/tenants/admin"

MAX_RETRY_NUM=60
WAIT_SECOND=3
COUNT=0
while (true); do
    QUERY_RESULT=$(curl -s -g  ${DBTOOLS_REST_API}/com_list?pg=daisypg |grep "errorCode")
    if [ "${QUERY_RESULT}" == "" ]; then
        log_info "INFO" "daisy_pg register to dbtools successful!"
        exit 0
    fi
    curl --connect-timeout 20 -m 300 -s -f -g "${DBTOOLS_REST_API}/dbinstance" -A "op-ubs-daisy_pg" -H "Content-Type:application/json" -X POST -d "{\"pg\":\"daisy\",\"ip\":\"${DAISY_PG_IP}\",\"port\":\"${DAISY_PG_PORT}\"}"
    ((COUNT++))
    if [ ${COUNT} -gt ${MAX_RETRY_NUM} ]; then
        log_info "ERROR" "daisy_pg register to dbtools failed!"
        exit 1
    fi
    log_info "INFO" "daisy_pg register to dbtools ${COUNT} times"
    sleep ${WAIT_SECOND}
done