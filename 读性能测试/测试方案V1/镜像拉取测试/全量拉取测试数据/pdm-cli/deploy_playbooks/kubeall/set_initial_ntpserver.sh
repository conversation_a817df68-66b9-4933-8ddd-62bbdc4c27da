#!/bin/bash

# shellcheck disable=SC2154
ntp_server_external=$1

# shellcheck disable=SC2154
cat > /etc/chrony.conf << EOF
driftfile /var/lib/chrony/drift
rtcsync
local stratum 10
logdir /var/log/chrony
cmdport 123
bindcmdaddress 127.0.0.1
bindcmdaddress ::1
logchange 0.5
allow all
EOF

if [[ -n "$ntp_server_external" ]];then
        OLD_IFS=$IFS
        IFS=',' read -ra server_ips <<< $ntp_server_external
        for i in "${!server_ips[@]}";do
                server_ip="${server_ips[$i]}"
                if [[ -n $server_ip ]];then
                        break
                fi
        done
        IFS=$OLD_IFS
        systemctl disable update-chrony
        systemctl stop update-chrony
        systemctl disable chronyd
        systemctl stop chronyd
        timeout 120 chronyd -q "server $server_ip iburst" || timeout 120 chronyd -q "server $server_ip iburst" "precisionswitch 1"
        hwclock -w
fi
systemctl restart chronyd
systemctl enable chronyd
systemctl restart update-chrony
systemctl enable update-chrony

exit 0

