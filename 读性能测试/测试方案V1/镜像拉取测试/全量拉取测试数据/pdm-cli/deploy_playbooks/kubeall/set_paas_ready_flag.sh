#!/bin/bash
# Started by AICoder, pid:l08cbt1f7etb8a714fe208a8a12c3c1488069a4d

action="$1"
pcmk_version="$2"
cm_name="op-platcom-cluster-status"

SCRIPT_DIR=$(
  cd "$(dirname "$0")" || exit
  pwd
)
cd "$SCRIPT_DIR" || exit
chmod +x common-function

# shellcheck source=/dev/null
. common-function

log_debug "Script started with action: $action, pacemaker version: $pcmk_version"

function create_cm() {
  log_debug "Creating ConfigMap $cm_name in admin namespace"
  kubectl apply --kubeconfig=/root/.kube/config -f - <<EOF
apiVersion: v1
data:
  status: success
kind: ConfigMap
metadata:
  name: "$cm_name"
  namespace: admin
EOF
  kubectl get configmap "$cm_name" -n admin >/dev/null 2>&1 || {
    log_debug "Failed to create ConfigMap $cm_name"
    return 1
  }
  log_debug "Successfully created ConfigMap $cm_name"
}

function check_pacemaker_finished() {
  folder="/paasdata/op-data/processd/kubeall/version/bin/pacemaker-cluster-$pcmk_version"
  target_file="pacemaker-cluster-$pcmk_version-already_installed_flag"
  log_debug "Checking pacemaker installation flag at: $folder/$target_file"
  if [[ -e "$folder/$target_file" ]]; then
    log_debug "Pacemaker installation flag found"
    return 0
  fi
  log_debug "Pacemaker installation flag not found"
  return 1
}

function check_vip_finished() {
  log_debug "Checking VIP configuration in crm"
  if timeout 20 pcs resource check router_vip --retry_wait=12 >/dev/null 2>&1 || timeout 20 pcs resource check router_ipv6_vip --retry_wait=12 >/dev/null 2>&1; then
    log_debug "VIP configuration found"
    return 0
  else
    log_debug "VIP configuration not found"
    return 1
  fi
}


function check_vip() {
    log_debug "Performing complete VIP check"
    # shellcheck disable=SC2091
    if $(check_vip_finished) && $(check_pacemaker_finished); then
      log_debug "VIP check passed successfully"
      return 0
    else
      log_debug "VIP check failed"
      return 1
    fi
}

function delete_cm() {
  log_debug "Attempting to delete ConfigMap $cm_name"
  res=$(kubectl get configmap "$cm_name" -n admin 2>&1)
  #configmap存在，删除
  if [ $? = 0 ];then
    kubectl delete cm "$cm_name" -n admin || {
      log_debug "Failed to delete ConfigMap $cm_name"
      return 1
    }
    log_debug "Successfully deleted ConfigMap $cm_name"
    return 0
  fi

  #configmap不存在，返回成功
  # shellcheck disable=SC2076
  if [[ "$res" =~ "(NotFound): configmaps" ]];then
    log_debug "ConfigMap $cm_name not found, no need to delete"
    return 0
  fi

  #其他原因的失败，返回失败
  log_debug "Failed to check ConfigMap $cm_name status"
  return 1
}

#检查action参数
if [[ -z "$action" ]]; then
  log_debug "Action parameter (deploy, upgrade or init) is required!"
  log_debug "请提供action参数（deploy、upgrade或init）！"
  exit 1
fi
# shellcheck disable=SC2034
retry_times=720
# shellcheck disable=SC2034
retry_interval=5
if [ "$action" = "init" ]; then
  log_debug "Starting initialization process..."
  log_debug "Deleting function..."
  cmd_retry_with_interval delete_cm
else
  log_debug "Starting deployment/upgrade process..."
  log_debug "Creating function..."
  cmd_retry_with_interval check_vip
  cmd_retry_with_interval create_cm
fi

log_debug "Script set_paas_ready_flag.sh execution completed"

# Ended by AICoder, pid:l08cbt1f7etb8a714fe208a8a12c3c1488069a4d
