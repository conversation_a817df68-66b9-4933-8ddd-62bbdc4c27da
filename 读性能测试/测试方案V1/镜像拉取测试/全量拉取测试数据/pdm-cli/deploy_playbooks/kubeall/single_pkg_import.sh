#!/bin/bash

SCRIPT_DIR=$(
  cd "$(dirname "$0")"
  pwd
)
cd $SCRIPT_DIR
chmod +x common-function
. common-function
log_file=/var/log/pdm-cli.log

function push_oci() {
  oci_path=$1
  cpName=$2
  cpVersion=$3
  cd $oci_path
  local cp_file="$cpName-$cpVersion.tgz"
  if [ ! -f $cp_file ]; then
    log_error "There is no oci file($cp_file), upload $cpName failed!"
    exit 1
  fi
  # TODO push
  tar -zxf $cp_file >/dev/null 2>&1
  cd $cpName-$cpVersion && oras push --insecure $repo_url_prefix/$cpName:$cpVersion * >/dev/null 2>&1
  if [ $? -ne 0 ]; then
    log_error "$cpName oci upload failed:$?"
    exit 1
  else
    log_info "$cpName oci upload successful"
  fi
  cd $SCRIPT_DIR
}

function push_chart() {
  chart_path=$1
  cpName=$2
  chartVersion=$3
  if [ ! -d $chart_path ]; then
    log_error "There is no chart dir, upload $cpName failed!"
    exit 1
  fi
  cd $chart_path
  local chart_file="$chart_path/$cpName-$chartVersion.tgz"
  if [ ! -f $chart_file ]; then
    log_error "There is no $chart_file file, upload $cpName failed!"
    exit 1
  fi
  # TODO push
  cd $chart_path && helm push $chart_file oci://$repo_url_prefix >/dev/null 2>&1

  if [ $? -ne 0 ]; then
    log_error "$cpName chart upload failed:$?"
    exit 1
  else
    log_info "$cpName upload successful"
  fi
  cd $SCRIPT_DIR
}

function push_image() {
  image_path=$1
  cpName=$2
  cpVersion=$3
  cd $image_path
  local cp_file="$image_path/$cpName-$cpVersion.tar"

  docker_tag=$(docker load -i $cp_file | grep "^Loaded image:" | awk -F "image: " '{print $2}') >/dev/null 2>&1
  if [ $? -ne 0 ] || [ $docker_tag == "" ]; then
    log_error "$cpName image with tag $cpVersion load failed:$?"
    exit 1
  fi
  docker tag $docker_tag $repo_url_prefix/$cpName:$cpVersion >/dev/null 2>&1
  if [ $? -ne 0 ]; then
    log_error "docker tag $cpName failed:$?"
    exit 1
  fi
  docker push $repo_url_prefix/$cpName:$cpVersion >/dev/null 2>&1
  if [ $? -ne 0 ]; then
    log_error "docker push $repo_url_prefix/$cpName:$cpVersion failed:$?"
    exit 1
  else
    log_info "$cpName upload successful"
  fi
  cd $SCRIPT_DIR
}

pkg_dir=$1
cp_name=$2
cp_version=$3
repo_url_prefix=$4
type=$5

SCRIPT_DIR=$(
  cd "$(dirname "$0")"
  pwd
)
chart_path="$pkg_dir/kubeallpackage/chart"
image_path="$pkg_dir/kubeallpackage/image"
oci_path="$pkg_dir/kubeallpackage/oci"
pkg_path="$pkg_dir/kubeallpackage/pkg"

if [ "$type" = "oci" ]; then
  push_oci $oci_path $cp_name $cp_version
  exit 0
fi

if [ "$type" = "chart" ]; then
  push_chart $chart_path $cp_name $cp_version
  exit 0
fi

if [ "$type" = "image" ]; then
  push_image $image_path $cp_name $cp_version
  exit 0
fi

log_error "not support type $type"
exit 1
