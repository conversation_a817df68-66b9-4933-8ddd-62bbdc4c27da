#!/bin/bash


process_ctrl_image_name=$1
process_ctrl_image_version=$2


SCRIPT_DIR=$(
  cd "$(dirname "$0")"
  pwd
)
cd $SCRIPT_DIR
chmod +x common-function
. common-function

retry_times=180
retry_interval=5



function apply_processd_cm() {
kubectl apply --kubeconfig=/root/.kube/config -f - <<EOF
apiVersion: v1
kind: ConfigMap
metadata:
  name: op-platcom-vars-processd
  namespace: admin
  annotations:
    meta.helm.sh/release-name: processd
    meta.helm.sh/release-namespace: admin
  labels:
    app.kubernetes.io/managed-by: Helm
    openpalette.vars/op-platcom: "true"
data:
  op-platcom-vars: |
    process_ctrl_image_name: "$process_ctrl_image_name"
    process_ctrl_image_version: "$process_ctrl_image_version"
    process_ctrl_lifecycle:
      postStart:
        exec:
          command:
            - kurl
            - -X
            - POST
            - --url
            - http://localhost:5001/api/v1/process/my/self
      preStop:
        exec:
          command:
            - kurl
            - -X
            - DELETE
            - --url
            - http://localhost:5001/api/v1/process/my/self
    process_ctrl_livenessProbe:
      httpGet:
        path: /api/v1/process/my/self/liveness
        port: 5001
        scheme: HTTP
      initialDelaySeconds: 15
      periodSeconds: 40
      timeoutSeconds: 10
    process_ctrl_readinessProbe:
      httpGet:
        path: /api/v1/process/my/self/readiness
        port: 5001
        scheme: HTTP
      initialDelaySeconds: 15
      periodSeconds: 40
      timeoutSeconds: 10
    process_ctrl_volumeMounts:
      - mountPath: /paasdata/op-data/processd/kubeall/run/hub/processd/sock/forPod
        name: kubeall-volume
      - mountPath: /paasdata/op-data/kubeall/sock
        name: kubeconfig-volume
      - mountPath: /paasdata/op-log/kubeall-process-ctrl/
        name: op-log
    process_ctrl_volumes:
      - name: kubeall-volume
        hostPath:
          path: /paasdata/op-data/processd/kubeall/run/hub/processd/sock/forPod
      - name: kubeconfig-volume
        hostPath:
          path: /paasdata/op-data/kubeall/sock
      - name: op-log
        hostPath:
          path: /paasdata/op-log/kubeall-process-ctrl/
EOF
}

cmd_retry_with_interval apply_processd_cm

exit 0
