#!/bin/bash

log_file=/var/log/pdm-cli.log
controller_node_count=$1
all_node_count=$2
build_version=$3

node_packages_cfg_ready=no

while [ "$node_packages_cfg_ready" != "yes" ]; do
  ready_node_count=$(kubectl get configmap --no-headers -l 'openpalette.packages/node' -n admin 2>/dev/null | grep "$build_version" | wc -l)
  if [ "$ready_node_count" -ge "$all_node_count" ]; then
    node_packages_cfg_ready=yes
  fi

  if [ $node_packages_cfg_ready != "yes" ]; then
    sleep 5
  fi
done

function wait_distribution_ready() {
  node_is_ready="no"
  while [ "$node_is_ready" != "yes" ]; do
    cm_name=$1
    labels=$(kubectl get -n admin $cm_name -o jsonpath='{.metadata.labels}' 2>/dev/null)
    current_core=$(echo $labels | jq '."current-pkg-core"')
    current_extension=$(echo $labels | jq '."current-pkg-extension"')
    current_meta=$(echo $labels | jq '."current-pkg-meta"')
    desired_core=$(echo $labels | jq '."desired-pkg-core"')
    desired_extension=$(echo $labels | jq '."desired-pkg-extension"')
    desired_meta=$(echo $labels | jq '."desired-pkg-meta"')
    node=$(echo $labels | jq '."openpalette.packages/node"')
    if [ "$current_core" = "$desired_core" ] && [ "$current_extension" = "$desired_extension" ] && [ "$current_meta" = "$desired_meta" ]; then
      echo "$node  core $current_core $desired_core extension $current_extension $desired_extension meta $current_meta $desired_meta" >>$log_file
      echo "$node distribution ready " >>$log_file
      node_is_ready="yes"
    fi
    if [ "$node_is_ready" != "yes" ]; then
      sleep 5
    fi
  done
}

# 获取所有带有标签 openpalette.role/paas_controller=true 的节点名称
nodes=$(kubectl get nodes --selector=openpalette.role/paas_controller=true -o custom-columns=NAME:.metadata.name --no-headers)
# 遍历每个节点名称，查找匹配的 ConfigMap，并执行 wait_distribution_ready
for node in $nodes; do
  echo "Processing ConfigMaps for node: $node"
  for name in $(kubectl get configmap -l 'openpalette.packages/node' -n admin -o name 2>/dev/null | grep "$node" | grep "$build_version"); do
    wait_distribution_ready "$name"
  done
done


echo "all distribution ready " >>$log_file

exit 0
