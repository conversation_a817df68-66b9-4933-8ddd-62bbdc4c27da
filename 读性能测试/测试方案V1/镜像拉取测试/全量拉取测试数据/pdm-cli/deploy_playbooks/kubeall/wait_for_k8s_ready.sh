#!/bin/bash

# Started by AICoder, pid:t0706y191471949141210b70809d424721c148d3
SCRIPT_DIR=$(
  cd "$(dirname "$0")" || exit
  pwd
)
cd "$SCRIPT_DIR" || exit
chmod +x common-function

# shellcheck source=/dev/null
. common-function

k8s_ready=
while [[ -z $k8s_ready ]]; do
  # 检查 default 命名空间是否存在
  res=$(kubectl get ns default --kubeconfig=/root/.kube/config | grep ^default | grep -v grep)
  if [[ -n "$res" ]]; then
    log_debug "Namespace 'default' exists."
  else
    log_debug "Namespace 'default' does not exist yet."
  fi

  # 获取 master 节点的数量
  master_node_count=$(kubectl get nodes --selector=node-role.kubernetes.io/master=true --kubeconfig=/root/.kube/config --no-headers | wc -l)
  log_debug "Number of master nodes: $master_node_count"

  # 获取 kube-system 命名空间下的静态 etcd 开头的 pod 数量
  etcd_pod_count=$(kubectl get pods -n kube-system --kubeconfig=/root/.kube/config --no-headers | grep '^etcd' | wc -l)
  log_debug "Number of etcd pods in 'kube-system' namespace: $etcd_pod_count"

  # 判断是否满足所有条件
  if [[ -n "$res" && $master_node_count -ge 1 && $master_node_count -eq $etcd_pod_count ]]; then
    k8s_ready=ready
    log_debug "All conditions met. Kubernetes cluster is ready."
  else
    log_debug "Conditions not met yet. Waiting for Kubernetes cluster to be ready..."
    sleep 10
  fi
done

log_debug "The K8s is ready!"
# Ended by AICoder, pid:t0706y191471949141210b70809d424721c148d3