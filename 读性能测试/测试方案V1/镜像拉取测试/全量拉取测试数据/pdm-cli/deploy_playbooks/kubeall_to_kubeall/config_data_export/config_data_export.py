import importlib
import logging
import os

from pathlib import Path

import yaml

LOG_DIR = "/paasdata/op-log/pdm-cli/kubeall_to_kubeall"
LOG_FILE = "/paasdata/op-log/pdm-cli/kubeall_to_kubeall/config_data_export.log"
CONFIG_PATH = "/etc/pdm/deploy_playbooks/kubeall_to_kubeall/config_data_export"\
              "/config_data_export.yml"
BUILD_INFO_PATH = "/etc/pdm/conf/buildinfo.conf"

if not os.path.exists(LOG_DIR):
    os.makedirs(LOG_DIR, 0o750, exist_ok=True)
logging.basicConfig(filename=LOG_FILE,
                    format="%(asctime)s-%(name)s - line:%(lineno)d - %(levelname)s"
                           " - %(message)s",
                    level=logging.DEBUG)
os.chmod(LOG_FILE, 0o640)
LOG = logging.getLogger(__name__)


def load_config(config_path):
    with open(config_path, 'r') as file:
        return yaml.safe_load(file)


def import_and_run_module(module_name, src_version, output_file):
    module_path, function_name = module_name.rsplit('.', 1)
    LOG.debug(f'{module_path}, {function_name}')
    module = importlib.import_module(module_path)
    function_to_run = getattr(module, function_name)
    return function_to_run(src_version, output_file)


def main(config_file, src_version, output_file):
    config = load_config(config_file)
    LOG.debug(f"yaml config: {config}")
    success = True

    for script_config in config:
        try:
            LOG.debug(f"Running script {script_config['module_name']} for "
                      f"{script_config['desc']}")
            result = import_and_run_module(
                module_name=script_config['module_name'] + '.handle',
                src_version=src_version,
                output_file=output_file
            )
            if not result:
                LOG.error(f"Script {script_config['module_name']} returned False")
                success = False
                break
            LOG.info(f"Running script {script_config['module_name']} succeed!")
        except Exception as e:
            LOG.error(f"An error occurred in {script_config['module_name']}: {e}")
            success = False
            break
    return success


if __name__ == "__main__":
    import sys

    if len(sys.argv) < 3:
        LOG.error(f"Wrong argv: {sys.argv}")
        LOG.error("Usage: python script.py <src_version> <output_file>")
        sys.exit(1)

    src_version = sys.argv[1]
    output_file = sys.argv[2]

    config_file_path = Path(CONFIG_PATH)
    if not config_file_path.is_file():
        LOG.debug(f"Configuration file {CONFIG_PATH} not found.")
        sys.exit(1)

    result = main(config_file_path, src_version, output_file)
    LOG.info("Config data export result: %s" % result)
    sys.exit(0 if result else 1)
