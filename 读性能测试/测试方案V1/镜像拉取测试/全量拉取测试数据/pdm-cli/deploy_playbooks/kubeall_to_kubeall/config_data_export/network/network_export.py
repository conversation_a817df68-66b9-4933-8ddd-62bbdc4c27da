# -*- coding: UTF-8 -*-
import configparser
import ipaddress
import json
import yaml
import re
import httplib2
import os
import paramiko
import logging
import sys
import subprocess

sys.path.append("/etc/pdm")

net_dict = {
    'iapi': 'net_iapi',
    'net_traffic': 'net_traffic',
    'iapi_v6': 'net_iapi_v6',
    'net_traffic_v6': 'net_traffic_v6'
}

openpalette_path = ''
inet_deploy_tcf_path = '/etc/network/inet_deploy_tcf.conf'
com_vars_path = '/root/common/com_vars.yml'
LOG = logging.getLogger(__name__)
networks = {}


def handle(src_version, output_file):
    try:
        global networks
        global openpalette_path

        openpalette_path = output_file

        parse_config()
        for name, network in networks.items():
            LOG.info(f'{name}: {network}')

        plat_external_ips = parse_com_vars()
        update_json(plat_external_ips)
        return True
    except Exception as ex:
        raise Exception("network export failed! except=%s" % ex)


def parse_com_vars():
    with open(com_vars_path, 'r') as f:
        data = yaml.safe_load(f)
        for item in data:
            if 'plat_external_ips' in item:
                return item['plat_external_ips']
    return ''


def update_json(plat_external_ips):
    global networks
    with open(openpalette_path, 'r') as f:
        data = json.load(f)

    host_name_with_iapi = get_all_host_names_with_iapi(data)
    iapi_ips = get_all_iapi_ips(host_name_with_iapi)
    for node in data['nodes']:
        for host_name, iapi_ip in iapi_ips.items():
            if node['name'] == host_name:
                node['net_iapi_ip'] = iapi_ip['net_iapi_ip']
                node['net_iapi_ip_v6'] = iapi_ip['net_iapi_ip_v6']

    for network in data['networks']:
        ip_versions = network['ip_versions']
        net_name = network['name']
        if net_name in networks:
            network_info = networks[net_name]
            network_info = fix_network_info_by_ip_versions(ip_versions, network_info)
            keys = ['cidrs', 'gateways', 'segment_id', 'static_routes']
            for key in keys:
                if network_info[key] != '' and \
                        not is_list_of_empty_strings(network_info[key]):
                    network[key] = network_info[key]
            if net_name == 'net_iapi':
                if ',' in plat_external_ips:
                    vips = plat_external_ips.split(',')
                    vips = fix_vips_by_ip_versions(ip_versions, vips)
                    network['vips'] = vips
                else:
                    vips = plat_external_ips
                    network['vips'] = fix_vips_by_ip_versions(ip_versions, vips)
                network['allocation_pools'] = get_iapi_allocation_pools(
                    network['ip_versions'], iapi_ips)

    # print(data['networks'])
    with open(openpalette_path, 'w') as f:
        json.dump(data, f, indent=4)


def is_list_of_empty_strings(obj):
    return isinstance(obj, list) and \
        all(isinstance(elem, str) and elem == '' for elem in obj)


def fix_network_info_by_ip_versions(ip_versions, network_info):
    def process_value(key, value, version_getter):
        # If the value is a list, reorder based on ip_versions
        if isinstance(value, list):
            if isinstance(ip_versions, list):
                if ip_versions[0] == version_getter(value[0]):
                    return [value[0], value[1]]
                else:
                    return [value[1], value[0]]
        # If the value is not a list, create a list based on ip_versions
        else:
            if isinstance(ip_versions, list):
                if ip_versions[0] == version_getter(value):
                    return [value, ""]
                else:
                    return ["", value]
            else:
                return value

        return value

    new_network_info = {}
    for key, value in network_info.items():
        value = fix_value_type(value)
        if key in ["cidrs", "gateways"]:
            new_network_info[key] = process_value(key, value, get_ip_version)
        elif key in ["static_routes"]:
            new_network_info[key] = process_value(
                key, value, get_static_routes_version)
        else:
            new_network_info[key] = value

    return new_network_info


def fix_vips_by_ip_versions(ip_versions, vips):
    if isinstance(ip_versions, list):
        if isinstance(vips, list):
            if ip_versions[0] != get_ip_version(vips[0]):
                vips.reverse()
        else:
            if ip_versions[0] == get_ip_version(vips):
                vips = [vips, ""]
            else:
                vips = ["", vips]
    else:
        if isinstance(vips, list):
            vips = vips[0]

    return vips


def fix_value_type(value):
    if isinstance(value, list) and len(value) == 1:
        return value[0]
    return value


def get_ip_version(ip_address):
    if ':' in ip_address:
        return 'IPV6'
    return 'IPV4'


def get_static_routes_version(static_routes):
    if '.' in static_routes:
        return 'IPV4'
    return 'IPV6'


def get_iapi_allocation_pools(ip_versions, iapi_ips):
    iapi_ips_v4_list = []
    iapi_ips_v6_list = []
    for host_name, iapi_ip in iapi_ips.items():
        if iapi_ip['net_iapi_ip'] != '':
            iapi_ips_v4_list.append(iapi_ip['net_iapi_ip'])
        if iapi_ip['net_iapi_ip_v6'] != '':
            iapi_ips_v6_list.append(iapi_ip['net_iapi_ip_v6'])
    iapi_ips_v4_str = compress_ip_list(iapi_ips_v4_list)
    iapi_ips_v6_str = compress_ip_list(iapi_ips_v6_list)

    if isinstance(ip_versions, list):
        result = []
        for ip_version in ip_versions:
            if ip_version == 'IPV4':
                result.append(iapi_ips_v4_str)
            if ip_version == 'IPV6':
                result.append(iapi_ips_v6_str)
        return result

    if isinstance(ip_versions, str):
        result = ''
        if ip_versions == 'IPV4':
            result = iapi_ips_v4_str
        if ip_versions == 'IPV6':
            result = iapi_ips_v6_str
        return result
    return ''


def compress_ip_list(ip_list):
    # 将IP地址转换为整数
    ip_int_list = [int(ipaddress.ip_address(ip)) for ip in ip_list]

    # 对IP地址进行排序
    ip_int_list.sort()

    result = []
    start_ip = None
    end_ip = None

    # 遍历排序后的IP地址列表
    for i, ip_int in enumerate(ip_int_list):
        # 如果是第一个IP地址或者当前IP地址比上一个IP地址大1
        if i == 0 or ip_int == ip_int_list[i-1] + 1:
            # 如果这是连续的IP地址
            if start_ip is None:
                start_ip = ip_int
            end_ip = ip_int
        else:
            # 如果不是连续的IP地址
            if start_ip is not None:
                # 如果有起始和结束IP地址，则添加范围
                if start_ip != end_ip:
                    result.append(str(ipaddress.ip_address(start_ip)) +
                                  '-' + str(ipaddress.ip_address(end_ip)))
                else:
                    result.append(str(ipaddress.ip_address(start_ip)))
            else:
                # 如果只有一个IP地址，则直接添加
                result.append(str(ipaddress.ip_address(ip_int)))
            start_ip = ip_int
            end_ip = ip_int

    # 处理最后一个IP地址范围
    if start_ip is not None:
        if start_ip != end_ip:
            result.append(str(ipaddress.ip_address(start_ip)) +
                          '-' + str(ipaddress.ip_address(end_ip)))
        else:
            result.append(str(ipaddress.ip_address(start_ip)))

    return ','.join(result)


def parse_config():
    # Load the configuration file
    config = configparser.ConfigParser()
    config.read(inet_deploy_tcf_path)

    # Parse the default section
    default = config['default']
    provider_names = default['provider'].split(',')

    for provider_name in provider_names:
        if provider_name not in config.sections():
            raise ValueError(f"Provider {provider_name} is not defined")

        provider = config[provider_name]

        for net_name, network_str in provider.items():
            net_name = net_dict.get(net_name, '')
            if net_name == '':
                continue
            if net_name.endswith('_v6'):
                routes_v6_str = provider.get('routes_v6', '')
                net_name = net_name[:-3]
                parse_network_v6(net_name, network_str, routes_v6_str)
            else:
                routes_str = provider.get('routes', '')
                parse_network(net_name, network_str, routes_str)
            parse_traffic_cidr(net_name, network_str, provider_name)


def parse_traffic_cidr(net_name, network_str, provider_name):
    global networks
    elements = network_str.split(':')
    interface_name = elements[0]
    vlan_id = ''
    if len(elements) > 1:
        vlan_id = elements[1]

    if net_name == 'net_traffic':
        if provider_name == 'ovs':
            interface_name = 'br-' + net_name
        else:
            if vlan_id != '':
                interface_name = interface_name + '.' + vlan_id

        cidr_v4, cidr_v6 = get_net_traffic_cidrs(interface_name)

        if cidr_v4 and cidr_v6:
            networks['net_traffic']['cidrs'] = [cidr_v4, cidr_v6]
            if networks['net_traffic']['gateways'] == '':
                networks['net_traffic']['gateways'] = ['', '']
            if networks['net_traffic']['static_routes'] == '':
                networks['net_traffic']['static_routes'] = ['', '']

        elif cidr_v4 or cidr_v6:
            networks['net_traffic']['cidrs'] = cidr_v4 or cidr_v6


def get_net_traffic_cidrs(interface_name):
    cidr_v4 = ''
    cidr_v6 = ''

    header = {'Content-Type': 'application/json'}
    msb_ip = get_from_comvars('openpalette_service_ip',
                              '/root/common/com_vars.yml')
    msb__port = get_from_comvars('zenap_msb_apigateway_port')
    op_service_url = "%s:%s" % (msb_ip, msb__port)
    url = ('http://%s/lbmanager/v1/tenants/admin/xlb/instances' %
           (op_service_url))
    http = httplib2.Http(timeout=60,
                         disable_ssl_certificate_validation=True)
    result, xlb = http.request(url, 'GET',
                                    headers=header)

    if not result:
        return '', ''
    content = json.loads(xlb)
    xlb_instances = content.get('xlb_instances', [])
    for xlb_instance in xlb_instances:
        xlb_networks = xlb_instance.get('xlb_networks', [])
        for xlb_network in xlb_networks:
            if xlb_network.get('interface', '') == interface_name:
                cidr_v4 = xlb_network.get('cidr_v4', '')
                cidr_v6 = xlb_network.get('cidr_v6', '')

    return cidr_v4, cidr_v6


def get_ipv4_cidr(interface_name):
    # 运行ifconfig命令
    result = subprocess.run(['ifconfig', interface_name], stdout=subprocess.PIPE)
    output = result.stdout.decode()

    # 使用正则表达式解析命令的输出，获取IPv4地址和子网掩码
    match = re.search(r'inet (\S+)\s+netmask (\S+)', output)
    if match:
        ip = match.group(1)
        mask = match.group(2)
        # 将子网掩码转换为前缀长度
        mask = sum([bin(int(x)).count('1') for x in mask.split('.')])
        network = ipaddress.ip_network(f'{ip}/{mask}', strict=False)
        return str(network)

    return ""


def get_ipv6_cidr(interface_name):
    # 运行ifconfig命令
    result = subprocess.run(['ifconfig', interface_name], stdout=subprocess.PIPE)
    output = result.stdout.decode()

    # 使用正则表达式解析命令的输出，获取IPv6地址和子网掩码
    match = re.search(r'inet6 (\S+)\s+prefixlen (\d+)', output)
    if match:
        ip = match.group(1)
        prefixlen = match.group(2)
        network = ipaddress.ip_network(f'{ip}/{prefixlen}', strict=False)
        return str(network)

    return ""


def parse_network(net_name, network_str, routes_str):
    global networks
    # Parse the network string
    elements = network_str.split(':')
    interface_name, vlan_id, ip_addr, gw = elements

    # Compute the CIDR
    if ip_addr == '':
        cidr = ''
        ip = ''
    else:
        ip_interface = ipaddress.ip_interface(ip_addr)
        cidr = str(ip_interface.network)
        ip = str(ip_interface.ip)

    # Parse the routes
    if routes_str == '':
        static_routes = []
    else:
        static_routes = parse_routes(routes_str, gw)

    if net_name in networks:
        # if it does, append the value to the existing list
        ip_list = []
        ip_list.append(networks[net_name]['ip'])
        ip_list.append(str(ip))
        gw_list = []
        gw_list.append(networks[net_name]['gateways'])
        gw_list.append(gw)
        route_list = []
        route_list.append(networks[net_name]['static_routes'])
        route_list.append(','.join(static_routes))
        cidr_list = []
        cidr_list.append(networks[net_name]['cidrs'])
        cidr_list.append(cidr)
        networks[net_name]['ip'] = ip_list
        networks[net_name]['gateways'] = gw_list
        networks[net_name]['static_routes'] = route_list
        networks[net_name]['cidrs'] = cidr_list
    else:
        # if it doesn't, add it to networks as a string
        networks[net_name] = {
            'ip': str(ip),
            'segment_id': vlan_id,
            'gateways': gw,
            'static_routes': ','.join(static_routes),
            'cidrs': cidr,
        }


def parse_network_v6(net_name, network_str, routes_v6_str):
    global networks
    # Parse the network string
    if network_str.count('[') == 2:
        # iapi_v6=eth3:1000:[172:166:2::51]/112:[172:166:2::1]
        # iapi_v6=eth3::[172:166:2::51]/112:[172:166:2::1]
        pattern = r"(.*?):(\d*?):\[(.*?)\]/(\d*?):\[(.*?)\]"
        match = re.match(pattern, network_str)
    elif network_str.count('[') == 1:
        # iapi_v6=eth3::[172:166:2::51]/112:
        pattern = r"(.*?):(\d*?):\[(.*?)\]/(\d*?):(.*?)"
        match = re.match(pattern, network_str)
    else:
        # iapi_v6=eth3:::
        # iapi_v6=eth3:1000::
        pattern = r"(.*?):(\d*?):(.*?)(.*?):(.*?)"
        match = re.match(pattern, network_str)

    if match:
        vlan_id = match.group(2)
        if match.group(3) == "":
            ip = ""
            cidr = ""
        else:
            ip = str(ipaddress.IPv6Interface(match.group(3) + '/' + match.group(4)).ip)
            cidr = str(ipaddress.IPv6Interface(match.group(3) +
                                               '/' + match.group(4)).network)
        if match.group(5) == "":
            gateway = ""
        else:
            gateway = str(ipaddress.IPv6Address(match.group(5)))

        # Parse the routes
        if routes_v6_str == '':
            static_routes = []
        else:
            static_routes = parse_routes_v6(routes_v6_str, str(gateway))

        if net_name in networks:
            # if it does, append the value to the existing list
            ip_list = []
            ip_list.append(networks[net_name]['ip'])
            ip_list.append(ip)
            gw_list = []
            gw_list.append(networks[net_name]['gateways'])
            gw_list.append(gateway)
            route_list = []
            route_list.append(networks[net_name]['static_routes'])
            route_list.append(','.join(static_routes))
            cidr_list = []
            cidr_list.append(networks[net_name]['cidrs'])
            cidr_list.append(cidr)
            networks[net_name]['ip'] = ip_list
            networks[net_name]['gateways'] = gw_list
            networks[net_name]['static_routes'] = route_list
            networks[net_name]['cidrs'] = cidr_list
        else:
            # if it doesn't, add it to networks as a string
            networks[net_name] = {
                'ip': ip,
                'segment_id': vlan_id,
                'gateways': gateway,
                'static_routes': ','.join(static_routes),
                'cidrs': cidr,
            }
    else:
        raise ValueError(f"Unable to parse network config: {network_str}")


def parse_routes(routes_str, gw):
    routes = routes_str.split(',')
    static_routes = []
    for route in routes:
        if route:
            gateway = route.split(':')[1]
            if gateway == gw:
                static_routes.append(route)
    return static_routes


def parse_routes_v6(routes_str, gw):
    # routes_v6=[10:0:0:0]/8:[172:166:2::1],[0:0:0:0]/0:[172:166:2::2]:
    routes = routes_str.split(',')
    static_routes = []
    for route in routes:
        if route:
            gateway = route.split(':[')[1]
            if gateway.split(']')[0] == gw:
                static_routes.append(route)
    return static_routes


def ssh_server(ip, cmd, user='ubuntu'):
    try:
        pkey = '/root/.ssh/id_rsa'
        key = paramiko.RSAKey.from_private_key_file(pkey)
        tran = paramiko.Transport((ip, 22))
        tran.connect(username=user, pkey=key)
        ssh1 = paramiko.SSHClient()
        ssh1.set_missing_host_key_policy(paramiko.RejectPolicy())
        ssh1._transport = tran
        stdin, stdout, stderr = ssh1.exec_command(cmd, timeout=10)
        res = stdout.readlines()
        ssh1.close()
        return res
    except Exception as ex:
        LOG.error(ex)
        return []


def get_from_comvars(varname, filepath=None):
    if not filepath:
        if os.path.exists('/root/common/port_vars.yml'):
            filepath = "/root/common/port_vars.yml"
        else:
            filepath = "/root/common/com_vars.yml"
    if not os.path.exists(filepath):
        return None
    f = open(filepath)
    dataMap = yaml.safe_load(f)
    f.close()
    for data in dataMap:
        if varname in data:
            return data[varname]
    return None


def get_nodes():
    need_get_from_nodeworker = True
    nodes = "/paasdata/op-data/pdm-cli/node_info/nodes"
    file_exist = os.path.exists(nodes)
    if file_exist:
        try:
            with open(nodes, 'r') as f:
                content = json.load(f)
                need_get_from_nodeworker = False
        except Exception as e:
            LOG.error("exception(%s) happens!" % e)
    if need_get_from_nodeworker:
        header = {'Content-Type': 'application/json'}
        msb_ip = get_from_comvars('openpalette_service_ip',
                                  '/root/common/com_vars.yml')
        msb__port = get_from_comvars('zenap_msb_apigateway_port')
        op_service_url = "%s:%s" % (msb_ip, msb__port)
        url = ('http://%s/nodeworker/v1/tenants/admin/nodes?isused=yes' %
               (op_service_url))
        http = httplib2.Http(timeout=60,
                             disable_ssl_certificate_validation=True)
        result, nodelist = http.request(url, 'GET',
                                        headers=header)
        if not result:
            return []
        content = json.loads(nodelist)
    if "nodes" not in content:
        return []
    return content["nodes"]


def get_all_api_ips():
    try:
        nodes = get_nodes()
        api_ips = parse_nodes_ip(nodes)
    except Exception as ex:
        LOG.error("generate hosts failed! except=%s" % ex)
        return {}
    LOG.info("generate hosts successfully!")
    return api_ips


def parse_nodes_ip(nodes):
    api_ips = {}
    for node in nodes:
        if "netinfo" in node and node["netinfo"].get("net_api"):
            api_ips[node["name"]] = node["netinfo"]["net_api"]["ip"]
    for host_name, api_ip in api_ips.items():
        LOG.info(f'{host_name}: {api_ip}')
    return api_ips


def get_iapi(res):
    iapi = {}
    iapi['net_iapi_ip'] = ''
    iapi['net_iapi_ip_v6'] = ''
    for line in res:
        if line.startswith('iapi=') or line.startswith('iapi ='):
            line = line.rstrip('\n')
            elements = line.split(':')
            _, vlan_id, ip_addr, gw = elements
            if ip_addr != '':
                ip, mask = ip_addr.split('/')
                iapi['net_iapi_ip'] = ip
            else:
                iapi['net_iapi_ip'] = ''
        if line.startswith('iapi_v6=') or line.startswith('iapi_v6 ='):
            if line.count('[') == 2:
                pattern = r"(.*?):(\d*?):\[(.*?)\]/(\d*?):\[(.*?)\]"
                match = re.match(pattern, line)
            elif line.count('[') == 1:
                pattern = r"(.*?):(\d*?):\[(.*?)\]/(\d*?):(.*?)"
                match = re.match(pattern, line)
            else:
                pattern = r"(.*?):(\d*?):(.*?)(.*?):(.*?)"
                match = re.match(pattern, line)

            if match:
                if match.group(3) != "":
                    ip = ipaddress.IPv6Interface(match.group(3))
                    iapi['net_iapi_ip_v6'] = str(ip.ip)
    return iapi


def get_all_iapi_ips(host_name_with_iapi):
    iapi_ips = {}
    api_ips = get_all_api_ips()
    for host_name, api_ip in api_ips.items():
        if host_name in host_name_with_iapi:
            cmd = 'sudo cat /etc/network/inet_deploy_tcf.conf|grep iapi'
            res = ssh_server(api_ip, cmd)
            iapi_ips[host_name] = get_iapi(res)

    for host_name, iapi_ip in iapi_ips.items():
        LOG.info(f'{host_name}: {iapi_ip}')
    return iapi_ips


def get_all_host_names_with_iapi(openpalette_data):
    all_host_names_with_iapi = []
    for node in openpalette_data['nodes']:
        for node_template in openpalette_data['node_templates']:
            if node['node_templates'] == node_template['name']:
                net_iapi_name = get_net_iapi_name(openpalette_data)
                if net_iapi_name in node_template['mapping_networks']:
                    all_host_names_with_iapi.append(node['name'])

    return all_host_names_with_iapi


def get_net_iapi_name(openpalette_data):
    for network in openpalette_data['networks']:
        if network.get('name', '') == 'net_iapi':
            return network.get('name', 'net_iapi')
    return 'net_iapi'


if __name__ == '__main__':
    pass
