from network_export import handle
import json
import shutil

def test():
    shutil.copyfile('openpalette.json', 'openpallte.json.bak')

    openpalette_path = '/openpalette.json'
    src_version = '1.1.1'

    handle(src_version, openpalette_path)

    with open('openpalette.json', 'r') as f:
        actual_output = json.load(f)

    with open('expected_output.json', 'r') as f:
        expected_output = json.load(f)

    shutil.move('openpallte.json.bak', 'openpalette.json')

    print(actual_output)
    print(expected_output)


if __name__ == '__main__':
    test()
