[default]
provider = ovs,linux

[mtu]
devicemtu = net_iapi:1500,net_traffic:1500

[ovs]
iapi = eth0:101:10.230.60.243/24:10.230.60.1
routes = 10.0.0.0/24:10.230.60.1


[linux]
net_traffic = eth1:102:191.116.53.243/24:191.116.53.1
net_api = eth3:102:191.116.54.243/24:191.116.54.1
routes = 192.168.0.0/24:191.116.53.1,192.166.0.0/24:191.116.54.1
net_traffic_v6=eth2:1000:[172:166:2::52]/112:[172:166:2::2]
routes_v6=[0:0:0:0]/0:[172:166:2::2]: