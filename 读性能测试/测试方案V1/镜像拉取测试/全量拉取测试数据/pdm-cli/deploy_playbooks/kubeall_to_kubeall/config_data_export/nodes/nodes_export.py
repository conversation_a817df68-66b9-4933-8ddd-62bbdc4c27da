import copy
import json
import logging
import sys
import traceback

sys.path.append("/etc/pdm")

from deploy_playbooks.common import common

COM_VARS_FILE = "/root/common/com_vars.yml"
PORT_VARS_FILE = "/root/common/port_vars.yml"
LOG = logging.getLogger(__name__)


def get_nodeworker_url():
    try:
        ops_ip = common.get_yaml_val(COM_VARS_FILE, 'openpalette_service_ip')
        ops_port = common.get_yaml_val(PORT_VARS_FILE,
                                       'openpalette_service_port')
    except Exception as e:
        error_info = "get op service ip and port exception:%s" % e
        LOG.error(error_info)
        raise Exception(error_info)
    if ":" in ops_ip:
        url = "http://[%s]:%s" % (ops_ip, ops_port)
    else:
        url = "http://%s:%s" % (ops_ip, ops_port)
    url = url + "/nodeworker/v1/tenants/admin/"
    return url


def get_nodes_list():
    url = get_nodeworker_url()
    url = url + "nodes"
    result, nodes = common.request(url, 'GET')
    if not result or 'nodes' not in nodes:
        raise Exception("get nodes list fail")
    return nodes


def get_nodes_serverid_uuid_map():
    nodes = get_nodes_list()["nodes"]
    map = {}
    for node in nodes:
        serverid = node.get("serverid", "")
        map[serverid] = node.get("uuid", "")
    return map


def handle(src_version, output_file):
    try:
        LOG.info("nodes export handle start, src_version:%s,"
                 " output_file:%s" % (src_version, output_file))
        nodes_serverid_uuid_map = get_nodes_serverid_uuid_map()
        with open(output_file, "r") as f1:
            openpalette_conf = json.load(f1)
        data = copy.deepcopy(openpalette_conf)
        openpalette_nodes = data.get("nodes", [])
        for node in openpalette_nodes:
            if not node.get("nodeid"):
                if nodes_serverid_uuid_map.get(node.get("uuid"), ""):
                    node["nodeid"] = nodes_serverid_uuid_map[node.get("uuid")]
                    LOG.info("update node(%s) nodeid to %s" %
                             (node.get("name"), node["nodeid"]))
                else:
                    error_info = "uuid %s not in nodeworker nodes' serverid"\
                                 % node.get("uuid")
                    raise Exception(error_info)
        with open(output_file, "w") as f2:
            json.dump(data, f2)
        LOG.info("nodes export handle end success")
        return True
    except Exception as e:
        LOG.error('traceback: %s' % traceback.format_exc())
        error_info = "%s" % e
        LOG.error(error_info)
        raise e
