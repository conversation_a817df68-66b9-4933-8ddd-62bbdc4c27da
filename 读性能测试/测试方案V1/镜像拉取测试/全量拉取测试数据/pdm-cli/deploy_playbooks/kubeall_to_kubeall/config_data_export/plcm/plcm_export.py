# plcm/plcm_export.py
import logging
import json
import sys
import traceback

sys.path.append("/etc/pdm")

from deploy_playbooks.common import common


LOG = logging.getLogger(__name__)
COM_VARS_FILE = "/root/common/com_vars.yml"
NEED_UPDATE_TCF_SCALE_ID = ["2", "300"]
NEW_TCF_SCALE_ID = "16"


def get_ntp_from_comvar():
    return common.get_yaml_val(COM_VARS_FILE, 'ntp_server_external')


def get_timezone():
    return common.get_timezone_by_cmd()


def need_update_tcf_scale_id(src_version: str, data):
    if not (src_version.startswith("v7.23.20") or
            src_version.startswith("v7.23.30")):
        LOG.debug("src_version %s not need reverse" % src_version)
        return False
    tcf_scenario = data.get("global", [])[0].get("tcf_scenario")
    if tcf_scenario != "RAN-MEC-MINI":
        LOG.debug("tcf_scenario %s not need reverse" % tcf_scenario)
        return False
    origin_scale_id = data.get("global", [])[0].get("tcf_scale_id")
    if origin_scale_id not in NEED_UPDATE_TCF_SCALE_ID:
        LOG.debug("origin_scale_id %s not need reverse" % origin_scale_id)
        return False
    return True


def has_key(key, data):
    if key not in data or not isinstance(data[key], list) or \
            len(data[key]) == 0:
        LOG.debug("key %s not exists in data" % key)
        return False
    return True


def update_funcset_gpu(data):
    if not has_key("features", data) or (not has_key("clusters", data)):
        LOG.debug("Not need update funcset gpu, return")
        return data
    features = data['features']
    clusters = data['clusters'][0]
    enable_gpu = clusters.get("enable_gpu", "").lower()
    funcset_gpu = "normal" if enable_gpu == "true" else "none"
    for feature in features:
        if feature.get("capability", "") == 'FunctionSet_gpu':
            feature['value'] = funcset_gpu
            LOG.debug("update funcset_gpu to %s" % funcset_gpu)
    return data


def update_global(data, src_version):
    if 'global' not in data or not isinstance(data['global'], list):
        data['global'] = []
        LOG.info("Created 'global' key as it was not present in the data.")
    if len(data['global']) == 0:
        data['global'].append({'ntp_server': '', 'timezone': ''})
        LOG.info("Added default structure to 'global' key.")

    ntp = get_ntp_from_comvar()
    timezone = get_timezone()
    LOG.debug(f"ntp: {ntp}, timezone: {timezone}")
    data['global'][0]['ntp_server'] = ntp
    data['global'][0]['timezone'] = timezone

    if need_update_tcf_scale_id(src_version, data):
        data['global'][0]['tcf_scale_id'] = NEW_TCF_SCALE_ID
    return data


def update_global_and_gpu(data, src_version):
    data_after_global_update = update_global(data, src_version)
    return update_funcset_gpu(data_after_global_update)


def handle(src_version, output_file):
    try:
        LOG.debug(f'plcm_export called with: {src_version}, {output_file}')
        with open(output_file, "r") as f:
            data = json.load(f)
        updated_data = update_global_and_gpu(data, src_version)
        with open(output_file, "w") as f:
            data = json.dump(updated_data, f, indent=4)
        LOG.info("plcm export succeed.")
        return True
    except Exception as e:
        LOG.error(f"An error occurred: {e}")
        LOG.error(traceback.format_exc())
        raise Exception(e)
