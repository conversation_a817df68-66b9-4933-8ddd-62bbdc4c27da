import unittest
from unittest.mock import patch

from xlb_export import ExportXLBConfig
from xlb_export import Utils

instance_input = {
    "xlb_type": "lvs",  # 选配，lvs或者xdp
    "xlb_name": "lvs1",  # XLB实例名称，string
    "namespace": "default",  # 租户。没配则默认是default
    "workmode": "service",  # 工作模式。没配默认是default
    "xlb_exvips": {  # 选配，额外vip，只能在workmode是service的模式下才可以配置
        "exvips_v4": ["*************"],  # exvips_v4和exvips_v6必配一个
        "exvips_v6": ["100::11"]
    },
    "xlb_nodes": [  # XLB实例创建的节点信息。单实例xlb时，配置一个节点；XLB使用HA时，配置两个节点。至多配置两个节点。
        {
            "node_ip": "***********",  # 节点的管理平面net_api的地址
            "netxlb_ip": "***********"  # XLB容器时存在字段，从创建的netxlb的IP组中的IP地址填入。
        },
        {
            "node_ip": "***********",
            "netxlb_ip": "***********"  # XLB容器时存在字段，从创建的netxlb的IP组中的IP地址填入。
        }],
    "xlb_networks": [  # XLB实例的网络信息，只能在workmode是default的模式下才可以配置
        {
            "interface": "bond0",  # 选配。当前网络使用的网口名称。

            "is_public_ip": "true",  # 选配，string。

            "cidr_v4": "*********/24",  # cidr_v4和cidr_v6必配一个
            "cidr_v6": "",

            # vips_v4和vips_v6是有条件选配配置。1）必须保证XLB实例的所有的网络中至少存在一个vip地址； 2）当is_public_ip配置为true时，当前的网络中至少存在一个vip地址
            "vips_v4": ["**********", "**********"],
            "vips_v6": [],

            "eth_nics": ["eth1", "eth2"]  # 选配。XLB网络使用的实际物理口。例如是bond口时，把bond口下的所有成员口配置在eth_nics中。
        },
        {
            "interface": "vgh-xxxx",  # 选配。当前网络使用的网口名称。

            "is_public_ip": "false",  # 选配，string。

            "cidr_v4": "*********/24",  # cidr_v4和cidr_v6必配一个
            "cidr_v6": "",

            # vips_v4和vips_v6是有条件选配配置。1）必须保证XLB实例的所有的网络中至少存在一个vip地址； 2）当is_public_ip配置为true时，当前的网络中至少存在一个vip地址
            "vips_v4": ["**********", "**********"],
            "vips_v6": [],

            "eth_nics": ["eth3", "eth4"]  # 选配。XLB网络使用的实际物理口。例如是bond口时，把bond口下的所有成员口配置在eth_nics中。
        }],
    "xlb_snat_rules": [  # 选配，XLB使用的SNAT规则，只能在workmode是default的模式下才可以配置
        {
            "source_cidr": "**********/24",  # 必配字段，内部网络平面cidr。配置要求：合法的cidr
            "dst_cidr": "************/24",  # 必配字段，目的网段cidr。根据该目的网段查路由，得到内访外的出向网口
            "to_source_ip": "*************",  # 必配字段，SNAT时替换源地址。该地址需要在出向网口所在的xlb_network对应的VIPS中
        },
        {
            "source_cidr": "172:30::/64",  # 必配字段，内部网络平面cidr。配置要求：合法的cidr
            "dst_cidr": "10:230:149::/64",  # 必配字段，目的网段cidr。根据该目的网段查路由，得到内访外的出向网口
            "to_source_ip": "10:230:149::25",  # 必配字段，SNAT时替换源地址。该地址需要在出向网口所在的xlb_network对应的VIPS中
        }],
    "xlb_routes": [  # 选配。跨L3网络访问时需要的路由信息。DR模式时需满足反向路由要求
        {
            "destination": "************/24",
            "nexthop": "*************4"
        },
        {
            "destination": "1111::/64",
            "nexthop": "1111::a1"
        }],
    "xlb_inner_snatrules": [  # 只能在workmode是default的模式下才可以配置
        {
            "source_cidr": "**********/24",  # 选配字段，如果不配，则默认以default、net-proxy网段进行匹配
            "dst_port": 12345,  # 必配字段，内访内目的端口号，int类型，1-65535
            "dst_ip": "**************",  # 必配字段，内访内目的地址
            "to_source_ip": "**************",  # 必配字段，内访内snat替换后的目的地址，所有规则的to_source_ip为相同
            "protocol": "tcp",  # 必配字段，内访内 协议类型，取tcp或者udp
        },
        {
            "source_cidr": "172:30::/64",  # 选配字段，如果不配，则默认以default、net-proxy网段进行匹配
            "dst_port": 12345,  # 必配字段，内访内目的端口号，int类型，1-65535
            "dst_ip": "10:230:149::123",  # 必配字段，内访内目的地址
            "to_source_ip": "10:230:149::123",  # 必配字段，内访内snat替换后的目的地址，所有规则的to_source_ip为相同
            "protocol": "tcp",  # 必配字段，内访内 协议类型，取tcp或者udp
        }
    ],
    "cpu_num": 16  # 选配。XLB独占使用的逻辑CPU个数，默认为4。极致场景下，此参数无效。
}

iplabels_input = {

    "iplabels": [{

        "lb_external_ip": "*************",

        "labels": ["net-test", "management-xlb"],

        "tenant": "spider"

    }, {

        "lb_external_ip": "1111::11",

        "labels": ["net-net"],

        "tenant": "default"

    }]

}

xlbippools_input = [
    {
        "apiVersion": "v1",
        "kind": "ConfigMap",
        "metadata": {
            "name": "xlbippool",
            "namespace": "admin"
        },
        "data": {
            "net_test": {
                "cidrv4": "190.0.0.0/24",
                "cidrv6": "111::/64",
                "serviceallocationpoolsv4": ["*********-***********"],
                "serviceallocationpoolsv6": ["111::1-111::fffe"],
                "loadBalancerIp_net_type": "External"
            },
            "net_name_list": ["net_test"]
        }
    },
    {
        "apiVersion": "v1",
        "kind": "ConfigMap",
        "metadata": {
            "name": "xlbippool-ccm1",
            "namespace": "admin"
        },
        "data": {
            "net_test2": {

                "cidrv4": "*********/24",
                "cidrv6": "121::/64",
                "serviceallocationpoolsv4": ["*********-***********"],
                "serviceallocationpoolsv6": ["121::1-121::fffe"],
                "loadBalancerIp_net_type": "External"

            },
            "net_name_list": ["net_test2"]
        }
    }
]

xlbippools_input2 = [
    {
        "apiVersion": "v1",
        "data": {
            "loadbalance_type": "xlb",
            "net_iapi": "{\"cidrv4\":\"************/24\",\"cidrv6\":\"\",\"serviceallocationpoolsv4\":[\"*************-*************\"],\"serviceallocationpoolsv6\":[],\"loadBalancerIp_net_type\":\"NodeNetwork\"}",
            "net_name_list": "[\"net_iapi\"]"
        },
        "kind": "ConfigMap",
        "metadata": {
            "creationTimestamp": "2024-01-01T03:03:28Z",
            "name": "xlbippool",
            "namespace": "admin",
            "resourceVersion": "9231",
            "uid": "c11a4b0d-2f03-4a47-a1cf-b4e05e6f4be2"
        }
    }
]


class TestUtils(unittest.TestCase):
    def test_get_ip_version_valid_ip4(self):
        result = Utils.get_ip_version('***********')
        self.assertEqual(result, 4)

    def test_get_ip_version_valid_ip6(self):
        result = Utils.get_ip_version('2001:db8::')
        self.assertEqual(result, 6)

    def test_get_ip_version_invalid_ip(self):
        try:
            Utils.get_ip_version('invalid_ip')
        except Exception as e:
            self.assertIn('get ip version error', e.args)


class TestExportXLBConfig(unittest.TestCase):
    def setUp(self):
        # patcher1 = patch('xlb_export.ExportXLBConfig.config_log', return_value=None)
        # self.mock_helper_method1 = patcher1.start()
        # self.addCleanup(patcher1.stop)

        patcher2 = patch('xlb_export.MSBConfig.get_msb_server', return_value='mocked')
        self.mock_helper_method2 = patcher2.start()
        self.addCleanup(patcher2.stop)

        patcher3 = patch('xlb_export.Utils.read_json', return_value={
            'networks': [
                {
                    'name': 'test_name1',
                    'cidr': [
                        '*********/24',
                        '100:1:1::/64'
                    ]
                }
            ],
            'additional_network_config': [
                {
                    'name': 'net-proxy',
                    'cidr': [
                        '*********/24',
                        '172:1:1::/64'
                    ]
                }
            ]
        })
        self.mock_helper_method3 = patcher3.start()
        self.addCleanup(patcher3.stop)

        patcher4 = patch('xlb_export.Utils.write_json', return_value=None)
        self.mock_helper_method4 = patcher4.start()
        self.addCleanup(patcher4.stop)

    def test_get_routes_v4(self):
        ret = ExportXLBConfig.get_routes(instance_input, 4)
        self.assertEqual('************/24:*************4', ret)

    def test_get_routes_v6(self):
        ret = ExportXLBConfig.get_routes(instance_input, 6)
        self.assertEqual('[1111::]/64:[1111::a1]', ret)

    def test_get_snatrules_v4(self):
        ret = ExportXLBConfig.get_snatrules(instance_input, 4)
        self.assertEqual('**********/24:************/24:*************', ret)

    def test_get_snatrules_v6(self):
        ret = ExportXLBConfig.get_snatrules(instance_input, 6)
        self.assertEqual('[172:30::]/64:[10:230:149::]/64:[10:230:149::25]', ret)

    def test_get_inner_snatrules_v4(self):
        ret = ExportXLBConfig.get_inner_snatrules(instance_input, 4)
        self.assertEqual('**********/24:**************:12345:tcp:**************', ret)

    def test_get_inner_snatrules_v6(self):
        ret = ExportXLBConfig.get_inner_snatrules(instance_input, 6)
        self.assertEqual('[172:30::]/64:[10:230:149::123]:12345:tcp:[10:230:149::123]', ret)

    def test_get_networks(self):
        export_handler = ExportXLBConfig('v1', 'output.json')
        output = export_handler.get_xlb_networks(instance_input, {})
        self.assertEqual(output.get('xlb_network_name', []), ['test_name1', 'net-proxy'])

    def test_get_networks_1(self):
        export_handler = ExportXLBConfig('v1', 'output.json')
        networks = instance_input['xlb_networks']
        instance_input['xlb_networks'] = []
        instance_input['xlb_networks'].append(networks[0])
        output = export_handler.get_xlb_networks(instance_input, {})
        self.assertEqual(output.get('xlb_network_name', []), 'test_name1')
        instance_input['xlb_networks'] = networks

    def test_reorganize_and_output_instance_config(self):
        export_handler = ExportXLBConfig('v1', 'output.json')
        export_handler.reorganize_and_output_instance_config({'xlb_instances': [instance_input]})

    # self.reorganize_and_output_iplabel_config(ip_labels)
    # self.reorganize_and_output_lb_global_config(xlbippools)
    def test_reorganize_and_output_iplabel_config(self):
        export_handler = ExportXLBConfig('v1', 'output.json')
        export_handler.reorganize_and_output_iplabel_config(iplabels_input)

    def test_reorganize_and_output_lb_global_config(self):
        export_handler = ExportXLBConfig('v1', 'output.json')
        export_handler.reorganize_and_output_lb_global_config(xlbippools_input2)




