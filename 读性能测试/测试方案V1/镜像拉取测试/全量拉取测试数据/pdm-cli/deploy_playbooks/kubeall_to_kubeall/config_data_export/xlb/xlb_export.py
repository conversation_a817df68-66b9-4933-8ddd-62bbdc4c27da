import http
from IPy import IP
import json
import logging
import requests
import sys
import traceback
import yaml
try:
    import commands
except ImportError:
    import subprocess as commands


xlb_url_prefix = '/lbmanager/v1/tenants/admin/xlb/'
port_vars_file = '/root/common/port_vars.yml'
com_vars_file = '/root/common/com_vars.yml'
HEADERS = {"accept": "application/json",
           "content-type": "application/json"}
LOG = logging.getLogger(__name__)
retry_times = 3
timeout = 10


def handle(src_version, output_file):
    try:
        export_handler = ExportXLBConfig('', src_version, output_file)
        export_handler.export()
        LOG.info("handle export xlb config success")
        return True
    except Exception as e:
        raise e


class ExportXLBConfig(object):
    def __init__(self, scene, src_version, output_file):
        try:
            # self.config_log()
            self.tcf_scenario = scene
            self.msb_server = MSBConfig.get_msb_server()
            self.src_version = src_version
            self.output_file = output_file
            self.export_config = Utils.read_json(output_file)
        except Exception:
            err_msg = "init xlb config handler error"
            LOG.error(", traceback:%s", err_msg, traceback.format_exc())
            raise Exception(err_msg)

    def export(self):
        LOG.info('======== export xlb config start ========')
        try:
            xlb_instances = (self.request_xlbmanager("instances").
                             get('xlb_instances', []))
            ip_labels = self.request_xlbmanager("iplabels")
            lb_global_config = self.request_xlbmanager("loadbalance_global")
            self.reorganize_and_output_xlb_related_config(xlb_instances, ip_labels, lb_global_config)
        except Exception:
            err_msg = "export xlb config error"
            LOG.error("%s, traceback:%s", err_msg, traceback.format_exc())
            raise Exception(err_msg)

    def reorganize_and_output_xlb_related_config(self, xlb_instances, ip_labels, lb_global_config):
        try:
            self.reorganize_and_output_instance_config(xlb_instances)
            self.reorganize_and_output_iplabel_config(ip_labels)
            self.reorganize_and_output_lb_global_config(lb_global_config)
        except Exception:
            err_msg = "reorganize 3 sheet config error"
            LOG.error("%s, traceback:%s", err_msg, traceback.format_exc())
            raise Exception(err_msg)

    def request_xlbmanager(self, resource_type):
        LOG.info("request xlbmanager get %s" % resource_type)
        url = self.msb_server + xlb_url_prefix + resource_type
        count = 0
        try:
            while count < retry_times:
                resp = requests.get(url=url, headers=HEADERS, allow_redirects=False,
                                    timeout=timeout)
                if resp.status_code == http.HTTPStatus.NOT_FOUND:
                    if (self.src_version < 'v7.23.30' or
                            'fusionized' in self.tcf_scenario):
                        LOG.warning('v7.23.20 or fusionized-TCF not support %s '
                                    'restful API' % resource_type)
                        return {}
                if resp.status_code != http.HTTPStatus.OK and resp.status_code != http.HTTPStatus.CREATED:
                    LOG.error('get xlb resource failed, code = %d, msg = %s', resp.status_code, resp.reason)
                    count += 1
                else:
                    return json.loads(resp.text)
            err_msg = "request xlbmanager retry timeout"
            raise Exception(err_msg)
        except Exception:
            err_msg = "request xlbmanager error"
            LOG.error("%s, traceback:%s", err_msg, traceback.format_exc())
            raise Exception(err_msg)

    def reorganize_and_output_instance_config(self, xlb_instances):
        if not xlb_instances:
            LOG.warning("export xlb instance config empty")
            return False
        try:
            outputs = []
            for instance in xlb_instances:
                exvip_data = self.get_xlb_exvips(instance)
                output = {
                    'xlb_name': instance.get('xlb_name'),
                    'xlb_type': instance.get('xlb_type', 'lvs'),
                    'workmode': instance.get('workmode', 'default'),
                    'tenant': instance.get('namespace', 'default'),
                    'xlb_exvips_v4': ','.join(exvip_data.get('exvips_v4', [])),
                    'xlb_exvips_v6': ','.join(exvip_data.get('exvips_v6', [])),
                    'cpu_num': str(instance.get('cpu_num', 0)),
                    'routes_v4': self.get_routes(instance, 4),
                    'routes_v6': self.get_routes(instance, 6),
                    'snatrules_v4': self.get_snatrules(instance, 4),
                    'snatrules_v6': self.get_snatrules(instance, 6),
                    'inner_snatrules_v4': self.get_inner_snatrules(instance, 4),
                    'inner_snatrules_v6': self.get_inner_snatrules(instance, 6)
                }
                output = self.get_xlb_networks(instance, output)
                outputs.append(output)
            self.export_config['xlb_instance_config'] = outputs
            Utils.write_json(self.output_file, self.export_config)
            LOG.info("export xlb instance config success")
            return True
        except Exception:
            err_msg = "reorganize xlb instance config error"
            LOG.error("%s, traceback:%s", err_msg, traceback.format_exc())
            raise Exception(err_msg)

    @classmethod
    def get_xlb_exvips(cls, body):
        try:
            data = body.get('xlb_exvips', None)
            if not data:
                return {}
            else:
                exvips_v4 = data.get("exvips_v4", None)
                exvips_v6 = data.get("exvips_v6", None)
                exvips_v4 = [] if not exvips_v4 else exvips_v4
                exvips_v6 = [] if not exvips_v6 else exvips_v6
                data['exvips_v4'] = exvips_v4
                data['exvips_v6'] = exvips_v6
                return data
        except Exception:
            return {}

    @classmethod
    def get_dict_value_by_key(cls, body, key):
        data = body.get(key, None)
        if not data:
            return []
        return data

    def get_routes(self, instance, target_version):
        try:
            xlb_routes = self.get_dict_value_by_key(instance, "xlb_routes")
            routes = []
            for route in xlb_routes:
                dst = route.get('destination', '')
                next_hop = route.get('nexthop', '')
                routes_version = Utils.get_ip_version(next_hop)
                if routes_version != target_version:
                    continue
                if target_version == 6:
                    next_hop = '[%s]' % next_hop
                    if '/' in dst:
                        dst_segments = dst.split('/')
                        dst = '[%s]' % dst_segments[0] + '/' + dst_segments[1]
                routes.append('%s:%s' % (dst, next_hop))
            return ','.join(routes)
        except Exception:
            err_msg = "get xlb routes error"
            LOG.error("%s, traceback:%s", err_msg, traceback.format_exc())
            raise Exception(err_msg)

    def get_snatrules(self, instance, target_version):
        try:
            xlb_snat_rules = self.get_dict_value_by_key(instance, 'xlb_snat_rules')
            snat_rules = []
            for rule in xlb_snat_rules:
                dst = rule.get('dst_cidr', '')
                src = rule.get('source_cidr', '')
                to_source_ip = rule.get('to_source_ip', '')
                ip_ver = Utils.get_ip_version(to_source_ip)
                if ip_ver != target_version:
                    continue
                if target_version == 6:
                    to_source_ip = '[%s]' % to_source_ip
                    if '/' in dst:
                        dst_segments = dst.split('/')
                        dst = '[%s]' % dst_segments[0] + '/' + dst_segments[1]
                    if '/' in src:
                        src_segments = src.split('/')
                        src = '[%s]' % src_segments[0] + '/' + src_segments[1]
                snat_rules.append('%s:%s:%s' % (src, dst, to_source_ip))
            return ','.join(snat_rules)
        except Exception:
            err_msg = "get xlb snat rules error"
            LOG.error("%s, traceback:%s", err_msg, traceback.format_exc())
            raise Exception(err_msg)

    def get_inner_snatrules(self, instance, target_version):
        try:
            xlb_inner_snat_rules = self.get_dict_value_by_key(instance, 'xlb_inner_snatrules')
            inner_snat_rules = []
            for rule in xlb_inner_snat_rules:
                src = rule.get('source_cidr', '')
                dst_ip = rule.get('dst_ip', '')
                dst_port = rule.get('dst_port', 0)
                to_source_ip = rule.get('to_source_ip', '')
                protocol = rule.get('protocol', '')
                ip_ver = Utils.get_ip_version(to_source_ip)
                if ip_ver != target_version:
                    continue
                if target_version == 6:
                    to_source_ip = '[%s]' % to_source_ip
                    dst_ip = '[%s]' % dst_ip
                    if '/' in src:
                        src_segments = src.split('/')
                        src = '[%s]' % src_segments[0] + '/' + src_segments[1]
                inner_snat_rules.append('%s:%s:%d:%s:%s' % (src, dst_ip, dst_port, protocol, to_source_ip))
            return ','.join(inner_snat_rules)
        except Exception:
            err_msg = "get xlb inner snat rules error"
            LOG.error("%s, traceback:%s", err_msg, traceback.format_exc())
            raise Exception(err_msg)

    def get_xlb_networks(self, instance, output):
        output['xlb_network_name'] = []
        output['interface'] = []
        output['eth_nics'] = []
        output['cidr_v4'] = []
        output['cidr_v6'] = []
        output['vips_v4'] = []
        output['vips_v6'] = []
        output['is_public_ip'] = []
        try:
            xlb_networks = instance.get('xlb_networks', [])
            for network in xlb_networks:
                vips_v4 = network.get("vips_v4", None)
                vips_v6 = network.get("vips_v6", None)
                eth_nics = network.get("eth_nics", None)
                if not vips_v4:
                    network['vips_v4'] = []
                if not vips_v6:
                    network['vips_v6'] = []
                if not eth_nics:
                    network['eth_nics'] = []
                net_name = self.get_net_name(network)
                output['xlb_network_name'].append(net_name)
                if net_name != 'net-proxy':
                    output['interface'].append(network.get('interface', ''))
                    output['eth_nics'].append(','.join(network.get('eth_nics', [])))
                else:
                    output['interface'].append('')
                    output['eth_nics'].append('')
                output['cidr_v4'].append(network.get('cidr_v4', ''))
                output['cidr_v6'].append(network.get('cidr_v6', ''))
                output['vips_v4'].append(','.join(network.get('vips_v4', [])))
                output['vips_v6'].append(','.join(network.get('vips_v6', [])))
                is_public_ip = network.get('is_public_ip', 'false')
                if is_public_ip == 'true':
                    output['is_public_ip'].append('yes')
                else:
                    output['is_public_ip'].append('no')
            if len(output['xlb_network_name']) == 1:
                output['xlb_network_name'] = output['xlb_network_name'][0]
                output['interface'] = output['interface'][0]
                output['eth_nics'] = output['eth_nics'][0]
                output['cidr_v4'] = output['cidr_v4'][0]
                output['cidr_v6'] = output['cidr_v6'][0]
                output['vips_v4'] = output['vips_v4'][0]
                output['vips_v6'] = output['vips_v6'][0]
                output['is_public_ip'] = output['is_public_ip'][0]
            elif len(output['xlb_network_name']) == 0:
                output['xlb_network_name'] = ''
                output['interface'] = ''
                output['eth_nics'] = ''
                output['cidr_v4'] = ''
                output['cidr_v6'] = ''
                output['vips_v4'] = ''
                output['vips_v6'] = ''
                output['is_public_ip'] = ''
            return output
        except Exception:
            err_msg = "get xlb networks error"
            LOG.error("%s, traceback:%s", err_msg, traceback.format_exc())
            raise Exception(err_msg)

    def get_net_name(self, network):
        try:
            net_name = network.get('netname', '')
            if net_name != '':
                return net_name
            cidr = network.get('cidr_v4', '') if network.get('cidr_v4', '') else network.get('cidr_v6', '')
            node_networks = self.export_config.get('networks', [])
            # cidr不一定是列表，但代码这样写似乎也行。无论cidr是字符串还是列表，都可以用in来判断是否包含目标cidr
            for node_network in node_networks:
                if cidr in node_network.get('cidrs', []):
                    return node_network.get('name', '')
            app_networks = self.export_config.get('additional_network_config', [])
            for app_network in app_networks:
                if cidr in app_network.get('cidr', []):
                    return app_network.get('name', '')
            return ''
        except Exception:
            err_msg = "get xlb network name error"
            LOG.error("%s, traceback:%s", err_msg, traceback.format_exc())
            raise Exception(err_msg)

    def reorganize_and_output_iplabel_config(self, ip_labels):
        if not ip_labels:
            LOG.warning("export ip labels config empty")
            return True
        try:
            outputs = []
            xlb_ip_labels = ip_labels.get("iplabels", [])
            for label in xlb_ip_labels:
                output = {
                    'xlb_external_ip_address': label.get('lb_external_ip', ''),
                    'tenant': label.get('tenant', 'default'),
                    'label': ','.join(label.get('labels', []))
                }
                outputs.append(output)
            self.export_config['loadbalancer_service_ip_config'] = outputs
            Utils.write_json(self.output_file, self.export_config)
            LOG.info("export ip label config success")
            return True
        except Exception:
            err_msg = "reorganize loadbalancer service ip config error"
            LOG.error("%s, traceback:%s", err_msg, traceback.format_exc())
            raise Exception(err_msg)

    def reorganize_and_output_lb_global_config(self, lb_global_config):
        if not lb_global_config:
            LOG.warning("export lb global config empty")
            return False
        try:
            outputs = []
            pools = self.get_dict_value_by_key(lb_global_config, 'loadbalanceIpPools')
            if len(pools) == 0:
                self.export_config['loadbalancer_global'] = outputs
                Utils.write_json(self.output_file, self.export_config)
            output = {
                'load_balancer_type': 'xlb',
                'load_balancer_networks': [],
                'loadBalancerIp_net_type': [],
                'loadBalancerIpV4s': [],
                'loadBalancerIpV6s': [],
                'tenant': []
            }
            for xlbippool in pools:
                output['load_balancer_networks'].append(xlbippool.get('net_name', ''))
                output['loadBalancerIp_net_type'].append(xlbippool.get('loadBalancerIp_net_type', ''))
                output['loadBalancerIpV4s'].append(','.join(xlbippool.get('serviceallocationpoolsv4', [])))
                output['loadBalancerIpV6s'].append(','.join(xlbippool.get('serviceallocationpoolsv6', [])))
                output['tenant'].append(xlbippool.get('namespace', 'default'))
            if len(output['tenant']) == 1:
                output['tenant'] = output['tenant'][0]
                output['loadBalancerIp_net_type'] = output['loadBalancerIp_net_type'][0]
                output['loadBalancerIpV6s'] = output['loadBalancerIpV6s'][0]
                output['loadBalancerIpV4s'] = output['loadBalancerIpV4s'][0]
                output['load_balancer_networks'] = output['load_balancer_networks'][0]
            outputs.append(output)
            self.export_config['loadbalancer_global'] = outputs
            Utils.write_json(self.output_file, self.export_config)
            LOG.info("export lb global config success")
            return True
        except Exception:
            err_msg = "reorganize loadbalancer global config error"
            LOG.error("%s, traceback:%s", err_msg, traceback.format_exc())
            raise Exception(err_msg)

    @staticmethod
    def organize_origin_str_to_json(origin_data):
        try:
            # origin_data = origin_data[1:-1]
            # origin_data = origin_data.replace('\\', '')
            return json.loads(origin_data)
        except Exception:
            err_msg = "json loads lb global data error"
            LOG.error("%s, traceback:%s", err_msg, traceback.format_exc())
            raise Exception(err_msg)


class Utils(object):
    @staticmethod
    def read_yaml_file(file_path):
        try:
            with open(file_path) as fp:
                documents = yaml.safe_load(fp)
            return documents
        except Exception:
            err_msg = "read json error"
            LOG.error("%s, traceback:%s", err_msg, traceback.format_exc())
            raise Exception(err_msg)

    @staticmethod
    def read_json(file_name):
        try:
            with open(file_name, 'r') as fp:
                return json.load(fp)
        except Exception:
            err_msg = "read json error"
            LOG.error("%s, traceback:%s", err_msg, traceback.format_exc())
            raise Exception(err_msg)

    @staticmethod
    def write_json(json_path, data):
        try:
            with open(json_path, 'w') as fp:
                json.dump(data, fp)
        except Exception:
            err_msg = "write json error"
            LOG.error("%s, traceback:%s", err_msg, traceback.format_exc())
            raise Exception(err_msg)

    @staticmethod
    def execute_cmd(cmd):
        try:
            status, output = commands.getstatusoutput(cmd)
            return status, output
        except Exception:
            err_msg = "exec command error"
            LOG.error("%s, traceback:%s", err_msg, traceback.format_exc())
            raise Exception(err_msg)

    @staticmethod
    def get_ip_version(ip_str):
        if ip_str == '':
            return 0
        try:
            return IP(ip_str).version()
        except Exception:
            err_msg = "get %s ip version error" % ip_str
            LOG.error("%s, traceback:%s", err_msg, traceback.format_exc())
            raise Exception(err_msg)


class MSBConfig(object):
    @classmethod
    def get_msb_server(cls):
        try:
            msb_ip = cls.get_msb_ip()
            msb_port = cls.get_msb_port()
            if IP(msb_ip).version() == 4:
                return 'http://%s:%s' % (msb_ip, msb_port)
            elif IP(msb_ip).version() == 6:
                return 'http://[%s]:%s' % (msb_ip, msb_port)
            else:
                LOG.error("get msb url ip error")
                return ''
        except Exception:
            err_msg = "get msb server error"
            LOG.error("%s, traceback:%s", err_msg, traceback.format_exc())
            raise Exception(err_msg)

    @classmethod
    def get_msb_ip(cls):
        try:
            key = 'openpalette_service_ip'
            data_map = Utils.read_yaml_file(com_vars_file)
            for data in data_map:
                if key in data:
                    return data.get(key, '')
            return ''
        except Exception:
            err_msg = "get msb ip error"
            LOG.error("%s, traceback:%s", err_msg, traceback.format_exc())
            raise Exception(err_msg)

    @classmethod
    def get_msb_port(cls):
        try:
            key = 'openpalette_service_port'
            data_map = Utils.read_yaml_file(port_vars_file)
            for data in data_map:
                if key in data:
                    return data.get(key, '')
            return ''
        except Exception:
            err_msg = "get msb port error"
            LOG.error("%s, traceback:%s", err_msg, traceback.format_exc())
            raise Exception(err_msg)


if __name__ == "__main__":
    try:
        if len(sys.argv) != 3:
            print("Usage: python xlb_export.py <src_version> <output_file>")
        else:
            arg1 = sys.argv[1]
            arg2 = sys.argv[2]
            handle(arg1, arg2)
    except Exception:
        err_msg = "export xlb config error"
        print("%s, traceback:%s" % (err_msg, traceback.format_exc()))
