---
- hosts: nodes
  become: true
  gather_facts: no
  remote_user: ubuntu
  vars_files:
  - vars.yml
  - /root/common/com_vars.yml
  tasks:
    - name: handle sdclient_vip when ipv6
      set_fact:
        sdclient_vip: "[{{ zenap_msb_sdclient_ip }}]"
      when:
          - "':' in zenap_msb_sdclient_ip"

    - block:
      - name: rm sdclient_col pacemaker service
        pacemaker_service:
          name: "sdclient_col"
          state: absent
        run_once: true

      - name: rm msb_zdh_net_config pacemaker service
        pacemaker_service:
          name: "msb_zdh_net_config"
          state: absent
        run_once: true

      - name: create sdclient zdh vip
        pacemaker_vip:
          name: "sdclient_zdh_vip"
          ip: "{{ zenap_msb_sdclient_zdh_ip }}"
          netmask: "{{ netzdh_netmask }}"
        run_once: true
        when:
          - zenap_msb_sdclient_zdh_ip != ""

      - name: create sdclient zdh v6 vip
        pacemaker_vip:
          name: "sdclient_zdh_vip_v6"
          ip: "{{ zenap_msb_sdclient_zdh_ip_v6 }}"
          netmask: "{{ netzdh_netmask_v6 }}"
        run_once: true
        when:
          - zenap_msb_sdclient_zdh_ip_v6 != ""

      - name: config sdclient zdh vip when ha_enable
        script: run4vipzdh.sh {{ zenap_msb_sdclient_zdh_ip }}
        run_once: true
        when:
          - zenap_msb_sdclient_zdh_ip != ""

      - name: config sdclient zdh v6 vip when ha_enable
        script: run4vipzdh.sh {{ zenap_msb_sdclient_zdh_ip_v6 }} v6
        run_once: true
        when:
          - zenap_msb_sdclient_zdh_ip_v6 != ""

      - name: copy msb_zdh_net_config ocf file
        template: src=msb_zdh_net_config dest=/usr/lib/ocf/resource.d/heartbeat/msb_zdh_net_config mode=0755

      - name: create msb_zdh_net_config resources when ha_enable
        pacemaker_service:
          name: msb_zdh_net_config
          ocf_name: msb_zdh_net_config
          monitor_interval: 10s
          start_timeout: 120s
          stop_timeout: 120s
        ignore_errors: no
        run_once: true

      - name: set controller node ip to sdclient
        uri:
          url: http://{{ sdclient_vip }}:10081/api/microservices/v1/config/node_ips
          method: PUT
          src: /etc/pdm/conf/controller_zdh_and_api_ips
          body_format: json
          timeout: 10
          status_code: 201
        run_once: true
        retries: 3
        register: _result
        until: _result.status == 201

      - name: set zdh vip to sdclient
        uri:
          url: http://{{ sdclient_vip }}:10081/api/microservices/v1/config/sdclient_vip
          method: PUT
          body: [{"network_plane_type": "zdh-net","sdclient_vip": "{{zenap_msb_sdclient_zdh_ip}}","sdclient_vip_v6": "{{zenap_msb_sdclient_zdh_ip_v6}}"}]
          body_format: json
          timeout: 10
          status_code: 201
        run_once: true
        retries: 3
        register: _result
        until: _result.status == 201
      when:
        - is_tcf is defined and is_tcf
        - tcf_scenario is defined and tcf_scenario == "fusionized-TCF" and not (is_new_fusionized_TCF is defined and is_new_fusionized_TCF == true)

    - block:
      - name: prepare initGlobalRules dir
        shell: mkdir -p /home/<USER>/shieldAgent/initGlobalRules
        ignore_errors: no

      - name: copy service_msb_rules.yml
        copy:
          src: service_msb_rules.yml
          dest: /home/<USER>/shieldAgent/initGlobalRules/service_msb_rules.yml
          mode: '0640'

      - name: copy service_networks.yml
        template: src=service_networks.yml dest=/home/<USER>/shieldAgent/initGlobalRules/service_networks.yml mode=0640

      - name: napprules reload
        shell: napprules reload --platname service --modelname msb
        ignore_errors: no
      when:
        - is_tcf is defined and is_tcf
        - tcf_scenario is defined and tcf_scenario == "fusionized-TCF"