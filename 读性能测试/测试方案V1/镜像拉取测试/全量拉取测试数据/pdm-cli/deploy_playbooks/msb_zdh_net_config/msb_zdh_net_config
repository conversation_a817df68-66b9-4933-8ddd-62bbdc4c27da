#jinja2:variable_start_string:'[%' , variable_end_string:'%]', trim_blocks: False
#!/bin/bash


: ${OCF_FUNCTIONS_DIR=${OCF_ROOT}/lib/heartbeat}
. ${OCF_FUNCTIONS_DIR}/ocf-shellfuncs

LOCAL5_PRINT="logger -i -p local5.info"
sdclient_zdh_vip=[%zenap_msb_sdclient_zdh_ip%]
sdclient_zdh_vip_v6=[%zenap_msb_sdclient_zdh_ip_v6%]
zdh_net_ip=[%net_zdh_ip%]
zdh_net_ip_v6=[%net_zdh_ip_v6%]
iptables_zdh_cmd=$(echo ${sdclient_zdh_vip} | grep -q ":" && echo "ip6tables -w" || echo "iptables -w")
iptables_v6_cmd="ip6tables -w"
RCLOCALFILE="/etc/rc.d/rc.local"
IPTABLES_RULE_LINES=4

meta_data() {
  cat <<END
<?xml version="1.0"?>
<!DOCTYPE resource-agent SYSTEM "ra-api-1.dtd">
<resource-agent name="msb_zdh_net_config">
<version>1.0</version>
<longdesc lang="en">msb_zdh_net_config RA</longdesc>
<shortdesc lang="en">msb_zdh_net_config RA</shortdesc>
<actions>
    <action name="start" timeout="120s" />
    <action name="stop" timeout="120s" />
    <action name="status" timeout="20s" />
    <action name="monitor" depth="0" timeout="60s" interval="10s" />
    <action name="meta-data" timeout="120s" />
    <action name="validate-all"  timeout="20s" />
</actions>
</resource-agent>
END
  return $OCF_SUCCESS
}

function print_ocf_log() {
    PID=$(printf "%d" $$)
    COMPNENT=msb_zdh_net_config

    if [ $1 = "debug" ]; then
        if [ "a${HA_debug}" = "a1" ]; then
            $LOCAL5_PRINT "$COMPNENT[$PID] ${FUNCNAME[*]:1} - $1: $2"
        fi
        return 0
    else
        $LOCAL5_PRINT "$COMPNENT[$PID] ${FUNCNAME[*]:1} - $1: $2"
    fi

}

install_iptables_rules() {
  if [ ! -e "$RCLOCALFILE" ]; then
    touch $RCLOCALFILE
    chmod +x $RCLOCALFILE
  fi
  installzdhrule PREROUTING
  installzdhrule OUTPUT
}

install_iptables_v6_rules() {
  if [ ! -e "$RCLOCALFILE" ]; then
    touch $RCLOCALFILE
    chmod +x $RCLOCALFILE
  fi
  installzdhv6rule PREROUTING
  installzdhv6rule OUTPUT
}

installzdhrule() {
  chain=$1
  ${iptables_zdh_cmd} -t nat -A $chain -m addrtype --dst-type LOCAL -d $sdclient_zdh_vip -p udp --dport 53 -j DNAT --to $zdh_net_ip 2>/dev/null
  echo "${iptables_zdh_cmd} -t nat -A $chain -m addrtype --dst-type LOCAL -d $sdclient_zdh_vip -p udp --dport 53 -j DNAT --to $zdh_net_ip" >>$RCLOCALFILE
  ${iptables_zdh_cmd} -t nat -A $chain -m addrtype --dst-type LOCAL -d $sdclient_zdh_vip -p tcp --dport 53 -j DNAT --to $zdh_net_ip 2>/dev/null
  echo "${iptables_zdh_cmd} -t nat -A $chain -m addrtype --dst-type LOCAL -d $sdclient_zdh_vip -p tcp --dport 53 -j DNAT --to $zdh_net_ip" >>$RCLOCALFILE
}

installzdhv6rule() {
  chain=$1
  ${iptables_v6_cmd} -t nat -A $chain -m addrtype --dst-type LOCAL -d $sdclient_zdh_vip_v6 -p udp --dport 53 -j DNAT --to $zdh_net_ip_v6 2>/dev/null
  echo "${iptables_v6_cmd} -t nat -A $chain -m addrtype --dst-type LOCAL -d $sdclient_zdh_vip_v6 -p udp --dport 53 -j DNAT --to $zdh_net_ip_v6" >>$RCLOCALFILE
  ${iptables_v6_cmd} -t nat -A $chain -m addrtype --dst-type LOCAL -d $sdclient_zdh_vip_v6 -p tcp --dport 53 -j DNAT --to $zdh_net_ip_v6 2>/dev/null
  echo "${iptables_v6_cmd} -t nat -A $chain -m addrtype --dst-type LOCAL -d $sdclient_zdh_vip_v6 -p tcp --dport 53 -j DNAT --to $zdh_net_ip_v6" >>$RCLOCALFILE
}

clear_iptables_rules() {
    clearzdhrule PREROUTING
    clearzdhrule OUTPUT
    sed -i '/'"${iptables_zdh_cmd}".*"${sdclient_zdh_vip}".*" 53 ".*"${zdh_net_ip}"'/d' $RCLOCALFILE
}

clear_iptables_v6_rules() {
    clearzdhv6rule PREROUTING
    clearzdhv6rule OUTPUT
    sed -i '/'"${iptables_v6_cmd}".*"${sdclient_zdh_vip_v6}".*" 53 ".*"${zdh_net_ip_v6}"'/d' $RCLOCALFILE
}

clearzdhrule() {
  chain=$1
  ${iptables_zdh_cmd} -t nat -D $chain -m addrtype --dst-type LOCAL -d $sdclient_zdh_vip -p udp --dport 53 -j DNAT --to $zdh_net_ip 2>/dev/null
  ${iptables_zdh_cmd} -t nat -D $chain -m addrtype --dst-type LOCAL -d $sdclient_zdh_vip -p tcp --dport 53 -j DNAT --to $zdh_net_ip 2>/dev/null
}

clearzdhv6rule() {
  chain=$1
  ${iptables_v6_cmd} -t nat -D $chain -m addrtype --dst-type LOCAL -d $sdclient_zdh_vip_v6 -p udp --dport 53 -j DNAT --to $zdh_net_ip_v6 2>/dev/null
  ${iptables_v6_cmd} -t nat -D $chain -m addrtype --dst-type LOCAL -d $sdclient_zdh_vip_v6 -p tcp --dport 53 -j DNAT --to $zdh_net_ip_v6 2>/dev/null
}

function monitor() {
    print_ocf_log debug "monitor ..."
    if [ -z $sdclient_zdh_vip ] && [ -z $sdclient_zdh_vip_v6 ];then
        print_ocf_log info "zenap_msb_sdclient_zdh_ip and zenap_msb_sdclient_zdh_ip_v6 is empty"
        return $OCF_SUCCESS
    fi

    #a=$(${iptables_zdh_cmd} -t nat -nL --line-numbers 2>/dev/null | grep ':53 ' |grep ${sdclient_zdh_vip})
    #echo "0000000000000000000$a">>/var/op-log/zenap_msb_sdclient/ocf-zdh.log

    if grep -q "DNS_HEALTHCHECK_IP" /usr/lib/ocf/resource.d/heartbeat/sdclient ; then
      if [ -n "$sdclient_zdh_vip" ]; then
        ${iptables_zdh_cmd} -t nat -nL --line-numbers 2>/dev/null | grep ':53 ' |grep ${sdclient_zdh_vip}
        if [ $? -eq 0 ]; then
          print_ocf_log "Warn" "iptables rules of sdclient_zdh_vip for dns setup installed"
          clear_iptables_rules
          print_ocf_log "Warn" "end to unnstall iptables rules of sdclient_zdh_vip for dns setup"
        fi
      fi
      if [ -n "$sdclient_zdh_vip_v6" ]; then
        ${iptables_v6_cmd} -t nat -nL --line-numbers 2>/dev/null | grep ':53 ' |grep ${sdclient_zdh_vip_v6}
        if [ $? -eq 0 ]; then
          print_ocf_log "Warn" "iptables rules of sdclient_zdh_vip_v6 for dns setup installed"
          clear_iptables_v6_rules
          print_ocf_log "Warn" "end to unnstall iptables rules of sdclient_zdh_vip_v6 for dns setup"
        fi
      fi
    else
      if [ -n "$sdclient_zdh_vip" ]; then
        rules=$(${iptables_zdh_cmd} -t nat -nL --line-numbers 2>/dev/null | grep ':53 ' |grep ${sdclient_zdh_vip} | wc -l)
        if [ $? -ne 0 ] || [ $rules -ne $IPTABLES_RULE_LINES ]; then
            print_ocf_log "Warn" "iptables rules of sdclient_zdh_vip for dns setup are lost, start to reinstall"
            clear_iptables_rules
            install_iptables_rules
            print_ocf_log "Warn" "end to reinstall iptables rules of sdclient_zdh_vip for dns setup"
        fi
      fi
      if [ -n "$sdclient_zdh_vip_v6" ]; then
        rules=$(${iptables_v6_cmd} -t nat -nL --line-numbers 2>/dev/null | grep ':53 ' |grep ${sdclient_zdh_vip_v6} | wc -l)
        if [ $? -ne 0 ] || [ $rules -ne $IPTABLES_RULE_LINES ]; then
            print_ocf_log "Warn" "iptables rules of sdclient_zdh_vip_v6 for dns setup are lost, start to reinstall"
            clear_iptables_v6_rules
            install_iptables_v6_rules
            print_ocf_log "Warn" "end to reinstall iptables rules of sdclient_zdh_vip_v6 for dns setup"
        fi
      fi
    fi

    #b=$(${iptables_zdh_cmd} -t nat -nL --line-numbers 2>/dev/null | grep ':53 ' |grep ${sdclient_zdh_vip})
    #echo "99999999999999999999999$b">>/var/op-log/zenap_msb_sdclient/ocf-zdh.log

    
    return $OCF_SUCCESS
}

function start() {
    print_ocf_log info "start ..."
    monitor
    return $OCF_SUCCESS

}

function stop() {
    print_ocf_log info "stop..."
    return $OCF_SUCCESS
}

function promote() {
    print_ocf_log info "promote ..."
    return $OCF_SUCCESS

}

function demote() {
    print_ocf_log info "demote ..."
    return $OCF_SUCCESS
}

function notify() {
    print_ocf_log info "notify ..."
    return $OCF_SUCCESS
}

function validate() {
    print_ocf_log info "validate ..."
    return $OCF_SUCCESS
}


case $__OCF_ACTION in
        monitor)
                monitor
                ;;
        start)
                start
                ;;
        stop)
                stop
                ;;
        promote)
                promote
                ;;
        demote)
                demote
                ;;
        notify)
                notify
                ;;
        meta-data)
                meta_data
                ;;
        validate-all)
                validate
                ;;
        *)
                echo "Usage: $0 {monitor|start|stop|restart|promote|demote|notify|validate-all|meta-data}"
                exit $OCF_ERR_UNIMPLEMENTED
                ;;

esac
status=$?
print_ocf_log debug "$__OCF_ACTION exit_status=$status"
exit $status
