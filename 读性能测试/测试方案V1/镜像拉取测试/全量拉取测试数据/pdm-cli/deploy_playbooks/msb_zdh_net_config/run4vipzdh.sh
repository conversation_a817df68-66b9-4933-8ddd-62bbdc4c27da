#!/bin/bash
umask 027

ocf_resource_name=sdclient
api_vip_name=${ocf_resource_name}_vip
zdh_vip_name=${ocf_resource_name}_zdh_vip
colocation_name=${ocf_resource_name}_col
ip_stack=$2
if [ "$ip_stack" = "v6" ]; then 
    zdh_vip_name=${ocf_resource_name}_zdh_vip_v6
    colocation_name=${ocf_resource_name}_col_v6
fi 

check_resource(){
  local resource_name=$1
  if crm status |grep "${resource_name}" &>/dev/null; then
      return 0
  fi
  return 1
}

if ! check_resource ${api_vip_name}; then
    echo "${api_vip_name} not exist,exit"
    exit 1
fi

if ! check_resource ${zdh_vip_name}; then
    echo "${zdh_vip_name} not exist,exit"
    exit 1
fi

#create colocation
if pcs cluster help --retry_wait=1 >/dev/null; then
    pcs constraint colocation add  ${zdh_vip_name}  with ${api_vip_name} id=${colocation_name} score=+inf --retry_wait=12
else
	crm configure colocation ${colocation_name} +inf: ${zdh_vip_name} ${api_vip_name}
fi
exit 0
