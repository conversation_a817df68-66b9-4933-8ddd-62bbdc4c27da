# -*- coding: UTF-8 -*-
import os
import sys
import json
import copy
import logging
import traceback
from IPy import IP

try:
    import ConfigParser

    class NewConfigParser(ConfigParser.ConfigParser):
        def write(self, fp):
            if self._defaults:
                fp.write("[%s]\n" % ConfigParser.DEFAULTSECT)
                for (key, value) in self._defaults.items():
                    fp.write("%s=%s\n" %
                             (key, str(value).replace('\n', '\n\t')))
                fp.write("\n")
            for section in self._sections:
                fp.write("[%s]\n" % section)
                for (key, value) in self._sections[section].items():
                    if (value is not None) or (self._optcre == self.OPTCRE):
                        key = "=".join((key, str(value).replace('\n', '\n\t')))
                    fp.write("%s\n" % (key))
                fp.write("\n")

except ImportError:
    import configparser as ConfigParser

    class NewConfigParser(ConfigParser.ConfigParser):
        def write(self, fp, space_around_delimiters=False):
            if space_around_delimiters:
                d = " {} ".format(self._delimiters[0])
            else:
                d = self._delimiters[0]
            if self._defaults:
                self._write_section(fp, self.default_section,
                                    self._defaults.items(), d)
            for section in self._sections:
                self._write_section(fp, section,
                                    self._sections[section].items(), d)


class GenerateData(object):
    def __init__(self):
        self.config_log()
        self.openpalette_file = '/etc/network/openpalette.json'
        self.conf_json_file = '/etc/pdm/conf/conf.json'
        self.vnm_network_file = '/etc/pdm/conf/vnm_network.conf'
        self.conf_json_data = {}
        self.openpalette_data = {}
        self.bm_network_data = {}  # generate bm_network section in conf.json

        self.node_network_mappings = []
        self.nodes = []
        self.networks = []
        self.node_hardware_info = []
        self.node_templates = []
        self.slb_external_networks = []
        self.slb_network_name_list = []

        self.network_type_dict = {}  # net_api: ovs,
        self.init_data()

    @staticmethod
    def format_ip(ip_str):
        try:
            return str(IP(ip_str))
        except Exception:
            return ip_str

    def is_ume_scene(self):
        try:
            scene = self.openpalette_data.get('global')[0]['tcf_scenario']
            if scene.upper() == 'UME-standard'.upper():
                logging.info('tcf_scenario is UME-standard')
                return True
            return False
        except Exception as e:
            logging.info('get tcf_scenario failed, %s' % e.args[0])
            return False

    def is_preset_scene(self):
        try:
            bear_mode = self.openpalette_data.get('global')[0]['scenariotype']
            if bear_mode.upper() == 'preset_node'.upper():
                logging.info('scenariotype is preset_node')
                return True
            return False
        except Exception as e:
            logging.info('get scenariotype failed, %s' % e.args[0])
            return False

    def is_ume_preset_scene(self):
        return self.is_ume_scene() and self.is_preset_scene()

    def init_data(self):
        try:
            self.openpalette_data = self.read_json(self.openpalette_file)
            self.node_network_mappings = \
                self.openpalette_data.get('node_network_mappings', [])
            self.nodes = \
                self.openpalette_data.get('nodes', [])
            self.networks = \
                self.openpalette_data.get('networks', [])
            self.node_hardware_info = \
                self.openpalette_data.get('node_hardware_info', [])
            self.node_templates = \
                self.openpalette_data.get('node_templates', [])
            self.slb_external_networks = \
                self.openpalette_data.get('slb_external_networks', [])
            self.conf_json_data = \
                self.read_json(self.conf_json_file)

        except Exception:
            logging.error(traceback.format_exc())

    def generate_inet_conf_tmpl(self):
        try:
            # 遍历每行节点配置，根据节点配置里的节点模板名找到对应节点模板，
            # 然后结合节点模板和网络配置页生成配置
            file_generated = False
            for node in self.nodes:
                roles = node.get('role', '')
                hardware_identity = node.get('node_hardware_info_identity', '')
                slot = node.get('slot', '')
                device_mode = \
                    self.get_device_model_in_node_hardware_info(
                        hardware_identity,
                        slot)
                logging.info('device mode : %s' % device_mode)
                if device_mode == '':
                    logging.warning('not find device mode, skip !')
                if 'PaaSController'.lower() not in roles.lower():
                    # minion节点
                    file_name = '/etc/pdm/netconfig_templates/Inet.conf_@_' + \
                                device_mode
                    logging.info('generate minion node:%s: %s ' %
                                 (node.get('name', ''), file_name))
                    self.generate_inet_conf_file(file_name, node,
                                                 device_mode)
                else:
                    # 控制节点
                    if self.is_isomerism():
                        # 控制节点异构
                        # todo 异构场景认为net_admin_ip必配
                        admin_ip = self.get_net_ip_in_node(node,
                                                           'net_admin_ip',
                                                           'net_admin_ip_v6')
                        file_name = '/etc/network/Inet_deploy.conf.tmpl_' + \
                                    admin_ip
                        logging.info('controller node isomerism :%s: %s ' %
                                     (node.get('name', ''), file_name))
                        self.generate_inet_conf_file(file_name, node,
                                                     device_mode)
                    else:
                        # 控制节点同构
                        # 同构的控制节点只要生成一个节Inet_deploy.conf.tmpl就可以了。
                        if not file_generated:
                            file_name = '/etc/network/Inet_deploy.conf.tmpl'
                            logging.info('node controller :%s: %s ' %
                                         (node.get('name', ''), file_name))
                            self.generate_inet_conf_file(file_name, node,
                                                         device_mode)
                            file_generated = True
        except Exception:
            logging.error(traceback.format_exc())

    def is_isomerism(self):
        try:
            # 如果控制节点的设备型号个数多余1 ， 则认为是异构场景
            controller_node_mode_list = []
            for node in self.nodes:
                roles = node.get('role', '')
                if 'PaaSController'.lower() in roles.lower():
                    hardware_identity = \
                        node.get('node_hardware_info_identity', '')
                    slot = node.get('slot', '')
                    device_mode = \
                        self.get_device_model_in_node_hardware_info(
                            hardware_identity,
                            slot)
                    controller_node_mode_list.append(device_mode.strip())
            mode_list = list(set(controller_node_mode_list))
            logging.info('controller node device mode: %s' % str(mode_list))
            if len(mode_list) > 1:
                return True
            return False
        except Exception:
            logging.error(traceback.format_exc())
            return False

    @staticmethod
    def get_provider(has_ovs, has_linux):
        try:
            res_list = []
            if has_ovs:
                res_list.append('ovs')
            if has_linux:
                res_list.append('linux')
            return ','.join(res_list)
        except Exception:
            logging.error(traceback.format_exc())
            return ''

    def generate_inet_conf_file(self, file_name, node, device_mode):
        try:
            # 生成配置文件， 分别生成：default, bond, ovs, linux
            cf = NewConfigParser()
            device_mode = node.get('node_templates', '')
            self.add_bond_section(cf, device_mode)
            has_ovs = self.add_ovs_section(cf, device_mode)
            has_linux = self.add_linux_section(cf, device_mode)
            if sys.version < '3':
                ConfigParser.DEFAULTSECT = 'default'
                cf.set(ConfigParser.DEFAULTSECT, 'version', '1.1')
                provider = self.get_provider(has_ovs, has_linux)
                if provider != '':
                    cf.set(ConfigParser.DEFAULTSECT, 'provider', provider)
                ConfigParser.DEFAULTSECT = 'default'
                # fixed ConfigParser hard default bug
            else:
                cf.add_section('default')
                ConfigParser.DEFAULTSECT = 'default'
                cf.set(ConfigParser.DEFAULTSECT, 'version', '1.1')
                provider = self.get_provider(has_ovs, has_linux)
                if provider != '':
                    cf.set(ConfigParser.DEFAULTSECT, 'provider', provider)
            self.log_configparser(cf)
            self.write_conf(file_name, cf)
        except Exception:
            logging.error(traceback.format_exc())

    @staticmethod
    def log_configparser(cf):
        try:
            # 这里有个 configparser 的 问题
            # sections里没有default，后面要调用cf的地方需要注意，只有write方式是正确的
            for section in cf.sections():
                logging.info(cf.items(section))
        except Exception:
            logging.error(traceback.format_exc())

    @staticmethod
    def has_linux_net_in_node_template(node_template):
        try:
            for exchange_type in node_template.get('exchange_types', []):
                if exchange_type in ['', '/', 'N/A']:
                    return True
            return False
        except Exception:
            logging.error(traceback.format_exc())
            return False

    def add_linux_section(self, cf, device_mode):
        try:
            node_template = self.get_node_template_by_device_mode(device_mode)
            node_template = self.format_node_template_dict(node_template)
            if not self.has_linux_net_in_node_template(node_template):
                logging.info('no linux in node template %s' % device_mode)
                return False
            section_name = 'linux'
            iface_list = []
            cf.add_section(section_name)
            nics = node_template.get('nics', [])
            exchange_types = node_template.get('exchange_types', [])
            mapping_networks = node_template.get('mapping_networks', [])
            default_gw = ''
            default_gw_v6 = ''
            route_list = []
            route_v6_list = []
            for index in range(len(nics)):
                # if exchange_types[index] not in ['', '/', 'N/A']:
                if 'ovs' in exchange_types[index].lower() or \
                        'dvs' in exchange_types[index].lower():
                    continue
                mapping_network = mapping_networks[index]
                nic = nics[index]
                networks = mapping_network.split(',')
                for network in networks:
                    self.network_type_dict.setdefault(network, 'linux')
                    iface_list, route_list, route_v6_list, \
                        default_gw, default_gw_v6 = \
                        self.add_net_to_section(cf, section_name,
                                                node_template, network,
                                                nic, iface_list,
                                                route_list, route_v6_list,
                                                default_gw, default_gw_v6)

            self.add_route_and_gw_to_cf(cf, section_name,
                                        route_list, route_v6_list,
                                        default_gw, default_gw_v6)
            return True
        except Exception:
            logging.error(traceback.format_exc())
            return False

    def add_ovs_data_mapping(self, cf, section_name, iface_list):
        try:
            data_mapping_str = self.get_data_mapping(iface_list)
            if data_mapping_str != '':
                cf.set(section_name, 'datamapping', data_mapping_str)
        except Exception:
            logging.error(traceback.format_exc())

    def add_route_and_gw_to_cf(self, cf, section_name,
                               route_list, route_v6_list,
                               default_gw, default_gw_v6):
        try:
            logging.info('ignore %s %s %s %s' %
                         (str(route_list), str(route_v6_list),
                          str(default_gw), str(default_gw_v6)))
            return

            # if len(route_list) != 0:
            #     cf.set(section_name, 'routes', ','.join(route_list))
            #     logging.info('add routes to %s : %s' %
            #                  (section_name, str(route_list)))
            # if len(route_v6_list) != 0:
            #     cf.set(section_name, 'routes_v6', ','.join(route_v6_list))
            #     logging.info('add routes_v6 to %s : %s' %
            #                  (section_name, str(route_v6_list)))
            # if default_gw != '':
            #     cf.set(section_name, 'defaultgw', default_gw)
            #     logging.info('add defaultgw to %s : %s' %
            #                  (section_name, str(default_gw)))
            # if default_gw_v6 != '':
            #     cf.set(section_name, 'defaultgw_v6',
            #            self.add_bracket_ip_v6(default_gw_v6))
            #     logging.info('add defaultgw_v6 to %s : %s' %
            #                  (section_name, str(default_gw_v6)))
        except Exception:
            logging.error(traceback.format_exc())

    def add_bond_section(self, cf, device_mode):
        bond_info_list = []
        try:
            cf.add_section('bond')
            node_template = self.get_node_template_by_device_mode(device_mode)
            node_template = self.format_node_template_dict(node_template)
            if not self.has_bond_config_in_node_template(node_template):
                logging.info('no bond in node template %s' % device_mode)
                return False, []
            bond_index = 0
            nics = node_template.get('nics', [])
            bond_types = node_template.get('bond_types', [])
            bond_modes = node_template.get('bond_modes', [])
            bond_members = node_template.get('bond_members', [])
            # bond_lacp_modes = node_template.get('bond_lacp_modes', [])
            # exchange_types = node_template.get('exchange_types', [])
            for index in range(len(nics)):
                nic = nics[index]
                bond_mode = bond_modes[index]
                bond_member = bond_members[index]
                bond_type = bond_types[index]
                if self.is_bond_config_legal(nic, bond_mode, bond_member):
                    logging.info('add bond : %s %s %s' %
                                 (nic, bond_member, bond_mode))
                    if bond_type.strip() == 'linux':
                        key = str(nic).strip()
                        # todo 后面可能需要修改 linux bond 的第一段不应该以br开头
                        cf.set('bond', key, 'br-%s:linux:%s:%s:' %
                               (key, bond_mode, bond_member))
                        bond_index += 1
                        bond_info_list.append({key: bond_member})
                    elif 'ovs' in bond_type:
                        key = str(nic).strip()
                        cf.set('bond', key, 'br-%s:ovs:%s:%s:' %
                               (key, bond_mode, bond_member))
                        bond_index += 1
                        bond_info_list.append({key: bond_member})
                    else:
                        logging.error('unsupport bond type')
            return True, bond_info_list
        except Exception:
            logging.error(traceback.format_exc())
            return False, bond_info_list

    @staticmethod
    def is_bond_config_legal(nic, bond_mode, bond_member):
        try:
            null_str_list = ['', '/', 'N/A']
            if nic.startswith('bond') and \
                    bond_mode not in null_str_list and \
                    bond_member not in null_str_list:
                return True
            return False
        except Exception:
            logging.error(traceback.format_exc())
            return False

    @staticmethod
    def has_bond_config_in_node_template(node_template):
        has_bond = False
        try:
            nics = node_template.get('nics', [])
            bond_modes = node_template.get('bond_modes', [])
            bond_members = node_template.get('bond_members', [])
            for index in range(len(nics)):
                nic = nics[index]
                bond_mode = bond_modes[index]
                bond_member = bond_members[index]
                null_str_list = ['', '/', 'N/A']
                if nic.startswith('bond') and \
                        bond_mode not in null_str_list and \
                        bond_member not in null_str_list:
                    has_bond = True
            return has_bond
        except Exception:
            logging.error(traceback.format_exc())
            return False

    def add_ovs_section(self, cf, device_mode):
        try:
            node_template = self.get_node_template_by_device_mode(device_mode)
            node_template = self.format_node_template_dict(node_template)
            if not self.has_ovs_net_in_node_template(node_template):
                logging.info('no ovs in node template %s' % device_mode)
                return False
            section_name = 'ovs'
            iface_list = []
            cf.add_section(section_name)
            cf.set(section_name, 'vlan_splinters', 'off')
            nics = node_template.get('nics', [])
            exchange_types = node_template.get('exchange_types', [])
            mapping_networks = node_template.get('mapping_networks', [])
            default_gw = ''
            default_gw_v6 = ''
            route_list = []
            route_v6_list = []
            for index in range(len(nics)):
                if 'ovs' not in exchange_types[index].lower() and \
                        'dvs' not in exchange_types[index].lower():
                    continue
                mapping_network = mapping_networks[index]
                nic = nics[index]
                networks = mapping_network.split(',')
                for network in networks:
                    self.network_type_dict.setdefault(network, 'ovs')
                    iface_list, route_list, route_v6_list, \
                        default_gw, default_gw_v6 = \
                        self.add_net_to_section(cf, section_name,
                                                node_template, network, nic,
                                                iface_list,
                                                route_list, route_v6_list,
                                                default_gw, default_gw_v6)

            self.add_route_and_gw_to_cf(cf, section_name,
                                        route_list, route_v6_list,
                                        default_gw, default_gw_v6)
            self.add_ovs_data_mapping(cf, section_name, iface_list)
            return True
        except Exception:
            logging.error(traceback.format_exc())
            return False

    def get_all_net_name_in_node_tmple(self, node_tmpl):
        all_name_list = []
        try:
            node_tmpl = self.format_node_template_dict(node_tmpl)
            for name_str in node_tmpl.get('mapping_networks', []):
                if name_str == '':
                    continue
                elif ',' in name_str:
                    tmp_list = \
                        [tmp_name.strip() for tmp_name in name_str.split(',')]
                    all_name_list.extend(tmp_list)
                else:
                    all_name_list.append(name_str.strip())
            return all_name_list
        except Exception:
            logging.error(traceback.format_exc())
            return all_name_list

    def get_all_network_dict_in_tmpl(self, node_tmpl):
        net_dict = {
            'net_api': 'papi',
            'net_iapi': 'iapi',
            'net_admin': 'admin',
            'net_sto_mgt': 'psto_mgt',
            'net_sto': 'psto',
            'net_sto2': 'psto2',
        }
        try:
            all_net_name = self.get_all_net_name_in_node_tmple(node_tmpl)
            for net_name in all_net_name:
                if net_name not in net_dict.keys():
                    net_dict[net_name] = self.get_net_name_format(net_name)
            if 'net_ipmi' in net_dict.keys():
                # not need add ipmi to node.tmpl, pdm can not process.
                net_dict.pop('net_ipmi')
            return net_dict
        except Exception:
            logging.error(traceback.format_exc())
            return net_dict

    @staticmethod
    def get_net_name_format(net_name):
        try:
            # translate net_ipmi to ipmi
            if net_name.startswith('net_'):
                return net_name[4:]
            return net_name
        except Exception:
            logging.error(traceback.format_exc())
            return net_name

    def add_net_to_section(self, cf, section_name,
                           node_tmpl, network, nic, iface_list,
                           route_list, route_v6_list,
                           default_gw, default_gw_v6):
        iface = copy.deepcopy(iface_list)
        route = copy.deepcopy(route_list)
        route_v6 = copy.deepcopy(route_v6_list)
        default_gw = copy.deepcopy(default_gw)
        default_gw_v6 = copy.deepcopy(default_gw_v6)
        try:
            net_dict = self.get_all_network_dict_in_tmpl(node_tmpl)
            if net_dict.get(network, '') != '':
                if self.is_network_double_stack(network):
                    cf.set(section_name,
                           net_dict[network] + '_v6',
                           nic)
                cf.set(section_name, net_dict[network], nic)
                iface.append(nic)
                tmp_route = \
                    self.get_route_in_networks_by_net(network,
                                                      ip_ver=4)
                if tmp_route != '':
                    route.append(tmp_route)
                tmp_route_v6 = \
                    self.get_route_in_networks_by_net(network,
                                                      ip_ver=6)
                if tmp_route_v6 != '':
                    route_v6.append(tmp_route_v6)
                tmp_gw = \
                    self.get_default_gw_in_networks_by_net(network,
                                                           ip_ver=4)
                if tmp_gw != '':
                    default_gw = tmp_gw
                tmp_gw_v6 = \
                    self.get_default_gw_in_networks_by_net(network,
                                                           ip_ver=6)
                if tmp_gw_v6 != '':
                    default_gw_v6 = tmp_gw_v6

            else:
                logging.warning('network %s not in node template' % network)
            return iface, route, route_v6, default_gw, default_gw_v6
        except Exception:
            logging.error(traceback.format_exc())
            return iface, route, route_v6, default_gw, default_gw_v6

    def get_data_mapping(self, iface_list):
        try:
            str_list = []
            iface_list = list(set(iface_list))
            logging.info('used iface: %s' % str(iface_list))
            for iface in iface_list:
                for mapping in self.node_network_mappings:
                    physnet_name = mapping.get('physnetName', '')
                    bridgeName = mapping.get('bridgeName', '')
                    port_name = mapping.get('portName', '')
                    if port_name == iface:
                        str_list.append('%s:%s:%s' %
                                        (physnet_name.strip(),
                                         bridgeName.strip(),
                                         port_name.strip()))
            if len(str_list) == 0:
                return ''
            res_str = ','.join(str_list)
            return '[' + res_str + ']'
        except Exception:
            logging.error(traceback.format_exc())
            return ''

    @staticmethod
    def add_bracket_ip_v6(ip_addr):
        try:
            if '/' not in ip_addr:
                if not ip_addr.startswith('['):
                    return '[' + str(ip_addr) + ']'
            ip, mask = ip_addr.split('/')
            if not ip.startswith('['):
                return '[' + str(ip) + ']' + '/' + str(mask)
            return ip_addr
        except Exception:
            return ip_addr

    @staticmethod
    def format_network(network):
        try:
            if network is None:
                return None
            new_network = copy.deepcopy(network)
            subnet_names = new_network.get('subnet_names', [])
            if not isinstance(subnet_names, list):
                new_network['subnet_names'] = \
                    [new_network.get('subnet_names', '')]
                new_network['ip_versions'] = \
                    [new_network.get('ip_versions', '')]
                new_network['cidrs'] = \
                    [new_network.get('cidrs', '')]
                new_network['vips'] = \
                    [new_network.get('vips', '')]
                new_network['gateways'] = \
                    [new_network.get('gateways', '')]
                new_network['allocation_pools'] = \
                    [new_network.get('allocation_pools', '')]
                new_network['service_allocation_pools'] = \
                    [new_network.get('service_allocation_pools', '')]
                new_network['is_default_gateways'] = \
                    [new_network.get('is_default_gateways', '')]
                new_network['static_routes'] = \
                    [new_network.get('static_routes', '')]
            return new_network
        except Exception:
            logging.error(traceback.format_exc())
            return network

    def get_default_gw_in_networks_by_net(self, net_name, ip_ver):
        try:
            for network in self.networks:
                if network.get('name', '') == net_name:
                    network = self.format_network(network)
                    subnet_names = network.get('subnet_names', [])
                    for index in range(len(subnet_names)):
                        ip_version = network.get('ip_versions', [])[index]
                        gateway = network.get('gateways', [])[index]
                        is_default_gw = \
                            network.get('is_default_gateways', [])[index]
                        if is_default_gw == 'yes' and \
                                str(ip_ver) in ip_version and \
                                gateway != '':
                            return gateway
            return ''
        except Exception:
            logging.error(traceback.format_exc())
            return ''

    def get_route_in_networks_by_net(self, net_name, ip_ver):
        try:
            for network in self.networks:
                if network.get('name', '') == net_name:
                    network = self.format_network(network)
                    subnet_names = network.get('subnet_names', [])
                    for index in range(len(subnet_names)):
                        ip_version = network.get('ip_versions', [])[index]
                        route = network.get('static_routes', [])[index]
                        if route != '' and str(ip_ver) in ip_version:
                            return route

            return ''
        except Exception:
            logging.error(traceback.format_exc())
            return ''

    def is_network_double_stack(self, net_name):
        try:
            for network in self.networks:
                network = self.format_network(network)
                if network.get('name', '') == net_name:
                    ip_version = network.get('ip_versions', [])
                    has_v4, has_v6 = self.get_ip_version(ip_version)
                    if has_v6 and has_v4:
                        return True
            return False
        except Exception:
            logging.error(traceback.format_exc())
            return False

    @staticmethod
    def get_ip_version(ip_version_list):
        has_v4 = False
        has_v6 = False
        try:
            for version in ip_version_list:
                if '4' in version:
                    has_v4 = True
                if '6' in version:
                    has_v6 = True
            return has_v4, has_v6
        except Exception:
            logging.error(traceback.format_exc())
            return has_v4, has_v6

    @staticmethod
    def format_node_template_dict(node_template):
        try:
            # todo node_template 为空也会生成一条空的记录，可能有问题，目前没报错。
            new_template = copy.deepcopy(node_template)
            if not isinstance(new_template.get('nics', ''), list):
                new_template['nics'] = \
                    [new_template.get('nics', '')]
                new_template['bond_types'] = \
                    [new_template.get('bond_types', '')]
                new_template['bond_modes'] = \
                    [new_template.get('bond_modes', '')]
                new_template['bond_lacp_modes'] = \
                    [new_template.get('bond_lacp_modes', '')]
                new_template['bond_members'] = \
                    [new_template.get('bond_members', '')]
                new_template['mapping_networks'] = \
                    [new_template.get('mapping_networks', '')]
                new_template['exchange_types'] = \
                    [new_template.get('exchange_types', '')]
                new_template['vf_numbers'] = \
                    [new_template.get('vf_numbers', '')]
            return new_template
        except Exception:
            logging.error(traceback.format_exc())
            return node_template

    @staticmethod
    def has_ovs_net_in_node_template(node_template):
        try:
            for exchange_type in node_template.get('exchange_types', []):
                if 'ovs' in exchange_type.lower():
                    return True
            return False
        except Exception:
            logging.error(traceback.format_exc())
            return False

    def get_node_template_by_device_mode(self, device_mode):
        try:
            for node_template in self.node_templates:
                if node_template.get('name', '') == device_mode:
                    return node_template
            logging.warning('find %s device mode in node template failed' %
                            device_mode)
            return {}
        except Exception:
            logging.error(traceback.format_exc())
            return {}

    def get_device_model_in_node_hardware_info(self, dev_id, slot):
        try:
            # 根据节点配置中 槽位号，节点硬件信息标识 到节点硬件管理信息页找到设备型号
            for hd_info in self.node_hardware_info:
                tmp_id = hd_info.get('identity', '')
                tmp_all_slot = hd_info.get('slot', '')
                tmp_all_slot = self.translate_slot_to_list(tmp_all_slot)
                if str(dev_id).strip() == str(tmp_id).strip() and \
                        str(slot).strip() in tmp_all_slot:
                    return hd_info.get('device_model', '')
            # 因为找不到设备型号也无所谓， 所以只报warning
            logging.warning('get device model failed, '
                            'device_id: %s, slot: %s' % (dev_id, slot))
            return ''
        except Exception:
            logging.error(traceback.format_exc())
            return ''

    @staticmethod
    def translate_slot_to_list(slot_str):
        slot_list = []
        try:
            # 1,2,3
            slot_str_list = slot_str.split(',')
            for slot in slot_str_list:
                slot_list.append(slot.strip())
            return slot_list
        except Exception:
            logging.error(traceback.format_exc())
            return slot_list

    def read_vnm_network_before(self):
        new_cf = ConfigParser.ConfigParser()
        try:
            old_cf = ConfigParser.ConfigParser()
            if os.path.exists(self.vnm_network_file):
                logging.info('%s already exist' % self.vnm_network_file)
                old_cf.read(self.vnm_network_file)

            for section in old_cf.sections():
                if section.lower() == 'neutron':
                    logging.warning('alread has %s section, overwrite it' %
                                    str(section))
                    continue
                if section.lower() == 'infra_network':
                    logging.warning('skip infra_network')
                    continue
                new_cf.add_section(section)
                opts = old_cf.options(section)
                for key in opts:
                    value = old_cf.get(section, key)
                    new_cf.set(section, key, value)
            return new_cf
        except Exception:
            logging.error(traceback.format_exc())
            return new_cf

    def process_prenode_neutron_section(self, cf):
        try:
            # 预置节点neutron 字段只填一个l3agent_type
            # todo l3_agent_type 阿里云场景怎么弄？ infra_network 目前不动会有问题吧？
            section_name = 'neutron'
            l3_agent_type = 'slb'
            if not self.has_slb_label_in_node():
                l3_agent_type = 'neutron'
            logging.info(
                'prenode neutron l3_agent_type: %s' % str(l3_agent_type))
            cf.set(section_name, 'l3agent_type', l3_agent_type)
            self.write_conf(self.vnm_network_file, cf)
        except Exception:
            logging.error(traceback.format_exc())

    def add_fake_neutron_to_vnm_network_conf(self):
        try:
            section_name = 'neutron'
            cf = self.read_vnm_network_before()
            cf.add_section(section_name)
            cf.set(section_name, 'bridge_mappings', 'physnet0:br-phy0')
            cf.set(section_name, 'tenant_network_types', 'vlan')
            cf.set(section_name, 'flat_networks', 'physnet0')
            cf.set(section_name, 'network_vlan_ranges', 'physnet0:100:4094')
            cf.set(section_name, 'external_network_bridge', 'br-iapi')
            if self.is_overlay_scene():
                cf.set(section_name, 'type_drivers', 'vlan,flat,vxlan')
            else:
                cf.set(section_name, 'type_drivers', 'vlan,flat')
            l3_agent_type = 'neutron'
            if self.has_slb_label_in_node():
                l3_agent_type = 'slb'
            cf.set(section_name, 'l3agent_type', l3_agent_type)
            self.write_conf(self.vnm_network_file, cf)
        except Exception:
            logging.error(traceback.format_exc())

    def add_neutron_to_vnm_network_conf(self):
        try:
            section_name = 'neutron'
            cf = self.read_vnm_network_before()
            cf.add_section(section_name)

            if self.is_ume_preset_scene():
                self.add_fake_neutron_to_vnm_network_conf()
                # self.process_prenode_neutron_section(cf)
                return

            bridge_mappings = \
                self.get_bridge_mapping_in_node_network_mappings()
            logging.info('new neutron bridge_mapping: %s' %
                         str(bridge_mappings))
            cf.set(section_name, 'bridge_mappings', bridge_mappings)

            type_drivers = self.get_neutron_type_drivers()
            logging.info('new neutron type_drivers: %s' % str(type_drivers))
            cf.set(section_name, 'type_drivers', type_drivers)

            cf.set(section_name, 'tenant_network_types', 'vlan')

            flat_networks = self.get_flat_network_in_node_network_mappings()
            if len(flat_networks) > 0:
                logging.info('new neutron flat_networks: %s' %
                             str(flat_networks))
                cf.set(section_name, 'flat_networks', ','.join(flat_networks))
            else:
                logging.info('no flat_networks')
            network_vlan_ranges = self.get_neutron_network_vlan_ranges()
            logging.info('new neutron network_vlan_ranges: %s' %
                         str(network_vlan_ranges))
            cf.set(section_name, 'network_vlan_ranges', network_vlan_ranges)
            cf.set(section_name, 'external_network_bridge', 'br-iapi')
            l3_agent_type = 'slb'
            logging.info('l3_agent_type: %s' % str(l3_agent_type))
            cf.set(section_name, 'l3agent_type', l3_agent_type)
            self.write_conf(self.vnm_network_file, cf)
        except Exception:
            logging.error(traceback.format_exc())

    @staticmethod
    def write_conf(file_name, cf):
        try:
            if file_name == '':
                logging.warning('filename is null, not need generate file')
                return
            with open(file_name, 'w+') as fp:
                cf.write(fp)
        except Exception:
            logging.error(traceback.format_exc())

    def has_slb_label_in_node(self):
        try:
            for node in self.nodes:
                labels = node.get('labels', '')
                if 'slb' in labels:
                    return True
            return False
        except Exception:
            logging.error(traceback.format_exc())
            return False

    def get_neutron_network_vlan_ranges(self):
        try:
            vlan_range_list = []
            for mapping in self.node_network_mappings:
                name = mapping.get('physnetName', '')
                vlan = mapping.get('VLAN', '')
                vlan_str_list = self.translate_vlan_to_format(vlan)
                for tmp_vlan in vlan_str_list:
                    vlan_range_list.append(name + ':' + tmp_vlan)
            return ','.join(vlan_range_list)
        except Exception:
            logging.error(traceback.format_exc())
            return ''

    @staticmethod
    def translate_vlan_to_format(all_vlan_str):
        vlan_str_list = []
        try:
            # 100,200-300 to 100  200:300
            vlan_list = all_vlan_str.split(',')
            for vlan_str in vlan_list:
                if '-' in vlan_str:
                    str_list = vlan_str.split('-')
                    start = str_list[0]
                    end = str_list[1]
                    vlan_str_list.append(start + ':' + end)
                elif vlan_str.strip() != '':
                    vlan_str_list.append(vlan_str + ':' + vlan_str)
            return vlan_str_list
        except Exception:
            logging.error(traceback.format_exc())
            return vlan_str_list

    def get_flat_network_in_node_network_mappings(self):
        flat_net_list = []
        try:
            for mapping in self.node_network_mappings:
                if mapping.get('is_flat_network', '') == 'yes':
                    physnet_name = mapping.get('physnetName', '')
                    if physnet_name == "":
                        continue
                    flat_net_list.append(physnet_name)
            if len(flat_net_list) > 1:
                logging.warning('only support one flat in network mapping')
            return flat_net_list
        except Exception:
            logging.error(traceback.format_exc())
            return flat_net_list

    def get_neutron_type_drivers(self):
        type_drivers_suffix = ''
        if self.is_overlay_scene():
            type_drivers_suffix = ',vxlan'
        try:
            if len(self.get_flat_network_in_node_network_mappings()) > 0:
                type_drivers = 'vlan,flat' + type_drivers_suffix
                return type_drivers
            else:
                type_drivers = 'vlan' + type_drivers_suffix
                return type_drivers
        except Exception:
            logging.error(traceback.format_exc())
            type_drivers = 'vlan' + type_drivers_suffix
            return type_drivers

    def get_bridge_mapping_in_node_network_mappings(self):
        try:
            map_str_list = []
            for mapping in self.node_network_mappings:
                name = mapping.get('physnetName', '').strip()
                bridge = mapping.get('bridgeName', '').strip()
                map_str_list.append(name + ':' + bridge)
            return ','.join(map_str_list)
        except Exception:
            logging.error(traceback.format_exc())

    def generate_neutron(self):
        try:
            pass
        except Exception:
            logging.error(traceback.format_exc())

    def add_bm_network_to_conf_json(self):
        try:
            self.bm_network_data = self.conf_json_data.get('bm_network', {})
            self.generate_bm_network()
            if self.conf_json_data.get('bm_network', {}) == {}:
                self.conf_json_data['bm_network'] = self.bm_network_data
            else:
                logging.warning('has bm_network in conf.json, overwrite it')
                logging.warning('old bm_network: %s' %
                                str(self.conf_json_data['bm_network']))
                self.conf_json_data['bm_network'] = self.bm_network_data
            logging.info('new bm_network: %s' % str(self.bm_network_data))
            self.write_json(self.conf_json_data, self.conf_json_file)
        except Exception:
            logging.error(traceback.format_exc())

    @staticmethod
    def write_json(body, filename):
        try:
            with open(filename, 'w+') as fp:
                json.dump(body, fp, indent=4)
        except Exception:
            logging.error(traceback.format_exc())

    def generate_bm_network(self):
        try:
            logging.info('network dict: %s' % str(self.network_type_dict))

            default_name = self.get_default_physnet()
            logging.info('default physnet : %s' % default_name)
            self.bm_network_data['default_physnet'] = default_name

            paas_controller_ip_list = self.get_paas_controller_ip()
            self.bm_network_data['paas_controller_ip'] = \
                paas_controller_ip_list

            # net_has_vlan_in_out = ['net_api', 'net_sto', 'net_sto_mgt']
            # 在bm_network下有vlan_id的，如api_vlan_id，
            # 则网络信息中不用添加 provider:segmentation_id
            net_has_vlan_in_out = self.add_vlan_id_to_bm_network()
            net_info_dict = \
                self.get_network_list_detail_info(net_has_vlan_in_out)
            self.bm_network_data.update(net_info_dict)

            if self.is_daisy_sink_scene():
                net_api_vip_pools = self.get_net_api_vip_pools()
                logging.info('net_api_vip_pools %s' % net_api_vip_pools)
                self.bm_network_data['net_api_vip_pools'] = net_api_vip_pools

        except Exception:
            logging.error(traceback.format_exc())

    def is_daisy_sink_scene(self):
        try:
            # has_net_admin_ip = False
            # for node in self.nodes:
            #     if node.get('net_admin_ip', '') != '' or \
            #             node.get('net_admin_ip_v6', '') != '':
            #         has_net_admin_ip = True
            # return not has_net_admin_ip
            # admin = self.get_network_by_net_name('net_admin')
            # return admin is None
            return self.is_ume_scene() and not self.is_preset_scene()
        except Exception:
            logging.error(traceback.format_exc())
            return False

    def add_vlan_id_to_bm_network(self):
        net_has_vlan_in_out = []
        try:
            if self.network_type_dict.get('net_api', 'ovs') == 'ovs':
                api_vlan_id = self.get_api_vlan_id()
                if api_vlan_id != '':
                    logging.info('api_vlan_id: %s' % str(api_vlan_id))
                    self.bm_network_data['api_vlan_id'] = api_vlan_id
                    net_has_vlan_in_out.append('net_api')
            if self.network_type_dict.get('net_sto', 'ovs') == 'ovs':
                sto_vlan_id = self.get_vlan_id_in_net('net_sto')
                if sto_vlan_id != '':
                    logging.info('sto_vlan_id: %s' % str(sto_vlan_id))
                    self.bm_network_data['sto_vlan_id'] = sto_vlan_id
                    net_has_vlan_in_out.append('net_sto')
            if self.network_type_dict.get('net_sto_mgt', 'ovs') == 'ovs':
                sto_mgt_vlan_id = self.get_vlan_id_in_net('net_sto_mgt')
                if sto_mgt_vlan_id != '':
                    logging.info('sto_mgt_vlan_id: %s' % str(sto_mgt_vlan_id))
                    self.bm_network_data['sto_mgt_vlan_id'] = sto_mgt_vlan_id
                    net_has_vlan_in_out.append('net_sto_mgt')
            return net_has_vlan_in_out
        except Exception:
            logging.error(traceback.format_exc())
            return net_has_vlan_in_out

    def is_net_api_vip_info_legal(self, net_dict):
        """
        Judge the validity of null format
        :param net_dict:  {
                    'name': str,
                    'ip_version': int,
                    'allocation_pools': list,
                    'cidr': str
                    }
        :return:
        """
        try:
            if net_dict.get('name', '').strip() == '':
                return False
            if net_dict.get('ip_version', '') not in [4, 6]:
                return False
            if not self.is_cidr_legal(net_dict.get('cidr', ''),
                                      net_dict.get('ip_version', '')):
                return False
            if len(net_dict.get('allocation_pools', [])) == 0:
                return False
            return True
        except Exception:
            logging.error(traceback.format_exc())
            return False

    @staticmethod
    def is_cidr_legal(cidr_str, version=None):
        try:
            if '/' not in cidr_str:
                return False
            IP(cidr_str)
            if version is None:
                return True
            else:
                return IP(cidr_str).version() == version
        except Exception:
            return False

    def get_net_api_vip_pools(self):
        try:
            api_network = self.get_network_by_net_name('net_api')
            if api_network is None:
                return {}
            subnet_list = []
            subnet_names_list = api_network.get('subnet_names', [])
            for index in range(len(subnet_names_list)):
                subnet_name = api_network.get('subnet_names', [])[index]
                if subnet_name.lower().endswith('_v4'):
                    subnet_name = subnet_name[:-3]
                ip_version = api_network.get('ip_versions', [])[index]
                ip_version = self.translate_ip_version_to_int(ip_version)
                cidr = api_network.get('cidrs', [])[index]
                cidr = self.format_ip(cidr)
                vip_str = api_network.get('vips', [])[index]
                reserved_pools = \
                    self.translate_allocation_pool_to_format(vip_str)
                tmp_subnet = {
                    'name': subnet_name,
                    'ip_version': ip_version,
                    'allocation_pools': reserved_pools,
                    'cidr': cidr
                }
                if not self.is_net_api_vip_info_legal(tmp_subnet):
                    logging.warning('%s is illegal' % str(tmp_subnet))
                    continue
                subnet_list.append(tmp_subnet)
            if len(subnet_list) == 0:
                return {}
            else:
                return {'subnet': subnet_list}
        except Exception:
            logging.error(traceback.format_exc())
            return {}

    def get_all_net_name_in_networks(self):
        net_name_list = []
        try:
            for network in self.networks:
                net_name = network.get('name', '')
                if net_name != '' and net_name not in net_name_list:
                    net_name_list.append(net_name)
            return list(set(net_name_list))
        except Exception:
            logging.error(traceback.format_exc())
            return list(set(net_name_list))

    @staticmethod
    def get_netname_not_slb(all_net, slb_net):
        not_slb_net_list = []
        try:
            for net in all_net:
                if net not in slb_net:
                    not_slb_net_list.append(net)
            return list(set(not_slb_net_list))
        except Exception:
            logging.error(traceback.format_exc())
            return list(set(not_slb_net_list))

    def get_linux_network(self):
        res = []
        try:
            for key, value in self.network_type_dict.items():
                if value == 'linux':
                    res.append(key)
            return res
        except Exception:
            logging.error(traceback.format_exc())
            return res

    def get_network_list_detail_info(self, net_has_vlan):
        network_dict = {}
        try:
            # slb network need provider:network_type
            #      and provider:segmentation_id info
            # net_sto need other process, ctrl, media not process
            # linux网络不用写到conf.json中
            # 将ovs网络分为两部分，slb网络 和非slb网络
            linux_networks = self.get_linux_network()
            net_name_list = self.get_all_net_name_in_networks()
            slb_net_name_list = self.get_slb_network_list()
            not_slb_net_list = self.get_netname_not_slb(net_name_list,
                                                        slb_net_name_list)

            # remove special process network
            special_net = ['net_sto', 'net_sto2', 'net_ctrl', 'net_media']
            slb_net_name_list = self.remove_special_net(special_net,
                                                        slb_net_name_list)
            not_slb_net_list = self.remove_special_net(special_net,
                                                       not_slb_net_list)

            # remove linux network
            slb_net_name_list = self.remove_special_net(linux_networks,
                                                        slb_net_name_list)
            # 将slb网络名保存下
            self.slb_network_name_list = slb_net_name_list
            not_slb_net_list = self.remove_special_net(linux_networks,
                                                       not_slb_net_list)

            if ('net_sto' in net_name_list or
                'net_sto2' in net_name_list) and \
                    'net_sto' not in linux_networks and \
                    'net_sto2' not in linux_networks:
                net_sto_info = self.get_net_sto_net_info()
                if net_sto_info is not None:
                    network_dict['net_sto'] = net_sto_info

            logging.info('all network name: %s' % str(net_name_list))
            logging.info('linux network name: %s' % str(linux_networks))
            logging.info('slb network name: %s' % str(slb_net_name_list))
            logging.info('not slb network name: %s' % str(not_slb_net_list))

            network_dict = self.append_networks_to_net_dict(network_dict,
                                                            not_slb_net_list,
                                                            net_has_vlan,
                                                            is_slb=False)
            network_dict = self.append_networks_to_net_dict(network_dict,
                                                            slb_net_name_list,
                                                            net_has_vlan,
                                                            is_slb=True)

            return network_dict
        except Exception:
            logging.error(traceback.format_exc())
            return network_dict

    def get_network_by_net_name(self, net_name):
        try:
            for network in self.networks:
                network = self.format_network(network)
                if network.get('name', '') == net_name:
                    return network
            return None
        except Exception:
            logging.error(traceback.format_exc())
            return None

    def get_net_sto_net_info(self):
        try:
            res = {}
            sto_network = self.get_network_by_net_name('net_sto')
            sto2_network = self.get_network_by_net_name('net_sto2')
            if sto_network is None and sto2_network is None:
                return None
            sto_subnet = self.get_subnet_info_in_network(sto_network)
            sto2_subnet = self.get_subnet_info_in_network(sto2_network)
            all_subnet = []
            all_subnet.extend(sto_subnet)
            all_subnet.extend(sto2_subnet)
            if len(all_subnet) == 0:
                logging.info('no sto subnet')
                return None
            res['subnet'] = all_subnet
            phy_net = self.get_physical_network_in_network(sto_network)
            net_type = self.get_network_type_in_network(sto_network)
            res['provider:physical_network'] = phy_net
            res['provider:network_type'] = net_type
            logging.info('add sto network : %s' % str(res))
            return res
        except Exception:
            logging.error(traceback.format_exc())
            return None

    @staticmethod
    def remove_special_net(special_net, all_net):
        new_net_list = copy.deepcopy(all_net)
        try:
            for net in special_net:
                if net in all_net:
                    new_net_list.remove(net)
            return new_net_list
        except Exception:
            logging.error(traceback.format_exc())
            return all_net

    def append_networks_to_net_dict(self, net_dict, name_list,
                                    net_has_vlan, is_slb=False):
        network_dict = copy.deepcopy(net_dict)
        try:
            for net_name in name_list:
                for network in self.networks:
                    network = self.format_network(network)
                    if network.get('name', '') == net_name:
                        subnet_info = \
                            self.get_subnet_info_in_network(network,
                                                            is_slb_net=is_slb)
                        if len(subnet_info) == 0:
                            logging.info('not subnet in %s' % net_name)
                            continue
                        network_dict[net_name] = {}
                        network_dict[net_name]['subnet'] = subnet_info
                        phy_net = \
                            self.get_physical_network_in_network(network)
                        network_dict[net_name]['provider:physical_network'] = \
                            phy_net
                        net_type = \
                            self.get_network_type_in_network(network)
                        network_dict[net_name]['provider:network_type'] = \
                            net_type
                        if net_name not in net_has_vlan:
                            vlan_id = self.get_vlan_id_in_network(network)
                            network_dict[net_name][
                                'provider:segmentation_id'] = vlan_id
                        logging.info('add network : %s: %s' %
                                     (net_name, str(network_dict[net_name])))
            return network_dict
        except Exception:
            logging.error(traceback.format_exc())
            return network_dict

    def get_vlan_id_in_network(self, network):
        try:
            vlan_id = network.get('segment_id', '')
            if isinstance(vlan_id, list):
                return vlan_id[0]
            return vlan_id
        except Exception:
            logging.error(traceback.format_exc())
            return ''

    def get_network_type_in_network(self, network):
        try:
            net_type = network.get('network_isolation_type', '')
            if isinstance(net_type, list):
                return net_type[0]
            return net_type
        except Exception:
            logging.error(traceback.format_exc())
            return 'vlan'

    def get_slb_network_list(self):
        slb_network_list = []
        try:
            for network in self.slb_external_networks:
                net = network.get('name', '')
                if net != '':
                    slb_network_list.append(net)
            return list(set(slb_network_list))
        except Exception:
            logging.error(traceback.format_exc())
            return list(set(slb_network_list))

    def get_physical_network_in_network(self, network):
        try:
            if 'physnet' in network:
                phy_net = network.get('physnet', '')
            else:
                phy_net = network.get('physical_network', '')
            if isinstance(phy_net, list):
                return phy_net[0]
            return phy_net
        except Exception:
            logging.error(traceback.format_exc())
            return ''

    def get_gateway_ip_format(self, gateway_str):
        try:
            if gateway_str == '':
                return None
            return self.format_ip(gateway_str)
        except Exception:
            logging.error(traceback.format_exc())
            return None

    def get_allocation_pool(self, network_info, index, is_slb_network):
        try:
            network_name = network_info.get('name', '')
            special_nets = ['net_sto', 'net_sto2']
            null_str = ['/', '']
            if self.is_daisy_sink_scene():
                # daisy下沉场景， net_api和slb网络的allocation_pool从业务地址池中取
                if is_slb_network or network_name == 'net_api':
                    allocation_pools_str = \
                        network_info.get('service_allocation_pools', [])[index]
                # sto网络， 先从业务地址池中取， 没取到，则从平台地址池中取
                elif network_name in special_nets:
                    allocation_pools_str = \
                        network_info.get('service_allocation_pools', [])[index]
                    if str(allocation_pools_str).strip() in null_str:
                        allocation_pools_str = \
                            network_info.get('allocation_pools', [])[index]
                # 其他网络都从平台地址池中取
                else:
                    allocation_pools_str = \
                        network_info.get('allocation_pools', [])[index]
            else:
                allocation_pools_str = \
                    network_info.get('allocation_pools', [])[index]
            allocation_pools = \
                self.translate_allocation_pool_to_format(allocation_pools_str)
            return allocation_pools
        except Exception:
            logging.error(traceback.format_exc())
            return []

    # 生成bm_network 中网络的子网信息
    def get_subnet_info_in_network(self, network, is_slb_net=False):
        subnet_list = []
        try:
            if network is None:
                return subnet_list
            network_info = self.format_network(network)
            subnet_names_list = network_info.get('subnet_names', [])
            for index in range(len(subnet_names_list)):
                subnet_name = network_info.get('subnet_names', [])[index]
                if subnet_name.lower().endswith('_v4'):
                    subnet_name = subnet_name[:-3]
                gateway_ip = network_info.get('gateways', [])[index]
                gateway_ip = self.get_gateway_ip_format(gateway_ip)
                ip_version = network_info.get('ip_versions', [])[index]
                ip_version = self.translate_ip_version_to_int(ip_version)
                cidr = network_info.get('cidrs', [])[index]
                cidr = self.format_ip(cidr)
                # net_api 和slb网络 需要特殊处理allocation_pool
                allocation_pools = self.get_allocation_pool(network_info,
                                                            index,
                                                            is_slb_net)
                if cidr == '' or len(allocation_pools) == 0:
                    logging.info('no cidr or allocation_pools in %s' %
                                 subnet_name)
                    continue
                tmp_subnet = {
                    'name': subnet_name,
                    'enable_dhcp': False,
                    'gateway_ip': gateway_ip,
                    'ip_version': ip_version,
                    'cidr': cidr,
                    'allocation_pools': allocation_pools
                }
                if is_slb_net:
                    tmp_subnet['action'] = 'register'
                subnet_list.append(tmp_subnet)
            return subnet_list
        except Exception:
            logging.error(traceback.format_exc())
            return subnet_list

    def translate_allocation_pool_to_format(self, all_pool_str):
        res_list = []
        try:
            # ************-*************** ==>
            #       [{start:************, end:***************}]
            # ************-***************, *************** ==>
            #       [{start:************, end:***************},
            #        {start:***************, end:***************}]
            if all_pool_str == '':
                return res_list
            pool_list = all_pool_str.split(',')
            for tmp_str in pool_list:
                if '-' not in tmp_str:
                    start = tmp_str.strip()
                    end = tmp_str.strip()
                else:
                    ip_list = tmp_str.split('-')
                    start = ip_list[0].strip()
                    end = ip_list[1].strip()
                res_list.append({
                    'start': self.format_ip(start),
                    'end': self.format_ip(end)
                })
            return res_list
        except Exception:
            logging.error(traceback.format_exc())
            return res_list

    @staticmethod
    def translate_ip_version_to_int(version_str):
        try:
            # translate IPV4 to 4 , IPV6 to 6
            if version_str.lower() == 'ipv4':
                return 4
            elif version_str.lower() == 'ipv6':
                return 6
        except Exception:
            logging.error(traceback.format_exc())
            return 4

    def get_slb_net_name_list(self):
        try:
            return []
        except Exception:
            logging.error(traceback.format_exc())
            return []

    def get_api_vlan_id(self):
        try:
            segment_id = ''
            for network in self.networks:
                if network.get('name', '') == 'net_api':
                    segment_id = network.get('segment_id', '')
            return segment_id
        except Exception:
            logging.error(traceback.format_exc())

    def get_vlan_id_in_net(self, net_name):
        try:
            segment_id = ''
            for network in self.networks:
                if network.get('name', '') == net_name:
                    segment_id = network.get('segment_id', '')
            return segment_id
        except Exception:
            logging.error(traceback.format_exc())

    def get_paas_controller_ip(self):
        try:
            paas_controller_ip_list = []
            for node in self.nodes:
                if 'PaaSController'.lower() in node.get('role', '').lower():
                    data = self.get_paas_controller_ip_data(node)
                    if data is not None:
                        paas_controller_ip_list.append(data)
            return paas_controller_ip_list
        except Exception:
            logging.error(traceback.format_exc())

    def get_paas_controller_ip_data(self, node_info):
        try:
            # api_ip, api_ip_v6
            # admin_ip, sto_ip, sto2_ip, sto_mgt_ip, ipmi_ip
            # if network is linux, not need add to paas controller ip
            data = {}

            admin_ip = self.get_net_ip_in_node(node_info,
                                               'net_admin_ip',
                                               'net_admin_ip_v6')
            if admin_ip != '' and \
                    self.network_type_dict.get('net_admin', 'ovs') == 'ovs':
                data['admin_ip'] = admin_ip

            ipmi_ip = self.get_net_ip_in_node(node_info,
                                              'net_ipmi_ip',
                                              'net_ipmi_ip_v6')
            if ipmi_ip != '' and \
                    self.network_type_dict.get('net_ipmi', 'ovs') == 'ovs':
                data['ipmi_ip'] = ipmi_ip

            temp_iapi_ip = self.get_net_ip_in_node(node_info,
                                                   'net_iapi_ip',
                                                   'net_iapi_ip_v6')
            if temp_iapi_ip != '' and \
                    self.network_type_dict.get('net_iapi', 'ovs') == 'ovs':
                data['temp_iapi_ip'] = temp_iapi_ip

            data = self.get_paas_controller_ip_data_api(data, node_info)
            data = self.get_paas_controller_ip_data_sto(data, node_info)
            data = self.get_paas_controller_ip_data_heartbeat(data, node_info)
            data = self.get_paas_controller_ip_data_ctrlgroup(data, node_info)
            return data
        except Exception:
            logging.error(traceback.format_exc())
            return None

    def get_paas_controller_ip_data_api(self, data, node_info):
        new_data = copy.deepcopy(data)
        try:
            # default net_api is ovs
            if self.network_type_dict.get('net_api', 'ovs') != 'ovs':
                logging.warning('net_api is not ovs, '
                                'not add paas_controller_ip')
                return new_data
            api_ip = node_info.get('net_api_ip', '')
            if api_ip != '':
                new_data['api_ip'] = self.format_ip(api_ip)
            api_ip_v6 = node_info.get('net_api_ip_v6', '')
            if api_ip_v6 != '':
                new_data['api_ip_v6'] = self.format_ip(api_ip_v6)
            return new_data
        except Exception:
            logging.error(traceback.format_exc())
            return new_data

    def get_paas_controller_ip_data_heartbeat(self, data, node_info):
        new_data = copy.deepcopy(data)
        try:
            if self.network_type_dict.get('net_heartbeat', 'ovs') != 'ovs':
                logging.warning('net_heartbeat is not ovs, '
                                'not add paas_controller_ip')
                return new_data
            default_key = self.get_net_heartbeat_default_stack_key()
            if default_key == '':
                logging.info('no net_heartbeat')
                return new_data
            logging.info('net_heartbeat default stack key is %s' % default_key)
            heartbeat_ip = node_info.get(default_key, '')
            if heartbeat_ip != '':
                new_data['heartbeat_ip'] = self.format_ip(heartbeat_ip)
            return new_data
        except Exception:
            logging.error(traceback.format_exc())
            return new_data

    def get_paas_controller_ip_data_ctrlgroup(self, data, node_info):
        new_data = copy.deepcopy(data)
        try:
            ctrl_group = node_info.get("controllergroup", "")
            new_data["controllergroup"] = ctrl_group
            return new_data
        except Exception:
            logging.error(traceback.format_exc())
            return new_data

    def get_net_heartbeat_default_stack_key(self):
        try:
            network = self.get_network_by_net_name('net_heartbeat')
            if network is None:
                return ''
            has_v4, has_v6 = self.get_ip_version(network.get('ip_versions'))
            if has_v4 and has_v6:
                logging.error('net_heartbeat can only have one subnet')
            if has_v4:
                return 'net_heartbeat_ip'
            if has_v6:
                return 'net_heartbeat_ip_v6'
        except Exception:
            logging.error(traceback.format_exc())
            return ''

    def get_paas_controller_ip_data_sto(self, data, node_info):
        new_data = copy.deepcopy(data)
        try:
            sto_ip = self.get_net_ip_in_node(node_info,
                                             'net_sto_ip',
                                             'net_sto_ip_v6')
            if sto_ip != '' and \
                    self.network_type_dict.get('net_sto', 'ovs') == 'ovs':
                new_data['sto_ip'] = sto_ip
            sto_mgt_ip = \
                self.get_net_ip_in_node(node_info,
                                        'net_sto_mgt_ip',
                                        'net_sto_mgt_ip_v6')
            if sto_mgt_ip != '' and \
                    self.network_type_dict.get('net_sto_mgt', 'ovs') == 'ovs':
                new_data['sto_mgt_ip'] = sto_mgt_ip
            sto2_ip = self.get_net_ip_in_node(node_info,
                                              'net_sto2_ip',
                                              'net_sto2_ip_v6')
            if sto2_ip != '' and \
                    self.network_type_dict.get('net_sto2', 'ovs') == 'ovs':
                new_data['sto2_ip'] = sto2_ip
            return new_data
        except Exception:
            logging.error(traceback.format_exc())
            return new_data

    def get_net_ip_in_node(self, node_info, net_v4, net_v6):
        try:
            ip_v4 = node_info.get(net_v4, '')
            ip_v6 = node_info.get(net_v6, '')
            if ip_v4 != '':
                return self.format_ip(ip_v4)
            if ip_v6 != '':
                return self.format_ip(ip_v6)
            return ''
        except Exception:
            logging.error(traceback.format_exc())
            return ''

    def get_default_physnet(self):
        try:
            default_physnet_name = ''
            for mapping in self.node_network_mappings:
                if mapping.get('is_default_physnet', '').lower() == 'yes':
                    if default_physnet_name != '':
                        logging.warning('more than one physnetName in conf')
                        continue
                    default_physnet_name = mapping.get('physnetName', '')
            return default_physnet_name
        except Exception:
            logging.error(traceback.format_exc())
            return ''

    def is_cpaas_scene(self):
        try:
            for conf in self.openpalette_data.get('global', []):
                if conf.get('tcf_scenario', '').upper() == \
                        'UME-standard'.upper():
                    return True
            return False
        except Exception:
            logging.error(traceback.format_exc())

    @staticmethod
    def read_json(file_name):
        try:
            if os.path.exists(file_name):
                with open(file_name) as fp:
                    return json.load(fp)
            return {}
        except Exception:
            logging.error(traceback.format_exc())
            return {}

    @staticmethod
    def config_log():
        try:
            log_format = '%(asctime)s %(levelname)s %(filename)s ' \
                         '[line:%(lineno)d] %(funcName)s %(message)s'
            log_file = '/var/log/generate_excel_inet_data.log'
            logging.basicConfig(level=logging.DEBUG,
                                filename=log_file,
                                filemode='a',
                                datefmt='%Y-%m-%d %H:%M:%S',
                                format=log_format)
            os.chmod(log_file, 0o640)
            logging.info('======== start ========')
        except Exception:
            logging.error(traceback.format_exc())

    def is_overlay_scene(self):
        try:
            global_list = self.openpalette_data.get("global", [])
            if global_list:
                global_dict = global_list[0]
                infranw_mode = global_dict.get("infranw_mode", "underlay")
                if infranw_mode.strip() == 'overlay':
                    return True
            return False
        except Exception as e:
            logging.info('get infranw_mode failed, %s' % e.args[0])
            return False

    def start(self):
        if self.is_ume_scene():
            if not self.is_preset_scene():
                logging.info('====write inet.tmpl')
                self.generate_inet_conf_tmpl()

                logging.info('====write conf.json')
                self.add_bm_network_to_conf_json()

            logging.info('====write vnm_network.conf')
            self.add_neutron_to_vnm_network_conf()
        else:
            # 非ume 则认为是标准tcf
            # 磁盘满的情况下， /etc/network/openpalette.json可能不存在？怎么搞？
            logging.info('====write tcf standard file')
            self.openpalette_file = '/etc/network/openpalette.json'
            logging.info('read conf from /etc/network/openpalette.json')
            self.init_data()
            # 生成假的vnm_network.conf的neutron section
            self.add_fake_neutron_to_vnm_network_conf()


if __name__ == '__main__':
    '''
    功能说明： 主要分为三部分，因为这三部分很多变量和函数可以共用， 所以在一个class中实现
    1. 生成基础网络配置模板：/etc/network/Inet_deploy.conf.tmpl
    2. 生成/etc/paas/conf/conf.json 中的bm_network字段，
       其中net_ctrl和net_media 由nw生成
    3. 生成/etc/pdm/conf/vnm_network.conf 中的neutron字段

    变更记录：
    1. 2020.11.7 没有配置cidr或allocation_pools任意一项，则conf.json的bm_network不生成该网络
    2. 2020.11.7 conf.json中网络gateway 为空字符串时，值需要改为null
      网络中dhcp 改为False
    3. 2020.11.10 Inet_deploy.conf.tmpl 中不要生成 net_ipmi,
      因为net_ipmi只要配临时地址，在boot流程里执行就可以了
    4. 2020.11.24 linux类型的网络不要生成到 conf.json的bm_network中
      注意，这里判断网络类型是ovs还是linux 中是在 generate_inet_conf_tmpl 中。
      所以 要保证先生成网络模板，再生成conf.json ，顺序不能乱。
    5. 2021.1.9 TFS-WX:7289133 【TCF for 网管】daisyseed支持预置节点开局--基础网络波及分析及开发落地
      预置节点不用生成基础网络配置模板和conf.json的bm_network字段。
      加入场景判断，tcf_scenario = UME-standard, scenariotype= preset_node
      预置节点场景下vnm_network.conf neutron字段只要l3agent_type字段，其他都不需要，
      vnm_network.conf infra_network 字段不能生成， 会影响部署前检查中的阿里云场景判断
    6. 2021.1.11 TFS-WX:7330853 【TCF for 网管】统一LCM，基础设施网络管理波及修改（部署场景）
      场景判断： 没有net_admin网络，则认为是daisy 下沉场景
      conf.json的bm_network中增加net_api_vip_pools 字段，
      bm_network中的net_api的allocation_pools 从业务地址池
    7. 2021.2.10 TFS-WX:7569611 TCF配置模板的SLB相关配置方案
      tcf 标准版上SLB，类似于cpaas预置节点场景
      只要生成vnm_network.conf文件
    8. 2021.4.1 IP地址格式化
    9. 需要兼容用户的错误配置
    10. 支持vnm_network.conf 中多 flat_networks

    bug or features
    1. 不支持控制节点和minion节点网络类型不同， 比如控制节点net_api为ovs, minion节点net_api为linux
       这种情况下可能会导致conf.json 中没有生成net_api网络。因为linux网络不写入conf.json
    2.

    '''
    tool = GenerateData()
    tool.start()
