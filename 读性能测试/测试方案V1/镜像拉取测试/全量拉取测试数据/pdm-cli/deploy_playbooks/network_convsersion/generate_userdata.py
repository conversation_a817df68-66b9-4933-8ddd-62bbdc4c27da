# -*- coding: UTF-8 -*-
import argparse
import os
import json
import copy
import logging
import re
import socket
import traceback
from IPy import IP


class Userdata(object):
    def __init__(self):
        self.config_logging()
        self.nodes_file = '/root/nodes'
        self.openpalette_file_standard = '/etc/network/openpalette.json'

        self.nodes = []
        self.preset_port_templates = []
        self.slb_external_networks = []

        self.mapping = {}  # key是  节点名，如paas-controller-3， 值是userdata
        self.data = {}  # /root/nodes文件
        self.openpalette_json = {}  # /etc/paas/config.d/openpalette.json
        self.nodes_json = {}

    def get_netname_key(self, template):
        try:
            if 'name' in template.keys():
                # 兼容旧的excel
                name_key = 'name'
            else:
                name_key = 'network_name'
            return name_key
        except Exception:
            logging.error(traceback.format_exc())
            return 'network_name'

    def get_gw_from_ext_network(self, network_name):
        gw_v4 = ''
        gw_v6 = ''
        networks = self.get_network_by_name(network_name)
        for network in networks:
            if 'gateways' in network.keys():
                key = 'gateways'
            else:
                key = 'route_nexthop'
            if IP(network.get(key)).version() == 4:
                gw_v4 = network.get(key)
            elif IP(network.get(key)).version() == 6:
                gw_v6 = network.get(key)
        return gw_v4, gw_v6

    def generate_userdata_by_id(self, template_id):
        try:
            ports = []
            template = self.get_template_by_id(template_id)
            if template_id is None:
                logging.warning('no template id %s in '
                                'prenode inject info sheet' % template_id)
                return None
            name_key = self.get_netname_key(template)
            for i in range(len(template[name_key])):
                network_name = template[name_key][i]
                mac = template['mac_address'][i]
                ip = template['ipv4_address'][i]
                ipv6 = template['ipv6_address'][i]
                if not self.check_preport_tmp_params(network_name, mac,
                                                     ip, ipv6):
                    return None
                gw_v4, gw_v6 = self.get_gw_from_ext_network(network_name)
                port = {
                    'gateway': gw_v4,
                    'gateway_ipv6': gw_v6,
                    'infra_network': template[name_key][i],
                    'ip': ip,
                    'ipv6': ipv6,
                    'mac': mac,
                    'name': '',
                    'role': 'host_nic'
                }
                ports.append(port)
            return {'ports': ports}
        except Exception:
            logging.error(traceback.format_exc())
            return None

    @staticmethod
    def check_mac_address(mac):
        macth_mac = re.match(r"^\s*([0-9a-fA-F]{2}:){5}[0-9a-fA-F]{2}\s*$",
                             mac)
        if not macth_mac:
            return False
        if mac == "ff:ff:ff:ff:ff:ff":
            logging.warning("Enter mac is a Broadcast mac")
            return False
        if mac.startswith('01'):
            logging.warning("Enter mac is a Multicast mac")
        return True

    @staticmethod
    def is_ipv4(ip):
        segs = str(ip).split('/')
        flag = True
        if len(segs) == 2:
            flag = True if 1 <= int(segs[1]) <= 32 else False
        try:
            socket.inet_pton(socket.AF_INET, segs[0])
        except AttributeError:
            try:
                socket.inet_aton(segs[0])
            except socket.error:
                return False
            return segs[0].count('.') == 3 and flag
        except socket.error:
            return False
        return True and flag

    @staticmethod
    def is_ipv6(ip):
        segs = str(ip).split('/')
        flag = True
        if len(segs) == 2:
            flag = True if 1 <= int(segs[1]) <= 128 else False
        try:
            socket.inet_pton(socket.AF_INET6, segs[0])
        except socket.error:
            return False
        return True and flag

    def get_network_by_name(self, name):
        networks = []
        for network in self.slb_external_networks:
            if network.get('name') == name:
                networks.append(copy.deepcopy(network))
        return networks

    def get_template_by_id(self, template_id):
        for template in self.preset_port_templates:
            if template['template_id'] == template_id:
                return copy.deepcopy(template)
        return None

    def get_preset_port_key(self, info_dict):
        try:
            # 兼容新旧excel
            if 'preset_port_templates' in info_dict.keys():
                return 'preset_port_templates'
            if 'preset_ports' in info_dict.keys():
                return 'preset_ports'
            return 'preset_ports'
        except Exception:
            logging.error(traceback.format_exc())
            return 'preset_ports'

    def generate_nodename_and_userdata_mapping(self):
        try:
            for node in self.nodes:
                key_name = self.get_preset_port_key(node)
                template_id = node.get(key_name, '')
                if template_id != '':
                    userdata = self.generate_userdata_by_id(template_id)
                    if userdata is None:
                        logging.info('no userdata in %s, skip' % node['name'])
                        continue
                    logging.info('generate node %s, userdata %s ' %
                                 (node['name'], str(userdata)))
                    if node['name'] in self.mapping.keys():
                        logging.warning('has duplicated node name')
                    self.mapping[node['name']] = userdata
                else:
                    logging.info('no template in node %s' % node['name'])
        except Exception:
            logging.error(traceback.format_exc())

    @staticmethod
    def write_json_file(file_path, data):
        with open(file_path, 'w') as fp:
            json.dump(data, fp)

    def write_nodes_file(self):
        self.nodes_json = self.read_json_file(self.nodes_file)
        for pool in self.nodes_json.get('nodepools'):
            for node in pool.get('nodes'):
                if node.get('name') in self.mapping.keys():
                    node['userdata'] = self.mapping[node.get('name')]
        self.write_json_file(self.nodes_file, self.nodes_json)

    @staticmethod
    def read_json_file(file_path):
        with open(file_path) as fp:
            return json.load(fp)

    @staticmethod
    def file_exist(file_path):
        return os.path.exists(file_path)

    def read_openpalette_file(self):
        if self.file_exist(self.openpalette_file_standard):
            self.openpalette_json = self.read_json_file(
                self.openpalette_file_standard)

    def get_sheets(self):
        self.nodes = self.openpalette_json.get('nodes')

        self.slb_external_networks = self.openpalette_json.get(
            'slb_external_networks')
        key_name = self.get_preset_port_key(self.openpalette_json)
        self.preset_port_templates = self.openpalette_json.get(key_name)

    def format_templates(self):
        try:
            templates = []
            for template in self.preset_port_templates:
                new_template = copy.deepcopy(template)
                if template == {}:
                    continue
                name_key = self.get_netname_key(template)
                if not isinstance(template.get(name_key), list):
                    new_template['name'] = [template.get(name_key, '')]
                    new_template['ipv4_address'] = [
                        template.get('ipv4_address', '')]
                    new_template['ipv6_address'] = [
                        template.get('ipv6_address', '')]
                    new_template['mac_address'] = [
                        template.get('mac_address', '')]
                templates.append(new_template)
            self.preset_port_templates = templates
        except Exception as e:
            logging.error('error: %s' % e.args[0])
            logging.error(traceback.format_exc())

    @staticmethod
    def config_logging(file_name='/var/log/generate_excel_inet_data.log'):
        logging.basicConfig(level=logging.DEBUG,
                            format='%(asctime)s %(levelname)s %(funcName)s'
                                   ' line[%(lineno)d]: %(message)s',
                            datefmt='%Y-%m-%d %H:%M:%S',
                            filename=file_name,
                            filemode='a')
        os.chmod(file_name, 0o640)
        logging.info('==== start generate user data====')

    def is_ume_scene(self):
        try:
            scene = self.openpalette_json.get('global')[0]['tcf_scenario']
            if scene.upper() == 'UME-standard'.upper():
                return True
            return False
        except Exception as e:
            logging.error('get tcf scene failed, %s' % e.args[0])
            return False

    def is_preset_scene(self):
        try:
            bear_mode = self.openpalette_json.get('global')[0]['scenariotype']
            if bear_mode.upper() == 'preset_node'.upper():
                return True
            return False
        except Exception as e:
            logging.error('get scene type failed, %s' % e.args[0])
            return False

    def is_ume_preset_scene(self):
        return self.is_ume_scene() and self.is_preset_scene()

    def start(self):
        try:
            # 读openpalette.json到内存
            self.read_openpalette_file()
            if not self.is_ume_preset_scene():
                # 必须 UME-standard 且  preset_node
                logging.error('scene is not ume preset, skip')
                return
            # 读取节点配置，预制网口注入信息， slb外部网络配置，三个表格到内存
            self.get_sheets()
            # 设配 一个节点只有一个port的情况，
            self.format_templates()
            # 生成节点名和userdata 的映射
            self.generate_nodename_and_userdata_mapping()
            # 读取/root/nodes, 根据映射，将userdata写到文件中
            self.write_nodes_file()
        except Exception as e:
            logging.error('error: %s' % e.args[0])
            logging.error(traceback.format_exc())

    def check_preport_tmp_params(self, network_name, mac, ip, ipv6):
        if not network_name:
            logging.error("get preset port templates network name "
                          "empty, error")
            return None
        # 如果mac地址非空，进行检查
        if mac and not self.check_mac_address(mac):
            logging.error("mac address %s is illegal" % mac)
            return None
        # 如果ipv4和ipv6地址都为空，错误
        if not ip and not ipv6:
            logging.error("At least one of ipv4 or ipv6 address "
                          "should be not empty")
            return None
        if ip and not self.is_ipv4(ip):
            logging.error("ipv4 address %s is illegal" % ip)
            return None
        if ipv6 and not self.is_ipv6(ipv6):
            logging.error("ipv6 address %s is illegal" % ipv6)
            return None
        return "success"


def main():
    userdata = Userdata()
    parser = argparse.ArgumentParser()
    parser.add_argument('-n', type=str, default=None)
    parser.add_argument('-o', type=str, default=None)
    args = parser.parse_args()
    if args.n and args.o:
        logging.info('generate user data input nodes file: %s', args.n)
        logging.info('generate user data input openpalette file: %s', args.o)
        if not userdata.file_exist(args.n):
            logging.error('nodes file %s is not exist' % args.n)
            return
        if not userdata.file_exist(args.o):
            logging.error('openpalette file %s is not exist' % args.o)
            return
        userdata.openpalette_file_standard = args.o
        userdata.nodes_file = args.n
    userdata.start()


if __name__ == '__main__':
    main()
