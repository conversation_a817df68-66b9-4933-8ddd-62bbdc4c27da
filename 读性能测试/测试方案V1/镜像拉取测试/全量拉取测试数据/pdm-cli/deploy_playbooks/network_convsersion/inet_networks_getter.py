# -*- coding: UTF-8 -*-
import os
import sys
import json
import yaml
import time
import copy
import logging
import traceback
import requests
import paramiko
from IPy import IP

try:
    import ConfigParser
    import commands
except ImportError:
    import subprocess as commands
    import configparser as ConfigParser


class writer(object):
    log = []

    def write(self, data):
        self.log.append(data)

    def flush(self):
        pass


class Utils(object):

    @staticmethod
    def config_logging(file_name='/var/log/inet_network_getter.log'):
        try:
            logging.basicConfig(level=logging.INFO,
                                format='%(asctime)s %(levelname)s %(funcName)s'
                                       ' line[%(lineno)d]: %(message)s',
                                datefmt='%Y-%m-%d %H:%M:%S',
                                filename=file_name,
                                filemode='a')
            os.chmod(file_name, 0o640)
            logging.info('==== start ====')
        except Exception:
            logging.debug(traceback.format_exc())

    @staticmethod
    def execute_cmd(cmd):
        status, output = commands.getstatusoutput(cmd)
        return status, output

    @staticmethod
    def ssh_server(ip, cmd, user='ubuntu'):
        try:
            logging.info(cmd)
            logger = writer()
            sys.stderr = logger
            pkey = '/root/.ssh/id_rsa'
            key = paramiko.RSAKey.from_private_key_file(pkey)
            tran = paramiko.Transport((ip, 22))
            tran.connect(username=user, pkey=key)
            ssh1 = paramiko.SSHClient()
            ssh1.set_missing_host_key_policy(paramiko.RejectPolicy())
            ssh1._transport = tran
            stdin, stdout, stderr = ssh1.exec_command(cmd, timeout=10)
            res = stdout.readlines()
            # logging.info(res)
            ssh1.close()
            return res
        except Exception:
            logging.error('error cmd:%s.' % traceback.format_exc())
            return []

    @staticmethod
    def is_ip_legal(ip_str):
        try:
            if '/' in ip_str:
                return False
            IP(ip_str)
            return True
        except Exception:
            logging.error(traceback.format_exc())
            return False

    @staticmethod
    def is_ip_and_mask_legal(ip_mask, version=None):
        # *************/24
        # 1111::2222/112
        try:
            if '/' not in ip_mask:
                return False
            ip = ip_mask.split('/')[0]
            mask = ip_mask.split('/')[1]
            IP(ip).make_net(mask)
            if version is not None:
                return IP(ip).version() == version
            return True
        except Exception:
            return False

    @staticmethod
    def get_all_ipv6_on_port(lines):
        ipv6_list = []
        try:
            for line in lines:
                line = line.strip()
                try:
                    ip_mask = line.strip().split()[1].strip()
                    if Utils.is_ip_and_mask_legal(ip_mask, 6):
                        if Utils.is_ipv6_local_link_address(ip_mask):
                            continue
                        ipv6_list.append(ip_mask.split('/')[0])
                except Exception:
                    pass
            return ipv6_list
        except Exception:
            logging.error(traceback.format_exc())
            return ipv6_list

    @staticmethod
    def get_all_ipv4_on_port(lines):
        ipv4_list = []
        try:
            for line in lines:
                line = line.strip()
                try:
                    ip_mask = line.strip().split()[1].strip()
                    if Utils.is_ip_and_mask_legal(ip_mask, 4):
                        ipv4_list.append(ip_mask.split('/')[0])
                except Exception:
                    pass
            return ipv4_list
        except Exception:
            logging.error(traceback.format_exc())
            return ipv4_list

    @staticmethod
    def get_all_ip_from_ifcfg_res(lines):
        ip_list = []
        try:
            for line in lines:
                ip = line.strip()
                try:
                    iptmp = IP(ip).strCompressed()
                    ip_list.append(iptmp)
                except Exception:
                    pass
            return ip_list
        except Exception:
            logging.error(traceback.format_exc())
            return ip_list

    @staticmethod
    def is_ipv6_local_link_address(ip_str):
        try:
            first_net = ip_str.split(':')[0]
            if 'fe80' <= first_net.strip().lower() <= 'febf':
                return True
            if ip_str.strip() == '::1/128':
                return True
            return False
        except Exception:
            logging.error(traceback.format_exc())
            return False

    @staticmethod
    def format_ip(ip_str):
        try:
            if ip_str is None:
                return None
            ip = IP(ip_str)
            if '/' in ip_str:
                ip.NoPrefixForSingleIp = 0
            return str(ip)
        except Exception:
            logging.error(traceback.format_exc())
            return ip_str


class NodeTemplate(object):
    class Network(object):
        def __init__(self, value, net_name, net_type):
            self.net_name = net_name
            self.nic_type = net_type
            self.nic_name = ''
            self.parser_str(value)

        def parser_str(self, value):
            try:
                str_list = value.split(':')
                self.nic_name = str_list[0].strip()
            except Exception:
                logging.error(traceback.format_exc())

    class Bond(object):
        def __init__(self, value, key):
            # 名字是bond1 , interface是br-bond1
            self.name = key
            self.interface = ''
            self.type = ''
            self.mode = ''
            self.members = ''
            self.master = ''
            self.lacp = ''
            self.parser_str(value)

        def get_members_str(self):
            return ','.join(self.members)

        def parser_str(self, value):
            try:
                # bond1=br-bond1:ovs:active-backup:eth2,eth3::
                str_list = value.split(':')
                self.interface = str_list[0].strip()
                self.type = str_list[1].strip()
                self.mode = str_list[2].strip()
                self.members = [eth.strip() for eth in str_list[3].split(',')]
                self.master = str_list[4].strip()
                self.lacp = str_list[5].strip()
            except Exception:
                logging.info(traceback.format_exc())

    class Nic(object):
        def __init__(self, nic_data):
            self.name = nic_data.get('nic_name')
            self.nic_type = nic_data.get('net_type')
            self.net_list = []
            self.bond = None

        def set_bond(self, bond):
            self.bond = bond

        def get_nic(self):
            return self.name

        def get_bond_type(self):
            try:
                if not self.is_bond():
                    # 没有默认为斜杠
                    return '/'
                if self.bond is None:
                    logging.warning('get bond type from nic failed')
                    return '/'
                if self.bond.type == 'ovs':
                    return 'dvs/sr-iov/ovs'
                elif self.bond.type == 'linux':
                    return 'linux'
                return '/'
            except Exception:
                return '/'

        def get_bond_mode(self):
            try:
                if not self.is_bond():
                    return '/'
                if self.bond is None:
                    return '/'
                return self.bond.type
            except Exception:
                return '/'

        def get_bond_lacp_mode(self):
            try:
                if not self.is_bond():
                    return '/'
                if self.bond is None:
                    return '/'
                return self.bond.lacp
            except Exception:
                return '/'

        def get_bond_member(self):
            try:
                if not self.is_bond():
                    return ''
                if self.bond is None:
                    return ''
                return self.bond.get_members_str()
            except Exception:
                return ''

        def add_networks(self, networks):
            try:
                for network in networks:
                    self.net_list.append(network.net_name)
            except Exception:
                logging.error(traceback.format_exc())

        def get_mapping_net(self):
            try:
                nets = []
                for net in self.net_list:
                    if net:
                        nets.append(net)
                return ','.join(nets)
            except Exception:
                logging.error(traceback.format_exc())
                return ''

        def get_exchange_type(self):
            try:
                if self.nic_type == 'ovs':
                    return 'OVS'
                elif self.nic_type == 'linux':
                    return '/'
            except Exception:
                logging.error(traceback.format_exc())
                return '/'

        def get_vf_number(self):
            try:
                return ''
            except Exception:
                logging.error(traceback.format_exc())
                return ''

        def is_bond(self):
            try:
                return self.name.startswith('bond')
            except Exception:
                return False

    def __init__(self, name, file_path):
        self.file_path = file_path
        self.template_name = name
        self.config = None

        self.has_bond = False
        self.bonds = []
        self.networks = []
        # todo 只生成这些网络？
        self.net_dict = {
            'papi': 'net_api',
            'papi_v6': 'net_api',
            'psto': 'net_sto',
            'psto_v6': 'net_sto',
            'psto2': 'net_sto2',
            'psto2_v6': 'net_sto2',
            'iapi': 'net_iapi',
            'iapi_v6': 'net_iapi',
        }
        self.nics = []

    @staticmethod
    def read_conf_file(file_path):
        try:
            config = ConfigParser.ConfigParser()
            if os.path.exists(file_path):
                logging.info('read %s' % file_path)
                config.read(file_path)
            return config
        except Exception:
            logging.error(traceback.format_exc())
            return None

    def init_data(self):
        try:
            self.config = self.read_conf_file(self.file_path)
            self.get_bond_section()
            self.get_ovs_section()
            self.get_linux_section()
            # todo 只支持ovs和Linux吗？
        except Exception:
            logging.error(traceback.format_exc())

    def get_linux_section(self):
        try:
            if not self.config.has_section('linux'):
                return
            for option in self.config.options('linux'):
                if option in self.net_dict.keys():
                    net_name = self.net_dict.get(option)
                    value = self.config.get('linux', option)
                    network = self.Network(value, net_name, 'linux')
                    self.networks.append(network)
        except Exception:
            logging.error(traceback.format_exc())

    def get_ovs_section(self):
        try:
            if not self.config.has_section('ovs'):
                return
            for option in self.config.options('ovs'):
                if option in self.net_dict.keys():
                    net_name = self.net_dict.get(option)
                    if self.get_networks_by_net_name(net_name) is not None:
                        continue
                    value = self.config.get('ovs', option)
                    network = self.Network(value, net_name, 'ovs')
                    self.networks.append(network)
        except Exception:
            logging.error(traceback.format_exc())

    def get_bond_section(self):
        try:
            if not self.config.has_section('bond'):
                return
            for option in self.config.options('bond'):
                value = self.config.get('bond', option)
                # br-bond0:ovs:active-backup:ens2f0,ens2f1::
                bond = self.Bond(value, option)
                self.bonds.append(bond)
        except Exception:
            logging.error(traceback.format_exc())

    def get_nic(self, net_name):
        try:
            network = self.get_networks_by_net_name(net_name)
            if network is None:
                return ''
            return network.nic_name
        except Exception:
            logging.error(traceback.format_exc())
            return ''

    def deal_output(self, content, ip, ver):
        try:
            i = 0
            name = ''
            if ver == 4:
                cpr = 'inet'
            else:
                cpr = 'inet6'
            while i < len(content) - 1:
                linecur = content[i].strip()
                linenext = content[i + 1].strip()
                if cpr in linenext:
                    linenextlist = linenext.split(' ')
                    ipname = linenextlist[1]
                    ipname = ipname.split('/')[0]
                    if ip == ipname:
                        name = self.get_name_from_line(linecur)
                        break
                i = i + 1
            return name
        except Exception:
            logging.error(traceback.format_exc())
            return ''

    def get_name_from_line(self, linecur):
        try:
            nic_name = ''
            if self.is_str2number(linecur[0]):
                linecurlist = linecur.split(':')
                nic_name = linecurlist[1].strip()
            return nic_name
        except Exception:
            return ''

    def is_str2number(self, ch):
        try:
            int(ch)
            return True
        except Exception:
            return False

    def get_nic_name_in_ip(self, ipv4, ipv6, api_ip):
        try:
            # 通过ip确定网口名字
            if not ipv4 and not ipv6:
                return ''
            if ipv4:
                cmd = 'ip -4 addr show'
                output = Utils.ssh_server(api_ip, cmd)
                nic_name = self.deal_output(output, ipv4, 4)
                if nic_name != '':
                    return nic_name
            if ipv6:
                cmd = 'ip -6 addr show'
                output = Utils.ssh_server(api_ip, cmd)
                nic_name = self.deal_output(output, ipv6, 6)
                return nic_name
        except Exception:
            logging.error(traceback.format_exc())
            return ''

    def get_bond_by_nic_name(self, name):
        try:
            for bond in self.bonds:
                if bond.name == name:
                    return bond
            return None
        except Exception:
            logging.error(traceback.format_exc())
            return None

    def get_bond_mode(self, net_name):
        try:
            network = self.get_networks_by_net_name(net_name)
            if network is None:
                return ''
            bond = self.get_bond_by_nic_name(network.nic_name)
            if bond is None:
                return ''
            return bond.mode
        except Exception:
            logging.error(traceback.format_exc())
            return ''

    def get_bond_lacp_mode(self, net_name):
        try:
            network = self.get_networks_by_net_name(net_name)
            if network is None:
                return ''
            bond = self.get_bond_by_nic_name(network.nic_name)
            if bond is None:
                return ''
            return bond.lacp
        except Exception:
            logging.error(traceback.format_exc())
            return ''

    def get_bond_members(self, net_name):
        try:
            network = self.get_networks_by_net_name(net_name)
            if network is None:
                return ''
            bond = self.get_bond_by_nic_name(network.nic_name)
            if bond is None:
                return ''
            return bond.get_members_str()
        except Exception:
            logging.error(traceback.format_exc())
            return ''

    def get_bond_type(self, net_name):
        try:
            network = self.get_networks_by_net_name(net_name)
            if network is None:
                return ''
            bond = self.get_bond_by_nic_name(network.nic_name)
            if bond is None:
                return ''
            return bond.type
        except Exception:
            logging.error(traceback.format_exc())
            return ''

    def get_nic_type(self, net_name):
        try:
            network = self.get_networks_by_net_name(net_name)
            if network is None:
                return ''
            return network.nic_type
        except Exception:
            logging.error(traceback.format_exc())
            return ''

    def get_networks_by_net_name(self, net_name):
        try:
            for network in self.networks:
                if network.net_name == net_name:
                    return network
            return None
        except Exception:
            logging.error(traceback.format_exc())
            return None


class UserConfig(object):
    def __init__(self):
        self.file_path = '/etc/pdm/conf/cpaas_to_tcf_user_config.json'
        self.data = {}
        self.cidr_v4 = ''
        self.cidr_v6 = ''
        self.api_ips = []
        self._parser_user_config()

    def add_api_ip(self, ip, is_ctl):
        if not is_ctl:
            return
        if ip in self.api_ips:
            return
        self.api_ips.append(ip)

    def get_cidr(self, ip_ver):
        if ip_ver == 4 and self.cidr_v4:
            return Utils.format_ip(self.cidr_v4)
        if ip_ver == 6 and self.cidr_v6:
            return Utils.format_ip(self.cidr_v6)
        return ''

    def _parser_user_config(self):
        """
        "nic_name": "eth1",  // net_iapi平面对应网口名
        "vips":"",  // 用某个节点的net_iapi地址作为vip，ipv4和ipv6地址用逗号分隔
         "host_ips":[  //这个host_ips 和nic_name 对开发非常不友好。。。
        {"ip_v4":"","ip_v6":""},
        {"ip_v4":"","ip_v6":""},
        { "ip_v4":"","ip_v6":""}
        ]
        @return:
        """
        try:
            if not os.path.exists(self.file_path):
                logging.info('not exist %s' % self.file_path)
                return
            with open(self.file_path) as fp:
                self.data = json.load(fp)
                logging.info('user config %s' % str(self.data))
        except Exception:
            logging.error(traceback.format_exc())

    def _is_legal_ip(self, ip):
        try:
            IP(ip)
            return True
        except Exception:
            return False

    def get_iapi_ip_on_node_by_nic(self, api_ip, ip_version):
        try:
            if api_ip not in self.api_ips:
                logging.info('minion node not need to get iapi ip by user config')
                return ''
            nic = self.get_iapi_nic()
            if ip_version == 4:
                cmd = 'ip -4 addr show %s | grep inet' % nic
                lines = Utils.ssh_server(api_ip, cmd)
                ips = Utils.get_all_ipv4_on_port(lines)
                # 为了解析网口上地址的 cidr
                self.get_host_ip_nic_on_node(ips[0], '', api_ip)
                return ips[0]
            elif ip_version == 6:
                cmd = 'ip -6 addr show %s | grep inet6' % nic
                lines = Utils.ssh_server(api_ip, cmd)
                ips = Utils.get_all_ipv6_on_port(lines)
                self.get_host_ip_nic_on_node('', ips[-1], api_ip)
                return ips[-1]
        except Exception:
            logging.error(traceback.format_exc())
            return ''

    def parser_ip_addr_show(self, lines):
        nic_ip_map = {}
        try:
            nic = ''
            for line in lines:
                if 'BROADCAST' in line:
                    nic = line.split()[1][:-1]
                    if nic not in nic_ip_map and nic:
                        nic_ip_map[nic] = []
                elif 'inet' in line:
                    ip, mask = line.split()[1].split('/')
                    if self._is_legal_ip(ip) and nic:
                        nic_ip_map[nic].append(ip + '/' + mask)
            return nic_ip_map
        except Exception:
            logging.error(traceback.format_exc())
            return nic_ip_map

    def get_host_ip_nic_on_node(self, ip_v4, ip_v6, api_ip):
        try:
            if api_ip not in self.api_ips:
                logging.info('minion node not need to get iapi ip by user config')
                return ''
            cmd = 'ip addr show'
            lines = Utils.ssh_server(api_ip, cmd)
            nic_ips = self.parser_ip_addr_show(lines)
            for nic, ips in nic_ips.items():
                for ip_mask in ips:
                    if ip_v4 + '/' in ip_mask and ip_v4:
                        self.set_ip_mask(ip_mask, api_ip)
                        return nic
                    if ip_v6 + '/' in ip_mask and ip_v6:
                        self.set_ip_mask(ip_mask, api_ip)
                        return nic
            return ''
        except Exception:
            logging.error(traceback.format_exc())
            return ''

    def get_host_ip_on_node(self, api_ip, ip_version):
        try:
            logging.info('get host ip on node %s %s' % (api_ip, ip_version))
            iapi_info = self.data.get('networks', {}).get('net_iapi', {})
            for host_ip in iapi_info.get('host_ips', []):
                ip = ''
                if ip_version == 4:
                    ip = host_ip.get('ip_v4')
                elif ip_version == 6:
                    ip = host_ip.get('ip_v6')
                if not ip:
                    continue
                ip = Utils.format_ip(ip)
                cmd = 'ip addr show |grep  "%s/" | ' \
                      'awk \'{print $2}\'' % ip
                lines = Utils.ssh_server(api_ip, cmd)
                for line in lines:
                    if ip + '/' in line.strip():
                        self.set_ip_mask(line.strip(), api_ip)
                        return ip
            return ''
        except Exception:
            logging.error(traceback.format_exc())
            return ''

    def set_ip_mask(self, ip_mask_str, api_ip):
        try:
            if api_ip not in self.api_ips:
                logging.info("api ip %s is not controller node, skip update cidr")
                return
            else:
                logging.info("api ip %s is controller node, update cidr")
            ip, mask = ip_mask_str.strip().split('/')
            if IP(ip).version() == 4:
                self.cidr_v4 = str(IP(ip).make_net(mask))
            elif IP(ip).version() == 6:
                self.cidr_v6 = str(IP(ip).make_net(mask))
        except Exception:
            logging.error(traceback.format_exc())

    def is_user_config_nic_name(self):
        try:
            iapi_info = self.data.get('networks', {}).get('net_iapi', {})
            return iapi_info.get('nic_name', '') != ''
        except Exception:
            logging.error(traceback.format_exc())
            return ''

    def is_user_config_vips(self):
        try:
            iapi_info = self.data.get('networks', {}).get('net_iapi', {})
            if iapi_info.get('vips', '') != '':
                return True
            return False
        except Exception:
            logging.error(traceback.format_exc())
            return False

    def is_user_config_host_ip(self):
        try:
            iapi_info = self.data.get('networks', {}).get('net_iapi', {})
            for host_ip in iapi_info.get('host_ips', []):
                if host_ip.get('ip_v4') != '':
                    return True
                if host_ip.get('ip_v6') != '':
                    return True
            return False
        except Exception:
            logging.error(traceback.format_exc())
            return False

    def is_user_config(self):
        try:
            iapi_info = self.data.get('networks', {}).get('net_iapi', {})
            if iapi_info.get('nic_name', '') != '':
                return True
            if iapi_info.get('vips', '') != '':
                return True
            for host_ip in iapi_info.get('host_ips', []):
                if host_ip.get('ip_v4') != '':
                    return True
                if host_ip.get('ip_v6') != '':
                    return True
            return False
        except Exception:
            logging.error(traceback.format_exc())
            return False

    def get_iapi_nic(self):
        try:
            iapi_info = self.data.get('networks', {}).get('net_iapi', {})
            return iapi_info.get('nic_name', '')
        except Exception:
            logging.error(traceback.format_exc())
            return ''

    def get_host_ips(self):
        try:
            iapi_info = self.data.get('networks', {}).get('net_iapi', {})
            return iapi_info.get('host_ips', [])
        except Exception:
            logging.error(traceback.format_exc())
            return ''

    def get_vips(self, ip_ver):
        ips = []
        try:
            iapi_info = self.data.get('networks', {}).get('net_iapi', {})
            vips = iapi_info.get('vips', '')
            for vip in vips.split(','):
                if self._is_legal_ip(vip):
                    if IP(vip).version() == int(ip_ver):
                        ips.append(Utils.format_ip(vip))
                else:
                    logging.warning('vip %s in %s is illegal ' %
                                    (vip, self.file_path))
            return ips
        except Exception:
            logging.error(traceback.format_exc())
            return ips

class DeployConf(object):
    class Network(object):
        def __init__(self, value, net_name, net_type, option):
            self.net_name = net_name
            self.nic_type = net_type
            self.nic_name = ''
            self.option = option
            self.parser_str(value)

        def parser_str(self, value):
            try:
                str_list = value.split(':')
                self.nic_name = str_list[0].strip()
            except Exception:
                logging.error(traceback.format_exc())

    class Bond(object):
        def __init__(self, value, key):
            # 名字是bond1 , interface是br-bond1
            self.name = key
            self.interface = ''
            self.type = ''
            self.mode = ''
            self.members = ''
            self.master = ''
            self.lacp = ''
            self.parser_str(value)

        def get_members_str(self):
            return ','.join(self.members)

        def parser_str(self, value):
            try:
                # bond1=br-bond1:ovs:active-backup:eth2,eth3::
                str_list = value.split(':')
                self.interface = str_list[0].strip()
                self.type = str_list[1].strip()
                self.mode = str_list[2].strip()
                self.members = [eth.strip() for eth in str_list[3].split(',')]
                self.master = str_list[4].strip()
                self.lacp = str_list[5].strip()
                # if self.lacp == '':
                #     # lacp 默认为active
                #     self.lacp = 'active'
            except Exception:
                logging.info(traceback.format_exc())

    def __init__(self, config, net_dict):
        self.net_dict = net_dict
        self.config = config
        self.bonds = []
        self.networks = []
        self.init_data()

    def init_data(self):
        try:
            self.get_bond_section()
            self.get_openvswitch_section('linux')
            self.get_openvswitch_section('ovs')
            # todo 只支持ovs和Linux吗？
        except Exception:
            logging.error(traceback.format_exc())

    def get_bond_section(self):
        try:
            if not self.config.has_section('bond'):
                return
            for option in self.config.options('bond'):
                value = self.config.get('bond', option)
                # br-bond0:ovs:active-backup:ens2f0,ens2f1::
                bond = self.Bond(value, option)
                self.bonds.append(bond)
        except Exception:
            logging.error(traceback.format_exc())

    def get_openvswitch_section(self, openvswitch_type):
        try:
            if not self.config.has_section(openvswitch_type):
                return
            for option in self.config.options(openvswitch_type):
                if option in self.net_dict.keys():
                    net_name = self.net_dict.get(option)
                    #if self.get_networks_by_net_name(net_name) is not None:
                    #    continue
                    value = self.config.get(openvswitch_type, option)
                    network = self.Network(value, net_name, openvswitch_type, option)
                    self.networks.append(network)
        except Exception:
            logging.error(traceback.format_exc())

    def get_nic(self, option):
        try:
            network = self.get_networks_by_net_option(option)
            if network is None:
                return ''
            return network.nic_name
        except Exception:
            logging.error(traceback.format_exc())
            return ''

    def get_bond_by_nic_name(self, name):
        try:
            for bond in self.bonds:
                if bond.name == name:
                    return bond
            return None
        except Exception:
            logging.error(traceback.format_exc())
            return None

    def get_bond_members(self, option):
        try:
            network = self.get_networks_by_net_option(option)
            if network is None:
                return ''
            bond = self.get_bond_by_nic_name(network.nic_name)
            if bond is None:
                return ''
            return bond.get_members_str()
        except Exception:
            logging.error(traceback.format_exc())
            return ''

    def get_bond_mode(self, net_name):
        try:
            network = self.get_networks_by_net_option(net_name)
            if network is None:
                return ''
            bond = self.get_bond_by_nic_name(network.nic_name)
            if bond is None:
                return ''
            return bond.mode
        except Exception:
            logging.error(traceback.format_exc())
            return ''

    def get_bond_lacp_mode(self, net_name):
        try:
            network = self.get_networks_by_net_option(net_name)
            if network is None:
                return ''
            bond = self.get_bond_by_nic_name(network.nic_name)
            if bond is None:
                return ''
            return bond.lacp
        except Exception:
            logging.error(traceback.format_exc())
            return ''

    def get_bond_type(self, net_name):
        try:
            network = self.get_networks_by_net_option(net_name)
            if network is None:
                return ''
            bond = self.get_bond_by_nic_name(network.nic_name)
            if bond is None:
                return ''
            return bond.type
        except Exception:
            logging.error(traceback.format_exc())
            return ''

    def get_networks_by_net_option(self, option):
        try:
            for network in self.networks:
                if network.option == option:
                    return network
            return None
        except Exception:
            logging.error(traceback.format_exc())
            return None

    def get_networks_by_net_name(self, net_name):
        try:
            for network in self.networks:
                if network.net_name == net_name:
                    return network
            return None
        except Exception:
            logging.error(traceback.format_exc())
            return None



class InitNetwork(object):
    class Port(object):
        def __init__(self, name, admin_state_up, tenant_id, network_id):
            self.__name = name
            self.__admin_state_up = admin_state_up
            self.__tenant_id = tenant_id
            self.__network_id = network_id

            self.__fixed_ips = []
            self.__is_daisy_vip = False

        def is_applied_success(self):
            return self.__fixed_ips != []

        def set_name(self, name):
            self.__name = name

        def get_name(self):
            return self.__name

        def set_fixed_ips(self, fixed_ips):
            # [
            #     {
            #         "subnet_id": "70f7a232-62ad-4671-9fde-8f74c60574fd",
            #         "ip_address": "**********"
            #     },
            #     {
            #         "subnet_id": "bd49e798-7e03-41b3-b9b2-51434dae173b",
            #         "ip_address": "3efe:ffff:0:f101::16b"
            #     }
            # ]
            # 如果fixed_ips为[] 则说明这个port申请失败,没有ip
            self.__fixed_ips = fixed_ips

        def get_fixed_ips(self):
            return self.__fixed_ips

        def return_data(self):
            return {
                'admin_state_up': self.__admin_state_up,
                'name': self.__name,
                'tenant_id': self.__tenant_id,
                'network_id': self.__network_id
            }

        def get_ip(self, subnet_id):
            try:
                for fixed_ip in self.__fixed_ips:
                    if fixed_ip.get('subnet_id') == subnet_id:
                        return fixed_ip.get('ip_address')
            except Exception:
                logging.error(traceback.format_exc())
                return ''

        def set_daisy_vip(self):
            self.__is_daisy_vip = True

        def is_daisy_vip_port(self):
            return self.__is_daisy_vip

    class Subnet(object):
        def __init__(self, data):
            pass

    # {
    #     "allocation_pools": [
    #         {
    #             "end": "**********",
    #             "start": "*********"
    #         }
    #     ],
    #     "cidr": "********/24",
    #     "description": null,
    #     "dns_nameservers": [],
    #     "enable_dhcp": false,
    #     "gateway_ip": "*********",
    #     "host_routes": [],
    #     "id": "70f7a232-62ad-4671-9fde-8f74c60574fd",
    #     "ip_version": 4,
    #     "ipv6_address_mode": null,
    #     "ipv6_ra_mode": null,
    #     "name": "subnet_api",
    #     "network_id": "c14ece8b-6bee-4d94-a3a8-48d243c0384d",
    #     "project_id": "paasvnm",
    #     "revision_number": 0,
    #     "service_types": [],
    #     "subnetpool_id": null,
    #     "tags": [],
    #     "tenant_id": "paasvnm"
    # }

    def __init__(self):
        self.com_vars_file = '/root/common/com_vars.yml'
        self.port_vars_file = '/root/common/port_vars.yml'
        self.conf_json_file = '/etc/pdm/conf/conf.json'
        self.paas_conf_file = '/etc/pdm/conf/paas.conf'
        self.vnm_network_file = '/etc/pdm/conf/vnm_network.conf'
        self.nodes_file = '/root/nodes'
        self.user_config = UserConfig()

        self.msb_ip = ''
        self.msb_port = ''
        self.msb_url = ''

        self.node_api_ips = []
        self.node_api_ip_v6s = []
        self.nodes_num = 0

        self.total_api_port_num = 61  # 1个daisy vip , 60个总预留节点数
        self.port_prefix = 'reserved_api_port'

        self.post_ports_data = {}  # 一次post多个 端口， 性能好， 但目前不使用。
        self.ports = []  # 需要生成的端口

        self.net_api_subnets_id = []
        self.net_api_network = {}
        self.net_iapi_network = {}
        self.net_api_subnets = []
        self.net_iapi_subnets = []

        self.net_iapi_vips = []
        self.iapi_ips = []

        self.net_sto_network = {}
        self.net_sto_subnets = []

        self.nodes_data = []

        self.error_code = []  # len(error_code) == 0 是 success
        self.print_data = None  # ut

    @staticmethod
    def read_yaml_file(file_path):
        with open(file_path) as fp:
            documents = yaml.safe_load(fp)
        return documents

    def get_msb_ip(self):
        try:
            data_map = self.read_yaml_file(self.com_vars_file)
            for data in data_map:
                if 'openpalette_service_ip' in data:
                    self.msb_ip = data.get('openpalette_service_ip', '')
        except Exception:
            logging.error(traceback.format_exc())

    def get_msb_port(self):
        try:
            data_map = self.read_yaml_file(self.port_vars_file)
            for data in data_map:
                if 'openpalette_service_port' in data:
                    self.msb_port = data.get('openpalette_service_port', '')
        except Exception:
            logging.error(traceback.format_exc())

    def get_msb_url(self):
        try:
            self.get_msb_ip()
            self.get_msb_port()
            if IP(self.msb_ip).version() == 4:
                self.msb_url = 'http://%s:%s' % (self.msb_ip, self.msb_port)
            elif IP(self.msb_ip).version() == 6:
                self.msb_url = 'http://[%s]:%s' % (self.msb_ip, self.msb_port)
        except Exception:
            logging.error(traceback.format_exc())

    @staticmethod
    def logging_request(resp, success_code):
        try:
            if resp.status_code == success_code:
                logging.info('url:%s, code:%s, text:%s' %
                             (resp.url, resp.status_code, resp.text))
            else:
                logging.warning('url:%s, code:%s, text:%s' %
                                (resp.url, resp.status_code, resp.text))
        except Exception:
            logging.error(traceback.format_exc())

    def get_nodes_from_node_worker(self, retry=1, wait_time=10):
        try:
            url = self.msb_url + '/nodeworker/v1/tenants/admin/nodes'
            headers = {'Content-type': 'application/json'}
            for _ in range(retry):
                resp = requests.get(url=url, headers=headers,
                                    allow_redirects=False, timeout=10.0)
                self.logging_request(resp, 200)
                if resp.status_code == 200:
                    data = json.loads(resp.text)
                    return data.get('nodes', [])
                time.sleep(wait_time)
            self.error_code.append('get nodes info from nodeworker error')
            return []
        except Exception:
            logging.error(traceback.format_exc())
            return []

    def get_ip_version(self, ip_str):
        try:
            return IP(ip_str).version()
        except Exception:
            return 0

    def is_ctl_node(self, node):
        try:
            roles = node.get('roles', [])
            return 'paas_controller' in roles
        except Exception:
            return False

    def parser_nodes_netinfo(self, nodes):
        try:
            self.nodes_num = 0
            self.node_api_ips = []
            self.node_api_ip_v6s = []
            for node in nodes:
                is_ctl = self.is_ctl_node(node)
                self.nodes_num += 1
                netinfo = node.get('netinfo', {})
                if 'net_api' in netinfo:
                    ip = netinfo.get('net_api').get('ip', '')
                    ip = Utils.format_ip(ip)
                    if self.get_ip_version(ip) == 4:
                        self.node_api_ips.append(ip)
                    elif self.get_ip_version(ip) == 6:
                        self.node_api_ip_v6s.append(ip)
                    self.user_config.add_api_ip(ip, is_ctl)
                if 'net_api_v4' in netinfo:
                    ip = netinfo.get('net_api_v4').get('ip', '')
                    ip = Utils.format_ip(ip)
                    self.node_api_ips.append(ip)
                    self.user_config.add_api_ip(ip, is_ctl)
                if 'net_api_v6' in netinfo:
                    ip = netinfo.get('net_api_v6').get('ip', '')
                    ip = Utils.format_ip(ip)
                    self.node_api_ip_v6s.append(ip)
                    self.user_config.add_api_ip(ip, is_ctl)
            # 如果双栈，则net_api和net_api_v4 重复添加了，需要去重
            self.node_api_ips = list(set(self.node_api_ips))
            self.node_api_ip_v6s = list(set(self.node_api_ip_v6s))
            logging.info("ctrl node api ips %s", self.user_config.api_ips)
        except Exception:
            logging.error(traceback.format_exc())

    def parser_node_data_to_nodes(self, nodes):
        try:
            self.nodes_data = []
            for node in nodes:
                host_name = node.get('hostname', '')
                inet_deploy_file = self.get_inet_deploy_file_name(node)
                node_template = NodeTemplate(host_name, inet_deploy_file)
                node_template.init_data()
                net_info = self.get_net_info_by_node(node, node_template)
                tmp_data = {
                    'name': host_name,
                    'net_info': net_info
                }
                self.nodes_data.append(tmp_data)
            return self.nodes_data
        except Exception:
            logging.error(traceback.format_exc())
            return self.nodes_data

    def diff_struct_handle(self, nodes, node_data):
        logging.info(node_data)
        filepathandname = '/var/log/tmp_inetdeploy.conf'
        for node in nodes:
            try:
                strconf = self.getnodedeployconf(node)
                logging.info(strconf)
                for node_data_onenode in node_data:
                    if node_data_onenode['name'] == node['hostname']:
                        net_info_onenode = node_data_onenode['net_info']
                        self.diff_struct_handle_onenode(net_info_onenode, strconf)
                self.deletefile(filepathandname)
            except Exception:
                self.deletefile(filepathandname)
                logging.error(traceback.format_exc())

    def diff_struct_handle_onenode(self, net_info_onenode, strconf):
        daisy_deploy_map = {'net_api':['papi','papi_v6'],'net_sto':['psto','psto_v6'],'net_sto2':['psto2','psto2_v6'],'net_iapi':['iapi','iapi_v6']}
        for onenet in net_info_onenode:
            net_name = onenet['net_name']
            if net_name in list(daisy_deploy_map.keys()):
                self.diff_struct_handel_onenet_onedeployconfig(daisy_deploy_map[net_name], onenet, strconf)

    def diff_struct_handel_onenet_onedeployconfig(self, matchlist, onenet, strconf):
        net_dict = {'papi': 'net_api','papi_v6': 'net_api','psto': 'net_sto','psto_v6': 'net_sto','psto2': 'net_sto2','psto2_v6': 'net_sto2','iapi': 'net_iapi','iapi_v6': 'net_iapi'}
        filepathandname = '/var/log/tmp_inetdeploy.conf'
        if not self.writestrconftofile(filepathandname, strconf):
            return False
        deployconfig = self.read_conf_file(filepathandname)
        deployconf = DeployConf(deployconfig, net_dict)
        for onematch in matchlist:
            nic = deployconf.get_nic(onematch)
            bond_members = deployconf.get_bond_members(onematch)
            if nic == '':
                continue
            if onenet['nic'] == nic and onenet['bond_members'] == bond_members:
                return
            onenet['nic'] = nic
            if bond_members != '':
                onenet['bond_members'] = bond_members
                onenet['bond_types'] = deployconf.get_bond_type(onematch)
                onenet['bond_modes'] = deployconf.get_bond_mode(onematch)
                onenet['bond_lacp_modes'] = deployconf.get_bond_lacp_mode(onematch)
            else:
                onenet['bond_members'] = ""
                onenet['bond_types'] = ""
                onenet['bond_modes'] = ""
                onenet['bond_lacp_modes'] = ""
            return

    def writestrconftofile(self, filepathandname, strconf):
        try:
            with open(filepathandname, 'w+') as fp:
                fp.write(strconf)
            return True
        except Exception:
            logging.info('write file %s fail' % filepathandname)
            return False

    def deletefile(self, filepathandname):
        try:
            if os.path.isfile(filepathandname):
                os.remove(filepathandname)
        except Exception:
            logging.info('delete file %s fail' % filepathandname)

    def getnodedeployconf(self, node):
        try:
            getnetinfo = node.get('netinfo', {})
            net_api_ip = getnetinfo.get('net_api', '').get('ip', '')
            cmd = 'sudo cat /etc/network/Inet_deploy.conf'
            lines = Utils.ssh_server(net_api_ip, cmd)
            return "".join(lines)
        except Exception:
            logging.error(traceback.format_exc())
            return ''

    def need_diff_struct_handle(self, nodes):
        #is_ume_scene not use because only ume can do this
        if not self.is_baremetal_scene():
            logging.info("baremetal scene, not need diff struct handle")
            return False
        server_system_info_set = set([])
        for node in nodes:
            roles = node.get('roles', [])
            extra_info = node.get('extra_info', {})
            #deviceinfo = node.get('deviceinfo', {})
            server_system_info = extra_info.get('server_system_info', "")            
            if 'minion' in roles or 'paas_controller' in roles:
                if server_system_info != "":
                    server_system_info_set.add(server_system_info)
        if len(server_system_info_set) > 1:
            return True
        logging.info("no node need diff struct handle")
        return False 

    def find_api_default_nic_by_ifcfg(self, api_ip):
        try:
            cmd = "sudo grep -r /etc/sysconfig/network-scripts/ -e OVS_BRIDGE=br-apidefault|awk -F  ':' '{print $1}'|awk -F  'ifcfg-' '{print $2}'"
            lines = Utils.ssh_server(api_ip, cmd)
            if len(lines) == 1:
                nic = lines[0].strip()
                logging.info('find br-apidefault nic name %s' % nic)
                return nic
            else:
                for line in lines:
                    line = line.strip()
                    if len(line) == 0:
                        continue
                    cmdipas = "sudo ip a s %s" % line
                    ipasresult = Utils.ssh_server(api_ip, cmdipas)
                    if "not exist" not in "".join(ipasresult):
                        logging.info('find br-apidefault nic name %s' % line)
                        return line

                logging.info('find br-apidefault nic name err: %s' % lines)
            return ''
        except Exception:
            logging.error(traceback.format_exc())
            return ''

    def get_nic_name_by_scene(self, node_template, ip_v4, ip_v6,
                              net_name, api_ip):
        """

        :param node_template:
        :param ip_v4:
        :param ip_v6:
        :param net_name:
        :param api_ip:   通过api_ip ssh
        :return:
        """
        if self.is_preset_node_scene():
            if net_name in ['net_api', 'net_api_v4', 'net_api_v6']:
                nic_name = self.find_api_default_nic_by_ifcfg(api_ip)
                if nic_name != '':
                    return nic_name
                else:
                    return node_template.get_nic_name_in_ip(ip_v4, ip_v6,
                                                            api_ip)
            if net_name == 'net_iapi':
                return self.get_preset_iapi_nic(ip_v4, ip_v6, api_ip)
            else:
                return node_template.get_nic_name_in_ip(ip_v4, ip_v6, api_ip)
        if self.is_overlay_scene():
            return node_template.get_nic_name_in_ip(ip_v4, ip_v6, api_ip)

    def get_preset_iapi_nic(self, ip_v4, ip_v6, api_ip):
        if self.user_config.is_user_config_host_ip():
            nic = self.user_config.get_host_ip_nic_on_node(ip_v4,
                                                           ip_v6,
                                                           api_ip)
            if nic:
                return nic
        if self.user_config.is_user_config_nic_name():
            nic = self.user_config.get_iapi_nic()
            if nic:
                return nic
        return ''

    def get_api_ip_in_net_info(self, netinfo):
        try:
            ip = self.get_net_api_address(4, netinfo)
            if ip != '':
                return ip
            ip_v6 = self.get_net_api_address(6, netinfo)
            if ip_v6 != '':
                return ip_v6
            logging.warning('not found api ip in net_info')
            return ''
        except Exception:
            logging.error(traceback.format_exc())
            return ''

    def need_add_ip(self, is_ctl, scene):
        if is_ctl and scene == 'preset':
            return True
        if scene == 'baremetal':
            return True
        return False

    def get_res_list_overlay_scene(self, net_list, netinfo,
                                   res_list, node_template, is_ctl):

        for net_name in net_list:
            logging.info('process %s' % net_name)
            real_net_name = net_name
            if not self.user_config.is_user_config() and net_name == 'net_iapi':
                real_net_name = 'net_api'
            need_add = self.need_add_ip(is_ctl, 'preset')
            ip_v4 = self.get_ip_in_net_info(real_net_name, netinfo, 4,
                                            need_add)
            ip_v6 = self.get_ip_in_net_info(real_net_name, netinfo, 6,
                                            need_add)
            api_ip = self.get_api_ip_in_net_info(netinfo)
            if ip_v4 == '' and ip_v6 == '':
                logging.info('no ip in %s, skip' % net_name)
                continue
            nic = self.get_nic_name_by_scene(node_template, ip_v4, ip_v6,
                                             real_net_name, api_ip)
            data = {
                "net_name": net_name,
                "ipv4": ip_v4,
                "ipv6": ip_v6,
                "nic": nic,
                "bond_modes": "",
                "bond_lacp_modes": "/",
                "bond_members": "",
                "exchange_types": "/",
                "bond_types": ""
            }
            res_list.append(data)
        return res_list

    def merge_dict_list_reprocess(self, merge_dict_list, suffix_dict_new):
        try:
            # 防止ipv4和ipv6都有的情况下，合并的网络却存在一个地址的情况
            merge_dict_list_new = []
            for merge_dict in merge_dict_list:
                if merge_dict.get('net_name') not \
                        in list(suffix_dict_new.keys()):
                    merge_dict_list_new.append(merge_dict)
                    continue
                if not merge_dict.get('ipv4') or not merge_dict.get('ipv6'):
                    tmp = suffix_dict_new[merge_dict.get('net_name')]
                    merge_dict_list_new.append(tmp)
                else:
                    merge_dict_list_new.append(merge_dict)
            return merge_dict_list_new
        except Exception:
            logging.error(traceback.format_exc())
            return merge_dict_list

    def get_merge_dict_list(self, suffix_dict, merge_name_list,
                            merge_dict_list):
        suffix_dict_new = {}
        for key, value_list in suffix_dict.items():
            if len(value_list) > 2:
                continue
            elif len(value_list) == 2:
                value_list[0]['net_name'] = key
                if not value_list[0]['ipv4']:
                    value_list[0]['ipv4'] = value_list[1]['ipv4']
                if not value_list[0]['ipv6']:
                    value_list[0]['ipv6'] = value_list[1]['ipv6']
                suffix_dict_new[key] = value_list[0]
            else:
                value_list[0]['net_name'] = key
                suffix_dict_new[key] = value_list[0]
        logging.info(suffix_dict_new)
        for key, value in suffix_dict_new.items():
            if key not in merge_name_list:
                merge_dict_list.append(value)
        return merge_dict_list, suffix_dict_new

    def get_suffix_dict(self, suffix_name_list,
                        name_with_value_dict):
        suffix_dict = {}
        for net_name in suffix_name_list:
            net_name_key = net_name[:-3]
            if net_name_key not in list(suffix_dict.keys()):
                suffix_dict[net_name_key] = \
                    [name_with_value_dict[net_name]]
            else:
                suffix_dict[net_name_key].append(
                    name_with_value_dict[net_name])
        return suffix_dict

    def merge_res_list(self, net_list, res_list):
        try:
            merge_dict_list = []
            suffix_name_list = []
            merge_name_list = []
            name_with_value_dict = {}
            # 获取不带_v4和_v6的名字和带_v4和_v6的名字
            for net_name in net_list:
                if 'v4' in net_name or 'v6' in net_name:
                    suffix_name_list.append(net_name)
                else:
                    merge_name_list.append(net_name)
            # 组dict,key为网络名字，value为该网络的所有相关信息
            for net_value in res_list:
                name_with_value_dict[net_value.get('net_name')] = \
                    net_value
            logging.info(suffix_name_list)
            # 合并操作，取不带_v4和_v6的的网络
            for res_dict in res_list:
                if res_dict.get('net_name') in suffix_name_list:
                    continue
                else:
                    merge_dict_list.append(res_dict)
            logging.info(merge_dict_list)
            # 对v4和v6网络合并
            suffix_dict = self.get_suffix_dict(suffix_name_list,
                                               name_with_value_dict)
            logging.info(suffix_dict)
            # 合并网络
            merge_dict_list, suffix_dict_new = \
                self.get_merge_dict_list(suffix_dict, merge_name_list,
                                         merge_dict_list)
            # 后处理
            merge_dict_list = \
                self.merge_dict_list_reprocess(merge_dict_list,
                                               suffix_dict_new)
            return merge_dict_list
        except Exception:
            logging.error(traceback.format_exc())
            return res_list

    def process_net_list(self, net_list):
        """
        ume 预置节点 删除net_mgt网络
        :param net_list:
        :return:
        """
        res = []
        ignore_list = ['net_mgt', 'net_mgt_v4', 'net_mgt_v6']
        if self.is_preset_node_scene():
            for net in net_list:
                if net not in ignore_list:
                    res.append(net)
            return res
        else:
            return net_list

    def get_all_iapi_vip(self):
        try:
            vips_v4 = self.get_net_iapi_vip({'ip_version': 4})
            vips_v6 = self.get_net_iapi_vip({'ip_version': 6})
            return vips_v4 + vips_v6
        except Exception:
            return []

    def get_net_info_by_node(self, node, node_template):
        try:
            res_list = []
            netinfo = node.get('netinfo', {})
            net_list = ['net_iapi', 'net_api', 'net_sto', 'net_sto2']
            is_ctl = self.is_ctl_node(node)
            if self.is_overlay_scene() or self.is_preset_node_scene():
                net_list = list(netinfo.keys())
                net_list = self.process_net_list(net_list)
                net_list.append('net_iapi')
                res_list = \
                    self.get_res_list_overlay_scene(net_list, netinfo,
                                                    res_list, node_template,
                                                    is_ctl)
                res_list = self.merge_res_list(net_list, res_list)
                return res_list
            else:
                return self.get_res_list_baremetal_scene(net_list, netinfo,
                                                         node_template,
                                                         is_ctl)
        except Exception:
            logging.error(traceback.format_exc())
            return []

    def get_res_list_baremetal_scene(self, net_list, netinfo,
                                     node_template, is_ctl):
        res_list = []
        for net_name in net_list:
            real_net_name = net_name
            if not self.is_separate_flag() and net_name == 'net_iapi':
                real_net_name = 'net_api'
            need_add = self.need_add_ip(is_ctl, 'baremetal')
            ip_v4 = self.get_ip_in_net_info(real_net_name, netinfo, 4, need_add)
            ip_v6 = self.get_ip_in_net_info(real_net_name, netinfo, 6, need_add)
            if ip_v4 == '' and ip_v6 == '':
                logging.info('no ip in %s, skip' % real_net_name)
                continue
            data = {
                "net_name": net_name,
                "ipv4": ip_v4,
                "ipv6": ip_v6,
                "nic": node_template.get_nic(real_net_name),
                "bond_modes": node_template.get_bond_mode(real_net_name),
                "bond_lacp_modes":
                    node_template.get_bond_lacp_mode(real_net_name),
                "bond_members":
                    node_template.get_bond_members(real_net_name),
                "exchange_types": node_template.get_nic_type(real_net_name),
                "bond_types": node_template.get_bond_type(real_net_name)
            }
            res_list.append(data)
        return res_list

    def get_net_api_address(self, ip_version, net_info):
        try:
            net_api_ip = net_info.get('net_api', {}).get('ip', '')
            if self.get_ip_version(net_api_ip) == ip_version:
                return net_api_ip
            if ip_version == 4:
                return net_info.get('net_api_v4', {}).get('ip', '')
            elif ip_version == 6:
                return net_info.get('net_api_v6', {}).get('ip', '')
            return ''
        except Exception:
            logging.error(traceback.format_exc())
            return ''

    def get_ip_in_net_info(self, net_name, net_info, ip_version,
                           need_add_ip):
        try:
            net_api_ip = net_info.get('net_api', '').get('ip', '')
            if net_name == 'net_iapi':
                ip = self.get_net_iapi_addresss(net_api_ip, ip_version)
                if need_add_ip:
                    self.iapi_ips.append(ip)
                return ip
            elif net_name != 'net_api':
                net_ip = net_info.get(net_name, {}).get('ip', '')
                if self.get_ip_version(net_ip) == ip_version:
                    return net_ip
                else:
                    return ''
            elif net_name == 'net_api':
                return self.get_net_api_address(ip_version, net_info)
            '''
             "netinfo": {
                "net_api": {"ip": "**********"},
                "net_mgt": {"ip": "**********"},
                "net_sto": {"ip": "**********"},
                "net_sto2": {"ip": "**********"},
                "pxe_ip": {"ip": "**********"}
            }
            '''
        except Exception:
            logging.error(traceback.format_exc())
            return ''

    def get_net_iapi_addresss(self, ssh_ip, ip_version):
        try:
            logging.info('get net iapi address')
            if self.is_preset_node_scene():
                if self.user_config.is_user_config_host_ip():
                    ip = self.user_config.get_host_ip_on_node(ssh_ip,
                                                              ip_version)
                    return ip
                if self.user_config.is_user_config_nic_name():
                    ip = self.user_config.get_iapi_ip_on_node_by_nic(ssh_ip,
                                                                     ip_version)
                    return ip
                else:
                    return ''
            if ip_version == 4:
                cmd = 'ip -4 addr show br-iapi | grep inet'
                lines = Utils.ssh_server(ssh_ip, cmd)
                ips = Utils.get_all_ipv4_on_port(lines)
                cmd = "cat /etc/sysconfig/network-scripts/ifcfg-br-iapi | grep -wF IPADDR | awk -F '=' '{print $2}'"
                lines = Utils.ssh_server(ssh_ip, cmd)
                ip_ifcfgs = Utils.get_all_ip_from_ifcfg_res(lines)
                return self.get_iapi_ip_without_vip(ips, ip_ifcfgs)
            elif ip_version == 6:
                cmd = 'ip -6 addr show br-iapi | grep inet6'
                lines = Utils.ssh_server(ssh_ip, cmd)
                ips = Utils.get_all_ipv6_on_port(lines)
                cmd = "cat /etc/sysconfig/network-scripts/ifcfg-br-iapi | grep -wF IPV6ADDR | awk -F '=' '{print $2}' | awk -F '/' '{print $1}'"
                lines = Utils.ssh_server(ssh_ip, cmd)
                ip_ifcfgs = Utils.get_all_ip_from_ifcfg_res(lines)
                return self.get_iapi_ip_without_vip(ips, ip_ifcfgs)
            return ''
        except Exception:
            logging.error(traceback.format_exc())
            return ''

    def get_iapi_ip_without_vip(self, ip_list, ip_ifcfgs):
        try:
            # 如果存在0+1，没有节点地址，会有问题。目前确认裸金属没有0+1场景
            if len(ip_list) == 1:
                return ip_list[0]
            else:
                iapi_vips = self.get_all_iapi_vip()
                ipai_vips_sub_ifgcfg = iapi_vips[:]
                for item in ip_ifcfgs:
                    if item in iapi_vips:
                        ipai_vips_sub_ifgcfg.remove(item)
                vips_in_com_var = \
                    self.get_outband_vips_from_com_var()
                vips_in_paas_conf = \
                    self.get_outband_vips_from_paas_conf()
                node_ips = []
                all_vips = ipai_vips_sub_ifgcfg + vips_in_com_var + vips_in_paas_conf
                for ip in ip_list:
                    if ip not in all_vips:
                        node_ips.append(ip)
                if len(node_ips) > 0:
                    if IP(node_ips[0]).version() == 4:
                        return node_ips[0]
                    else:
                        return node_ips[-1]
            return ''
        except Exception:
            logging.error(traceback.format_exc())
            return ''

    @staticmethod
    def file_exist(file_path):
        return os.path.exists(file_path)

    @staticmethod
    def list_dir(file_path):
        return os.listdir(file_path)

    def get_inet_deploy_file_name(self, node):
        try:
            controller_common_file = '/etc/network/Inet_deploy.conf.tmpl'
            isomerism_file = '/etc/network/Inet_deploy.conf.tmpl_%s'
            minion_path = '/etc/pdm/netconfig_templates/'
            # minion_file = 'Inet.conf_@_%s'
            roles = node.get('roles', [])
            netinfo = node.get('netinfo', {})
            is_controller = True
            if 'paas_controller' not in roles:
                is_controller = False
            if is_controller is False:
                if not self.file_exist(minion_path):
                    logging.error('no minion file path %s' % minion_path)
                    return ''
                file_list = self.list_dir(minion_path)
                file_name = file_list[0]
                return os.path.join(minion_path, file_name)
            if 'pxe_ip' in netinfo:
                admin_ip = netinfo.get('pxe_ip').get('ip', '')
                if self.file_exist(isomerism_file % admin_ip):
                    return isomerism_file % admin_ip
            return controller_common_file
        except Exception:
            logging.error(traceback.format_exc())
            return ''

    def generate_ports_data(self):
        try:
            # 第一次调用，所以超时时间设为5min, 确保vnm 起来
            self.ports = []
            networks_data = self.get_network_from_vnm('net_api',
                                                      retry_times=30,
                                                      wait_time=10)
            if networks_data is None:
                return
            networks = networks_data.get('networks', [])
            if len(networks) == 0:
                logging.warning('no net_api in vnm networks')
                return
            net_api = networks[0]
            self.net_api_network = copy.deepcopy(net_api)
            self.net_api_subnets_id = net_api.get('subnets', [])
            admin_state_up = net_api.get('admin_state_up')
            tenant_id = net_api.get('tenant_id')
            network_id = net_api.get('id')
            apply_num = self.total_api_port_num - self.nodes_num
            if apply_num > 0:
                for index in range(apply_num):
                    port_suffix = str(index + 1)
                    port_name = self.port_prefix + port_suffix
                    port = self.Port(name=port_name,
                                     admin_state_up=admin_state_up,
                                     tenant_id=tenant_id,
                                     network_id=network_id)
                    self.ports.append(port)
        except Exception:
            logging.error(traceback.format_exc())

    def get_network_from_vnm(self, net_name, wait_time=10, retry_times=1):
        try:
            # {
            #     "networks": [
            #         {
            #             "admin_state_up": true,
            #             "availability_zone_hints": [],
            #             "availability_zones": [],
            #             "description": null,
            #             "id": "c14ece8b-6bee-4d94-a3a8-48d243c0384d",
            #             "ipv4_address_scope": null,
            #             "ipv6_address_scope": null,
            #             "mtu": 1500,
            #             "name": "net_api",
            #             "project_id": "paasvnm",
            #             "provider:network_type": "vlan",
            #             "provider:physical_network": "physnet1",
            #             "provider:segmentation_id": 1016,
            #             "revision_number": 1,
            #             "router:external": false,
            #             "shared": false,
            #             "status": "ACTIVE",
            #             "subnets": [
            #                 "70f7a232-62ad-4671-9fde-8f74c60574fd",
            #                 "bd49e798-7e03-41b3-b9b2-51434dae173b"
            #             ],
            #             "tags": [],
            #             "tenant_id": "paasvnm",
            #             "vlan_transparent": null
            #         }
            #     ]
            # }
            url = self.msb_url + '/vnm/v2.0/networks?name=%s' % net_name
            headers = {'Content-type': 'application/json'}
            # 第一次调用vnm 的接口，考虑到vnm没起的情况， 重试 5min
            for _ in range(retry_times):
                resp = requests.get(url=url, headers=headers,
                                    allow_redirects=False)
                self.logging_request(resp, 200)
                if resp.status_code == 200:
                    networks = json.loads(resp.text)
                    return networks
                time.sleep(wait_time)
            self.error_code.append('get network from vnm failed')
            return None
        except Exception:
            logging.error(traceback.format_exc())
            return None

    def get_subnets_from_vnm(self, subnet_id):
        try:
            # {
            #     "subnet": {
            #         "allocation_pools": [
            #             {
            #                 "end": "**********",
            #                 "start": "*********"
            #             }
            #         ],
            #         "cidr": "********/24",
            #         "description": null,
            #         "dns_nameservers": [],
            #         "enable_dhcp": false,
            #         "gateway_ip": "*********",
            #         "host_routes": [],
            #         "id": "70f7a232-62ad-4671-9fde-8f74c60574fd",
            #         "ip_version": 4,
            #         "ipv6_address_mode": null,
            #         "ipv6_ra_mode": null,
            #         "name": "subnet_api",
            #         "network_id": "c14ece8b-6bee-4d94-a3a8-48d243c0384d",
            #         "project_id": "paasvnm",
            #         "revision_number": 0,
            #         "service_types": [],
            #         "subnetpool_id": null,
            #         "tags": [],
            #         "tenant_id": "paasvnm"
            #     }
            # }

            url = self.msb_url + '/vnm/v2.0/subnets/%s' % subnet_id
            headers = {'Content-type': 'application/json'}
            resp = requests.get(url=url, headers=headers,
                                allow_redirects=False)
            self.logging_request(resp, 200)
            if resp.status_code == 200:
                return json.loads(resp.text)
            return None
        except Exception:
            logging.error(traceback.format_exc())
            return None

    def get_subnet_data(self):
        try:
            self.net_api_subnets = []
            self.net_iapi_subnets = []
            for subnet_id in self.net_api_subnets_id:
                subnet_data = self.get_subnets_from_vnm(subnet_id)
                if subnet_data is None:
                    continue
                self.net_api_subnets.append(subnet_data.get('subnet'))
            self.get_iapi_info()
            self.get_sto_info()
        except Exception:
            logging.error(traceback.format_exc())

    def get_iapi_info(self):
        try:
            networks_data = self.get_network_from_vnm('net_iapi')
            if networks_data is None:
                return
            networks = networks_data.get('networks', [])
            if len(networks) == 0:
                logging.warning('no net_iapi in vnm networks')
                return
            net_iapi = networks_data.get('networks', [])[0]
            self.net_iapi_subnets = []
            self.net_iapi_network = copy.deepcopy(net_iapi)
            net_iapi_subnets_id = net_iapi.get('subnets', [])
            for subnet_id in net_iapi_subnets_id:
                subnet_data = self.get_subnets_from_vnm(subnet_id)
                if subnet_data is None:
                    continue
                self.net_iapi_subnets.append(subnet_data.get('subnet'))
        except Exception:
            logging.error(traceback.format_exc())

    def get_sto_info(self):
        try:
            networks_data = self.get_network_from_vnm('net_sto')
            if networks_data is None:
                return
            networks = networks_data.get('networks', [])
            if len(networks) == 0:
                logging.warning('no net_sto in vnm networks')
                return
            net_sto = networks_data.get('networks', [])[0]
            self.net_sto_subnets = []
            self.net_sto_network = copy.deepcopy(net_sto)
            subnets_id = net_sto.get('subnets', [])
            for subnet_id in subnets_id:
                subnet_data = self.get_subnets_from_vnm(subnet_id)
                if subnet_data is None:
                    continue
                self.net_sto_subnets.append(subnet_data.get('subnet'))
        except Exception:
            logging.error(traceback.format_exc())

    def get_vips_by_nodeworker(self, retry=1, wait_time=10):
        try:
            url = self.msb_url + '/nodeworker/v1/tenants/admin/ports'
            headers = {'Content-type': 'application/json'}
            for _ in range(retry):
                resp = requests.get(url=url, headers=headers,
                                    allow_redirects=False, timeout=10.0)
                self.logging_request(resp, 200)
                if resp.status_code == 200:
                    data = json.loads(resp.text)
                    return data.get('vips', [])
                time.sleep(wait_time)
            self.error_code.append('get vip info from nodeworker error')
            return []
        except Exception:
            logging.error(traceback.format_exc())
            return []

    def get_vip_str(self, ip_version):
        vips = self.get_vips_by_nodeworker(retry=6)
        ip_list = []
        for vip_info in vips:
            ip = vip_info.get('ip')
            tmp_ver = self.get_ip_version(ip)
            if tmp_ver == ip_version:
                ip_list.append(Utils.format_ip(ip))
        return ip_list

    def apply_port_from_nodeworker(self, data, retry=1, wait_time=10):
        """
        接口返回：
        {"ip":"************","name":"daisy_vip","result":true}
        :param retry:
        :param wait_time:
        :return:
        """
        try:
            url = self.msb_url + '/nodeworker/v1/tenants/admin/ports'
            headers = {'Content-type': 'application/json',
                       'Accept': 'application/json'}
            for _ in range(retry):
                resp = requests.post(url=url, data=json.dumps(data),
                                     headers=headers,
                                     allow_redirects=False, timeout=10.0)
                self.logging_request(resp, 200)
                if resp.status_code == 200:
                    data = json.loads(resp.text)
                    return data
                time.sleep(wait_time)
            self.error_code.append('apply vip from nodeworker failed')
            return None
        except Exception:
            logging.error(traceback.format_exc())
            return None

    def get_daisy_vip(self, ip_version):
        post_data = {"name": "daisy_vip", "network": "net_api"}
        data = self.apply_port_from_nodeworker(post_data)
        if data is None:
            return ''
        if data.get('result') is True:

            ip = data.get('ip', '')
            if self.get_ip_version(ip) == ip_version:
                return ip
        return ''

    def get_net_api_cidr_from_nodes(self, ip_version):
        try:
            node_data = self.read_json_file(self.nodes_file)
            key = 'net_api'
            if ip_version == 6:
                key = 'net_api_v6'
            return node_data.get('cidr').get(key)
        except Exception:
            logging.error(traceback.format_exc())
            return ''

    def is_net_api_v4_stack(self):
        try:
            node_data = self.read_json_file(self.nodes_file)
            key = 'net_api'
            return key in node_data.get('cidr').keys()
        except Exception:
            logging.error(traceback.format_exc())
            return False

    def is_net_api_v6_stack(self):
        try:
            node_data = self.read_json_file(self.nodes_file)
            key = 'net_api_v6'
            return key in node_data.get('cidr').keys()
        except Exception:
            logging.error(traceback.format_exc())
            return False

    def get_preset_network(self):
        """
        生成预置节点网络信息， 只生成net_api网络
        :return:
        """
        try:
            net_list = []
            api_v4 = None
            api_v6 = None
            if self.is_net_api_v4_stack():
                logging.info('preset net_api v4')
                net_api_v4 = {
                    'name': 'net_api',
                    'network_type': self.get_network_type('net_api'),
                    'vips': self.get_vip_str(4),
                    'daisy_vip': Utils.format_ip(self.get_daisy_vip(4)),
                    'ip_ranges': [{
                        'cidr': Utils.format_ip(
                            self.get_net_api_cidr_from_nodes(4)),
                        'gateway': '',
                        'ip_ranges':
                            self.generate_start_end_list(self.node_api_ips),
                        'vlan_id': ''
                    }]
                }
                net_list.append(net_api_v4)
                api_v4 = net_api_v4
            if self.is_net_api_v6_stack():
                logging.info('preset net_api v6')
                net_api_v6 = {
                    'name': 'net_api',
                    'network_type': self.get_network_type('net_api'),
                    'vips': self.get_vip_str(6),
                    'daisy_vip': Utils.format_ip(self.get_daisy_vip(6)),
                    'ip_ranges': [{
                        'cidr': Utils.format_ip(
                            self.get_net_api_cidr_from_nodes(6)),
                        'gateway': '',
                        'ip_ranges':
                            self.generate_start_end_list(self.node_api_ip_v6s),
                        'vlan_id': ''
                    }]
                }
                net_list.append(net_api_v6)
                api_v6 = net_api_v6
            iapi_net = self.get_preset_iapi_network(api_v4, api_v6)
            net_list.extend(iapi_net)
            return net_list
        except Exception:
            logging.error(traceback.format_exc())
            return []

    def get_preset_iapi_vip(self, ip_ver):
        try:
            if not self.user_config.is_user_config_vips():
                # 无大网地址， 选router_vip
                if ip_ver == 4:
                    resource_name = 'router_vip'
                else:
                    resource_name = 'router_ipv6_vip'
                cmd = 'crm_resource -q -r %s -g ip' % resource_name
                status, output = Utils.execute_cmd(cmd)
                if status == 0:
                    return [output.strip()]
            else:
                return self.user_config.get_vips(ip_ver)
            logging.error('get preset iapi vip failed')
            return []
        except Exception:
            logging.error(traceback.format_exc())
            return []

    def get_vip_by_resource_name(self, resource_name):
        try:
            cmd = 'crm_resource -q -r %s -g ip' % resource_name
            status, output = Utils.execute_cmd(cmd)
            if status == 0:
                return output.strip()
            return ''
        except Exception:
            logging.error(traceback.format_exc())
            return ''

    def replace_api_to_iapi(self, api_v4, api_v6):
        res = []
        try:
            logging.info('replace api to iapi')
            if api_v4 is not None:
                api_net = copy.deepcopy(api_v4)
                api_net['name'] = 'net_iapi'
                api_net['network_type'] = self.get_network_type('net_iapi')
                api_net['is_external_slb'] = False
                if 'daisy_vip' in api_net:
                    api_net.pop('daisy_vip')
                api_net['vips'] = self.get_preset_iapi_vip(4)
                res.append(api_net)
            if api_v6 is not None:
                api_net = copy.deepcopy(api_v6)
                api_net['name'] = 'net_iapi'
                api_net['network_type'] = self.get_network_type('net_iapi')
                api_net['is_external_slb'] = False
                if 'daisy_vip' in api_net:
                    api_net.pop('daisy_vip')
                api_net['vips'] = self.get_preset_iapi_vip(6)
                res.append(api_net)
            return res
        except Exception:
            logging.error(traceback.format_exc())
            return res

    def get_preset_iapi_network(self, api_v4, api_v6):
        iapi_list = []
        try:
            exist_north_network = self.user_config.is_user_config()
            if not exist_north_network:
                return self.replace_api_to_iapi(api_v4, api_v6)
            vip_v4 = self.get_preset_iapi_vip(4)
            if vip_v4:
                iapi_list.append({
                    "name": "net_iapi",
                    "network_type": self.get_network_type('net_iapi'),
                    "vips": vip_v4,
                    "ip_ranges": [{
                        'cidr': self.user_config.get_cidr(4),
                        'gateway': '',
                        'ip_ranges':
                            self.get_iapi_ip_range({'ip_version': 4}),
                        'vlan_id': ''
                    }],
                    "is_default_gw": "0",
                    "is_external_slb": False,
                    "no_north_network": not exist_north_network
                })
            vip_v6 = self.get_preset_iapi_vip(6)
            if vip_v6:
                iapi_list.append({
                    "name": "net_iapi",
                    "network_type": self.get_network_type('net_iapi'),
                    "vips": vip_v6,
                    "ip_ranges": [{
                        'cidr': self.user_config.get_cidr(6),
                        'gateway': '',
                        'ip_ranges':
                            self.get_iapi_ip_range({'ip_version': 6}),
                        'vlan_id': ''
                    }],
                    "is_default_gw": "0",
                    "is_external_slb": False,
                    "no_north_network": not exist_north_network
                })
            return iapi_list
        except Exception:
            logging.error(traceback.format_exc())
            return iapi_list

    def combine_network_data(self):
        try:
            networks = []
            if self.is_preset_node_scene():
                return self.get_preset_network()
            if self.is_overlay_scene():
                return networks
            for subnet in self.net_api_subnets:
                subnet_id = subnet.get('id', '')
                tmp_network = {
                    # 'name': subnet.get('name', ''),
                    # todo name固定写死?
                    'name': 'net_api',
                    'network_type': self.get_network_type('net_api'),
                    'vips': [""],  # todo  需要一个空字符串？
                    'daisy_vip': Utils.format_ip(self.get_daisy_ip(subnet_id)),
                    'ip_ranges': [{
                        'cidr': Utils.format_ip(subnet.get('cidr')),
                        'gateway': Utils.format_ip(subnet.get('gateway_ip')),
                        'vlan_id': self.net_api_network.get(
                            'provider:segmentation_id', ''),
                        'ip_ranges': self.get_ip_ranges(subnet)
                    }],
                    'is_default_gw': '0'
                }
                networks.append(tmp_network)
            iapi_networks = self.get_baremetal_iapi_network()
            networks.extend(iapi_networks)
            sto_networks = self.combine_sto_network_data()
            networks.extend(sto_networks)
            return networks
        except Exception:
            logging.error(traceback.format_exc())
            return []

    def get_baremetal_iapi_network(self):
        res = []
        try:
            not_separate = False if self.is_separate_flag() else True
            if not not_separate:
                for subnet in self.net_iapi_subnets:
                    vips = self.get_net_iapi_vip(subnet)
                    ip_ranges = self.get_iapi_ip_range(subnet)
                    if len(ip_ranges) == 0:
                        logging.info('not exist iapi node ip , skip')
                        continue
                    tmp_network = {
                        'name': 'net_iapi',
                        'network_type': self.get_network_type('net_iapi'),
                        'vips': vips,
                        'ip_ranges': [{
                            'cidr': Utils.format_ip(subnet.get('cidr')),
                            'gateway': Utils.format_ip(subnet.get('gateway_ip')),
                            'vlan_id': self.net_iapi_network.get(
                                'provider:segmentation_id', ''),
                            'ip_ranges': ip_ranges
                        }],
                        'is_default_gw': '1',
                        "is_external_slb": not_separate,
                    }
                    res.append(tmp_network)
            else:
                # 裸设备合一这种场景就不支持大网改配了吧。
                for subnet in self.net_api_subnets:
                    vips = self.get_net_iapi_vip(subnet)
                    tmp_network = {
                        'name': 'net_iapi',
                        'network_type': self.get_network_type('net_iapi'),
                        'vips': vips,
                        'ip_ranges': [{
                            'cidr': Utils.format_ip(subnet.get('cidr')),
                            'gateway': Utils.format_ip(
                                subnet.get('gateway_ip')),
                            'vlan_id': self.net_api_network.get(
                                'provider:segmentation_id', ''),
                            'ip_ranges': self.get_ip_ranges(subnet)
                        }],
                        'is_default_gw': '1',
                        "is_external_slb": not_separate,
                    }
                    res.append(tmp_network)
            return res
        except Exception:
            logging.error(traceback.format_exc())
            return res

    def get_all_ip_in_pools(self, pools):
        ips = []
        for pool in pools:
            start = IP(pool.get('start')).int()
            end = IP(pool.get('end')).int()
            for i in range(start, end + 1):
                ips.append(str(IP(i)))
        return ips

    def get_iapi_ip_range(self, subnet):
        try:
            ip_ver = subnet.get('ip_version')
            ips = []
            for ip in self.iapi_ips:
                if not self.is_legal_ip(ip):
                    continue
                if self.get_ip_version(ip) == ip_ver:
                    ips.append(ip)
            # pools = subnet.get('allocation_pools', [])
            # subnet_ips = self.get_all_ip_in_pools(pools)
            # all_ip = subnet_ips + ips
            # all_ip = list(set(all_ip))
            return self.generate_start_end_list(ips)
        except Exception:
            logging.error(traceback.format_exc())
            return []

    def get_network_type(self, net_name):
        try:
            name_type_dict = {
                'net_api': 'MANAGEMENT',
                'net_iapi': 'PUBLICAPI',
                'net_sto': 'STORAGE_DATA',
                'net_sto2': 'STORAGE_DATA'
            }
            return name_type_dict.get(net_name, '')
        except Exception:
            logging.error(traceback.format_exc())
            return ''

    def format_allocation_pool(self, pools):
        res = []
        try:
            for pool in pools:
                format_pool = {
                    'start': Utils.format_ip(pool.get('start')),
                    'end': Utils.format_ip(pool.get('end'))
                }
                res.append(format_pool)
            return res
        except Exception:
            logging.error(traceback.format_exc())
            return res

    def combine_sto_network_data(self):
        try:
            networks = []
            for subnet in self.net_sto_subnets:
                name = ''
                if subnet.get('name', '') == 'subnet_sto':
                    name = 'net_sto'
                elif subnet.get('name', '') == 'subnet_sto2':
                    name = 'net_sto2'
                if name == '':
                    continue
                pools = subnet.get('allocation_pools', [])
                tmp_network = {
                    'name': name,
                    'network_type': self.get_network_type(name),
                    'vips': [""],
                    'ip_ranges': [{
                        'cidr': Utils.format_ip(subnet.get('cidr')),
                        'gateway': Utils.format_ip(subnet.get('gateway_ip')),
                        'vlan_id': self.net_sto_network.get(
                            'provider:segmentation_id', ''),
                        'ip_ranges': self.format_allocation_pool(pools)
                    }],
                    'is_default_gw': '0'
                }
                networks.append(tmp_network)
            return networks
        except Exception:
            logging.error(traceback.format_exc())
            return []

    def read_conf_file(self, file_path):
        config = ConfigParser.ConfigParser()
        config.read(file_path)
        return config

    def read_json_file(self, file_path):
        with open(file_path) as fp:
            return json.load(fp)

    def is_ume_scene(self):
        try:
            # paas_conf_path = "/etc/pdm/conf/paas.conf"
            pass

        except Exception:
            logging.error(traceback.format_exc())

    def is_baremetal_scene(self):
        try:
            conf_json = self.read_json_file(self.conf_json_file)
            bear_mode = conf_json["region"]["scenariotype"]
            return bear_mode == 'baremetal'
        except Exception:
            logging.error(traceback.format_exc())
            return False

    def is_preset_node_scene(self):
        try:
            conf_json = self.read_json_file(self.conf_json_file)
            bear_mode = conf_json["region"]["scenariotype"]
            return bear_mode == 'preset_node'
        except Exception:
            logging.error(traceback.format_exc())
            return False

    def is_separate_flag(self):
        try:
            # todo enable True, disable False
            # enable 通过平台地址访问paas， disable 从slb访问paas
            vnm_conf = self.read_conf_file(self.vnm_network_file)
            if vnm_conf.has_section('slb'):
                if vnm_conf.has_option('slb', 'separate_flag'):
                    value = vnm_conf.get('slb', 'separate_flag')
                    return value == 'enable'
            return False
        except Exception:
            logging.error(traceback.format_exc())
            return False

    def is_overlay_scene(self):
        try:
            # google云场景等判断
            vnm_conf = self.read_conf_file(self.vnm_network_file)
            if vnm_conf.has_section('infra_network'):
                if vnm_conf.has_option('infra_network', 'infranw_mode'):
                    value = vnm_conf.get('infra_network', 'infranw_mode')
                    if value == 'overlay':
                        return True
            return False
        except Exception:
            logging.error(traceback.format_exc())
            return False

    def is_slb_scene(self):
        try:
            conf_json = self.read_json_file(self.conf_json_file)
            clusters_info = conf_json["clusters"]
            has_slb = False
            for cluster in clusters_info:
                if "nodes" not in cluster:
                    continue
                for node in cluster["nodes"]:
                    labels = node["labels"]
                    if self.has_slb_labels(labels):
                        has_slb = True
            return has_slb
        except Exception:
            logging.error(traceback.format_exc())
            return False

    def has_slb_labels(self, labels):
        try:
            if labels:
                # todo 是否要考虑多集群
                if "slb" in labels and "privilege" in labels:
                    if labels["slb"] == "true" and \
                            labels["privilege"] == "true":
                        return True
            return False
        except Exception:
            logging.error(traceback.format_exc())
            return False

    def get_net_iapi_vip(self, subnet):
        try:
            ip_version = int(subnet.get('ip_version'))
            if self.is_baremetal_scene() and self.is_slb_scene() and not \
                    self.is_separate_flag():
                return self.get_plat_external_ips_from_comvars(ip_version)
            elif self.is_baremetal_scene() and self.is_slb_scene() and \
                    self.is_separate_flag():
                if self.user_config.is_user_config_vips():
                    return self.user_config.get_vips(ip_version)
                else:
                    return self.get_plat_external_ips_from_paasconf(ip_version)
            else:
                return self.get_paas_router_ip_from_paasconf(ip_version)
        except Exception:
            logging.error(traceback.format_exc())
            return []

    def get_paas_router_ip_from_paasconf(self, ip_version):
        try:
            paas_conf = self.read_conf_file(self.paas_conf_file)
            public_vip_v4 = ''
            public_vip_v6 = ''
            if paas_conf.has_section('router'):
                if 'paas_router_ip' in paas_conf.options('router'):
                    public_vip_v4 = paas_conf.get('router',
                                                  'paas_router_ip')
                if 'paas_router_ip_v6' in paas_conf.options('router'):
                    public_vip_v6 = paas_conf.get('router',
                                                  'paas_router_ip_v6')
            res = []
            if ip_version == 4 and Utils.is_ip_legal(public_vip_v4):
                res.append(Utils.format_ip(public_vip_v4))
            elif ip_version == 6 and Utils.is_ip_legal(public_vip_v6):
                res.append(Utils.format_ip(public_vip_v6))
            if len(res) == 0:
                return self.get_baremetal_iapi_vip_from_crm(ip_version)
            return res
        except Exception:
            logging.error(traceback.format_exc())
            return []

    def get_plat_external_ips_from_paasconf(self, ip_version):
        try:
            paas_conf = self.read_conf_file(self.paas_conf_file)
            plat_external_ips = ''
            if 'paas' in paas_conf.sections():
                if 'plat_external_ips' in paas_conf.options('paas'):
                    plat_external_ips = paas_conf.get('paas',
                                                      'plat_external_ips')
            res = []
            if plat_external_ips:
                external_ips = [ip.strip() for ip
                                in plat_external_ips.split(',')]
                for ip in external_ips:
                    if Utils.is_ip_legal(ip) and \
                            IP(ip).version() == ip_version:
                        res.append(Utils.format_ip(ip))
                        # 取第一个地址
                        break
            if len(res) == 0:
                return self.get_baremetal_iapi_vip_from_crm(ip_version)
            return res
        except Exception:
            logging.error(traceback.format_exc())
            return []

    def get_outband_vips_from_com_var(self):
        try:
            data_map = self.read_yaml_file(self.com_vars_file)
            provider_vips = []
            for data in data_map:
                if 'provider_outband_ip' in data:
                    provider_vips.append(data.get('provider_outband_ip', ''))
                if 'provider_outband_ip_v6' in data:
                    provider_vips.append(
                        data.get('provider_outband_ip_v6', ''))
            return provider_vips
        except Exception:
            return []

    @staticmethod
    def get_conf_item_val(conf, item_name):
        sections = conf.sections()
        for sec in sections:
            if conf.has_option(sec, item_name):
                val = conf.get(sec, item_name)
                return val
        return ''

    def get_outband_vips_from_paas_conf(self):
        try:
            paas_conf = self.read_conf_file(self.paas_conf_file)
            provider_vips = []
            vip = self.get_conf_item_val(paas_conf, 'provider_outband_ip')
            provider_vips.append(vip)
            vip6 = self.get_conf_item_val(paas_conf, 'provider_outband_ip_v6')
            provider_vips.append(vip6)
            return provider_vips
        except Exception:
            return []

    def get_baremetal_iapi_vip_from_crm(self, ip_version):
        try:
            resources_v4 = ['paas_controller_vip',
                            'paas_controller_vip_v4',
                            'seperate_vip_v4',
                            'net_iapi_paas_vip_v4',
                            'net_iapi_paas_vip']
            resources_v6 = ['paas_controller_vip_v6',
                            'seperate_vip_v6',
                            'net_iapi_paas_vip_v6']
            if ip_version == 4:
                resources = resources_v4
            else:
                resources = resources_v6
            for res_name in resources:
                vip = self.get_vip_by_resource_name(res_name)
                if vip and IP(vip).version() == ip_version:
                    return [vip.strip()]
            return []
        except Exception:
            logging.error(traceback.format_exc())
            return []

    def get_plat_external_ips_from_comvars(self, ip_version):
        try:
            # com_vars.yml
            # - {plat_external_ips: '7efe:ffff:0:f101::235,
            #                        ************,
            #                        5efe:ffff:0:f101::235,
            #                        6efe:ffff:0:f101::235,
            #                        ************,
            #                        ************'}

            data_map = self.read_yaml_file(self.com_vars_file)
            plat_external_ips = ''
            for data in data_map:
                if 'plat_external_ips' in data:
                    plat_external_ips = data.get('plat_external_ips', '')
            res = []
            if plat_external_ips:
                external_ips = [ip.strip() for ip in
                                plat_external_ips.split(',')]
                for ip in external_ips:
                    if Utils.is_ip_legal(ip) and \
                            IP(ip).version() == ip_version:
                        res.append(Utils.format_ip(ip))
                        # 只取第一个地址
                        break
            if len(res) == 0:
                return self.get_baremetal_iapi_vip_from_crm(ip_version)
            return res
        except Exception:
            logging.error(traceback.format_exc())
            return []

    def get_ip_ranges(self, subnet):
        try:
            ip_version = subnet.get('ip_version')
            subnet_id = subnet.get('id', '')
            ip_list = []
            for port in self.ports:
                if port.is_applied_success() and not port.is_daisy_vip_port():
                    ip = port.get_ip(subnet_id)
                    ip_list.append(ip)
            if ip_version == 4:
                ip_list.extend(self.node_api_ips)
            elif ip_version == 6:
                ip_list.extend(self.node_api_ip_v6s)
            return self.generate_start_end_list(ip_list)
        except Exception:
            logging.error(traceback.format_exc())
            return []

    def is_legal_ip(self, ip):
        try:
            IP(ip)
            return True
        except Exception:
            return False

    def generate_start_end_list(self, ip_list):
        try:
            res_list = []
            legal_ip_list = []
            for ip in ip_list:
                if self.is_legal_ip(ip):
                    legal_ip_list.append(ip)
            legal_ip_list.sort(key=lambda ip_addr: IP(ip_addr).int())
            start_index = 0
            while start_index < len(legal_ip_list):
                end_index = start_index + 1
                while end_index < len(legal_ip_list) and \
                        IP(legal_ip_list[end_index]).int() - \
                        IP(legal_ip_list[end_index - 1]).int() == 1:
                    end_index += 1
                tmp = {
                    'start': Utils.format_ip(legal_ip_list[start_index]),
                    'end': Utils.format_ip(legal_ip_list[end_index - 1])
                }
                res_list.append(tmp)
                start_index = end_index
            return res_list
        except Exception:
            logging.error(traceback.format_exc())
            # 暴力生成，每个ip都生成一个range
            res_list = []
            for ip in ip_list:
                tmp = {
                    'start': Utils.format_ip(ip),
                    'end': Utils.format_ip(ip)
                }
                res_list.append(tmp)
            return res_list

    def set_daisy_vip(self):
        try:
            for port in self.ports:
                if port.is_applied_success():
                    port.set_daisy_vip()
                    break
            else:
                # 一个都没有申请成功的话，则报错。
                if not self.is_preset_node_scene():
                    self.error_code.append('all port applied failed')
        except Exception:
            logging.error(traceback.format_exc(()))

    def get_daisy_ip(self, subnet_id):
        try:
            for port in self.ports:
                if port.is_daisy_vip_port():
                    return port.get_ip(subnet_id)
            return ''
        except Exception:
            logging.error(traceback.format_exc())
            return ''

    def post_ports_to_vnm(self):
        try:
            for port in self.ports:
                name = port.get_name()
                data = self.get_port_from_vnm(name)
                ports = data.get('ports', [])
                if len(ports) > 0:
                    # 有个bug, 如果之前这个port 应用已经申请了，我会认为这个端口用于升级。。
                    # 所以要确保这个端口名之前没有被应用申请。
                    fixed_ips = self.get_fixed_ips_from_vnm_return(data)
                    port.set_fixed_ips(fixed_ips)
                else:
                    port_data = port.return_data()
                    combine_data = {'ports': [port_data]}
                    res, return_data = self.apply_port_from_vnm(combine_data,
                                                                retry_time=3)
                    if res is True:
                        fixed_ips = self.get_fixed_ips_from_vnm_return(
                            return_data)
                        port.set_fixed_ips(fixed_ips)
                    else:
                        port.set_fixed_ips([])
            # 只用调用一次。 set_daisy_vip
            self.set_daisy_vip()
        except Exception:
            logging.error(traceback.format_exc())

    def get_fixed_ips_from_vnm_return(self, vnm_return_data):
        try:
            # vnm port创建成功 返回值 和 通过port名查的结果一样
            # {
            #     "ports": [
            #         {
            #             "id": "6e4bd50f-37da-43f6-8c89-9cced1953556",
            #             "name": "reserved_api_port1",
            #             "network_id": "c14ece8b-6bee-4d94-a3a8-48d243c0384d",
            #             "tenant_id": "paasvnm",
            #             "mac_address": "fa:16:3e:72:af:9a",
            #             "admin_state_up": true,
            #             "status": "DOWN",
            #             "device_id": "",
            #             "device_owner": "",
            #             "fixed_ips": [
            #                 {
            #                     "subnet_id":
            #                        "70f7a232-62ad-4671-9fde-8f74c60574fd",
            #                     "ip_address": "**********"
            #                 },
            #                 {
            #                     "subnet_id":
            #                        "bd49e798-7e03-41b3-b9b2-51434dae173b",
            #                     "ip_address": "3efe:ffff:0:f101::16b"
            #                 }
            #             ],
            #             "project_id": "paasvnm",
            #             "binding:host_id": "",
            #             "allowed_address_pairs": [
            #
            #             ],
            #             "security_groups": [
            #                 "5ce85a9a-6de4-405e-a948-2743f7e09f6e"
            #             ],
            #             "binding:vnic_type": "normal",
            #             "binding:profile": {
            #
            #             },
            #             "binding:vif_type": "unbound",
            #             "binding:vif_details": {
            #
            #             },
            #             "extra_dhcp_opts": [
            #
            #             ],
            #             "description": "",
            #             "tags": [
            #
            #             ],
            #             "created_at": "2021-01-22T09:14:51Z",
            #             "updated_at": "2021-01-22T09:14:51Z",
            #             "revision_number": 1
            #         }
            #     ]
            # }
            # todo 只取第一个， 那么如果有名字重复的则会有问题。
            return vnm_return_data.get('ports')[0].get('fixed_ips', [])
        except Exception:
            logging.error(traceback.format_exc())
            return []

    def apply_port_from_vnm(self, data, retry_time=1):
        try:
            # 只支持一次post一个port
            url = self.msb_url + '/vnm/v2.0/ports'
            headers = {'Content-type': 'application/json'}
            json_data = json.dumps(data)
            logging.info('post data:%s' % str(json_data))
            for _ in range(retry_time):
                resp = requests.post(url=url, data=json_data, headers=headers,
                                     allow_redirects=False, timeout=10.0)
                self.logging_request(resp, 201)
                if resp.status_code == 201:
                    logging.info('apply ports success')
                    return True, json.loads(resp.text)
            logging.error('apply port failed')
            return False, None
        except Exception:
            logging.error(traceback.format_exc())
            return False, None

    def apply_ports_from_vnm(self, data):
        try:
            # 这个接口 只能全成功和全失败。
            # 这样的话 ，data 里有多个port就没意义了。所以一个一个的申请
            url = self.msb_url + '/vnm/v2.0/ports'
            headers = {'Content-type': 'application/json'}
            json_data = json.dumps(data)
            logging.info('post data:%s' % str(json_data))
            # 考虑到vnm 性能，创建60个port大概需要15s, 保守起见， 超时时间设为1min
            resp = requests.post(url=url, data=json_data, headers=headers,
                                 allow_redirects=False, timeout=60.0)
            self.logging_request(resp, 201)
            if resp.status_code == 201:
                logging.info('apply ports success')
                return True
            return False
        except Exception:
            logging.error(traceback.format_exc())

    def del_port_from_vnm(self, port_id):
        try:
            url = self.msb_url + '/vnm/v2.0/ports/%s' % port_id
            headers = {'Content-type': 'application/json'}
            requests.delete(url=url, headers=headers,
                            allow_redirects=False)
        except Exception:
            logging.error(traceback.format_exc())

    def get_port_from_vnm(self, port_name):
        try:
            url = self.msb_url + '/vnm/v2.0/ports?name=%s' % port_name
            headers = {'Content-type': 'application/json'}
            resp = requests.get(url=url, headers=headers,
                                allow_redirects=False)
            if resp.status_code == 200:
                return json.loads(resp.text)
            else:
                self.logging_request(resp, 200)
                return {}
        except Exception:
            logging.error(traceback.format_exc())
            return {}

    def del_all_ports_appleid_from_vnm(self):
        try:
            # 仅用于测试
            nums = self.total_api_port_num - self.nodes_num
            for index in range(nums):
                name = self.port_prefix + str(index + 1)
                data = self.get_port_from_vnm(name)
                ports = data.get('ports', [])
                if len(ports) > 0:
                    port = ports[0]
                else:
                    continue
                port_id = port.get('id')
                self.del_port_from_vnm(port_id)
                print('delete name: %s,  %s' % (name, port_id))

        except Exception:
            logging.error(traceback.format_exc())

    def print_json_data(self, networks, nodes):
        try:
            res_data = {
                'networks': networks,
                'nodes': nodes
            }
            self.print_data = res_data
            logging.info('networks data: %s' % str(res_data))
            print(json.dumps(res_data))
        except Exception:
            logging.error(traceback.format_exc())

    def sys_exit(self):
        try:
            if len(self.error_code) == 0:
                sys.exit(0)
            else:
                for item in self.error_code:
                    print(item)
                sys.exit(1)
        except Exception:
            logging.error(traceback.format_exc())
            sys.exit(1)


def get_network_data(nums=None, manager=None):
    try:
        # manager 便于UT测试，所以加入manager参数
        Utils.config_logging()
        if manager is None:
            manager = InitNetwork()
        if nums is not None:
            manager.total_api_port_num = int(nums)

        # 先获取msb_ip port
        manager.get_msb_url()
        # 通过nodeworker接口获取节点的信息
        nodes = manager.get_nodes_from_node_worker(retry=10)
        # 解析节点信息中的net_api网络信息
        manager.parser_nodes_netinfo(nodes)
        # 对每个需要申请的port实例化出一个port对象。生成post数据
        manager.generate_ports_data()
        # 将port一个个的向vnm申请，并将vnm返回结果保存到port中
        manager.post_ports_to_vnm()
        # 获取net_api, net_iapi, net_sto的每个子网信息
        manager.get_subnet_data()
        # 读取Inet.conf文件 生成nodes 的body体
        node_data = manager.parser_node_data_to_nodes(nodes)
        if manager.need_diff_struct_handle(nodes):
            manager.diff_struct_handle(nodes, node_data)
        # 将子网信息和port 信息组合起来，生成networks的body
        networks = manager.combine_network_data()
        # 屏幕输出， 组合nodes 和networks
        manager.print_json_data(networks, node_data)
        # 脚本显式退出
        manager.sys_exit()
    except Exception:
        logging.error(traceback.format_exc())
        sys.exit(1)


def del_network_data(nums=None):
    # 测试函数： 不要调用
    manager = InitNetwork()
    if nums is not None:
        manager.total_api_port_num = int(nums)
    manager.get_msb_url()
    nodes = manager.get_nodes_from_node_worker()
    manager.parser_nodes_netinfo(nodes)
    manager.del_all_ports_appleid_from_vnm()


if __name__ == '__main__':
    '''

    功能说明：升级时，生成daisy需要的网络信息networks字段和节点网络配置信息nodes字段。

    入口函数：get_network_data ，备注，不要传入参数， 参数只适合调试用
    代码以脚本方式提供，输出包括脚本退出码和屏幕打印：
    脚本退出码：
    0 成功
    1 失败
    屏幕打印：
    打印 json格式body
    '''
    # config_logging()
    if len(sys.argv) == 1:
        get_network_data()
    # elif len(sys.argv) == 2:
    #     if sys.argv[1] == 'del':
    #         del_network_data()
    #     elif sys.argv[1] == 'create':
    #         get_network_data()
    # elif len(sys.argv) == 3:
    #     if sys.argv[1] == 'del':
    #         del_network_data(sys.argv[2])
    #     elif sys.argv[1] == 'create':
    #         get_network_data(sys.argv[2])
