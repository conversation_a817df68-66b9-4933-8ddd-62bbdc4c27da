# -*- coding: UTF-8 -*-
import os
import copy
import json
import logging
import datetime
import traceback
from IPy import IP

try:
    import ConfigParser
except ImportError:
    import configparser as ConfigParser

log_file = '/var/log/slb_generate_conf.log'
log_format = '%(asctime)s %(levelname)s : %(message)s'
log_date_format = '%Y-%m-%d %H:%M:%S'
log_mode = 'a'
SECTION_SLB = 'slb'
SECTION_PAASCONF = 'paasconf'
SECTION_INFRA_NETWORK = 'infra_network'
SECTION_GCLOUD = 'google'
SECTION_ROUTES = 'routes'
WORKMODE = ['service', 'interface', 'interface_backup']
SEPARATE_FLAG = ['yes', 'no']
LVS_INETRULES = ['off', 'on']
LB7_FWD_TYPE = ['self', 'msb']
REQ_MIN_RX_RANGE = range(100000, 10000001)
DETECT_MULT_RANGE = range(2, 256)
ACPT_ECHO_MIN_RANGE = range(0, 10000000)


class logger(object):
    @staticmethod
    def info(result, filename='', code='', **kwargs):
        res = str(result)
        if filename != '':
            res += '\t file: %s' % str(filename)
        if code != '':
            res += '\t error_code: %s' % str(code)
        if kwargs:
            res += str(kwargs)
        logging.info(traceback.extract_stack()[-2][2] + ' ' + res)

    @staticmethod
    def warning(result, filename='', code='', **kwargs):
        res = str(result)
        if filename != '':
            res += '\t file: %s' % str(filename)
        if code != '':
            res += '\t error_code: %s' % str(code)
        if kwargs:
            res += str(kwargs)
        logging.warning(traceback.extract_stack()[-2][2] + ' ' + res)

    @staticmethod
    def error(result, filename='', code='', **kwargs):
        res = str(result)
        if filename != '':
            res += '\t file: %s' % str(filename)
        if code != '':
            res += '\t error_code: %s' % str(code)
        if kwargs:
            res += str(kwargs)
        logging.error(traceback.extract_stack()[-2][2] + ' ' + res)


class Utils(object):
    @staticmethod
    def config_logger():
        logging.basicConfig(level=logging.DEBUG,
                            format=log_format,
                            datefmt=log_date_format,
                            filename=log_file,
                            filemode=log_mode)
        os.chmod(log_file, 0o640)
        logger.info('========Starts: ' + str(datetime.datetime.now()))

    @staticmethod
    def format_ip(ip_str):
        try:
            return str(IP(ip_str))
        except Exception:
            return ip_str

    @staticmethod
    def format_cidr(cidr):
        try:
            ip_version = IP(cidr).version()
            cidr = str(IP(cidr))
            if '/' not in cidr:
                if ip_version == 4:
                    result = cidr + '/32'
                else:
                    result = cidr + '/128'
                return result
            else:
                return cidr
        except Exception:
            return cidr

    @staticmethod
    def read_json(filename):
        try:
            if os.path.exists(filename):
                with open(filename) as fp:
                    return json.load(fp)
            return {}
        except Exception:
            return {}

    @staticmethod
    def write_conf(cf, filename):
        with open(filename, 'w+') as fp:
            cf.write(fp)

    @staticmethod
    def string_to_list(string, default_split=','):
        res = []
        temp_list = string.split(default_split)
        for temp in temp_list:
            res.append(temp.strip())
        return res

    @staticmethod
    def format_ipaddr_list(input_list):
        output_list = []
        for ip in input_list:
            output_list.append(Utils.format_ip(ip))
        return output_list

    @staticmethod
    def list_to_string(lists, with_bracket=False, default_split=','):
        if not isinstance(lists, list):
            logger.error('%s is not list type' % lists)
            return ''
        res = ''
        for item in lists:
            res += str(item) + default_split
        res = res.replace("u'", "'")
        res = res.replace("'", '"')
        if res.endswith(default_split):
            res = res[:-1]
        if not with_bracket:
            return res
        else:
            return '[' + res + ']'

    @staticmethod
    def is_useless_list(list):
        try:
            if not list:
                return True
            useless_flag = True
            for ops in list:
                if ops and ops not in ['', '/', 'N/A']:
                    useless_flag = False
            return useless_flag
        except Exception:
            return True

    @staticmethod
    def is_null_string(dst_str):
        try:
            if not isinstance(dst_str, str):
                dst_str = str(dst_str)
            dst_str = dst_str.strip()
            if dst_str in ['', '/', 'N/A']:
                return True
            return False
        except Exception:
            return True

    @staticmethod
    def analysis_real_ipaddress(list):
        try:
            ipaddress = []
            for addr in list:
                if '/' in addr:
                    temp_ip = addr[:addr.find('/')]
                    ipaddress.append(temp_ip.strip())
                else:
                    ipaddress.append(addr.strip())
            return ipaddress
        except Exception:
            return []

    @staticmethod
    def is_ip_legal(ip_str, version=None):
        try:
            if '/' in ip_str:
                return False
            IP(ip_str)
            if version is None:
                return True
            else:
                return IP(ip_str).version() == version
        except Exception:
            return False

    @staticmethod
    def is_cidr_legal(cidr_str, version=None):
        try:
            if '/' not in cidr_str:
                return False
            IP(cidr_str)
            if version is None:
                return True
            else:
                return IP(cidr_str).version() == version
        except Exception:
            return False


class SlbConf(object):
    inject_file = '/etc/network/openpalette.json'
    generated_file = '/etc/pdm/conf/vnm_network.conf'
    slb_external_networks = []
    slb_serviceips = []
    data = {}
    workmode = 'service'  # service , interface, interface_backup
    bearmode = 'baremetal'  # baremetal, prenode
    is_standard_tcf = False
    infranw_mode = 'underlay'
    gcloud_flag = 'false'

    def start(self):
        try:
            network_cf = self.read_vnm_network_before()
            self.init_data()
            if not self.data:
                logger.warning("openpalette.json is None!")
                return
            self.log_openpalette_data()
            self.add_slb_section(network_cf)
            self.add_paasconf_section(network_cf)
            self.add_infra_network_section(network_cf)
            self.add_gcloud_section(network_cf)
            self.add_routes_section(network_cf)
            Utils.write_conf(network_cf, self.generated_file)
            return
        except Exception:
            logger.error("FAULT ERROR:%s" % traceback.format_exc())
            return False

    def log_openpalette_data(self):
        try:
            global_conf = self.data.get('global', [])
            logger.info("global=%s" % global_conf)
            nodes = self.data.get("nodes", [])
            logger.info("nodes=%s" % nodes)
            networks = self.data.get('networks', [])
            logger.info("networks=%s" % networks)
            slb_global = self.data.get("slb_global", [])
            logger.info("slb_global=%s" % slb_global)
            slb_serviceips = self.data.get("slb_serviceips", [])
            logger.info("slb_serviceips=%s" % slb_serviceips)
            external_networks = self.data.get("slb_external_networks", [])
            logger.info("external_networks=%s" % external_networks)
            if 'preset_ports' in self.data:
                preset_ports = self.data.get("preset_ports", [])
                logger.info("preset_ports=%s" % preset_ports)
            if 'preset_port_templates' in self.data:
                preset_port_templates = self.data.get(
                    "preset_port_templates", [])
                logger.info("preset_port_templates=%s" % preset_port_templates)
        except Exception:
            logger.error("FAULT ERROR:%s" % traceback.format_exc())

    def remove_all_space_in_sheets(self, data, sheet_names):
        """
        删除openpalette.json 文件中对应表格 中的空行。
        目前只兼容列表和字符串两种格式。
        :param data:
        :param sheet_names:
        :return:
        """
        res = {}
        for sheet_name, sheet_content in data.items():
            if sheet_name not in sheet_names:
                res[sheet_name] = sheet_content
                continue
            lines = []
            for line in sheet_content:
                tmp_line = {}
                for key, value in line.items():
                    if isinstance(value, list):
                        tmp_val = []
                        for item in value:
                            tmp_val.append(self.remove_space_in_string(item))
                    else:
                        tmp_val = self.remove_space_in_string(value)
                    value = tmp_val
                    tmp_line[key] = value
                lines.append(tmp_line)
            res[sheet_name] = lines
        return res

    def remove_space_in_string(self, string):
        tmp_string = string.replace(" ", "")
        return tmp_string.strip()

    def init_data(self):
        try:
            self.data = {}
            self.inject_file = '/etc/network/openpalette.json'
            if os.path.exists(self.inject_file):
                self.data = Utils.read_json(self.inject_file)
                backup_data = copy.deepcopy(self.data)
                try:
                    sheet_list = ['slb_external_networks', 'slb_serviceips',
                                  'slb_global', 'preset_ports']
                    self.data = self.remove_all_space_in_sheets(self.data,
                                                                sheet_list)
                except Exception:
                    logger.error("FAULT ERROR:%s" % traceback.format_exc())
                    self.data = backup_data
            else:
                logger.error('not exist /etc/network/openpalette.json')
        except Exception:
            logger.error("FAULT ERROR:%s" % traceback.format_exc())
            self.data = {}
            return False

    def add_gcloud_section(self, cf):
        try:
            cf.add_section(SECTION_GCLOUD)
            self.set_gcloud_flag(cf)
            self.get_slbvm_list(cf)
            self.get_aliases(cf)
        except Exception:
            logger.error("FAULT ERROR:%s" % traceback.format_exc())

    def set_gcloud_flag(self, cf):
        try:
            logger.info("=========set gcloud_flag=========")
            global_list = self.data.get("global", [])
            if not global_list:
                self.gcloud_flag = "false"
                return
            global_dict = global_list[0]
            self.gcloud_flag = \
                global_dict.get("gcloud_flag", "false")
            if self.gcloud_flag != "true":
                self.gcloud_flag = "false"
            logger.info("======gcloud_flag=%s" % self.gcloud_flag)
        except Exception:
            logger.error("FAULT ERROR:%s" % traceback.format_exc())
            self.gcloud_flag = "false"
        finally:
            cf.set(SECTION_GCLOUD, 'gcloud_flag', self.gcloud_flag)

    def set_slbvm_list(self, names, net_iapi_ips, labels, temp_node):
        for label in labels:
            label_lower = label.lower()
            if label_lower.startswith('slb') and \
                    label[label.find(':') + 1:].lower() == 'true':
                temp_node["vm_name"] = names
                temp_node["vm_addr"] = net_iapi_ips
                break
        return temp_node

    def get_slbvm_list(self, cf):
        slbvm_list = []
        try:
            logger.info("=========set slbvm_list conf=========")
            if self.gcloud_flag.lower() == "false":
                slbvm_list = '[]'
                return
            nodes = self.data.get("nodes", [])
            for node in nodes:
                temp_node = {}
                names = node.get("hostname", "").strip()
                net_iapi_ips = node.get("net_iapi_ip", "").strip()
                labels = node.get("labels", "").strip()
                labels = labels.split(',')
                temp_node = \
                    self.set_slbvm_list(names, net_iapi_ips,
                                        labels, temp_node)
                if temp_node:
                    logger.info("======temp_node=%s" % temp_node)
                    slbvm_list.append(temp_node)
            slbvm_list = Utils.list_to_string(slbvm_list, True)
        except Exception:
            logger.error("FAULT ERROR:%s" % traceback.format_exc())
            slbvm_list = '[]'
        finally:
            cf.set(SECTION_GCLOUD, 'slbvm_list', slbvm_list)

    def get_aliases(self, cf):
        aliases = []
        try:
            logger.info("=========set aliases conf=========")
            if self.gcloud_flag.lower() == "false":
                aliases = '[]'
                return
            external_networks = self.data.get("slb_external_networks", [])
            for external_network in external_networks:
                logger.info("=========set aliases=========")
                temp_external_networks = {}
                name = external_network.get("name", "").strip()
                net_nic = \
                    external_network.get("gcloud_net_nic", "").strip()
                aliase_ips = \
                    external_network.get("gcloud_aliase_ip", "").strip()
                temp_external_networks["net_name"] = name
                temp_external_networks["net_nic"] = net_nic
                temp_external_networks["aliase_ip"] = \
                    aliase_ips.split(',')
                if temp_external_networks:
                    logger.info("======temp_external_networks=%s"
                                % temp_external_networks)
                    aliases.append(temp_external_networks)
            logger.info("=========aliases list_to_string=========")
            aliases = Utils.list_to_string(aliases, True)
        except Exception:
            logger.error("FAULT ERROR:%s" % traceback.format_exc())
            aliases = '[]'
        finally:
            cf.set(SECTION_GCLOUD, 'aliases', aliases)

    def add_infra_network_section(self, cf):
        cf.add_section(SECTION_INFRA_NETWORK)
        self.get_infra_conf(cf)

    def get_infra_conf(self, cf):
        self.set_infra_mode(cf)
        if self.infranw_mode != 'overlay':
            logger.warning("infranw_mode not overlay!")
            return
        self.set_infra_other(cf)

    def set_infra_other(self, cf):
        networks = self.data.get("networks", [])
        for network in networks:
            type = network.get("type", "")
            name = network.get("name", "")
            if name == 'net_iapi' and (type.upper() == 'PUBLICAPI' or
                                       type.lower() == 'net_iapi'):
                self.set_pre_networks(cf, network)
            elif name == 'net_api' and (type.upper() == 'MANAGEMENT' or
                                        type.lower() == 'net_api'):
                self.set_infra_networks(cf, network)
        self.set_tunnel_conf(cf)

    def set_tunnel_conf(self, cf):
        tunnel_network = 'net_admin'
        tunnel_type = 'vxlan'
        cf.set(SECTION_INFRA_NETWORK, 'tunnel_network', tunnel_network)
        cf.set(SECTION_INFRA_NETWORK, 'tunnel_type', tunnel_type)

    def set_infra_networks(self, cf, network):
        infra_networks = '[]'
        try:
            cidrs = network.get('cidrs', [])
            gateways = network.get('gateways', [])
            infra_cidr = self.get_infra_ip(cidrs)
            infra_gateway = self.get_infra_ip(gateways)
            if infra_cidr:
                infranet_list = [{
                    "name": "net_api", "subnets": [{
                        "name": "subnet_api", "cidr": infra_cidr,
                        "gateway_ip": infra_gateway}]}]
                infra_networks = Utils.list_to_string(infranet_list, True)
        except Exception:
            logger.info("FAULT ERROR:%s" % traceback.format_exc())
        finally:
            logger.info("set infra_networks = %s" % infra_networks)
            cf.set(SECTION_INFRA_NETWORK, 'infra_networks', infra_networks)

    @classmethod
    def get_infra_ip(self, inputs):
        try:
            output = ''
            if isinstance(inputs, list):
                for input in inputs:
                    try:
                        if IP(input).version() == 4:
                            output = input
                            break
                    except Exception:
                        continue
            else:
                output = inputs.strip()
            return output
        except Exception:
            logger.info("FAULT ERROR:%s" % traceback.format_exc())
            return ''

    def set_pre_networks(self, cf, network):
        pre_networks = '[]'
        try:
            pre_cidr = ''
            cidrs = network.get('cidrs', [])
            if isinstance(cidrs, list):
                for cidr in cidrs:
                    try:
                        if IP(cidr).version() == 4:
                            pre_cidr = cidr
                            break
                    except Exception:
                        continue
            else:
                pre_cidr = cidrs.strip()
            if pre_cidr:
                pre_networks_list = [{"name": "net_admin", "cidr": pre_cidr}]
                logger.info("get pre_networks=%s" % pre_networks_list)
                pre_networks = Utils.list_to_string(pre_networks_list, True)
        except Exception:
            logger.info("FAULT ERROR:%s" % traceback.format_exc())
        finally:
            logger.info("set pre_networks = %s" % pre_networks)
            cf.set(SECTION_INFRA_NETWORK, 'pre_networks', pre_networks)

    def set_infra_mode(self, cf):
        infranw_mode = 'underlay'
        try:
            global_list = self.data.get("global", [])
            if global_list:
                global_dict = global_list[0]
                infranw_mode = global_dict.get("infranw_mode", "underlay")
                infranw_mode = infranw_mode.strip()
                if not infranw_mode:
                    infranw_mode = 'underlay'
                self.infranw_mode = infranw_mode
        except Exception:
            logger.error("FAULT ERROR:%s" % traceback.format_exc())
        finally:
            logger.info("set infra_mode = %s" % infranw_mode)
            self.infranw_mode = infranw_mode
            cf.set(SECTION_INFRA_NETWORK, 'infranw_mode', infranw_mode)

    def add_paasconf_section(self, cf):
        try:
            cf.add_section(SECTION_PAASCONF)
            self.get_serviceips_conf(cf)
        except Exception:
            logger.error("FAULT ERROR:%s" % traceback.format_exc())

    def add_slb_section(self, cf):
        try:
            cf.add_section(SECTION_SLB)
            self.get_global_conf_from_data(cf)
            self.get_slb_node_type_from_data(cf)
            self.slb_external_networks = self.data.get(
                "slb_external_networks", [])
            self.slb_serviceips = self.data.get("slb_serviceips", [])
            if self.slb_external_networks:
                self.get_routes_conf(cf)
                self.get_natrules_conf(cf)
                self.get_bfdsessions_conf(cf)
                self.get_external_prenetworks_conf(cf)
                self.get_external_networks_conf(cf)
                self.get_external_networks_gateways_conf(cf)
                self.get_inner_network_conf(cf)
                self.get_lvs_external_networks_conf(cf)
        except Exception:
            logger.error("FAULT ERROR:%s" % traceback.format_exc())

    def add_routes_section(self, cf):
        try:
            if self.infranw_mode != 'overlay':
                return
            cf.add_section(SECTION_ROUTES)
            try:
                routes_v4, routes_v6 = self.set_static_routes_conf()
            except Exception:
                logger.error("except Exception in "
                             "add_routes_section, set default")
                routes_v4 = '[]'
                routes_v6 = '[]'
            finally:
                cf.set(SECTION_ROUTES, 'routes_v4', routes_v4)
                cf.set(SECTION_ROUTES, 'routes_v6', routes_v6)
        except Exception:
            logger.error("FAULT ERROR:%s" % traceback.format_exc())

    def set_static_routes_conf(self):
        try:
            # 只取net_iapi的静态路由信息
            net_name = 'net_iapi'
            networks = self.data.get('networks', [])
            for network in networks:
                net = self.format_network(network)
                if net.get('name') == net_name:
                    routes_v4, routes_v6 = self.extract_static_routes(net)
                    return routes_v4, routes_v6
            return '[]', '[]'
        except Exception:
            logger.error("FAULT ERROR:%s" % traceback.format_exc())
            return '[]', '[]'

    def extract_static_routes(self, net):
        try:
            routes_v4 = []
            routes_v6 = []
            static_routes = net.get('static_routes', [])
            if not static_routes:
                return '[]', '[]'
            for static_route in static_routes:
                if not static_route:
                    continue
                routes_v4, routes_v6 = \
                    self.make_routes_dict(static_route, routes_v4, routes_v6)
            routes_v4 = Utils.list_to_string(routes_v4, True)
            routes_v6 = Utils.list_to_string(routes_v6, True)
            return routes_v4, routes_v6
        except Exception:
            logger.error("FAULT ERROR:%s" % traceback.format_exc())
            return '[]', '[]'

    def get_routes_dict_list(self, ip, routes_v4, routes_v6):
        if self.get_ip_version(ip[0]) == 4 \
                and self.get_ip_version(ip[1]) == 4:
            route_dict = {"destination": ip[0],
                          "nexthop": ip[1]}
            routes_v4.append(route_dict)
        elif self.get_ip_version(ip[0]) == 6 \
                and self.get_ip_version(ip[1]) == 6:
            route_dict = {"destination": ip[0],
                          "nexthop": ip[1]}
            routes_v6.append(route_dict)
        else:
            logger.warning("illegal: ip of static routes "
                           "has different ip version")
        return routes_v4, routes_v6

    def make_routes_dict(self, static_route, routes_v4, routes_v6):
        ip_list = static_route.split(',')
        for ip in ip_list:
            if '[' in ip and ']' in ip:
                ip = self.split_ip(ip, 6)
            elif '[' not in ip and ']' not in ip:
                ip = self.split_ip(ip, 4)
            else:
                logger.warning("illegal: ip of static routes "
                               "is not ipv4 or ipv6 %s" % ip)
                continue
            if not ip:
                continue
            if len(ip) < 2:
                logger.warning("illegal: static routes should include "
                               "destination and nexthop %s" % ip)
                continue
            routes_v4, routes_v6 = \
                self.get_routes_dict_list(ip, routes_v4, routes_v6)
        return routes_v4, routes_v6

    def split_ip(self, ip, version):
        try:
            return_ip = []
            if version == 4:
                ip = ip.split(':')
                return ip
            else:
                index_list = []
                for i in range(len(ip)):
                    if ip[i] in ['[', ']']:
                        index_list.append(i)
                if len(index_list) != 4:
                    logger.error("ipv6 format is error")
                else:
                    mask_indexleft = ip.index('/')
                    mask_indexright = \
                        ip[mask_indexleft::].index(':')
                    mask_value = \
                        ip[mask_indexleft::][:mask_indexright]
                    ipdes = \
                        ip[index_list[0] + 1:index_list[1]] + mask_value
                    iphop = \
                        ip[index_list[2] + 1:index_list[3]]
                    return_ip.append(ipdes)
                    return_ip.append(iphop)
                return return_ip
        except Exception:
            logger.error("FAULT ERROR:%s" % traceback.format_exc())
            return []

    def get_slb_node_type_from_data(self, cf):
        node_type = 'tcf'
        try:
            logger.info("====get_slb_node_type_from_data===")
            nodes = self.data.get("nodes", [])
            if not nodes:
                logger.warning("get nodes from data failed!!")
            else:
                for node in nodes:
                    labels = node.get("labels", "")
                    labels = labels.strip()
                    if "slb:true" not in labels:
                        continue
                    t_type = node.get("type", "")
                    t_type = t_type.strip().upper()
                    if t_type == 'VM':
                        node_type = 'tcf-vm'
                        break
                    elif t_type == 'BM' or t_type == 'IRONIC_BM':
                        node_type = 'tcf-bm'
                        break
        except Exception:
            logger.error("except Exception in get_global_conf, set default")
        finally:
            cf.set(SECTION_SLB, 'node_type', node_type)

    def get_lvs_external_networks_conf(self, cf):
        lvs_external_networks = []
        try:
            logger.info("===========set lvs_external_networks==========")
            for ex_network in self.slb_external_networks:
                name = ex_network.get("name", "")
                subnet_names = ex_network.get("subnet_names", "")
                subnet_names = self.__construct_subnet_name(subnet_names)
                lvs_ipaddresses = ex_network.get("lvs_ipaddresses", "")
                if Utils.is_null_string(name) or \
                        Utils.is_null_string(subnet_names) or \
                        Utils.is_null_string(lvs_ipaddresses):
                    lvs_external_networks = []
                    logger.info("null string!")
                    break
                lvs_ipaddresses = Utils.string_to_list(lvs_ipaddresses)
                lvs_ipaddresses = Utils.format_ipaddr_list(lvs_ipaddresses)
                lvs_ipaddresses = self.append_mask_to_preset_ip(
                    name, lvs_ipaddresses)
                logger.info("lvs_ipaddresses=%s" % lvs_ipaddresses)
                lvs_external_network = {"name": name,
                                        "subnets": [{
                                            "name": subnet_names,
                                            "ip_addresses": lvs_ipaddresses}]
                                        }
                lvs_external_networks = self.__add_lvs_network(
                    lvs_external_networks, lvs_external_network)
            lvs_external_networks = Utils.list_to_string(
                lvs_external_networks, True)
        except Exception:
            logger.error("FAULT ERROR:%s" % traceback.format_exc())
            lvs_external_networks = '[]'
        finally:
            cf.set(SECTION_SLB, 'lvs_external_networks', lvs_external_networks)

    def __add_lvs_network(self, lvs_external_networks, lvs_network):
        try:
            name = lvs_network.get("name", "")
            subnets = lvs_network.get("subnets", [])
            is_network_find = False
            for index, lvs_external_network in \
                    enumerate(lvs_external_networks):
                net_name = lvs_external_network.get("name", "")
                if net_name != name:
                    continue
                is_network_find = True
                temp_subnets = lvs_external_network.get("subnets", [])
                temp_subnets.extend(subnets)
                lvs_external_network['subnets'] = copy.deepcopy(temp_subnets)
                lvs_external_networks[index] = copy.deepcopy(
                    lvs_external_network)
            if not is_network_find:
                lvs_external_networks.append(lvs_network)
            return lvs_external_networks
        except Exception:
            logger.error("FAULT ERROR:%s" % traceback.format_exc())
            return []

    def get_serviceips_conf(self, cf):
        try:
            logger.info("=========set serviceips conf=========")
            serviceips = []
            for slb_serviceip in self.slb_serviceips:
                temp_serviceip = {}
                slb_scope = slb_serviceip.get("slb_scope", "").strip()
                label = slb_serviceip.get("label", "").strip()
                fixed_ip_address = slb_serviceip.get(
                    "fixed_ip_address", "").strip()
                floating_ip_address = slb_serviceip.get(
                    "floating_ip_address", "").strip()
                external_ip_address = slb_serviceip.get(
                    "external_ip_address", "").strip()
                entry = slb_serviceip.get("entry", "").strip()
                label = label.split(",")
                slb_scope = slb_scope if slb_scope else 'slb'
                temp_serviceip['external_ip_address'] = \
                    Utils.format_ip(external_ip_address)
                temp_serviceip['fixed_ip_address'] = \
                    Utils.format_ip(fixed_ip_address)
                temp_serviceip['floating_ip_address'] = \
                    Utils.format_ip(floating_ip_address)
                temp_serviceip['roles'] = []
                temp_serviceip['label'] = label
                temp_serviceip['scope'] = slb_scope
                temp_serviceip['entry'] = entry
                logger.info("======temp_serviceip=%s" % temp_serviceip)
                serviceips.append(temp_serviceip)
            serviceips = Utils.list_to_string(serviceips, True)
        except Exception:
            logger.error("FAULT ERROR:%s" % traceback.format_exc())
            serviceips = '[]'
        finally:
            cf.set(SECTION_PAASCONF, 'serviceips', serviceips)

    def update_external_networks_gws(self, gateway, external_networks_gateway,
                                     external_networks_gateways):
        try:
            if IP(gateway).version() == 4:
                external_networks_gateway["gateway_v4"] = gateway
                external_networks_gateways.append(external_networks_gateway)
            if IP(gateway).version() == 6:
                external_networks_gateway["gateway_v6"] = gateway
                external_networks_gateways.append(external_networks_gateway)
            return external_networks_gateways
        except Exception:
            logger.error("FAULT ERROR:%s" % traceback.format_exc())
            return external_networks_gateways

    def get_external_networks_gateways_conf(self, cf):
        logger.info("=========set external_networks_gateways conf========")
        external_networks_gws = '[]'
        try:
            ex_networks_gws = []
            for slb_external_network in self.slb_external_networks:
                name = slb_external_network.get("name", "")
                if Utils.is_null_string(name):
                    continue
                gateway = slb_external_network.get("gateways", "")
                gateway = Utils.format_ip(gateway.strip())
                if not gateway:
                    continue
                external_networks_gw = {"name": name}
                for index, existed_gw in enumerate(ex_networks_gws):
                    existed_name = existed_gw.get("name", "")
                    if name == existed_name:
                        external_networks_gw = existed_gw
                        ex_networks_gws.pop(index)
                        break
                external_networks_gws = self.update_external_networks_gws(
                    gateway, external_networks_gw, ex_networks_gws)
            external_networks_gws = Utils.list_to_string(
                external_networks_gws, True)
        except Exception:
            logger.error("FAULT ERROR:%s" % traceback.format_exc())
        finally:
            cf.set(SECTION_SLB, 'external_networks_gateways',
                   external_networks_gws)

    def get_external_networks_conf(self, cf):
        try:
            logger.info("=========set external_networks conf=========")
            external_networks = []
            for slb_external_network in self.slb_external_networks:
                name = slb_external_network.get("name", "")
                subnet_name = slb_external_network.get("subnet_names", "")
                if Utils.is_null_string(name) or \
                        Utils.is_null_string(subnet_name):
                    continue
                subnet_name = self.__construct_subnet_name(subnet_name)
                temp_ex_networks = self.__make_each_ex_networks(
                    name, subnet_name, slb_external_network)
                external_networks = copy.deepcopy(
                    self.__exnetwork_combination(
                        external_networks, temp_ex_networks))
            external_networks = Utils.list_to_string(
                external_networks, True)
        except Exception:
            logger.error("FAULT ERROR:%s" % traceback.format_exc())
            external_networks = '[]'
        finally:
            cf.set(SECTION_SLB, 'external_networks', external_networks)

    def __construct_subnet_name(self, subnet_name):
        try:
            # subnet_net_ext_ne_v4 change to subnet_net_ext_ne
            subnet_name = subnet_name.strip()
            if subnet_name.endswith("_v4"):
                subnet_name = subnet_name[:-3]
            return subnet_name
        except Exception:
            logger.error("FAULT ERROR:%s" % traceback.format_exc())
            return ''

    def __exnetwork_combination(self, src_exnetworks, dest_ex_networks):
        try:
            logger.info("==========__exnetwork_combination===========")
            logger.info("src=%s, dst =%s" % (src_exnetworks, dest_ex_networks))
            external_networks = []
            if not src_exnetworks:
                return dest_ex_networks
            if not dest_ex_networks:
                return src_exnetworks
            dest_ex_network = dest_ex_networks[0]
            dest_name = dest_ex_network['name']
            is_find = False
            for src_exnetwork in src_exnetworks:
                src_name = src_exnetwork['name']
                if dest_name != src_name:
                    external_networks.append(src_exnetwork)
                    continue
                is_find = True
                temp_subnets = []
                src_subnets = src_exnetwork['subnets']
                dest_subnets = dest_ex_network['subnets']
                temp_subnets.extend(src_subnets)
                temp_subnets.extend(dest_subnets)
                temp_ex_network = {"name": dest_name, "subnets": temp_subnets}
                external_networks.append(copy.deepcopy(temp_ex_network))
            if not is_find:
                external_networks.extend(dest_ex_networks)
                return external_networks
            return external_networks
        except Exception:
            logger.error("FAULT ERROR:%s" % traceback.format_exc())
            return []

    def __make_each_ex_networks(self, name, subnet_name, external_dict):
        try:
            external_networks = []
            subnets = self.__make_each_subnets(name, subnet_name,
                                               external_dict)
            logger.info("net_name=%s, subnet_name=%s, make subnets:%s"
                        % (name, subnet_name, subnets))
            if not subnets:
                return external_networks
            temp_external_network = {"name": name, "subnets": subnets}
            external_networks.append(copy.deepcopy(temp_external_network))
            logger.info("external_networks = %s" % external_networks)
            return external_networks
        except Exception:
            logger.error("FAULT ERROR:%s" % traceback.format_exc())
            return []

    def __make_each_subnets(self, net_name, subnet_name, external_dict):
        try:
            logger.info("make each_subnets, dst_dict=%s" % external_dict)
            ip_addresses = external_dict.get("ip_addresses", [])
            slb_scope = external_dict.get("slb_scope", [])
            extraip_addresses = external_dict.get("extraip_addresses", [])
            extraip_perport = external_dict.get("extraip_perport", [])
            logger.info(type(slb_scope))
            logger.info(slb_scope)
            logger.info(ip_addresses)
            logger.info(extraip_addresses)
            if isinstance(slb_scope, list):
                return self.__make_multiple_scope_subnets(net_name,
                                                          subnet_name,
                                                          ip_addresses,
                                                          slb_scope,
                                                          extraip_addresses,
                                                          extraip_perport)
            else:
                return self.__make_single_scope_subnets(net_name,
                                                        subnet_name,
                                                        ip_addresses,
                                                        extraip_addresses,
                                                        extraip_perport)
        except Exception:
            logger.error("FAULT ERROR:%s" % traceback.format_exc())
            return []

    def get_network_mask(self, net_name, ip_ver):
        try:
            if self.is_standard_tcf:
                return self.get_network_mask_in_network(net_name, ip_ver)
            else:
                return self.get_network_mask_from_template(net_name, ip_ver)
        except Exception:
            logger.error("FAULT ERROR:%s" % traceback.format_exc())
            return ''

    def get_network_mask_from_template(self, net_name, ip_ver):
        try:
            templates = self.get_preset_template()
            for template in templates:
                name_key = self.get_netname_key(template)
                for index in range(len(template[name_key])):
                    if template[name_key][index] == net_name:
                        ip_mask = ''
                        if ip_ver == 4:
                            ip_mask = template['ipv4_address'][index]
                        elif ip_ver == 6:
                            ip_mask = template['ipv6_address'][index]
                        if not self.has_mask_in_ip(ip_mask):
                            logging.info('no mask in %s' % ip_mask)
                            return ''
                        mask = ip_mask[ip_mask.find('/') + 1:]
                        return mask
            return ''
        except Exception:
            logger.error("FAULT ERROR:%s" % traceback.format_exc())
            return ''

    @staticmethod
    def has_mask_in_ip(ip_str):
        try:
            if '/' not in ip_str:
                return False
            if ip_str.endswith('/'):
                return False
            return True
        except Exception:
            return False

    def get_preset_port_key(self, info_dict):
        try:
            # 兼容新旧excel
            if 'preset_port_templates' in info_dict.keys():
                return 'preset_port_templates'
            if 'preset_ports' in info_dict.keys():
                return 'preset_ports'
            return 'preset_ports'
        except Exception:
            logging.error(traceback.format_exc())
            return 'preset_ports'

    def get_netname_key(self, template):
        try:
            if 'name' in template.keys():
                # 兼容旧的excel
                name_key = 'name'
            else:
                name_key = 'network_name'
            return name_key
        except Exception:
            logging.error(traceback.format_exc())
            return 'network_name'

    def get_preset_template(self):
        try:
            templates = []
            default_key = self.get_preset_port_key(self.data)
            for template in self.data.get(default_key):
                new_template = copy.deepcopy(template)
                name_key = self.get_netname_key(template)
                if not isinstance(template.get(name_key), list):
                    new_template[name_key] = [template.get(name_key, '')]
                    new_template['ipv4_address'] = [
                        template.get('ipv4_address', '')]
                    new_template['ipv6_address'] = [
                        template.get('ipv6_address', '')]
                    new_template['mac_address'] = \
                        [template.get('mac_address', '')]
                templates.append(new_template)
            return templates
        except Exception:
            logger.error("FAULT ERROR:%s" % traceback.format_exc())
            return []

    @staticmethod
    def format_network(network):
        try:
            if network is None:
                return None
            new_network = copy.deepcopy(network)
            subnet_names = new_network.get('subnet_names', [])
            if not isinstance(subnet_names, list):
                new_network['subnet_names'] = \
                    [new_network.get('subnet_names', '')]
                new_network['ip_versions'] = \
                    [new_network.get('ip_versions', '')]
                new_network['cidrs'] = \
                    [new_network.get('cidrs', '')]
                new_network['vips'] = \
                    [new_network.get('vips', '')]
                new_network['gateways'] = \
                    [new_network.get('gateways', '')]
                new_network['allocation_pools'] = \
                    [new_network.get('allocation_pools', '')]
                new_network['service_allocation_pools'] = \
                    [new_network.get('service_allocation_pools', '')]
                new_network['is_default_gateways'] = \
                    [new_network.get('is_default_gateways', '')]
                new_network['static_routes'] = \
                    [new_network.get('static_routes', '')]
            return new_network
        except Exception:
            logging.error(traceback.format_exc())
            return network

    def get_ip_version(self, ip_str):
        try:
            return IP(ip_str).version()
        except Exception:
            return None

    def get_network_mask_in_network(self, net_name, ip_ver):
        try:
            if net_name == 'net-iapi':
                net_name = 'net_iapi'
                # todo 方案未固定
            networks = self.data.get('networks', [])
            for network in networks:
                net = self.format_network(network)
                if net.get('name') == net_name:
                    for cidr in net.get('cidrs', []):
                        if self.get_ip_version(cidr) == ip_ver:
                            return str(IP(cidr)).split('/')[1]
            return ''
        except Exception:
            logger.error("FAULT ERROR:%s" % traceback.format_exc())
            return ''

    def append_mask_to_preset_ip(self, net_name, ip_list):
        try:
            if not self.is_standard_tcf and self.bearmode != 'prenode':
                # cpaas 非预置节点不用加掩码，其他场景都要加掩码。
                return ip_list
            else:
                return self.process_ip_list(ip_list, net_name)
        except Exception:
            logger.error("FAULT ERROR:%s" % traceback.format_exc())
            return []

    def process_ip_list(self, ip_list, net_name):
        try:
            new_ip_list = []
            for ip in ip_list:
                if ':' in ip:
                    mask = self.get_network_mask(net_name, 6)
                else:
                    mask = self.get_network_mask(net_name, 4)
                if mask == '':
                    logger.error('get %s mask failed' % net_name)
                    new_ip_list.append(ip)
                    continue
                new_ip_list.append(self.combine_ip_and_mask(ip, mask))
            return new_ip_list
        except Exception:
            logger.error("FAULT ERROR:%s" % traceback.format_exc())
            return []

    def combine_ip_and_mask(self, ip_str, mask):
        try:
            if '/' in ip_str:
                return str(IP(ip_str))
            else:
                return str(IP(ip_str)) + '/' + str(mask)
        except Exception:
            logger.error("FAULT ERROR:%s" % traceback.format_exc())
            return ip_str

    def __make_single_scope_subnets(self, net_name, subnet_name, ip_addresses,
                                    extraip_addresses, extraip_perport):
        try:
            subnets = []
            ip_addresses = Utils.string_to_list(ip_addresses)
            ip_addresses = Utils.format_ipaddr_list(ip_addresses)
            extraip_addresses = Utils.string_to_list(extraip_addresses)
            extraip_addresses = Utils.format_ipaddr_list(extraip_addresses)
            if Utils.is_useless_list(ip_addresses):
                return subnets
            extraip_perport = extraip_perport.strip()
            if extraip_perport:
                extraip_perport = int(extraip_perport)
            extraip_perport = extraip_perport if extraip_perport else 0
            extraip_addresses = [] if \
                Utils.is_useless_list(extraip_addresses) \
                else extraip_addresses
            ip_addresses = self.append_mask_to_preset_ip(net_name,
                                                         ip_addresses)
            extraip_addresses = self.append_mask_to_preset_ip(
                net_name, extraip_addresses)
            temp_subnet = {"name": subnet_name,
                           "scope": "slb",
                           "ip_addresses": ip_addresses}
            if self.workmode == 'interface' or \
                    self.workmode == 'interface_backup':
                temp_subnet['extraip_perport'] = extraip_perport
                temp_subnet['extraip_addresses'] = extraip_addresses
            subnets.append(temp_subnet)
            return subnets
        except Exception:
            logger.error("FAULT ERROR:%s" % traceback.format_exc())
            return []

    def __make_multiple_scope_subnets(self, net_name, subnet_name,
                                      ip_addresses,
                                      slb_scope, extraip_addresses,
                                      extraip_perport):
        try:
            subnets = []
            for index, ip_address in enumerate(ip_addresses):
                temp_subnet = {}
                temp_ipaddrs = self.__make_ip_list(ip_address)
                extraip_address = extraip_addresses[index]
                temp_extra_addrs = self.__make_ip_list(extraip_address)
                scope = slb_scope[index].strip()
                scope = scope if scope else 'slb'
                extraperport = extraip_perport[index].strip()
                extraperport = int(extraperport) if extraperport else 0
                if not temp_ipaddrs:
                    continue
                temp_ipaddrs = self.append_mask_to_preset_ip(net_name,
                                                             temp_ipaddrs)
                temp_extra_addrs = self.append_mask_to_preset_ip(
                    net_name, temp_extra_addrs)
                temp_subnet['name'] = subnet_name
                temp_subnet['scope'] = scope
                temp_subnet['ip_addresses'] = temp_ipaddrs
                if self.workmode == 'interface' or \
                        self.workmode == 'interface_backup':
                    temp_subnet['extraip_perport'] = extraperport
                    temp_subnet['extraip_addresses'] = temp_extra_addrs
                subnets.append(temp_subnet)
            return subnets
        except Exception:
            logger.error("FAULT ERROR:%s" % traceback.format_exc())
            return []

    def __make_ip_list(self, ip_address):
        try:
            ip_address = ip_address.strip()
            if not ip_address:
                return []
            ip_address = ip_address.split(',')
            if Utils.is_useless_list(ip_address):
                return []
            else:
                temp_ipaddrs = Utils.format_ipaddr_list(ip_address)
                return temp_ipaddrs
        except Exception:
            logger.error("FAULT ERROR:%s" % traceback.format_exc())
            return []

    def get_natrules_conf(self, cf):
        try:
            logger.info("=========set natrules conf=========")
            natrules = []
            for ex_network in self.slb_external_networks:
                natrule_dst_cidr = ex_network.get("natrule_dst_cidr", [])
                natrule_src_cidr = ex_network.get("natrule_src_cidr", [])
                natrule_tosource = ex_network.get("natrule_tosource", [])
                if not isinstance(natrule_dst_cidr, list):
                    natrule_dst_cidr = Utils.string_to_list(natrule_dst_cidr)
                    natrule_src_cidr = Utils.string_to_list(natrule_src_cidr)
                    natrule_tosource = Utils.string_to_list(natrule_tosource)
                if Utils.is_useless_list(natrule_dst_cidr) or \
                        Utils.is_useless_list(natrule_src_cidr) or \
                        Utils.is_useless_list(natrule_tosource):
                    natrules = []
                else:
                    temp_natrules = self.__make_each_natrules(natrule_dst_cidr,
                                                              natrule_src_cidr,
                                                              natrule_tosource)
                    natrules.extend(temp_natrules)
            natrules = Utils.list_to_string(natrules, True)
        except Exception:
            logger.error("FAULT ERROR:%s" % traceback.format_exc())
            natrules = '[]'
        finally:
            cf.set(SECTION_SLB, 'natrules', natrules)

    def __make_each_natrules(self, dst_cidrs, src_cidrs, tosource):
        try:
            natrules = []
            for index, dst_cidr in enumerate(dst_cidrs):
                if Utils.is_null_string(dst_cidr) or \
                        Utils.is_null_string(src_cidrs[index]) \
                        or Utils.is_null_string(tosource[index]):
                    continue
                temp_natrule = {
                    "destination": Utils.format_cidr(dst_cidr),
                    "sourceip": Utils.format_cidr(src_cidrs[index]),
                    "action": "snat",
                    "parameters": {
                        "tosource": Utils.format_ip(tosource[index])}}
                natrules.append(temp_natrule)
            return natrules
        except Exception:
            logger.error("FAULT ERROR:%s" % traceback.format_exc())
            return []

    def get_gw_in_external_network(self, ex_network):
        if 'gateways' in ex_network.keys():
            logger.info('get gw from new option gateways')
            return Utils.format_ip(ex_network.get("gateways", ""))
        route_nexthop = Utils.format_ip(ex_network.get("route_nexthop", ""))
        return route_nexthop

    def get_bfdsessions_conf(self, cf):
        try:
            bfdsessions = []
            if self.workmode != 'service':
                bfdsessions = '[]'
                cf.set(SECTION_SLB, 'bfdsessions', bfdsessions)
                return
            for ex_network in self.slb_external_networks:
                bfdflag = ex_network.get("bfdsessions", "")
                bfdflag = bfdflag.strip()
                enable_bfdflag_list = ['enable', 'enable-onearmecho']
                if bfdflag not in enable_bfdflag_list:
                    continue
                ip_addresses = ex_network.get("ip_addresses", [])
                ip_addresses = self.__analysis_ip_addresses(ip_addresses)
                route_nexthop = self.get_gw_in_external_network(ex_network)
                if not ip_addresses or not route_nexthop:
                    continue
                temp_bfds = self.__make_each_bfdsessions(
                    ip_addresses, ex_network, route_nexthop, bfdflag)
                bfdsessions.extend(temp_bfds)
            bfdsessions = Utils.list_to_string(bfdsessions, True)
        except Exception:
            logger.error("FAULT ERROR:%s" % traceback.format_exc())
            bfdsessions = '[]'
        finally:
            cf.set(SECTION_SLB, 'bfdsessions', bfdsessions)


    def __make_each_bfdsessions(self, ip_addresses, ex_network, route_nexthop, bfdflag):
        try:
            bfdsessions = []
            for local_ip in ip_addresses:
                temp_bfd = {}
                temp_bfd['local_ip'] = Utils.format_ip(local_ip)
                temp_bfd['peer_ip'] = Utils.format_ip(route_nexthop)
                temp_bfd['required_min_rx'] = 1000000
                temp_bfd['detect_mult'] = 3
                temp_bfd['desired_min_tx'] = 1000000
                temp_bfd['required_min_echo_rx'] = 0
                if bfdflag == 'enable':
                    temp_bfd['bfd_mode'] = 'default'
                else:
                    temp_bfd['bfd_mode'] = 'one-arm-echo'
                temp_bfd['key_id'] = 0
                bfdsessions.append(temp_bfd)
            return bfdsessions
        except Exception:
            logger.error("FAULT ERROR:%s" % traceback.format_exc())
            return []

    def __fix_bfd_conf(self, bfd_required_min_rx, bfd_detect_mult,
                       bfd_desired_min_tx, bfd_required_min_echo_rx):
        try:
            if int(bfd_required_min_rx) not in REQ_MIN_RX_RANGE:
                required_min = 1000000
            else:
                required_min = int(bfd_required_min_rx)
            if int(bfd_detect_mult) not in DETECT_MULT_RANGE:
                detect_mult = 3
            else:
                detect_mult = int(bfd_detect_mult)
            if int(bfd_desired_min_tx) not in REQ_MIN_RX_RANGE:
                desired_min = 1000000
            else:
                desired_min = int(bfd_desired_min_tx)
            if int(bfd_required_min_echo_rx) not in ACPT_ECHO_MIN_RANGE:
                min_echo_rx = 0
            else:
                min_echo_rx = int(bfd_required_min_echo_rx)
            return required_min, detect_mult, desired_min, min_echo_rx
        except Exception:
            logger.error("FAULT ERROR:%s" % traceback.format_exc())
            return 1000000, 3, 1000000, 0

    def __analysis_ip_addresses(self, ip_addresses):
        try:
            if not ip_addresses:
                return []

            if isinstance(ip_addresses, list):
                temp_ipaddresses = []
                for temp_ipaddrs in ip_addresses:
                    temp_list = temp_ipaddrs.split(',')
                    temp_list = Utils.analysis_real_ipaddress(temp_list)
                    temp_ipaddresses.extend(temp_list)
                return temp_ipaddresses
            else:
                ip_addresses = ip_addresses.split(',')
                ip_addresses = Utils.analysis_real_ipaddress(ip_addresses)
                return ip_addresses
        except Exception:
            logger.error("FAULT ERROR:%s" % traceback.format_exc())
            return []

    def get_inner_network_conf(self, cf):
        try:
            if self.is_standard_tcf:
                inner_networks = [{"name": "net-api"}]
            else:
                inner_networks = [{"name": "net_api"}]
            inner_networks = Utils.list_to_string(inner_networks, True)
        except Exception:
            logger.error("FAULT ERROR:%s" % traceback.format_exc())
            inner_networks = '[]'
        finally:
            cf.set(SECTION_SLB, 'inner_networks', inner_networks)

    def get_external_prenetworks_conf(self, cf):
        try:
            external_prenetworks = []
            for ex_network in self.slb_external_networks:
                name = ex_network.get("name", "")
                name = name.strip()
                if not name:
                    continue
                if name not in external_prenetworks:
                    external_prenetworks.append(name)
            if not external_prenetworks:
                return
            external_prenetworks = str(external_prenetworks).replace('u"', '')
            external_prenetworks = str(external_prenetworks).replace("u'", "")
            external_prenetworks = str(external_prenetworks).replace('"', '')
            external_prenetworks = external_prenetworks.replace("'", "")
        except Exception:
            logger.error("FAULT ERROR:%s" % traceback.format_exc())
            external_prenetworks = '[]'
        finally:
            cf.set(SECTION_SLB, 'external_prenetworks', external_prenetworks)

    @staticmethod
    def parser_route_v4_str_to_format(string):
        try:
            # ********/24:**********:
            if string.strip() == '':
                return None
            destination = string.split(':')[0]
            next_hop = string.split(':')[1]
            if not Utils.is_ip_legal(next_hop, 4):
                return None
            if not Utils.is_cidr_legal(destination, 4):
                return None
            data = {
                'destination': Utils.format_cidr(destination),
                'nexthop': Utils.format_ip(next_hop),
            }
            return data
        except Exception:
            logger.error(traceback.format_exc())
            return None

    @staticmethod
    def parser_route_v6_str_to_format(string):
        try:
            if string.strip() == '':
                return None
            last_ip_start = string.rfind('[')
            last_ip_end = string.rfind(']')
            next_hop = string[last_ip_start + 1: last_ip_end]
            split_index = string.find('/')
            mask = string[split_index + 1: last_ip_start - 1]
            ip = string[1: split_index - 1]
            destination = ip + '/' + mask
            if not Utils.is_cidr_legal(destination, 6):
                return None
            if not Utils.is_ip_legal(ip, 6):
                return None
            data = {
                'destination': Utils.format_cidr(destination),
                'nexthop': Utils.format_ip(next_hop)
            }
            return data
        except Exception:
            logger.error(traceback.format_exc())
            return None

    def get_routes_conf_new(self):
        try:
            res = []
            for ex_network in self.slb_external_networks:
                routes_str = ex_network.get('routes', '')
                route_list = routes_str.split(',')
                for route_str in route_list:
                    route = self.parser_route_v4_str_to_format(route_str)
                    if route is not None:
                        res.append(route)
                    route = self.parser_route_v6_str_to_format(route_str)
                    if route is not None:
                        res.append(route)
            return res
        except Exception:
            logger.error("FAULT ERROR:%s" % traceback.format_exc())
            return []

    def get_routes_conf(self, cf):
        try:
            logger.info("===========set routes==========")
            routes = []
            for ex_network in self.slb_external_networks:
                temp_route = {}
                if 'routes' in ex_network.keys():
                    logger.info('get routes by new routes column')
                    routes = self.get_routes_conf_new()
                    break
                route_dst_cidr = ex_network.get("route_dst_cidr", "")
                route_nexthop = ex_network.get("route_nexthop", "")
                if not route_nexthop.strip() or not route_dst_cidr.strip():
                    continue
                temp_route['destination'] = Utils.format_cidr(route_dst_cidr)
                temp_route['nexthop'] = Utils.format_ip(route_nexthop)
                routes.append(temp_route)
            routes = Utils.list_to_string(routes, True)
        except Exception:
            logger.error("FAULT ERROR:%s" % traceback.format_exc())
            routes = '[]'
        finally:
            cf.set(SECTION_SLB, 'routes', routes)

    def get_bear_mode(self):
        try:
            # self.bearmode  must in prenode , baremetal
            global_conf = self.data.get('global', [])
            if global_conf[0]['scenariotype'].upper() == 'preset_node'.upper():
                self.bearmode = 'prenode'
            else:
                self.bearmode = 'baremetal'
            logger.info('get bearmode %s' % self.bearmode)
        except Exception:
            self.bearmode = 'baremetal'
            logger.info('get bearmode failed, default is %s' % self.bearmode)

    def get_global_conf_from_data(self, cf):
        try:
            logger.info("====get_global_conf_from_data===")
            self.get_bear_mode()
            self.get_tcf_scene()
            slb_global = self.data.get("slb_global", [])
            if not slb_global:
                logger.warning("slb_global is not found!")
                self.workmode = 'service'
                separate_flag = 'enable'
                lvs_inetrules = 'off'
                lb7_fwd_type = 'self'
                persistentconns = '[]'
                redundancys = '[]'
                lvs_cpunum = 4
                slb_memory = 'base'
                maxconn = '30w'
                tcp_timeout = ''
                udp_timeout = ''
                icmp_timeout = ''
                http_timeout = ''
                ka_adv_interval = 2
                ka_adv_timeout_times = 3
                exclusivecpus = '1'
                gwg_routes = '[]'
                apiroute_cpu_limits = 1.0
                apiroute_memory_limits = 1.0
                apiroute_l7_max_conns = "20000"
                apiroute_l4_max_conns = "10000"
                apiroute_l7_limit_conn = "true"
                apiroute_l4_limit_conn = "true"
                multi_gw = "[]"
            else:
                slb_global_dict = slb_global[0]
                self.workmode = slb_global_dict.get("workmode", "service")
                separate_flag = slb_global_dict.get("separate_flag", "yes")
                lvs_inetrules = slb_global_dict.get("lvs_inetrules", "off")
                lb7_fwd_type = slb_global_dict.get("lb7_fwd_type", "self")
                apiroute_l7_max_conns = slb_global_dict.get(
                    "apiroute_l7_max_connections", "20000")
                apiroute_l4_max_conns = slb_global_dict.get(
                    "apiroute_l4_max_connections", "10000")
                apiroute_l7_limit_conn = slb_global_dict.get(
                    "apiroute_l7_enable_limit_conn", "true")
                apiroute_l4_limit_conn = slb_global_dict.get(
                    "apiroute_l4_enable_limit_conn", "true")
                lvs_cpunum = self.get_int_conf(slb_global_dict, 'lvs_cpunum')
                exclusivecpus = slb_global_dict.get("slb_exclusivecpus", "1")
                ka_adv_interval = self.get_int_conf(
                    slb_global_dict, 'ka_adv_interval')
                ka_adv_timeout_times = self.get_int_conf(
                    slb_global_dict, 'ka_adv_timeout_times')
                slb_memory = slb_global_dict.get("slb_memory", "base")
                maxconn = slb_global_dict.get("maxconn", "30w")
                tcp_timeout = self.get_int_conf(slb_global_dict, 'tcp_timeout')
                udp_timeout = self.get_int_conf(slb_global_dict, 'udp_timeout')
                icmp_timeout = self.get_int_conf(
                    slb_global_dict, 'icmp_timeout')
                http_timeout = self.get_int_conf(
                    slb_global_dict, 'http_timeout')
                self.workmode, separate_flag, lvs_inetrules, lb7_fwd_type = \
                    self.__fix_conf_value(self.workmode,
                                          separate_flag,
                                          lvs_inetrules,
                                          lb7_fwd_type)
                persistentconns, redundancys = \
                    self.construct_conns_redundancys(slb_global_dict)
                gwg_routes = self.construct_gwg_routes(slb_global_dict)
                apiroute_cpu_limits = self.construct_apiroute_limits(slb_global_dict, 'apiroute_cpu_limits')
                apiroute_memory_limits = self.construct_apiroute_limits(slb_global_dict, 'apiroute_memory_limits')
                multi_gw = self.construct_multi_gw(slb_global_dict)
                logger.info("persistentconns=%s" % persistentconns)
                logger.info("redundancys=%s" % redundancys)
        except Exception:
            logger.error("except Exception in get_global_conf, set default")
            self.workmode = 'service'
            separate_flag = 'enable'
            lvs_inetrules = 'off'
            lb7_fwd_type = 'self'
            persistentconns = '[]'
            redundancys = '[]'
            lvs_cpunum = 4
            slb_memory = 'base'
            maxconn = '30w'
            tcp_timeout = ''
            udp_timeout = ''
            icmp_timeout = ''
            http_timeout = ''
            ka_adv_interval = 2
            ka_adv_timeout_times = 3
            exclusivecpus = '1'
            gwg_routes = '[]'
            apiroute_cpu_limits = 1.0
            apiroute_memory_limits = 1.0
            apiroute_l7_max_conns = "20000"
            apiroute_l4_max_conns = "10000"
            apiroute_l7_limit_conn = "true"
            apiroute_l4_limit_conn = "true"
            multi_gw = '[]'
        finally:
            cf.set(SECTION_SLB, 'workmode', self.workmode)
            cf.set(SECTION_SLB, 'separate_flag', separate_flag)
            cf.set(SECTION_SLB, 'lvs_inetrules', lvs_inetrules)
            cf.set(SECTION_SLB, 'lb7_fwd_type', lb7_fwd_type)
            cf.set(SECTION_SLB, 'persistentconns', persistentconns)
            cf.set(SECTION_SLB, 'redundancys', redundancys)
            cf.set(SECTION_SLB, 'gwg_routes', gwg_routes)
            cf.set(SECTION_SLB, 'lvs_cpunum', str(lvs_cpunum))
            cf.set(SECTION_SLB, 'slb_memory', slb_memory)
            cf.set(SECTION_SLB, 'maxconn', maxconn)
            cf.set(SECTION_SLB, 'tcp_timeout', str(tcp_timeout))
            cf.set(SECTION_SLB, 'udp_timeout', str(udp_timeout))
            cf.set(SECTION_SLB, 'icmp_timeout', str(icmp_timeout))
            cf.set(SECTION_SLB, 'http_timeout', str(http_timeout))
            cf.set(SECTION_SLB, 'ka_adv_interval', str(ka_adv_interval))
            cf.set(SECTION_SLB, 'ka_adv_timeout_times',
                   str(ka_adv_timeout_times))
            cf.set(SECTION_SLB, 'exclusivecpus', str(exclusivecpus))
            cf.set(SECTION_SLB, 'apiroute_cpu_limits', str(apiroute_cpu_limits))
            cf.set(SECTION_SLB, 'apiroute_memory_limits', str(apiroute_memory_limits))
            cf.set(SECTION_SLB, 'apiroute_l7_max_conns', str(apiroute_l7_max_conns))
            cf.set(SECTION_SLB, 'apiroute_l4_max_conns', str(apiroute_l4_max_conns))
            cf.set(SECTION_SLB, 'apiroute_l7_limit_conn', str(apiroute_l7_limit_conn))
            cf.set(SECTION_SLB, 'apiroute_l4_limit_conn', str(apiroute_l4_limit_conn))
            cf.set(SECTION_SLB, 'multi_gw', str(multi_gw))

    def construct_apiroute_limits(self, slb_global_dict, name):
        default_value = 1.0
        try:
            if name not in slb_global_dict:
                return default_value
            tmp_value = slb_global_dict.get(name, str(default_value))
            try:
                value = float(tmp_value)
                if value < 1 or value > 100:
                    return default_value
                value = round(value, 1)
            except Exception:
                value = default_value
            return value
        except Exception:
            logger.info('get %s failed, default is %d' % (name, default_value))
            return default_value

    def get_int_conf(self, slb_global_dict, name):
        default_value = self.__get_default_value_by_name(name)
        try:
            if name not in slb_global_dict:
                return default_value
            tmp_value = slb_global_dict.get(name, str(default_value))
            try:
                value = int(tmp_value)
            except Exception:
                value = default_value
            return value
        except Exception:
            value = default_value
            logger.info('get %s failed, default is %d' % (name, value))
            return value

    def __get_default_value_by_name(self, name):
        default_value = 0
        if name == 'http_timeout':
            default_value = 1800
        elif name == 'icmp_timeout':
            default_value = 60
        elif name == 'udp_timeout':
            default_value = 300
        elif name == 'tcp_timeout':
            default_value = 600
        elif name == 'ka_adv_timeout_times':
            default_value = 3
        elif name == 'ka_adv_interval':
            default_value = 2
        elif name == 'lvs_cpunum':
            default_value = 4
        return default_value

    def __get_snat_port_ranges(self, slb_global_dict):
        if 'persistent_snatconn_portrange' in slb_global_dict:
            snat_port_ranges = slb_global_dict.get(
                "persistent_snatconn_portrange", [])
        elif 'persistentconns_port_ranges' in slb_global_dict:
            snat_port_ranges = slb_global_dict.get(
                "persistentconns_port_ranges", [])
        else:
            snat_port_ranges = []
        return snat_port_ranges

    def construct_conns_redundancys(self, slb_global_dict):
        try:
            snat_port_ranges = self.__get_snat_port_ranges(slb_global_dict)
            dnat_port_ranges = slb_global_dict.get(
                "persistent_dnatconn_portrange", [])
            persistentconns_snat, redundancys = \
                self.__tmp_construct_conns_redundancys(
                    snat_port_ranges, slb_global_dict)
            persistentconns_dnat, redundancys = \
                self.__tmp_construct_conns_redundancys(
                    dnat_port_ranges, slb_global_dict)
            persistentconns = self.__reconstruct_persistentconns(
                persistentconns_snat, persistentconns_dnat)
            persistentconns = Utils.list_to_string(persistentconns, True)
            redundancys = Utils.list_to_string(redundancys, True)
            return persistentconns, redundancys
        except Exception:
            logger.error("FAULT ERROR:%s" % traceback.format_exc())
            return '[]', '[]'

    @staticmethod
    def construct_multi_gw(slb_global_dict):
        try:
            multi_gw_struct = []
            slb_scope = slb_global_dict.get("slb_scope", [])
            if not isinstance(slb_scope, list):
                slb_scope = Utils.string_to_list(slb_scope)
            multi_gw_enable = slb_global_dict.get("multi_gw_enable", [])
            if not isinstance(multi_gw_enable, list):
                multi_gw_enable = Utils.string_to_list(multi_gw_enable)

            for index in range(len(slb_scope)):
                multi_gw = multi_gw_enable[index]
                if multi_gw == "":
                    multi_gw = "false"
                multi_gw_struct.append({
                    "scope": slb_scope[index],
                    "multi_gw": multi_gw,
                })
            multi_gw_struct = \
                Utils.list_to_string(multi_gw_struct, True)
            logger.info(multi_gw_struct)
            return multi_gw_struct
        except Exception:
            logger.error("FAULT ERROR:%s" % traceback.format_exc())
            return '[]'

    def construct_gwg_routes(self, slb_global_dict):
        """
        Returns:[{"gwg_name": "slb", "destinations": [""]},
        {"gwg_name": "scope1", "destinations":
        ["10.1.2.0/24", "6666:1234::0/64",
         "11.8.0.0/24", "85.45.6.0/24"]}]
        Examples:
            slb:None
            scope1:10.1.2.0/24,6666:1234::0/64,11.8.0.0/24,85.45.6.0/24
            json:"gwg_routes_destinations"=
            ["","10.1.2.0/24,6666:1234::0/64,11.8.0.0/24,85.45.6.0/24"]
        """
        try:
            gwg_routes_struct = []
            slb_scope = slb_global_dict.get("slb_scope", [])
            logger.info(slb_scope)
            if not isinstance(slb_scope, list):
                slb_scope = Utils.string_to_list(slb_scope)
            gwg_routes_destinations = \
                slb_global_dict.get("gwg_routes_destinations", [])
            if len(slb_scope) == 1:
                logger.info("only one slb cluster,return []")
                gwg_routes_struct = \
                    Utils.list_to_string(gwg_routes_struct, True)
                return gwg_routes_struct
            else:
                for index, scope in enumerate(slb_scope):
                    if scope == 'slb':
                        continue
                    gwg_routes_destinations_tmp = \
                        Utils.string_to_list(gwg_routes_destinations[index])
                    temp_gwg_routes = \
                        self.make_gwg_routes_byscope(
                            gwg_routes_destinations_tmp, scope)
                    gwg_routes_struct.extend(temp_gwg_routes)
                gwg_routes_struct = \
                    Utils.list_to_string(gwg_routes_struct, True)
                logger.info(gwg_routes_struct)
                return gwg_routes_struct
        except Exception:
            logger.error("FAULT ERROR:%s" % traceback.format_exc())
            return '[]'

    def __reconstruct_persistentconns(self, persistent_snat, persistent_dnat):
        try:
            persistentconns = []
            if not persistent_snat and not persistent_dnat:
                return persistentconns
            persistentconns = persistent_snat
            persistentconns = self.__add_dnatconns_to_persistentconns(
                persistentconns, persistent_dnat)
            return persistentconns
        except Exception:
            logger.error("FAULT ERROR:%s" % traceback.format_exc())
            return []

    def __add_dnatconns_to_persistentconns(self, persistentconns,
                                           dnat_persistent):
        try:
            for persistent in dnat_persistent:
                scope1 = persistent.get("scope", "slb")
                dnatconn = persistent.get("snatconn", [])
                is_scope_find = False
                for index, persistentconn in enumerate(persistentconns):
                    scope2 = persistentconn.get("scope", "slb")
                    if scope1 == scope2:
                        is_scope_find = True
                        persistentconn['dnatconn'] = copy.deepcopy(dnatconn)
                        persistentconns[index] = copy.deepcopy(persistentconn)
                if not is_scope_find:
                    tmp_persistent = {"scope": scope1, "dnatconn": dnatconn}
                    persistentconns.append(copy.deepcopy(tmp_persistent))
            return persistentconns
        except Exception:
            logger.error("FAULT ERROR:%s" % traceback.format_exc())
            return []

    def __tmp_construct_conns_redundancys(self, port_ranges, slb_global_dict):
        try:
            slb_scope = slb_global_dict.get("slb_scope", [])
            slaveinst_num = slb_global_dict.get(
                "redundancys_slaveinst_num", [])
            if not isinstance(slb_scope, list):
                slb_scope = Utils.string_to_list(slb_scope)
                slaveinst_num = Utils.string_to_list(slaveinst_num)
            # slaveinst_num='' or slaveinst_num=[]
            if not slaveinst_num or Utils.is_useless_list(slaveinst_num):
                redundancys = []
            else:
                if self.workmode != 'service':
                    redundancys = []
                else:
                    redundancys = self.construct_redundancys_conf(
                        slaveinst_num, slb_scope)
            if not port_ranges:
                persistentconns = []
            else:
                if self.workmode == 'interface':
                    persistentconns = []
                else:
                    persistentconns = self.construct_persistentconns_conf(
                        port_ranges, slb_scope)
            logger.info("persistentconns=%s" % persistentconns)
            logger.info("redundancys=%s" % redundancys)
            return persistentconns, redundancys
        except Exception:
            logger.error("FAULT ERROR:%s" % traceback.format_exc())
            return [], []

    def construct_persistentconns_conf(self, port_ranges, slb_scope):
        try:
            persistentconns = []
            if isinstance(port_ranges, list) and not \
                    self.__check_scope_before(port_ranges, slb_scope):
                return persistentconns
            if len(slb_scope) == 1:
                list_range = Utils.string_to_list(port_ranges)
                scope = 'slb'
                return self.__make_persistentconns_byscope(list_range, scope)
            else:
                for index, scope in enumerate(slb_scope):
                    list_range = Utils.string_to_list(port_ranges[index])
                    temp_persistentconns = \
                        self.__make_persistentconns_byscope(list_range, scope)
                    persistentconns.extend(temp_persistentconns)
                return persistentconns
        except Exception:
            logger.error("FAULT ERROR:%s" % traceback.format_exc())
            return []

    def __make_persistentconns_byscope(self, list_range, scope):
        try:
            persistentconns = []
            scope = 'slb' if not scope else scope
            if not list_range:
                return persistentconns
            destports = self.__make_destports(list_range)
            if not destports:
                return persistentconns
            else:
                persistentconns = [{
                    "scope": scope,
                    "snatconn": [{"destports": destports}]}]
                return persistentconns
        except Exception:
            logger.error("FAULT ERROR:%s" % traceback.format_exc())
            return []

    def make_gwg_routes_byscope(self, gwg_routes, scope):
        try:
            gwg_routes_struct = []
            if not gwg_routes:
                return gwg_routes_struct
            else:
                gwg_routes_struct = [{
                    "gwg_name": scope,
                    "destinations": gwg_routes}]
                return gwg_routes_struct
        except Exception:
            logger.error("FAULT ERROR:%s" % traceback.format_exc())
            return []

    def __make_destports(self, list_range):
        try:
            destports = []
            for range in list_range:
                temp = {}
                if not range.strip():
                    continue
                elif '-' not in range:
                    temp['start'] = int(range)
                    temp['end'] = int(range)
                    destports.append(temp)
                else:
                    start_port = range[:range.find('-')]
                    end_port = range[range.find('-') + 1:]
                    temp['start'] = int(start_port)
                    temp['end'] = int(end_port)
                    destports.append(temp)
            return destports
        except Exception:
            logger.error("FAULT ERROR:%s" % traceback.format_exc())
            return []

    def __check_scope_before(self, relation_conf, slb_scope):
        try:
            if len(slb_scope) != len(set(slb_scope)):
                logger.error("slb_scope=%s, scope is duplicate!" % slb_scope)
                # return False
            if len(relation_conf) > 1 and len(relation_conf) != len(slb_scope):
                logger.error("relation_conf=%s, slb_scope=%s, can not match!"
                             % (relation_conf, slb_scope))
                return False
            return True
        except Exception:
            logger.error("FAULT ERROR:%s" % traceback.format_exc())
            return False

    def construct_redundancys_conf(self, slaveinst_num, slb_scope):
        try:
            redundancys = []
            if not self.__check_scope_before(slaveinst_num, slb_scope):
                return redundancys
            if len(slaveinst_num) == 1:
                if not slaveinst_num[0].strip():
                    return redundancys
                redundancys = [{"scope": "slb",
                                "slaveinst_num": str(slaveinst_num[0])}]
                return redundancys
            else:
                for index, num_str in enumerate(slaveinst_num):
                    if not num_str:
                        continue
                    scope = slb_scope[index] if slb_scope[index] else 'slb'
                    temp_redundancy = {"scope": scope,
                                       "slaveinst_num": str(num_str)}
                    redundancys.append(temp_redundancy)
                return redundancys
        except Exception:
            logger.error("FAULT ERROR:%s" % traceback.format_exc())
            return []

    def __fix_conf_value(self, workmode, separate_flag,
                         lvs_inetrules, lb7_fwd_type):
        try:
            logger.info("__fix_conf_value")
            if workmode not in WORKMODE:
                logger.error("workmode is [%s], set default" % self.workmode)
                self.workmode = 'service'
            if separate_flag not in SEPARATE_FLAG:
                logger.error("separate_flag is [%s], "
                             "set default" % separate_flag)
                separate_flag = 'yes'
            separate_flag = 'enable' if \
                separate_flag == 'yes' else 'disable'
            if lvs_inetrules not in LVS_INETRULES:
                logger.error("lvs_inetrules is [%s], "
                             "set default" % lvs_inetrules)
                lvs_inetrules = 'off'
            if lb7_fwd_type not in LB7_FWD_TYPE:
                logger.error("lb7_fwd_type is [%s], "
                             "set default" % lb7_fwd_type)
                lb7_fwd_type = 'self'
            return self.workmode, separate_flag, lvs_inetrules, lb7_fwd_type
        except Exception:
            logger.error("FAULT ERROR:%s" % traceback.format_exc())
            return 'service', 'enable', 'off', 'self'

    def judge_section(self, section):
        if section.lower() == 'slb' or section.lower() == 'paasconf' \
                or section.lower() == 'infra_network' \
                or section.lower() == 'google' \
                or section.lower() == 'routes':
            logger.warning("&&slb or paasconf or infra_network "
                           "exist! skip!&&")
            return False
        return True

    def read_vnm_network_before(self):
        try:
            network_cf = ConfigParser.ConfigParser()
            if not os.path.exists(self.generated_file):
                logger.info("vnm_network.conf not exist! create new!")
                return network_cf

            network_cf.read(self.generated_file)
            new_network_cf = ConfigParser.ConfigParser()
            for section in network_cf.sections():
                if not self.judge_section(section):
                    continue
                new_network_cf.add_section(section)
                opts = network_cf.options(section)
                for key in opts:
                    value = network_cf.get(section, key)
                    new_network_cf.set(section, key, value)
            return new_network_cf
        except Exception:
            network_cf = ConfigParser.ConfigParser()
            return network_cf

    def get_tcf_scene(self):
        try:
            # 默认是ume场景
            global_info = self.data.get('global')[0]
            if str(global_info.get('tcf_scenario')).lower() != 'ume-standard':
                self.is_standard_tcf = True
                logger.info('standard tcf')
                return True
            else:
                self.is_standard_tcf = False
                logger.info('not standard tcf')
                return False
        except Exception:
            logger.error("FAULT ERROR:%s" % traceback.format_exc())
            self.is_standard_tcf = False
            return False


if __name__ == '__main__':
    Utils.config_logger()
    slbconf_obj = SlbConf()
    slbconf_obj.start()
