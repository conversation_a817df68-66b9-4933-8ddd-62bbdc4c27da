# -*- coding: UTF-8 -*-
import copy
import os
import re
import yaml
import json
import sys
import paramiko
import requests
import traceback
import logging
import argparse
import subprocess as commands
from IPy import IP

com_var = '/root/common/com_vars.yml'
port_var = '/root/common/port_vars.yml'
error_code = 1

BACKUP_PATH = "/paasdata/op-conf/inetagent/recover_node_backup/"
BACKUP_FILE = "backup_node.json"
USER_INPUT = "/paasdata/op-data/pdm-cli/reverse_build_recover_template_data/" \
             "preset_params.json"

class writer(object):
    log = []

    def write(self, data):
        self.log.append(data)

    def flush(self):
        pass


class UpdateUserdata(object):

    @staticmethod
    def read_json_file(filename):
        content = {}
        try:
            with open(filename, "r") as fo:
                content = json.load(fo)
        except Exception as e:
            err_str = ("open file %s error or json load this file error:\n%s" %
                       (filename, e))
            logging.error(err_str)
        return content

    @staticmethod
    def write_json_file(file_json, file_path):
        try:
            json_str = json.dumps(file_json, indent=4)
            with open(file_path, "w+") as f:
                f.write(json_str)
            os.chmod(file_path, 0o640)
        except Exception as e:
            err_str = ("open file %s error or json dumps error:\n%s" %
                       (file_path, e))
            logging.error(err_str)

    @staticmethod
    def config_logging(file_name='/paasdata/op-log/inetagent/update_userdata.log'):
        logging.basicConfig(level=logging.DEBUG,
                            format='%(asctime)s %(levelname)s %(funcName)s'
                                   ' line[%(lineno)d]: %(message)s',
                            datefmt='%Y-%m-%d %H:%M:%S',
                            filename=file_name,
                            filemode='a')
        os.chmod(file_name, 0o640)
        logging.info('==== start====')

    def parser_parameter(self):
        parser = argparse.ArgumentParser()
        parser.add_argument('--serverid', type=str, default=None)
        parser.add_argument('--nwname', type=str, default=None)
        parser.add_argument('--mac', type=str, default=None)
        server_id = ''
        nw_name = ''
        mac = ''
        args = parser.parse_args()
        if args.serverid:
            logging.info('server id: %s', args.serverid)
            server_id = args.serverid
        if args.nwname:
            logging.info('nwname: %s', args.nwname)
            nw_name = args.nwname
        if args.mac:
            logging.info('mac: %s', args.mac)
            mac = args.mac
        return server_id, nw_name, mac

    @staticmethod
    def read_yaml_file(file_path):
        with open(file_path) as fp:
            documents = yaml.safe_load(fp)
        return documents

    def get_msb_url(self):
        try:
            ip = ''
            port = ''
            data_map = self.read_yaml_file(com_var)
            for data in data_map:
                if 'openpalette_service_ip' in data:
                    ip = data.get('openpalette_service_ip', '')
            data_map = self.read_yaml_file(port_var)
            for data in data_map:
                if 'openpalette_service_port' in data:
                    port = data.get('openpalette_service_port', '')
            if IP(ip).version() == 4:
                return 'http://%s:%s' % (ip, port)
            elif IP(ip).version() == 6:
                return 'http://[%s]:%s' % (ip, port)
            return ''
        except Exception:
            logging.error(traceback.format_exc())
            return ''

    def write_backup_node_file(self, node, ipaddrs):
        if not os.path.exists(BACKUP_PATH):
            os.makedirs(BACKUP_PATH, mode=0o750)
        self.write_json_file(node, BACKUP_PATH + BACKUP_FILE)
        cmd_mkdir = "sudo mkdir -m 750 %s" % BACKUP_PATH
        for ip in ipaddrs:
            self.ssh_server(ip, cmd_mkdir)
        self.copy_file_to_every_node(BACKUP_PATH + BACKUP_FILE)
        logging.info("backup node file to other controller nodes done!")

    def get_node_info(self, server_id):
        headers = {
            "accept": "application/json",
            "content-type": "application/json"}
        base_url = self.get_msb_url()
        url = base_url + '/nodeworker/v1/tenants/admin/nodes'
        resp = requests.get(url=url, headers=headers,
                            allow_redirects=False, timeout=10.0)
        if resp.status_code != 200:
            logging.warning('get node info failed, url: %s, code: %s' %
                            (url, resp.status_code))
            node = self.read_json_file(BACKUP_PATH + BACKUP_FILE)
            if node and node.get("serverid") == server_id:
                return node
            self.error_exit()
            return {}
        logging.info(resp.text)
        data = json.loads(resp.text)
        nodes = data.get('nodes', [])
        target, ipaddrs = self.get_target_node(nodes, server_id)
        if target:
            self.write_backup_node_file(target, ipaddrs)
            return target
        self.error_exit()

    def parser_node(self, node):
        userdata = node.get('userdata', {})
        if not userdata:
            logging.info('not exist userdata, skip')
            self.normal_exit()
        api_ip = ''
        try:
            api_ip = node.get('netinfo', {}).get('net_api', {}).get('ip', '')
        except Exception:
            logging.warning('parser api ip failed')
            self.error_exit()
        uuid = node.get('uuid', '')
        if not uuid:
            logging.info('not exist uuid, skip')
            self.normal_exit()
        logging.info('api ip: %s, uuid : %s' % (api_ip, uuid))
        logging.info('old userdata : %s' % str(userdata))
        return api_ip, userdata, uuid

    @staticmethod
    def execute_cmd(cmd):
        status, output = commands.getstatusoutput(cmd)
        return status, output

    def copy_file_to_every_node(self, filepath):
        try:
            cmd = ('ansible nodes -i /etc/pdm/hosts -m copy -a '
                   '"src=%s dest=%s mode=0640"  -u ubuntu -b' % (filepath, filepath))
            self.execute_cmd(cmd)
        except Exception:
            logging.error(traceback.format_exc())

    @staticmethod
    def ssh_server(ip, cmd, user='ubuntu'):
        try:
            logging.info(cmd)
            logger = writer()
            sys.stderr = logger
            pkey = '/root/.ssh/id_rsa'
            key = paramiko.RSAKey.from_private_key_file(pkey)
            tran = paramiko.Transport((ip, 22))
            tran.connect(username=user, pkey=key)
            ssh1 = paramiko.SSHClient()
            ssh1.set_missing_host_key_policy(paramiko.RejectPolicy())
            ssh1._transport = tran
            stdin, stdout, stderr = ssh1.exec_command(cmd, timeout=10)
            res = stdout.readlines()
            # logging.info(res)
            ssh1.close()
            return res
        except Exception:
            logging.error('error cmd:%s.' % traceback.format_exc())
            return []

    def is_legal_ip(self, ip):
        try:
            IP(ip)
            return True
        except Exception:
            return False

    def parser_ip_addr_show(self, lines):
        nic_ip_map = {}
        nic_mac_map = {}
        try:
            nic = ''
            for line in lines:
                if 'BROADCAST' in line:
                    nic = line.split()[1][:-1]
                    if nic not in nic_ip_map and nic:
                        nic_ip_map[nic] = []
                elif 'link/ether' in line:
                    mac = line.split()[1]
                    if nic not in nic_mac_map and mac:
                        nic_mac_map[nic] = mac
                elif 'inet' in line:
                    ip, mask = line.split()[1].split('/')
                    if self.is_legal_ip(ip) and nic:
                        nic_ip_map[nic].append(ip + '/' + mask)
            return nic_ip_map, nic_mac_map
        except Exception:
            logging.error(traceback.format_exc())
            return nic_ip_map, nic_mac_map

    def get_all_ifconfig(self, api_ip):
        cmd = 'ip addr show'
        lines = self.ssh_server(api_ip, cmd)
        nic_ips, nic_mac = self.parser_ip_addr_show(lines)
        logging.info('nic-ips: %s' % str(nic_ips))
        logging.info('nic-mac: %s' % str(nic_mac))
        return nic_ips, nic_mac

    def get_mac_by_ip(self, nic_ips, nic_mac, ip, ipv6):
        for nic, ips in nic_ips.items():
            if ip in ips:
                return nic_mac[nic]
            elif ipv6 in ips:
                return nic_mac[nic]
        return ''

    def put_userdata_to_node(self, uuid, body):
        try:
            logging.info('updating userdata to nodeworker')
            headers = {"accept": "application/json",
                       "content-type": "application/json",
                       "User-Agent": "iNetManager"}
            url = self.get_msb_url() + '/nodeworker/v1/tenants/admin/nodes/%s' % uuid
            body = json.dumps(body)
            resp = requests.put(url=url,
                                data=body,
                                headers=headers,
                                allow_redirects=False,
                                timeout=10.0)
            if resp.status_code not in [200, 201]:
                logging.error('put userdata to nodeworker failed, '
                              'code: %s, body: %s' % (resp.status_code, body))
                self.error_exit()
            else:
                logging.info('put userdata to nodeworker success')
        except Exception:
            logging.error(traceback.format_exc())

    def error_exit(self):
        sys.exit(error_code)

    def normal_exit(self):
        sys.exit(0)

    def get_user_input_mac(self):
        try:
            res = {}
            user_params = self.read_json_file(USER_INPUT).get("user_params", [])
            for user_param in user_params:
                if user_param.get("name", "") == "slb_preset_network_info":
                    values = user_param.get("value", [])
                    net_name = ""
                    mac = ""
                    for value in values:
                        for item in value:
                            if item.get("name", "") == "infra_network":
                                net_name = item.get("value", "")
                            elif item.get("name", "") == "mac":
                                mac = item.get("value", "")
                        if mac:
                            res[net_name] = mac
            logging.info("get user input mac: %s", res)
            return res
        except Exception:
            logging.error(traceback.format_exc())
            return {}

    @staticmethod
    def update_recover_minion_json(server_id, ports):
        logging.info('updating recover_minion_json')
        nwnames = []
        macs = []
        for port in ports:
            nwnames.append(port.get("infra_network", ""))
            macs.append(port.get("mac", ""))
        os.system("$(command -v pythonlatest || echo python) "
                  "/etc/pdm/deploy_playbooks/network_convsersion/"
                  "update_userdata_mac.py --serverid %s --nwname %s "
                  "--mac %s" % (server_id, ','.join(nwnames), ','.join(macs)))

    def compare_with_node_ifconfig(self, api_ip, userdata):
        ports = []
        nic_ips, nic_mac = self.get_all_ifconfig(api_ip)
        for port in userdata.get('ports', []):
            old_mac = port.get('mac', '')
            new_port = copy.deepcopy(port)
            ipv4 = port.get('ip')
            ipv6 = port.get('ipv6')
            mac = self.get_mac_by_ip(nic_ips, nic_mac, ipv4, ipv6)
            if mac and old_mac != mac:
                new_port['mac'] = mac
                logging.info(
                    'update mac, old: %s, new: %s' % (old_mac, mac))
            ports.append(new_port)
        logging.info("compare_with_node_ifconfig ports:%s", ports)
        return ports

    @staticmethod
    def compare_with_input_config(net_mac_mappings, userdata):
        ports = []
        for port in userdata.get('ports', []):
            old_mac = port.get('mac', '')
            new_port = copy.deepcopy(port)
            for net_name in net_mac_mappings.keys():
                if net_name == port.get("infra_network", ""):
                    if old_mac != net_mac_mappings[net_name]:
                        new_port['mac'] = net_mac_mappings[net_name]
                        logging.info(
                            'update user input mac, old: %s, new: %s' %
                            (old_mac, net_mac_mappings[net_name]))
            ports.append(new_port)
        logging.info("compare_with_input_config ports:%s", ports)
        return ports

    def process_only_server_id(self, server_id):
        node = self.get_node_info(server_id)
        api_ip, userdata, uuid = self.parser_node(node)
        if not userdata:
            logging.warning("got userdata is empty!")
            self.normal_exit()
        net_mac_mappings = self.get_user_input_mac()
        if not net_mac_mappings:
            logging.info("user input empty, get change port from node")
            ports = self.compare_with_node_ifconfig(api_ip, userdata)
        else:
            logging.info("user input not empty, change to user mac config")
            ports = self.compare_with_input_config(net_mac_mappings, userdata)
        if not ports:
            logging.info('mac not change, skip')
            self.normal_exit()
        self.put_userdata_to_node(uuid, {'userdata': {'ports': ports}})
        self.update_recover_minion_json(server_id, ports)

    def check_mac(self, value):
        try:
            if value.find('-') != -1:
                pattern = re.compile(r"^\s*([0-9a-fA-F]{2,2}-)"
                                     r"{5,5}[0-9a-fA-F]{2,2}\s*$")
                if pattern.match(value):
                    return True
                else:
                    return False
            if value.find(':') != -1:
                pattern = re.compile(r"^\s*([0-9a-fA-F]{2,2}:)"
                                     r"{5,5}[0-9a-fA-F]{2,2}\s*$")
                if pattern.match(value):
                    return True
                else:
                    return False
            return False
        except Exception:
            return False

    def check_nw_name_and_mac(self, nw_name, mac):
        try:
            nw_list = nw_name.split(',')
            mac_list = mac.split(',')
            if len(nw_list) != len(mac_list):
                print('nwname and mac must be same length')
                self.error_exit()
            for net in nw_list:
                if net:
                    continue
                print('not support empty nwname')
                self.error_exit()
            for mac in mac_list:
                if not self.check_mac(mac):
                    print('illegal mac %s' % mac)
                    self.error_exit()
        except Exception:
            logging.error(traceback.format_exc())
            self.error_exit()

    def process_user_config(self, server_id, nw_name, mac):
        node = self.get_node_info(server_id)
        api_ip, userdata, uuid = self.parser_node(node)
        ports = []
        self.check_nw_name_and_mac(nw_name, mac)
        net_mac_map = dict(zip(nw_name.split(','), mac.split(',')))
        for port in userdata.get('ports', []):
            infra_network = port.get('infra_network', '')
            new_port = copy.deepcopy(port)
            if infra_network in net_mac_map.keys():
                new_port['mac'] = net_mac_map[infra_network]
            ports.append(new_port)
        self.put_userdata_to_node(uuid, {'userdata': {'ports': ports}})

    def is_ume_preset(self):
        try:
            scene = ''
            mode = ''
            data_map = self.read_yaml_file(com_var)
            for data in data_map:
                if 'tcf_scenario' in data:
                    scene = data.get('tcf_scenario', '')
                if 'bearer_mode' in data:
                    mode = data.get('bearer_mode', '')
            if scene == 'UME-standard' and mode == 'prenode':
                return True
            return False
        except Exception:
            logging.error("check env scene error! %s", traceback.format_exc())
            return False

    def main(self):
        self.config_logging()
        server_id, nw_name, mac = self.parser_parameter()
        if not self.is_ume_preset():
            logging.warning("not ume preset node, exit")
            self.normal_exit()
        if server_id != '' and nw_name == '' and mac == '':
            logging.info('process pdm config')
            self.process_only_server_id(server_id)
        elif server_id != '' and nw_name != '' and mac != '':
            logging.info('process user config')
            self.process_user_config(server_id, nw_name, mac)
        else:
            logging.error('illegal parameter')
            self.error_exit()

    @staticmethod
    def get_target_node(nodes, server_id):
        target = {}
        ipaddrs = []
        try:
            for node in nodes:
                net_api = node.get("netinfo", {}).get("net_api", {}).get("ip", "")
                if "paas_controller" in node.get("roles", []) and net_api:
                    ipaddrs.append(net_api)
                if node.get("serverid") == server_id:
                    target = node
            return target, ipaddrs
        except Exception:
            logging.error(traceback.format_exc())
            return {}, []


if __name__ == '__main__':
    UpdateUserdata().main()
