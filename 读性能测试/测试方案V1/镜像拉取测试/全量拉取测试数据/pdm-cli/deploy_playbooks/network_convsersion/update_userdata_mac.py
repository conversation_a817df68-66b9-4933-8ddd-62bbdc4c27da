import argparse
import sys
import os
import json
import logging
import time

LOG_DIR = "/paasdata/op-log/pdm-cli"
LOG_FILE = "/paasdata/op-log/pdm-cli/update_userdata_mac.log"
RECOVER_MINION_FILE = "/paasdata/op-data/pdm-cli/recover_minion.json"

if not os.path.exists(LOG_DIR):
    os.makedirs(LOG_DIR, 0o750)
logging.basicConfig(level=logging.DEBUG,
                    format='%(asctime)s %(levelname)s %(funcName)s'
                    ' line[%(lineno)d]: %(message)s',
                    datefmt='%Y-%m-%d %H:%M:%S',
                    filename=LOG_FILE,
                    filemode='a')
LOG = logging.getLogger(__name__)
if os.path.exists(LOG_FILE):
    os.chmod(LOG_FILE, 0o640)


def sync_dir(dir_path, dest=None, hosts=None, retry_count=5, exclude_ip=''):
    if hosts is None:
        hosts = "/etc/pdm/hosts"
    if dir_path.endswith("/"):
        dir_path = os.path.split(dir_path)[0]
    src = dir_path
    if not dest:
        dest = os.path.split(dir_path)[0]
    exclude_cmd = ''
    if exclude_ip:
        exclude_cmd = '-l nodes,!%s ' % exclude_ip
    cmd = ('ansible nodes -i %s %s-m synchronize -a '
           '"src=%s dest=%s recursive=yes delete=yes rsync_timeout=60" '
           '-u ubuntu -b >/dev/null 2>/dev/null' %
           (hosts, exclude_cmd, src, dest))
    LOG.debug('Sync dir. \n%s', cmd)
    for _ in range(retry_count):
        result = os.system(cmd + '>/dev/null 2>/dev/null')
        if result != 0:
            time.sleep(5)
            LOG.debug('Sync dir failed and retry.')
        else:
            break
    else:
        LOG.debug('sync dir %s failed.' % src)
        return False
    LOG.debug('Sync dir successfully.')
    return True


def update_userdata_mac(serverid, nwname, mac):
    nwnames = nwname.split(",")
    macs = mac.split(",")
    if len(nwnames) != len(macs):
        raise Exception("nwname %s and mac %s length not equal" %
                        (nwnames, macs))
    if not os.path.exists(RECOVER_MINION_FILE):
        raise Exception("recover minion file %s not exist" %
                        RECOVER_MINION_FILE)
    with open(RECOVER_MINION_FILE, "r+") as f:
        minion_info = json.load(f)
        node_info = minion_info.get("node", {})
        if node_info.get("serverid", "") != serverid:
            raise Exception("serverid %s not exist int recover minion file" %
                            serverid)
        ports = node_info.get("userdata").get("ports")
        for index, nwname in enumerate(nwnames):
            for port_info in ports:
                if port_info.get("infra_network") == nwname:
                    port_info["mac"] = macs[index]
                    break
        f.seek(0, 0)
        json.dump(minion_info, f, indent=4)
    sync_dir(RECOVER_MINION_FILE)


if __name__ == "__main__":
    try:
        useage = ("python /etc/pdm/deploy_playbooks/"
                  "network_convsersion/update_userdata_mac.py"
                  " --serverid <serverid> --nwname <nwname1,nwname2> "
                  "--mac <mac1,mac2>")
        parser = argparse.ArgumentParser(usage=useage)
        parser.add_argument('--serverid', dest="serverid", required=True)
        parser.add_argument('--nwname', dest="nwname", required=True)
        parser.add_argument('--mac', dest="mac", required=True)
        args = parser.parse_args()
        update_userdata_mac(args.serverid, args.nwname, args.mac)
        sys.exit(0)
    except Exception as e:
        err_msg = "update userdata mac failed: %s" % str(e)
        LOG.debug(err_msg)
        sys.exit(1)
