# -*- coding: UTF-8 -*-
import requests
import json

try:
    import <PERSON>fig<PERSON><PERSON><PERSON> as configparser
except ImportError:
    import configparser
import logging
import traceback

try:
    import commands
except ImportError:
    import subprocess as commands

br_apidefault_file = '/etc/sysconfig/network-scripts/ifcfg-br-apidefault'
vnm_file = '/paasdata/op-conf/inetmanager/inetmanager.conf'
output_file = "/paasdata/op-data/pdm-cli/network_display/basic_netinfo.json"
cf = configparser.ConfigParser()
cf.read(vnm_file)
baseurl = cf.get("COMMON", "baseurl_vnm")
baseurl = baseurl + "/subnets?"
headers = {'content-type': 'application/json'}
# sto网络名称和子网名称
sto_names = ['net_sto', 'net_sto', 'sto_mgt']
sto_params = [{'name': 'subnet_sto'},
              {'name': 'subnet_sto_v6'},
              {'name': 'subnet_sto_mgt'}]


#   子网掩码和cidr之间的转换
def netmask_to_cidr(net_mask):
    """
    :param net_mask: net_mask ip addr (eg: *************)
    :return: equivalent cidr number to given netmask ip (eg: 24)
    """
    cd = sum([bin(int(x)).count('1') for x in net_mask.split('.')])
    return str(cd)


def create_netinfo(net_name, subnet_name, cidr):
    return {"net_name": net_name,
            "subnet_name": subnet_name,
            "cidr": cidr,
            "namespace": "admin"}


def get_net_api():
    logging.info('getting net_api')
    basic_netinfo = {"network_type": "plat network", "netinfo": []}
    try:
        with open(br_apidefault_file, "r") as f:
            lines = f.readlines()
            ipv4 = ''
            netmask = ''
            ipv6 = ''
            #   读取网卡文件
            for line in lines:
                if 'IPADDR' in line:
                    ipv4 = line.split('=')[1].strip()
                    logging.info('getting net_api IPv4: %s' % ipv4)
                if 'NETMASK' in line:
                    nm = line.split('=')[1].strip()
                    netmask = netmask_to_cidr(nm) if nm else ''
                    logging.info('getting net_api netmask: %s ' % netmask)
                if 'IPV6ADDR' in line:
                    ipv6 = line.split('=')[1].strip()
                    logging.info("getting net_api IPv6: %s" % ipv6)
            #   构造写入数据
            if ipv4:
                cidr = ipv4 + '/' + netmask
                netinfo = create_netinfo('net_api',
                                         'subnet_api', cidr)
                basic_netinfo['netinfo'].append(netinfo)
                logging.info('creating netinfo: %s' % netinfo)
            if ipv6:
                netinfo = create_netinfo('net_api',
                                         'subnet_api_v6', ipv6)
                basic_netinfo['netinfo'].append(netinfo)
                logging.info('creating netinfo: %s' % netinfo)
        return basic_netinfo
    except Exception:
        logging.error(traceback.format_exc())
        return basic_netinfo


def get_net_sto():
    logging.info('getting net_sto')
    basic_netinfo = {"network_type": "plat network", "netinfo": []}
    for i, param in enumerate(sto_params):
        response = requests.get(baseurl, params=param, headers=headers)
        if response:
            response.encoding = 'utf-8'
            content = json.loads(response.text)
            cidr = content.get("subnets")[0].get("cidr") \
                if content.get("subnets") else ''
            logging.info('getting response')
            if cidr:
                netinfo = create_netinfo(sto_names[i], param['name'], cidr)
                basic_netinfo['netinfo'].append(netinfo)
                logging.info('creating netinfo: %s' % netinfo)
    return basic_netinfo


# 获取网口信息
# 根据网口信息判断要解析的网络
cmdstr = "ovs-vsctl show | grep \"Bridge br-apidefault\""
status, output = commands.getstatusoutput(cmdstr)
if not status and output:
    basic_netinfo = get_net_api()
else:
    basic_netinfo = get_net_sto()

# 生成json文件写入数据
with open(output_file, 'w+') as fp:
    json.dump(basic_netinfo, fp)
