#!/bin/bash
#shellcheck disable=SC1083,SC2124,SC2046,SC2005,SC2028,SC2010,SC2115,SC2029,SC2034,SC2086,SC2013,SC2002

HOST_NAME="ubuntu"
readonly PROG_NAME=$(basename "$0")
ARGS="$@"

SCENARIO_FILE="/root/common/com_vars.yml"
LOG_PATH="/paasdata/op-log/nwmaster/get_network_info.log"
RESULT_FILE="/paasdata/op-data/pdm-cli/network_display/container_netinfo.json"
RESULT_ARRAY=()
KNITTER_JSON="/paasdata/op-conf/nwmaster/knitter.json"
IPPOOL_FLAG=0
GATEWAY_FLAG=0
ROUTES_FLAG=0
IPVX="ipv4"


usage()
{
    cat <<- EOF
    Usage : $PROG_NAME [OPTION]...
    Show Network information(include CIDR,GATEWAY,HOSTROUTES,IPPOOL..).
    OPTIONS:
        -h      Show help information.
        -p      Get networks' net_name,namespace,cidr and ippools.
        -g      Get networks' net_name,namespace,cidr and gateway.
        -r      Get networks' net_name,namespace,cidr and routes.
    EXAMPLE:
        ./$PROG_NAME
        ./$PROG_NAME -p
        ./$PROG_NAME -g
        ./$PROG_NAME -r
        ./$PROG_NAME -rpg
EOF
}

cmdline()
{
    while getopts :hpgr OPTION
    do
        case $OPTION in
            h)
                usage
                exit 0
                ;;
            p)
                IPPOOL_FLAG=1
                ;;
            g)
                GATEWAY_FLAG=1
                ;;
            r)
                ROUTES_FLAG=1
                ;;
            \?)
                echo "ERROR, invalid option -$OPTARG"
                usage
                exit 1
                ;;
        esac
    done

}

log_print(){
    TIME=$(date  +%Y-%m-%d\ %H:%M:%S)
    DEPLOY_LOG=${LOG_PATH}
    TYPE="$1"
    SOURCE="$2"
    PID="$3"
    OUTPUT="$4"
    if [ "$TYPE" == "INFO" ]; then
        echo "INFO $TIME    [$SOURCE] [PID:$PID] $OUTPUT" >> $DEPLOY_LOG
    elif [ "$TYPE" == "ERROR" ]; then
        echo "ERROR $TIME    [$SOURCE] [PID:$PID] $OUTPUT" >> $DEPLOY_LOG
    elif [ "$TYPE" == "WARNING" ]; then
        echo "WARNING $TIME    [$SOURCE] [PID:$PID] $OUTPUT" >> $DEPLOY_LOG
    fi
    if [ -f "${LOG_PATH}" ];then
	    chmod -R 640 $LOG_PATH
    fi
}

set_ipvx_by_url(){
  local origin_url=$1
  ip_addr=$(echo $origin_url | sed "s/.*\[\(.*\)\].*/\1/g")
  if [[ $ip_addr =~ .*:.* ]]; then
    IPVX="ipv6"
  else
    IPVX="ipv4"
  fi
}

get_host_routes_str(){
  local host_routes_str=""
  local host_routes_obj
  host_routes_obj="$1"
  len=$(echo "$host_routes_obj" |jq-linux64 '. |length')

  for ((i=0; i<len; i++))
  do
    host_route=$(echo "$host_routes_obj" |jq-linux64 '.['"${i}"']')
    destination=$(echo "$host_route" |jq-linux64 '.destination' | sed 's/\"//g')
    nextHop=$(echo "$host_route" |jq-linux64 '.nexthop' | sed 's/\"//g')
    host_routes_str="${host_routes_str} destination: ${destination}, nexthop: ${nextHop};"
  done
  echo "\"${host_routes_str}\""

}

get_ippools_str(){
  local ippools_str=""
  local ippools_obj
  ippools_obj="$1"
  len=$(echo "$ippools_obj" |jq-linux64 '. |length')
  for ((i=0; i<len; i++))
  do
    ippool=$(echo "$ippools_obj" |jq-linux64 '.['"${i}"']')
    start=$(echo "$ippool" |jq-linux64 '.start' | sed 's/\"//g')
    end=$(echo "$ippool" |jq-linux64 '.end' | sed 's/\"//g')
    ippools_str="${ippools_str} start: ${start}, end: ${end};"
  done
  echo "\"${ippools_str}\""

}

write(){
  local net_name
  local cidr
  local namespace
  local gateway
  local routes
  local ippools
  local result

  net_name="$1"
  cidr="$2"
  namespace="$3"
  gateway="$4"
  routes="$5"
  ippools="$6"

  result="\"net_name\":${net_name},\"subnet_name\":\"\",\"cidr\":${cidr},\"namespace\":${namespace}"

  if [ "$GATEWAY_FLAG" -eq "1" ];then
    result="${result},\"gateway\":${gateway}"
  fi

  if [ "$ROUTES_FLAG" -eq "1" ];then
    result="${result},\"routes\":${routes}"
  fi

  if [ "$IPPOOL_FLAG" -eq "1" ];then
    result="${result},\"ippools\":${ippools}"
  fi
  log_print INFO "$0.$LINENO" $$ "result:{${result}}"
  RESULT_ARRAY[${#RESULT_ARRAY[*]}]="{${result}}"

}

write_ipv4_info_cpass(){
  local network_name
  local network_namespace
  local network
  network_name="$1"
  network_namespace="$2"
  network="$3"

  local network_cidr_ipv4
  network_cidr_ipv4=$(echo "$network" |jq-linux64 '.cidr' )
  rc=$?
  if [ "$rc" -ne 0 ]; then
    log_print ERROR "$0.$LINENO" $$ "get ipv4 cidr from network wrong"
    exit 1
  fi
  if [ "$network_cidr_ipv4" == "\"\"" ]; then
    log_print WARNING "$0.$LINENO" $$ "network ipv4 cidr is \"\", will skip it"
    return 0
  fi

  local network_gateway_ipv4
  network_gateway_ipv4=$(echo "$network" |jq-linux64 '.gateway' )
  rc=$?
  if [ "$rc" -ne 0 ]; then
    log_print ERROR "$0.$LINENO" $$ "get ipv4 gateway from network wrong"
    exit 1
  fi

  local network_routes_ipv4_obj
  network_routes_ipv4_obj=$(echo "$network" |jq-linux64 '.host_routes' )
  rc=$?
  if [ "$rc" -ne 0 ]; then
    log_print ERROR "$0.$LINENO" $$ "get ipv4 host_routes from network wrong"
    exit 1
  fi
  network_routes_str=$(get_host_routes_str "$network_routes_ipv4_obj")


  local network_ippools_ipv4_obj
  network_ippools_ipv4_obj=$(echo "$network" |jq-linux64 '.allocation_pools' )
  rc=$?
  if [ "$rc" -ne 0 ]; then
    log_print ERROR "$0.$LINENO" $$ "get ipv4 allocation_pools from network wrong"
    exit 1
  fi
  network_ippools_str=$(get_ippools_str "$network_ippools_ipv4_obj")

  write "$network_name" "$network_cidr_ipv4" "$network_namespace" "$network_gateway_ipv4" "$network_routes_str" "$network_ippools_str"

}

write_ipv6_info_cpass(){
  local network_name
  local network_namespace
  local network
  network_name="$1"
  network_namespace="$2"
  network="$3"

  local network_cidr_ipv6
  network_cidr_ipv6=$(echo "$network" |jq-linux64 '.cidr_ipv6' )
  rc=$?
  if [ "$rc" -ne 0 ]; then
    log_print ERROR "$0.$LINENO" $$ "Get ipv6 cidr from network wrong"
    exit 1
  fi
  if [ "$network_cidr_ipv6" == "\"\"" ]; then
    log_print WARNING "$0.$LINENO" $$ "network ipv6 cidr is \"\", will skip it"
    return 0
  fi


  local network_gateway_ipv6
  network_gateway_ipv6=$(echo "$network" |jq-linux64 '.gateway_ipv6' )
  rc=$?
  if [ "$rc" -ne 0 ]; then
    log_print ERROR "$0.$LINENO" $$ "Get ipv6 gateway from network wrong"
    exit 1
  fi

  local network_routes_ipv6_obj
  network_routes_ipv6_obj=$(echo "$network" |jq-linux64 '.host_routes_ipv6' )
  rc=$?
  if [ "$rc" -ne 0 ]; then
    log_print ERROR "$0.$LINENO" $$ "get ipv6 host_routes from network wrong"
    exit 1
  fi
  network_routes_str=$(get_host_routes_str "$network_routes_ipv6_obj")


  local network_ippools_ipv6_obj
  network_ippools_ipv6_obj=$(echo "$network" |jq-linux64 '.allocation_pools_ipv6' )
  rc=$?
  if [ "$rc" -ne 0 ]; then
    log_print ERROR "$0.$LINENO" $$ "get ipv6 allocation_pools from network wrong"
    exit 1
  fi
  network_ippools_str=$(get_ippools_str "$network_ippools_ipv6_obj")

  write "$network_name" "$network_cidr_ipv6" "$network_namespace" "$network_gateway_ipv6" "$network_routes_str" "$network_ippools_str"

}

write_network_info_cpaas(){
  local network
  network="$1"

  local network_name
  network_name=$(echo "$network" |jq-linux64 '.name' )
  rc=$?
  if [ "$rc" -ne 0 ]; then
    log_print ERROR "$0.$LINENO" $$ "get network name from network wrong"
    exit 1
  fi

  if [ "$network_name" == "\"\"" ]; then
    log_print WARNING "$0.$LINENO" $$ "network name is \"\", will skip it"
    return 0
  fi

  local network_namespace
  network_namespace=$(echo "$network" |jq-linux64 '.owner' )
  rc=$?
  if [ "$rc" -ne 0 ]; then
    log_print ERROR "$0.$LINENO" $$ "get owner(namespace) from network wrong"
    exit 1
  fi
  if [ "$network_namespace" == "\"\"" ]; then
    log_print WARNING "$0.$LINENO" $$ "network owner(namespace) is \"\", will skip it"
    return 0
  fi

  write_ipv4_info_cpass "$network_name" "$network_namespace" "$network"
  rc=$?
  if [ "$rc" -ne 0 ]; then
    log_print ERROR "$0.$LINENO" $$ "call write_ipv4_info_cpass func wrong"
    exit 1
  fi

  write_ipv6_info_cpass "$network_name" "$network_namespace" "$network"
  rc=$?
  if [ "$rc" -ne 0 ]; then
    log_print ERROR "$0.$LINENO" $$ "call write_ipv6_info_cpass func wrong"
    exit 1
  fi

}

get_valid_tenant(){
  local tenant_name
  local tenant_obj
  local tenant_net_num
  tenant_obj="$1"
  tenant_name=$(echo "$tenant_obj" |jq-linux64 '.name' | sed 's/\"//g' )
  rc=$?
  if [ "$rc" -ne 0 ]; then
    log_print ERROR "$0.$LINENO" $$ "get tenant name from tenant_obj error"
    exit 1
  fi
  if [ "$tenant_name" == "" ];then
    log_print WARNING "$0.$LINENO" $$ "get invalid tenant name [\"\"] from tenant_obj"
    return 0
  fi
  tenant_net_num=$(echo "$tenant_obj" |jq-linux64 '.net_number' | sed 's/\"//g' )
  rc=$?
  if [ "$rc" -ne 0 ]; then
    log_print ERROR "$0.$LINENO" $$ "get tenant net_number from tenant_obj error"
    exit 1
  fi
  if [ "$tenant_net_num" -eq 0 ];then
    log_print WARNING "$0.$LINENO" $$ "tenant[$tenant_name] have 0 network, will skip it"
    return 0
  fi
  echo "$tenant_name"
}

get_valid_tenants(){
  local addr
  local tenants_json_obj
  local tenants_json_obj_len
  local tenants_name=()
  addr="$1"
  tenants_json_obj=$(curl -g "${addr}/tenants/")

  rc=$?
  if [ "$rc" -ne 0 ]; then
      log_print ERROR "$0.$LINENO" $$ "curl "${addr}/tenants/" error"
      exit 1
  fi

  tenants_json_obj_len=$(echo "$tenants_json_obj" |jq-linux64 '.tenant |length' )

  for ((i=0; i<tenants_json_obj_len; i++))
  do
    tenant_obj=$(echo "$tenants_json_obj" |jq-linux64 '.tenant['"${i}"']')
    rc=$?
    if [ "$rc" -ne 0 ]; then
      log_print ERROR "$0.$LINENO" $$ "get tenants[${i}] from tenant_json_obj error"
      exit 1
    fi
    log_print INFO "$0.$LINENO" $$ "Start to handle num[${i}] tenant: $tenant_obj"
    #network_name=$(echo "$networks_json_obj" |jq-linux64 .networks[${i}])
    #echo "$network"
    tenant_name=$(get_valid_tenant "$tenant_obj")
    rc=$?
    if [ "$rc" -ne 0 ]; then
      log_print ERROR "$0.$LINENO" $$ "call get_valid_tenant func error"
      exit 1
    fi
    if [ "$tenant_name" != "" ];then
      tenants_name[${#tenants_name[*]}]="$tenant_name"
    fi

  done
  echo "${tenants_name[*]}"
}

get_specific_tenant_networks_info(){
  local addr
  local tenant_name
  addr="$1"
  tenant_name="$2"
  local url
  url="${addr}/tenants/${tenant_name}/networks?all=true"
  log_print INFO "$0.$LINENO" $$ "nwmaster url is $url"

  local http_status_code
  http_status_code=$(timeout 20s curl -i -g -m 10 -o /dev/null -s -w %{http_code} "$url")
  rc=$?
  if [ "$rc" -ne 0 ]; then
    log_print ERROR "$0.$LINENO" $$ "curl -g $url ERROR, code: $rc"
    exit 1
  fi
  #echo "code:$http_status_code"
  if [ "$http_status_code" -gt 300 ] || [ "$http_status_code" -lt 200 ];then
    log_print ERROR "$0.$LINENO" $$ "visit nwmaster url error, http_status_code: $http_status_code"
    exit 1
  fi

  local networks_json_obj
  networks_json_obj=$(timeout 20s curl -g "$url" )
  rc=$?
  if [ "$rc" -ne 0 ]; then
    log_print ERROR "$0.$LINENO" $$ "curl -g $url ERROR, code: $rc"
    exit 1
  fi

  log_print INFO "$0.$LINENO" $$ "Get networks json object: ${networks_json_obj} "

  local networks_json_arr_len
  networks_json_arr_len=$(echo "$networks_json_obj" |jq-linux64 '.networks |length')
  rc=$?
  if [ "$rc" -ne 0 ]; then
    log_print ERROR "$0.$LINENO" $$ "get json object \"networks\"'s length from json wrong"
    exit 1
  fi

  log_print INFO "$0.$LINENO" $$ "There are [$networks_json_arr_len] networks to handle"

  for ((i=0; i<networks_json_arr_len; i++))
  do
    network=$(echo "$networks_json_obj" |jq-linux64 '.networks['"${i}"']')
    rc=$?
    if [ "$rc" -ne 0 ]; then
      log_print ERROR "$0.$LINENO" $$ "get num[${i}] network from networks obj error"
      exit 1
    fi

    log_print INFO "$0.$LINENO" $$ "Start to handle num[${i}] network: $network"

    write_network_info_cpaas "$network"
    rc=$?
    if [ "$rc" -ne 0 ]; then
      log_print ERROR "$0.$LINENO" $$ "call write_network_info_cpaas func error"
      exit 1
    fi
  done
}

get_ume_standard_network_info(){
  log_print INFO "$0.$LINENO" $$ "Scenario is cpaas or tcf cpass"

  local addr
  addr=$(cat "$KNITTER_JSON" |jq-linux64 '.conf.manager.self_service.url' | sed 's/\"//g' )
  rc=$?
  if [ "$rc" -ne 0 ]; then
    echo "get nwmaster url from knitter.json wrong"
    log_print ERROR "$0.$LINENO" $$ "get nwmaster url from knitter.json wrong"
    exit 1
  fi
  if [ "$addr" == "" ];then
    log_print ERROR "$0.$LINENO" $$ "get invalid addr [\"\"] from nwmaster knitter.json"
    exit 1
  fi
  #判断addr是否是空
  set_ipvx_by_url $addr
  if [ "$IPVX" == "ipv4" ]; then
    addr=$(echo $addr | sed "s/\[//g" | sed "s/\]//g")
  fi

  #tenants=($(get_valid_tenants "$addr"))
  #rc=$?
  #if [ "$rc" -ne 0 ]; then
   # log_print ERROR "$0.$LINENO" $$ "Get all tenants wrong"
   # exit 1
  #fi

  #log_print INFO "$0.$LINENO" $$ "Get "${#tenants[*]}" tenants: ["${tenants[*]}"]"

  tenants=("admin")
  for tenant in ${tenants[*]}
  do
    log_print INFO "$0.$LINENO" $$ "Start to handle tenant: ${tenant}"
    get_specific_tenant_networks_info "$addr" "$tenant"
    rc=$?
    if [ "$rc" -ne 0 ]; then
      log_print ERROR "$0.$LINENO" $$ "some error ocurred, when handling tenant: ${tenant} "
    fi
  done

}

get_tcf_network_info(){
  #调试
  echo "Not support this scenario!"
  log_print ERROR "$0.$LINENO" $$ "Not support this scenario!"
  exit 1

}

get_is_tcf(){
  local scenario_file
  scenario_file="$1"

  local is_tcf
  is_tcf=$(grep -w "is_tcf" "$scenario_file" |awk '{print $3}' | sed 's/\}//g')
  echo "$is_tcf"
}

get_tcf_scenario(){
    local scenario_file
    scenario_file="$1"

    local tcf_scenario
    tcf_scenario=$(grep -w "tcf_scenario" "$scenario_file" |awk '{print $3}' | sed 's/\}//g')
    echo "$tcf_scenario"
}

generate_final_json_result(){
  local result_str
  local len
  local json_result
  len=${#RESULT_ARRAY[*]}
  for ((i=0; i < len ; i++))
  do
    if [ "$i" -eq $((len-1)) ];then
      result_str="${result_str}${RESULT_ARRAY[$i]}"
      break
    fi
    result_str="${result_str}${RESULT_ARRAY[$i]},"
  done


  json_result="{\"network_type\":\"application network\",\"netinfo\":[$result_str]}"
  echo -e "json_result: \n$json_result"
  if command -v pythonlatest &> /dev/null; then
    python_cmd="pythonlatest"
  else
    python_cmd="python"
  fi
  json=$(echo "$json_result" |$python_cmd -m json.tool )
  echo "$json" > "$RESULT_FILE"
  chmod -R 640 "$RESULT_FILE"

}


main()
{
    cmdline $ARGS
    rm -f "$RESULT_FILE"
    if [ ! -f "$SCENARIO_FILE" ]; then
      echo "$SCENARIO_FILE not exist or not a common file!"
      log_print ERROR "$0.$LINENO" $$ "$SCENARIO_FILE not exist or not a common file!"
      exit 1
    fi
    local is_tcf
    is_tcf=$(get_is_tcf "$SCENARIO_FILE")

    tcf_scenario=$(get_tcf_scenario "$SCENARIO_FILE")

    log_print INFO "$0.$LINENO" $$ "is_tcf: $is_tcf; tcf_scenario: $tcf_scenario"
    if [[ "$is_tcf" == "true" && "$tcf_scenario" == "UME-standard" ]] || [ "$is_tcf" == "false" ]; then
      get_ume_standard_network_info
      echo -e "result: \n${RESULT_ARRAY[*]}"
      #echo "${RESULT_ARRAY[*]}"
      log_print INFO "$0.$LINENO" $$ "获取到的所有的网络信息: ${RESULT_ARRAY[*]}"
      generate_final_json_result
    else
      get_tcf_network_info
    fi

}
main
