#!/bin/bash

daemon_file="/etc/docker/daemon.json"
output_dir="/paasdata/op-data/pdm-cli/network_display/"
output_file="docker_netinfo.json"

docker0_cidr_ipv4=""
docker0_cidr_ipv6=""
dockerBridge_cidr_ipv4=""
dockerBridge_cidr_ipv6=""
docker0_network="false"
dockerBridge_network="false"

netinfo_items=$(cat <<"EOF"
        {
            "net_name":"",
            "subnet_name":"",
            "cidr":"",
            "namespace":""
        }
EOF
)

get_docker0_ipv4(){
    docker0_ipv4=$(ip route list dev docker0 |awk '{print $1}')
    if [ -n "$docker0_ipv4" ]; then
        docker0_cidr_ipv4="$docker0_ipv4"
        echo "docker0_cidr_ipv4=$docker0_ipv4"
    fi
}

get_dockerBridge_ipv4(){
    dockerBridge_ipv4=$(ip route list dev dockerBridge |awk '{print $1}')
    if [ -n "$dockerBridge_ipv4" ]; then
        dockerBridge_cidr_ipv4="$dockerBridge_ipv4"
        echo "dockerBridge_cidr_ipv4=$dockerBridge_ipv4"
    fi
}

get_docker0_ipv6(){
    docker0_ipv6=$(ip -6 route list dev docker0 |grep "fdff" |awk '{print $1}' |sort -u)
    if [ -n "$docker0_ipv6" ]; then
        docker0_cidr_ipv6="$docker0_ipv6"
        echo "docker0_cidr_ipv6=$docker0_ipv6"
    fi
}

get_dockerBridge_ipv6(){
    dockerBridge_ipv6=$(ip -6 route list dev dockerBridge |grep "fdff" |awk '{print $1}' |sort -u)
    if [ -n "$dockerBridge_ipv6" ]; then
        dockerBridge_cidr_ipv6="$dockerBridge_ipv6"
        echo "dockerBridge_cidr_ipv6=$dockerBridge_ipv6"
    fi
}

get_docker0_network(){
    docker0=$(docker network ls |awk '{print $2}' |grep -w "bridge")
    if [ -n "$docker0" ]; then
        echo "docker0 network is existed!"
        docker0_network="true"
    fi
}

get_dockerBridge_network(){
    dockerBridge=$(docker network ls |awk '{print $2}' |grep -w "dockerBridge")
    if [ -n "$dockerBridge" ]; then
        echo "dockerBridge network is existed!"
        dockerBridge_network="true"
    fi
}

# --- main ---

if [ ! -s "$daemon_file" ]; then
    echo "$daemon_file not existed or file is null!"
    exit 1
fi
if [ ! -d "$output_dir" ]; then
    echo "$output_dir not existed!"
    exit 1
fi

get_docker0_network
get_dockerBridge_network
get_docker0_ipv4
get_dockerBridge_ipv4
get_docker0_ipv6
get_dockerBridge_ipv6

if timeout 15 cat "$daemon_file" |grep "ipv6" |grep "true"; then
    echo "docker ipv6 is true!"

    if [ "$docker0_network" == "true" ] && [ "$dockerBridge_network" == "true" ]; then
netinfo_item=$(cat <<"EOF"
        {
            "net_name":"docker0",
            "subnet_name":"",
            "cidr":"docker0_cidr_ipv4",
            "namespace":""
        },
        {
            "net_name":"docker0",
            "subnet_name":"",
            "cidr":"docker0_cidr_ipv6",
            "namespace":""
        },
        {
            "net_name":"dockerBridge",
            "subnet_name":"",
            "cidr":"dockerBridge_cidr_ipv4",
            "namespace":""
        },
        {
            "net_name":"dockerBridge",
            "subnet_name":"",
            "cidr":"dockerBridge_cidr_ipv6",
            "namespace":""
        }
EOF
)
    fi

    if [ "$docker0_network" == "true" ] && [ "$dockerBridge_network" == "false" ]; then
netinfo_item=$(cat <<"EOF"
        {
            "net_name":"docker0",
            "subnet_name":"",
            "cidr":"docker0_cidr_ipv4",
            "namespace":""
        },
        {
            "net_name":"docker0",
            "subnet_name":"",
            "cidr":"docker0_cidr_ipv6",
            "namespace":""
        }
EOF
)
    fi

    if [ "$docker0_network" == "false" ] && [ "$dockerBridge_network" == "true" ]; then
netinfo_item=$(cat <<"EOF"
        {
            "net_name":"dockerBridge",
            "subnet_name":"",
            "cidr":"dockerBridge_cidr_ipv4",
            "namespace":""
        },
        {
            "net_name":"dockerBridge",
            "subnet_name":"",
            "cidr":"dockerBridge_cidr_ipv6",
            "namespace":""
        }
EOF
)
    fi

else
    echo "docker ipv6 is false!"

    if [ "$docker0_network" == "true" ] && [ "$dockerBridge_network" == "true" ]; then
netinfo_item=$(cat <<"EOF"
        {
            "net_name":"docker0",
            "subnet_name":"",
            "cidr":"docker0_cidr_ipv4",
            "namespace":""
        },
        {
            "net_name":"dockerBridge",
            "subnet_name":"",
            "cidr":"dockerBridge_cidr_ipv4",
            "namespace":""
        }
EOF
)
    fi

    if [ "$docker0_network" == "true" ] && [ "$dockerBridge_network" == "false" ]; then
netinfo_item=$(cat <<"EOF"
        {
            "net_name":"docker0",
            "subnet_name":"",
            "cidr":"docker0_cidr_ipv4",
            "namespace":""
        }
EOF
)
    fi

    if [ "$docker0_network" == "false" ] && [ "$dockerBridge_network" == "true" ]; then
netinfo_item=$(cat <<"EOF"
        {
            "net_name":"dockerBridge",
            "subnet_name":"",
            "cidr":"dockerBridge_cidr_ipv4",
            "namespace":""
        }
EOF
)
    fi
fi

if [ "$docker0_network" == "true" ] || [ "$dockerBridge_network" == "true" ]; then
    netinfo_items=${netinfo_item/docker0_cidr_ipv4/$docker0_cidr_ipv4}
    netinfo_items=${netinfo_items/docker0_cidr_ipv6/$docker0_cidr_ipv6}
    netinfo_items=${netinfo_items/dockerBridge_cidr_ipv4/$dockerBridge_cidr_ipv4}
    netinfo_items=${netinfo_items/dockerBridge_cidr_ipv6/$dockerBridge_cidr_ipv6}
fi

cat > ${output_dir}${output_file} <<EOF
{
    "network_type":"docker",
    "netinfo": [
$netinfo_items
    ]
}
EOF

