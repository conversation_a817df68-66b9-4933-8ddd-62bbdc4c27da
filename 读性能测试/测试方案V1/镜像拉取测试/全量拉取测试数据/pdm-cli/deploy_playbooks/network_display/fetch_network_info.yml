- remote_user: ubuntu
  become: true
  become_method: sudo
  hosts: nodes
  gather_facts: no
  vars:
  - collected_network_info_dir: /paasdata/op-data/pdm-cli/collected_network_info
  - network_info_dir: /paasdata/op-data/pdm-cli/network_display
  tasks:
  - name: fetch files
    synchronize:
      src: "{{ network_info_dir }}"
      dest: "{{ collected_network_info_dir }}/{{ inventory_hostname }}/"
      mode: pull
    ignore_errors: yes
  - name: delete file
    file:
      path: "{{ network_info_dir }}"
      state: absent