#!/bin/sh

input_file="/etc/kubernetes/manifests/kube-apiserver.yml"
output_dir="/paasdata/op-data/pdm-cli/network_display/"
output_filename="k8s_netinfo.json"

# 定义netinfo中元素的格式，"EOF"表示不替换字符串中的变量$cidr
netinfo_item=$(cat <<"EOF"
        {
            "net_name":"",
            "subnet_name":"",
            "cidr":"$cidr",
            "namespace":""
        }
EOF
)

if [ ! -f "$input_file" ]; then
    echo "$input_file NOT existed!"
    exit 1
fi
if [ ! -d "$output_dir" ]; then
    echo "$output_dir NOT existed!"
    exit 1
fi

# 从kube-apiserver.yml文件中获取k8s的service的网段配置
svc_addrs=$(< $input_file awk -F= '/service-cluster-ip-range/ {print $2}')
svc_array=${svc_addrs//,/ }

# 根据获取的k8s的service的网段配置生成需要输出的网段信息格式文本
netinfo_items=
# shellcheck disable=SC2068
for cidr in ${svc_array[@]}
do
    if [ -z "$netinfo_items" ]; then
        netinfo_items=${netinfo_item/'$cidr'/$cidr}
    else
        netinfo_items=$(cat <<EOF
${netinfo_items},
${netinfo_item/'$cidr'/$cidr}
EOF
)
    fi
done

# 输出网段信息到k8s_netinfo.json文件中
cat > ${output_dir}${output_filename} <<EOF
{
    "network_type":"k8s",
    "netinfo": [
$netinfo_items
    ]
}
EOF
